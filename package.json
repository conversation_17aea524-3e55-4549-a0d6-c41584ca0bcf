{"name": "tooler", "version": "0.1.0", "private": true, "type": "module", "scripts": {"clean": "rm -rf public && rm -rf node_modules", "test": "jest --passWithNoTests", "deploy": "firebase deploy --only hosting", "dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src/**/*.js src/**/*.jsx", "lint:fix": "eslint --fix src/**/*.js src/**/*.jsx", "eslint-check": "eslint --print-config .eslintrc.js | eslint-config-prettier-check", "prepare": "husky install"}, "dependencies": {"@algolia/recommend": "4.13.0", "@algolia/recommend-react": "1.3.0", "@algolia/ui-components-horizontal-slider-react": "1.0.0", "@algolia/ui-components-horizontal-slider-theme": "1.0.0", "@fortawesome/fontawesome-free-regular": "5.0.13", "@fortawesome/fontawesome-free-solid": "5.0.13", "@fortawesome/fontawesome-svg-core": "1.2.36", "@fortawesome/free-brands-svg-icons": "5.15.4", "@fortawesome/free-solid-svg-icons": "5.15.4", "@fortawesome/react-fontawesome": "0.1.15", "@mui/material": "5.10.12", "@mui/styles": "5.14.18", "algoliasearch": "4.11.0", "axios": "1.8.4", "bootstrap": "5.1.1", "eslint-plugin-react": "7.31.10", "formik": "2.2.9", "i18next": "21.2.0", "i18next-browser-languagedetector": "6.1.2", "i18next-http-backend": "1.4.5", "install": "^0.13.0", "instantsearch.css": "7.4.5", "jspdf": "3.0.1", "lodash": "4.17.21", "posthog-js": "^1.257.0", "prop-types": "15.7.2", "qs": "6.10.5", "react": "17.0.2", "react-bootstrap": "1.6.3", "react-code-input": "3.10.1", "react-data-table-component": "7.5.2", "react-datepicker": "4.8.0", "react-dom": "17.0.2", "react-floating-action-button": "1.0.5", "react-i18next": "11.12.0", "react-icons": "4.3.1", "react-instantsearch-dom": "6.15.0", "react-instantsearch-dom-maps": "6.15.0", "react-multivalue-text-input": "2.5.0", "react-phone-number-input": "3.1.38", "react-router": "5.2.0", "react-router-dom": "6.4.3", "react-select": "5.2.1", "react-window": "1.8.10", "reactstrap": "8.9.0", "rsuite": "5.6.1", "sweetalert2": "11.6.13", "swiper": "11.0.5", "universal-cookie": "8.0.1", "xlsx": "0.18.5", "yup": "0.32.11"}, "devDependencies": {"@babel/plugin-transform-runtime": "7.19.6", "@babel/preset-env": "7.19.4", "@babel/preset-react": "7.18.6", "@emotion/react": "11.10.5", "@emotion/styled": "11.10.5", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^12.1.5", "@testing-library/user-event": "^14.6.1", "@vitejs/plugin-react": "4.3.4", "babel-plugin-styled-components": "2.0.7", "eslint": "8.57.1", "eslint-config-airbnb": "19.0.4", "eslint-config-prettier": "8.5.0", "eslint-config-react-app": "7.0.0", "eslint-plugin-prettier": "4.2.1", "husky": "8.0.0", "jest": "29.2.2", "jest-environment-jsdom": "^30.1.2", "prettier": "2.7.1", "react-functional-select": "3.3.3", "sass": "1.55.0", "styled-components": "5.3.6", "vite": "6.2.4", "vite-plugin-html": "3.2.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}