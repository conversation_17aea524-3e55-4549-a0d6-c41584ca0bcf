/*
  • help
  ---------- ---------- ---------- ---------- ----------
*/

.help {
  &__topSection {
    h1 {
      margin-bottom: 15px;
      @media(min-width: 992px) {
        margin-bottom: 50px;
      }
    }

    .form-group {
      position: relative;
      margin: 0 auto;

      @media(min-width: 1200px) {
        max-width: 1020px;
      }

      input {
        padding: 15px 30px;
        width: 100%;
        margin-bottom: 0;
        min-height: 77px;
        border-radius: 70px;
      }

      .button-searchHelp {
        position: absolute;
        right: 10px;
        background: $yellow;
        width: 55px;
        height: 55px;
        border-radius: 55px;
        top: 10px;

        svg {
          font-size: 25px;
        }
      }
    }
  }

  &__popular-questions {
    margin-top: 40px;
    @media(min-width: 992px) {
      margin-top: 95px;
    }

    p {
      text-decoration: underline;
      text-align: left;
      @media(min-width: 992px) {
        text-align: right;
      }

      &:hover {
        text-decoration: none;
      }
    }

    .questions {
      margin-top: 20px;
      @media(min-width: 992px) {
        margin-top: 140px;
      }

      p {
        padding: 15px 10px;
        background: $white;
        border-radius: 30px;
        text-align: center;
        text-decoration: none;
        position: relative;
        margin-bottom: 10px;

        @media(min-width: 992px) {
          top: -20px;
          margin-bottom: 30px;
          padding: 25px 10px;
        }

        @media(min-width: 1300px) {
          font-size: 26px;
        }
      }

      .mobile-image {
        max-width: 200px;
        margin: 20px auto 0;
      }
    }
  }

  &__messageBlock {
    min-height: 520px;
    background-size: cover;
    display: flex;
    align-items: center;
    position: relative;
    margin-top: 0;
    @media(min-width: 992px) {
      margin-top: 160px;
    }

    .container-message {
      width: 100%;
    }

    .left-side,
    .right-side {
      position: relative;
      display: flex;
      align-items: flex-start;

      p {
        padding: 15px;
        background: $white;
        border-radius: 30px;
        text-align: center;
        max-width: 260px;

        @media(min-width: 992px) {
          padding: 25px 35px;
        }

        @media(min-width: 1300px) {
          max-width: 410px;
        }
      }

      &--image {
        img {
          object-fit: cover;
        }
      }
    }

    .left-side {
      margin-bottom: 50px;
      @media(min-width: 992px) {
        margin-bottom: 0;
      }

      &--image {
        position: relative;
        top: 50px;

        img {
          height: 130px;
          width: 101px;
          @media(min-width: 992px) {
            height: 190px;
            width: 147px;
          }
        }
      }

      p {
        margin-left: 20px;
      }
    }

    .right-side {
      flex-direction: row-reverse;

      &--image {
        position: relative;
        top: 50px;

        img {
          height: 95px;
          width: 125px;
          float: right;
          @media(min-width: 992px) {
            height: 165px;
            width: 235px;
          }
        }
      }
    }

    .equip,
    .equipGrey {
      position: absolute;
      z-index: -1;
      display: none;
      @media(min-width: 992px) {
        display: block;
      }
    }

    .equip {
      right: 270px;
      top: 16px;
      max-width: 150px;
      @media(min-width: 1300px) {
        top: 0;
      }
    }

    .equipGrey {
      max-width: 100px;
      top: -90px;
      left: 100px;
    }
  }
}