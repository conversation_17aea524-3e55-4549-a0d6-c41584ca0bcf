.padding {
  padding-top: 50px;
}

.transparent {
  background-color: transparent;
}

.ais-SearchBox {
  margin: 0 0 1em 0;
  width: 100% !important;
}

.ais-InfiniteHits-loadMore:disabled[disabled] {
  .load-more {
    display: none;
  }
}

.ais-Pagination {
  margin-top: 1em;
}

.left-panel {
  float: left;
}

.right-panel {
  width: 250px;
  margin-left: 260px;
}

.ais-InstantSearch {
  max-width: 960px;
  overflow: hidden;
  margin: 0 auto;
}

.ais-Hits-item {
  margin-bottom: 1em;

  img {
    margin-right: 1em;
  }
}

.align-items {
  p {
    display: inline-flex;
    letter-spacing: 10px;
  }
}

.equipper-box-text {
  display: block;
}

.toola-search-results {
  width: 70px;
  max-height: 100px;
  @media (max-width: 992px) {
    display: none;
  }
}

.Container-search-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 0 10% !important;
  background-color: $light-grey;
  padding: 0;
  margin: 0;
}

.space-down {
  padding-bottom: 3%;
}

@media (min-width: 750px) and (max-width: 1000px) {
  .no-margin {
    margin: 0 !important;
  }

  .margin-right {
    margin-right: 4%;
  }
}

@media (max-width: 1000px) {
  .margin-right {
    margin-right: 4%;
  }
}

.list-size {
  width: 100%;
  height: 100%;
}

.btn {
  &:focus,
  &:hover,
  &:active {
    outline: none !important;
    box-shadow: none !important;
  }
}

.empty-state-container {
  justify-content: center;
  display: grid;
  margin-top: 20px;

  .empty-content__image {
    text-align: center;
  }

  img {
    margin-bottom: 30px;
    @media (max-width: 992px) {
      max-width: 50%;
    }
  }
}

.isSM {
  @media (max-width: 768px) {
    visibility: hidden !important;
    display: none !important;
  }
}

.text-align-center {
  text-align: center;
}

.ais-infinite-hits--showmoreButton:disabled {
  display: none;
}

.Container-filters {
  background-color: $white;
  border-radius: 17px !important;
  margin: 10px;
  max-height: 720px;
  min-height: 500px;
  width: 100%;
  z-index: 100;
  padding: 02px 10px 50px 20px !important;
}

.no-underline {
  text-transform: capitalize;
  text-decoration: none;
}

.scrollBar {
  margin: 20px 0;
  padding: 0 10px;
  height: auto;
  overflow-y: scroll;
  overflow-x: hidden;
  @media (min-width: 992px) {
    min-height: 500px;
  }
}

.scrollBar::-webkit-scrollbar {
  width: 6px;
}

.scrollBar::-webkit-scrollbar-track {
  border-radius: 20px;
  background-color: rgb(235, 231, 231);
}

.scrollBar::-webkit-scrollbar-thumb {
  border-radius: 20px;
  background-color: $yellow;
}

​ .Container-filters ais-GeoSearch-map {
  width: 100%;
  border-radius: 17px;
}

.ais-GeoSearch-map {
  border-radius: 17px !important;
}

.ais-Hits-item,
.ais-InfiniteHits-item {
  align-items: center !important;
  background: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  width: 100% !important;
}

.ais-InfiniteHits-loadMore,
.ais-Menu-showMore {
  padding: 0 !important;
  border-radius: 5% !important;
  background-color: transparent !important;
  color: $primary-color !important;
  background-image: none !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  margin-bottom: 1rem;
}

.ais-ClearRefinements {
  button {
    border-radius: 5% !important;
    background-color: transparent !important;
    color: $yellow !important;
    background-image: none !important;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    padding: 0;
  }

  h5 {
    color: gray !important;
    font-weight: bold;
    font-size: 14px;
    margin-top: 0;
  }
}

.ais-Menu-showMore {
  width: 20%;
}

.search-result-datepicker {
  .react-daterange-picker__wrapper {
    display: flex;
    flex-grow: 1;
    flex-shrink: 0;
    align-items: center;
    border: 0;
    flex-direction: column;
  }

  .react-daterange-picker__inputGroup {
    min-width: 231px;
    height: 36px;
    flex-grow: 1;
    padding: 0 45% 0 8%;
    box-sizing: content-box;
    border-radius: 24px;
    border: 1px solid lightgray;
    margin: 0 0 5% 51%;
  }

  .iput-location {
    width: 325px;
    height: 31px;
    flex-grow: 1;
    padding: 1% 0 0 8%;
    box-sizing: content-box;
    border-radius: 24px;
    border: 1px solid lightgray;
    margin: 2% 0.5% 5% 0;
  }

  .iput-location {
    input {
      border: none;
      width: 80%;
      margin-left: 5px;

      &:focus,
      &:active,
      &:hover {
        border: none;
        outline: none;
      }
    }
  }
}

.search-result-datepicker .iput-location input::selection {
  background: none;
}

.color-gray {
  color: gray;
}

.list-size .ais-InstantSearch {
  min-width: 1200px;
  overflow: hidden;
  margin: 0 auto;
}

.search-count {
  margin-top: 60px;
}

.filters-row {
  flex: 0 0 auto !important;
  width: 11.666667% !important;
}

.show-more-filters {
  color: gray !important;
  font-weight: bold;
  font-size: 14px;
}

.current-refinement-item {
  margin: 3px;
  height: 20px;
  border: 2px solid black;
  border-radius: 49px;
  padding: 8px;
  background: white;
  text-decoration: none;
}

.uic-HorizontalSlider-list {
  grid-auto-columns: 75%;
  @media (min-width: 992px) {
    grid-auto-columns: calc(25% - 2px);
    margin-right: -20px;
  }

  &::-webkit-scrollbar-track {
    background-color: rgba(236, 168, 105, 0.2);
  }

  &::-webkit-scrollbar {
    height: 7px;
    background-color: rgba(236, 168, 105, 0.2);
  }

  &::-webkit-scrollbar-thumb {
    background-color: $yellow;
  }
}

.uic-HorizontalSlider-list-inventory {
  @media (min-width: 992px) {
    display: grid;
    grid-auto-columns: 140% !important;
    margin-right: -20px;
  }
}

.label {
  padding-right: 20px;
  font-size: 18px;
}

.current-refinement-container {
  overflow-x: scroll;
  overflow-y: hidden;
  -ms-overflow-style: none;
  scrollbar-width: none;
  display: flex;
  align-items: flex-start;
  width: calc(100% - 170px);
  position: relative;
  z-index: 1;
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.equipment_specification {
  padding: 10px 16px;
  border-radius: 6px;
  border: 1px solid $border-grey;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px 0 rgba(173, 173, 173, 0.15);
  margin-top: 0;
  width: max-content;
  min-width: fit-content;
  display: flex;
  align-items: center;

  img {
    height: 18px;
    position: relative;
    top: -2px;
  }
}

.uic-HorizontalSlider-item {
  padding-right: 20px;

  p {
    margin-bottom: 0 !important;
    @media (min-width: 992px) {
      min-height: 40px !important;
    }
  }

  .col-lg-4 {
    margin: 30px 0 25px;
    width: 100% !important;
  }
}

.uic-HorizontalSlider-navigation {
  color: $black;
  border-radius: 50%;
  width: 40px;

  svg {
    width: 20px;
    height: 20px;
  }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
