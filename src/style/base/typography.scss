//------------------------------------------------------------------------
// ............... Summary....................
// . Head titles
// . Base titles
// . Label titles
//------------------------------------------------------------------------
//------------------------------------------------------------------------
// Head titles
//------------------------------------------------------------------------

.t-header {
  font-family: $secondary-font;
  font-weight: 700;

  &-h1 {
    @extend .t-header;
    font-size: 24px;
    line-height: 100%;

    @media(min-width: 992px) {
      font-size: 57px;
      line-height: 64px;
    }
  }

  &-h2 {
    @extend .t-header;
    font-size: 22px;
    line-height: 20px;

    @media(min-width: 992px) {
      font-size: 37px;
      line-height: 50px;
    }
  }

  &-h3 {
    @extend .t-header;

    font-size: 21px;
    line-height: 27px;
    @media(min-width: 992px) {
      font-size: 34px;
      line-height: 35px;
    }
  }

  &-h4 {
    @extend .t-header;

    font-size: 19px;
    line-height: 25px;
    @media(min-width: 992px) {
      font-size: 29px;
      line-height: 35px;
    }
  }

  &-h5 {
    @extend .t-header;

    font-size: 26px;
    line-height: 32px;

  }

  &-h6 {
    @extend .t-header;

    font-size: 22px;
    line-height: 28px;
    @media(min-width: 992px) {
      font-size: 24px;
      line-height: 32px;
    }

    &.bold {
      font-weight: 700;
    }

    &.bigger {
      @media(min-width: 992px) {
        font-size: 26px;
      }
    }
  }
}

.t-subheading {
  font-family: $primary-font;

  &-1 {
    @extend .t-subheading;

    font-size: 18px;
    line-height: 22px;
    @media(min-width: 992px) {
      font-size: 24px;
      line-height: 28px;
    }

    &.bold {
      font-weight: 700;
    }
  }

  &-2 {
    @extend .t-subheading;
    font-size: 14px;
    line-height: 17px;

    @media(min-width: 992px) {
      font-size: 16px;
      line-height: 130%;
    }

    &.bold {
      font-weight: 700;
    }
  }

  &-3 {
    @extend .t-subheading;

    font-size: 14px;
    line-height: 24px;

    &.bold {
      font-weight: 700;
    }
  }

  &-4 {
    @extend .t-subheading;

    font-size: 14px;
    line-height: 20px;

    &.bold {
      font-weight: 700;
    }
  }
}

.t-body {
  font-family: $primary-font;
  font-weight: normal;

  &-large {
    @extend .t-body;

    font-size: 16px;
    line-height: 22px;
    @media(min-width: 992px) {
      font-size: 18px;
      line-height: 26px;
    }
    
    &.bold {
      font-weight: 700;
    }
  }

  &-price {
    @extend .t-body;
    font-size: 14px;
    line-height: 22px;
    @media(min-width: 992px) {
      font-size: 12px;
      line-height: 26px;
    }
    &.bold {
      font-weight: 700;
    }

  }

  &-regular {
    @extend .t-body;

    font-size: 14px;
    line-height: 20px;
    @media(min-width: 992px) {
      font-size: 16px;
      line-height: 24px;
    }

    &.bold {
      font-weight: 700;
    }
  }
  &-price {
    @extend .t-body;
    font-size: 14px;
    line-height: 20px;
    @media(min-width: 992px) {
      font-size: 12px;
      line-height: 24px;
    }

    &.bold {
      font-weight: 700;
    }
  }

  &-small {
    @extend .t-body;

    font-size: 10px;
    line-height: 13px;
    @media(min-width: 992px) {
      font-size: 14px;
      line-height: 20px;
    }

    &.bold {
      font-weight: bold;
    }
  }
  &-extra-small {
    @extend .t-body;

    font-size: 10px;
    line-height: 13px;
   

    &.bold {
      font-weight: bold;
    }
  }
}

.t-caption {
  font-family: $primary-font;

  &-small {
    font-size: 12px;
    line-height: 20px;

    &.bold {
      font-weight: 700;
    }
  }
}