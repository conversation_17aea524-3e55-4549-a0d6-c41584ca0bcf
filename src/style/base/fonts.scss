// Import fonts here
/*@font-face {
  font-family: 'airbnb-cereal-light';
  src: url('/src/fonts/airbnb-cereal-font/AirbnbCereal_W_Lt.otf');
  font-weight: bold;
}
@font-face {
  font-family: 'airbnb-cereal-medium';
  src: url('/src/fonts/airbnb-cereal-font/AirbnbCereal_W_Bk.otf');
  font-weight: normal;
}
@font-face {
  font-family: 'airbnb-cereal-bold';
  src: url('/src/fonts/airbnb-cereal-font/AirbnbCereal_W_Md.otf');
  font-weight: bold;
}*/

@font-face {
  font-family: 'airbnb-cereal-medium';
  src: url('/src/fonts/airbnb-cereal-font/AirbnbCerealWBk.woff2') format('woff2'),
  url('/src/fonts/airbnb-cereal-font/AirbnbCerealWBk.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'airbnb-cereal-light';
  src: url('/src/fonts/airbnb-cereal-font/AirbnbCerealWLt.woff2') format('woff2'),
  url('/src/fonts/airbnb-cereal-font/AirbnbCerealWLt.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'airbnb-cereal-bold';
  src: url('/src/fonts/airbnb-cereal-font/AirbnbCerealWMd.woff2') format('woff2'),
  url('/src/fonts/airbnb-cereal-font/AirbnbCerealWMd.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
