.round-button {
  font-size: 14px;
  font-weight: 600;
  padding: 10px 25px;
  border-radius: 6px;
  min-width: 130px;
  color: $white;
  border: 2px solid;
  transition: background ease-in-out 0.8s;
  @media (min-width: 572px) {
    padding: 10px 15px;
    font-size: 17px;
  }
  @media (max-width: 992px) {
    padding: 12px 6px;
  }

  &.black {
    color: $fake-black;
    border: none;
    background: white;
  }

  &:hover {
    opacity: 0.8;
  }

  &.yellow {
    background: $yellow;
    border: 2px solid $yellow;
  }

  &.white {
    background: $white;
    border-color: $white;

    &:hover {
      background: transparent;
      border-color: $black;
    }

    &.no-hover {
      background: $white;
      border-color: $white;
    }
  }

  &.grey {
    background: $check-grey;
    border-color: $check-grey;

    &:hover {
      background: transparent !important;
      border-color: $check-grey !important;
    }
  }

  &.shadow {
    //box-shadow: 0 3px 10px #00000029;
    box-shadow: none !important;
  }

  &.border-yellow {
    border-color: $yellow;
    background: transparent;
    color: $black;

    &:hover {
      background: $yellow;
      border-color: $yellow;
    }
  }

  &.grey {
    background: $check-grey;
    color: $white;
    border: 1px solid $check-grey;

    &:hover {
      background: none;
      color: $check-grey;
    }
  }

  &.light-yellow {
    background: $light-yellow;
    border: 2px solid $light-yellow;

    &:hover {
      background: none;
    }
  }

  &.fake-black {
    background: $fake-black;
    border: 2px solid $fake-black;
  }

  &.light-gray {
    background: $light-gray;
    border-color: $light-gray;

    &:hover {
      background: none;
    }
  }

  &.border-neutrals-gray {
    background: transparent;
    border-color: $neutrals-gray;

    &:hover {
      background: $neutrals-gray;
    }
  }

  &.transparent {
    background: transparent;
    border: 2px solid transparent;
  }

  &.disabled {
    opacity: 0.8;
  }

  &.with-arrow {
    &:after {
      content: url('../style/assets/img/Icons/ArrowRightBlack.svg');
      height: 23px;
      padding-left: 8px;
    }
  }

  &.with-arrow-white {
    &:after {
      content: url('../style/assets/img/Icons/ArrowRightWhite.svg');
      height: 23px;
      padding-left: 8px;
    }
  }

  &.cart {
    &:after {
      content: url('../style/assets/img/Icons/ShoppingCartBlack.svg');
      height: 24px;
      padding-left: 8px;
    }
  }

  &.extrabold {
    font-weight: 600;
  }

  &.no-minw-mob {
    @media (max-width: 572px) {
      min-width: auto;
    }
  }

  &.bg-neutrals-gray {
    background: $neutrals-gray;
  }
}

.w-btn-80 {
  .round-button {
    @media (max-width: 992px) {
      width: 80%;
    }
  }
}

.fixed-button-modal {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  padding-top: 10px;
  padding-bottom: 10px;
  background-color: white;

  .scrollBarModal & {
    width: calc(100% + 20px);
  }
}

.explore-btn {
  font-size: 12px !important;
}
