/*
  ---------- ---------- -----Summary----- ---------- ---
  • Form select / dropdown
  • Input type Date
  • Form group
  • Radio box
  • Range box
  • Check box
  • Sign up focus phone number
  ---------- ---------- ---------- ---------- ----------
*/
/*
  • Form select / dropdown
  ---------- ---------- ---------- ---------- ----------
*/
.form-control {
  &:focus {
    border-color: transparent;
    box-shadow: none;
  }
}

.form-select {
  box-shadow: 0 3px 10px #00000029;
  border-radius: 26px;
  min-width: 130px;
  max-width: 100%;
  width: 100%;
  border: 0;
  margin-bottom: 10px;
  text-align: center;
  -webkit-appearance: none;
  position: relative;
  min-height: 30px;
  background-image: none;
  @media (min-width: 768px) {
    min-width: 210px;
    max-width: 70%;
    min-height: 50px;
  }
  @media (min-width: 1200px) {
    margin-bottom: 0;
  }

  &::after {
    position: absolute;
    right: 12px;
    top: 14px;
    transition: ease all 0.3s;
    @media (min-width: 768px) {
      right: 20px;
      top: 23px;
    }
  }
}

.show {
  .dropdown-toggle {
    &::after {
      transform: rotate(180deg);
    }
  }
}

.dropdown-menu {
  &.show {
    width: 100%;
    border-radius: 7px;
  }

  .dropdown-item {
    border-radius: 30px;
    text-align: center;
    padding: 10px;

    &:hover {
      background-color: transparent;
    }

    &:active {
      color: $primary-color;
      background-color: transparent;
    }
  }
}

/*
  • Input type Date
  ---------- ---------- ---------- ---------- ----------
*/

input[type='date']::-webkit-calendar-picker-indicator {
  position: absolute;
  left: -20px;
  top: 8px;
  opacity: 0.8;
  filter: invert(0.6);
  @media (min-width: 768px) {
    top: 15px;
  }
}

input::-webkit-datetime-edit {
  position: relative;
  left: 0;
}

input::-webkit-datetime-edit-fields-wrapper {
  position: relative;
  left: 0;
}

/*
  • Form group
  ---------- ---------- ---------- ---------- ----------
*/

.form-group {
  .form-control {
    font-family: $primary-font;
    font-weight: normal;
    font-size: 16px;
    line-height: 24px;
    border-radius: 5px;
    height: 48px;
    width: auto;
    color: $fake-black;
    border-color: $light-gray;
    padding: 12px 18px;

    &:focus,
    &:active,
    &:hover {
      border: 1px solid $check-grey;
    }

    &::-webkit-input-placeholder {
      /* Edge */
      font-family: $primary-font;
      font-weight: normal;
      font-size: 16px;
      line-height: 24px;

      color: $neutrals-gray;
    }

    &:-ms-input-placeholder {
      /* Internet Explorer 10-11 */
      font-family: $primary-font;
      font-weight: normal;
      font-size: 16px;
      line-height: 24px;

      color: $neutrals-gray;
    }

    &::placeholder {
      font-family: $primary-font;
      font-weight: normal;
      font-size: 16px;
      line-height: 24px;

      color: $neutrals-gray;
    }

    &.is-valid {
      border-color: $new-green;
      box-shadow: 0 1px 8px 0 rgba(13, 163, 23, 0.1);
    }

    &.is-invalid {
      border-color: $red;
      box-shadow: 0 1px 8px 0 rgba(255, 49, 58, 0.1);
    }
  }

  input[type='date'] {
    position: relative;
    padding-left: 35px;
    width: 100%;
  }

  &.location {
    input {
      width: 100%;
      padding-left: 45px;
    }

    li {
      position: relative;

      &:before {
        content: url('../style/assets/img/Icons/Search.svg');
        width: 24px;
        display: inline-block;
        position: absolute;
        top: 11px;
        left: 11px;
      }

      .date-location-modal & {
        &:before {
          content: url('../style/assets/img/Icons/Search.svg');
        }
      }
    }

    .autocomplete-options-menu {
      li {
        &:before {
          display: none;
        }
      }
    }
  }
}

.label-input {
  font-family: $primary-font;
  font-weight: normal;
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 6px;
  color: $fake-black;
}

/*
  • Radio box
  ---------- ---------- ---------- ---------- ----------
*/

.radio-box [type='radio']:checked,
.radio-box [type='radio']:not(:checked) {
  position: absolute;
  left: -9999px;
}

.radio-box [type='radio']:checked ~ label,
.radio-box [type='radio']:not(:checked) ~ label {
  position: relative;
  cursor: pointer;
  line-height: 20px;
  display: inline-block;
}

.radio-box.left [type='radio']:checked ~ label,
.radio-box.left [type='radio']:not(:checked) ~ label {
  padding-left: 25px;
  line-height: 27px;
  font-size: 14px;
  @media (min-width: 572px) {
    padding-left: 35px;
    font-size: 16px;
  }
}

.radio-box.left [type='radio']:checked ~ label {
  font-weight: 700;
}

.radio-box [type='radio']:checked ~ label:before,
.radio-box [type='radio']:not(:checked) ~ label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 19px;
  height: 19px;
  border: 2px solid $yellow;
  border-radius: 100%;
  background: $yellow;
  @media (min-width: 992px) {
    width: 22px;
    height: 22px;
  }
}

.radio-box.left [type='radio']:checked ~ label:before,
.radio-box.left [type='radio']:not(:checked) ~ label:before {
  right: auto;
  left: 0;
}

.radio-box [type='radio']:not(:checked) ~ label:before {
  border-color: $neutrals-gray;
  background: $white;
}

.radio-box [type='radio']:checked ~ label:after,
.radio-box [type='radio']:not(:checked) ~ label:after {
  content: '';
  width: 11px;
  height: 11px;
  background: $white;
  position: absolute;
  top: 4px;
  left: 4px;
  border-radius: 100%;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  @media (min-width: 992px) {
    width: 10px;
    height: 10px;
    top: 6px;
    left: 5px;
  }
}

.radio-box.left [type='radio']:checked ~ label:after,
.radio-box.left [type='radio']:not(:checked) ~ label:after {
  left: 6px;
  right: auto;
}

.radio-box [type='radio']:not(:checked) ~ label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

.radio-box [type='radio']:checked ~ label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}

.check-label {
  display: flex !important;
  font-size: 16px;
  padding-left: 10px;
  flex-direction: row-reverse;
  justify-content: flex-end;
  @media (max-width: 992px) {
    font-size: 15px;
  }
}

/*
  • Range box
  ---------- ---------- ---------- ---------- ----------
*/
.dual-range {
  --range-size: 11px;
  --range-width: 200px;
  --handle-size: 1.3;
  height: var(--range-size);
  width: var(--range-width);
  background: $light-grey;
  border-radius: 50px;
  position: relative;
  user-select: none;

  .highlight {
    position: absolute;
    height: var(--range-size);
    width: calc(
      calc(var(--x-2) - var(--x-1)) +
        calc(var(--range-size) * var(--handle-size))
    );
    left: var(--x-1);
    background: $yellow;
    z-index: 1;
    border-radius: 50px;
  }

  .handle {
    width: calc(var(--range-size) * var(--handle-size));
    height: calc(var(--range-size) * var(--handle-size));
    background: $white;
    border: 1px solid $medium-grey;
    position: absolute;
    box-shadow: var(--shadow);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
    cursor: grab;

    &:active {
      cursor: grabbing;
    }

    &.left {
      left: var(--x-1);
    }

    &.right {
      left: var(--x-2);
    }

    &::after {
      content: attr(data-value) 'kg';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
    }

    &.height {
      &::after {
        content: attr(data-value) 'm';
      }
    }

    &.price {
      &::after {
        content: attr(data-value) '$';
      }
    }
  }
}

input[type='range'] {
  box-sizing: border-box;
  appearance: none;
  width: 155px;
  margin: 0;
  overflow: hidden;
  padding: 0 2px;
  border: 0;
  outline: none;
  background: linear-gradient($light-grey, $light-grey) no-repeat center;
  background-size: 100% 11px;
  pointer-events: none;
  border-radius: 45%;

  &:active,
  &:focus {
    outline: none;
  }

  &::-webkit-slider-thumb {
    height: 15px;
    width: 15px;
    border-radius: 45%;
    border: 1px solid $medium-grey;
    background: $white;
    position: relative;
    margin: 5px 0;
    cursor: pointer;
    appearance: none;
    pointer-events: all;
    z-index: 2;

    &::before {
      content: ' ';
      display: block;
      position: relative;
      top: 13px;
      left: 100%;
      width: 2000px;
      height: 1px;
      background: $medium-grey;
    }
  }
}

/*
  • Check box
  ---------- ---------- ---------- ---------- ----------
*/

.check-box {
  input {
    padding: 0;
    height: initial;
    width: initial;
    margin-bottom: 0;
    display: none;
    cursor: pointer;
  }

  label {
    position: relative;
    cursor: pointer;
    display: flex;
    min-height: 34px;
    align-items: center;
    font-size: 15px;

    &:before {
      content: '';
      -webkit-appearance: none;
      background-color: transparent;
      border: 2px solid $check-grey;
      border-radius: 3px;
      padding: 10px;
      display: inline-block;
      position: absolute;
      right: 0;
      vertical-align: middle;
      cursor: pointer;
      margin-right: 5px;
      width: 22px;
      height: 22px;
      @media (min-width: 992px) {
        width: 26px;
        height: 26px;
      }
    }
  }

  input:checked + label:after {
    content: '';
    display: block;
    position: absolute;
    top: 12px;
    right: 14px;
    width: 7px;
    height: 14px;
    border: solid $white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    @media (min-width: 992px) {
      top: 10px;
      right: 15px;
      width: 8px;
      height: 12px;
    }
  }

  input:checked + label:before {
    background-color: $yellow;
    border-color: $yellow;
  }

  input:disabled + label:before {
    background-color: transparent;
    border-color: transparent;
  }

  &.left {
    label {
      &::before {
        right: auto;
        left: 0;
      }
    }

    input:checked + label:after {
      left: 8px;
      right: auto;
      @media (min-width: 992px) {
        right: auto;
        left: 9px;
      }
    }
  }
}

/*
  • Sign up focus phone number
  ---------- ---------- ---------- ---------- ----------
*/

input.PhoneInputInput {
  &:focus-visible {
    outline: none;
  }
}

.css-u94lkr-control {
  min-height: 48px !important;
}

.style-wrapper {
  display: flex;
}

.badge-area-show {
  position: relative;
}

.bagde-flag-wrap {
  position: absolute;
  top: 20px;
  left: -10px;
}

.bagde-flag-wrap::before {
  content: '';
  position: absolute;
  top: 35px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 12px 12px 0;
  border-color: transparent $green transparent transparent;
}

.bagde-flag {
  background: $green;
  letter-spacing: 0;
  font-size: 14px;
  z-index: 100000;
  line-height: 15px;
  font-weight: 600;
  padding: 10px 15px;
  display: block;
}
