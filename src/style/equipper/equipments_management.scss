/*
  • Global
  • Search bar
  • equipments content
  ---------- ---------- ---------- ---------- ----------
*/

/*
  • Global
  ---------- ---------- ---------- ---------- ----------
*/

.equipments-management {
  padding: 60px 70px;
  background: $white;
  border-radius: 30px;
  @media (max-width: 992px) {
    margin-top: 30px;
  }

  .width-filter-category {
    @media (min-width: 992px) {
      width: 25%;
    }

    .filter-category {
      .accordionDrop {
        border-left: 1px solid $border-grey;
      }
    }

    .search-inventory {
      margin-left: 0;

      .ais-SearchBox {
        width: 100% !important;

        .ais-SearchBox-form {
          width: 100%;
        }
      }
    }
  }

  &.white-bg {
    @media (max-width: 992px) {
      background: none;
      padding-left: 0;
      padding-right: 0;
    }
  }

  .equipments-content {
    img {
      min-height: 165px;
    }

    @media (max-width: 992px) {
      h2 {
        margin-top: 15px;
      }
      .price {
        font-size: 18px;
        line-height: 20px;
        margin-bottom: 0;
      }
      .actions {
        margin-top: 20px;

        button {
          margin-bottom: 10px;
          width: 100%;
        }
      }
    }
  }
}

/*
  • Search bar
  ---------- ---------- ---------- ---------- ----------
*/

.equipments-search {
  .tabulation & {
    margin-top: 8px;
  }

  .form-group {
    position: relative;
    max-width: 350px;

    .form-control {
      min-width: 350px;
      margin-bottom: 0;
      @media (max-width: 572px) {
        min-width: 100%;
      }
    }

    button {
      position: absolute;
      right: 5px;
      top: 7px;
      background: $light-grey;
      width: 36px;
      height: 36px;
      border-radius: 35px;
      @media (max-width: 992px) {
        right: 4px;
        top: calc(50% - 15px);
        background: $yellow;
        width: 30px;
        height: 30px;
      }

      svg {
        color: $near-grey;
        @media (max-width: 992px) {
          color: $primary-color;
        }
      }
    }
  }

  .round-button {
    svg {
      margin-right: 10px;
    }
  }
}

/*
  • equipments content
  ---------- ---------- ---------- ---------- ----------
*/

.white-bg {
  padding: 10px 25px 5px;
  border-radius: 30px;
  background: $white;

  &.mobile-noBg {
    @media (max-width: 992px) {
      background: none;
    }
  }
}

@media (max-width: 992px) {
  .white-bg {
    background: none;
    padding: 0px;
  }
}

.css-zun73v.Mui-checked,
.css-zun73v.MuiCheckbox-indeterminate {
  color: #eca869 !important;
}
.equipments-content {
  margin-top: 15px;
  border: 1px solid $check-grey;
  border-radius: 14px;
  padding: 25px;
  @media (max-width: 992px) {
    margin-bottom: 10px;
  }

  img {
    border: 1px solid $check-grey;
    border-radius: 7px;
    @media (max-width: 992px) {
      max-height: 300px;
      width: 100%;
      object-fit: cover;
    }
    @media (max-width: 572px) {
      max-height: 180px;
    }
  }

  .price {
    margin-bottom: 5px;
    @media (max-width: 992px) {
      margin-top: 10px;
    }
  }

  .padding-l-0 {
    @media (max-width: 992px) {
      padding-right: 0 !important;
    }
  }

  .price {
    padding-bottom: 0;
  }
}

.equipments-all {
  margin-top: 40px;
  text-align: center;
}

.loading-tolo {
  display: flex;
  justify-content: space-evenly;
}

.underline-center {
  display: flow-root !important;
  text-decoration: underline;
}

.loading-image-tolo-size {
  height: auto;
  @media (min-width: 992px) {
    height: 500px;
  }
}

/*
  • Modal
  ---------- ---------- ---------- ---------- ----------
*/

.file_box {
  text-align: center;
  padding: 32px 24px;
  border: 1px solid $border-grey;
  border-radius: 14px;
  background: $white;
  box-shadow: 0 4px 17px 0 rgba(173, 173, 173, 0.07);
  height: 340px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  height: 340px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  @media (max-width: 992px) {
    margin-bottom: 24px;
  }
  &:hover {
    cursor: pointer;
    border: outset;
  }

  h3 {
    margin-bottom: 10px;
  }

  span {
    text-decoration: underline;
    cursor: pointer;

    &:hover {
      text-decoration: none;
    }
  }

  label {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    width: 100%;
  }

  &__input {
    justify-content: center;
    align-items: center;
    margin-top: 40px;
  }
}

.bottom-file {
  margin-top: 30px;

  button {
    width: 100%;
  }
}

.add-equipment-modal {
  h2 {
    margin-top: 0;
  }
}

.mr-10 {
  margin-right: 10px;
}

.margin-top-250 {
  margin-top: 250px;
}

.accordion {
  border-radius: 28px;
  background: $white;

  &.accordion-equipments-management {
    margin-top: 0;

    @media (min-width: 1200px) {
      height: calc(100% - 15px);
    }

    .result-box__image {
      img {
        max-height: 160px;
      }
    }

    .scrollBarModal__accordion {
      overflow-x: hidden;
      height: calc(350vh - 120px);
    }

    .result-box {
      &.with-border {
        border-radius: 12px;
        border: 1px solid $border-grey;
        box-shadow: 0 11px 40px 0 rgba(6, 28, 61, 0.07);
        margin-bottom: 4px;
        padding: 20px;
        position: relative;
        @media (max-width: 992px) {
          margin-bottom: 15px;
        }

        .close-button {
          position: absolute;
          left: 10px;
          top: 10px;
          background: none;
        }
      }
    }
  }
}

.status {
  border-radius: 30px;
  background: $white;
  margin-bottom: 25px;

  h2 {
    padding-left: 40px;
    position: relative;
    margin-bottom: 25px;

    &::before {
      content: '';
      width: 28px;
      height: 28px;
      position: absolute;
      left: 0;
      margin-right: 12px;
      border-radius: 30px;
    }

    span {
      margin-left: 10px;
    }
  }

  &.available {
    h2 {
      &::before {
        background: $green;
      }
    }
  }

  &.booked {
    h2 {
      &::before {
        background: $rose;
      }
    }
  }

  &.idle {
    h2 {
      &::before {
        background: $grey;
      }
    }
  }
}

.underline-yellow {
  text-decoration: underline;
  color: $yellow;
}

.result-box {
  &.with-border {
    border-radius: 12px;
    border: 1px solid $border-grey;
    margin-bottom: 4px;
    padding: 20px;
    position: relative;
    @media (max-width: 992px) {
      margin-bottom: 15px;
    }
  }
}

.status-box {
  width: auto;
  height: 39px;
  padding: 6px 16px;
  gap: 16px;
  border-radius: 4px 0px 0px 0px;
  opacity: 1;
  @media (max-width: 992px) {
    padding: 5px;
    text-align: center;
    font-size: 12px;
  }
}

.one-equipment {
  @media (max-width: 992px) {
    .result-box {
      margin: 0;
      border: 0;
      padding: 0px 20px;
    }
  }
}

.status-available {
  background: #0da3171a;
  color: $new-green;
}

.status-idle {
  background: #a9a9a91a;
  color: $grey-titles;
}

.status-booked {
  background: #f0e68c1a;
  color: $yellow;
}

.status-text {
  text-transform: capitalize;
}
.alias-display {
  word-wrap: break-word; /* Ensures long text breaks and wraps properly */
  white-space: normal; /* Ensures it doesn't stay in a single line */
  display: block; /* Ensure the alias gets its own line */
  margin-bottom: 10px; /* Adds space between alias and the next element */
}
