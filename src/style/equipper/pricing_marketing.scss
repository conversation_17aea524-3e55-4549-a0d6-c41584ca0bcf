/*
  ---------- ---------- -----summary----- ---------- ---
  • Apply promotion
  • Delete promotion
  ---------- ---------- ---------- ---------- ----------
*/

/*
  • Apply promotion
  ---------- ---------- ---------- ---------- ----------
*/

.promotion-modal {
  .dropdown-toggle {
    width: 100%;
    text-align: left;
    box-shadow: 0 3px 10px $grey;
    height: 50px;

    &:hover {
      box-shadow: 0 3px 10px $grey !important; //to force class btn on hover
    }

    &::after {
      position: absolute;
      right: 22px;
      top: 24px;
    }
  }

  .dropdown {
    margin-bottom: 16px;
  }

  .btn-content {
    margin-top: 60px;
  }
}

/*
  • Delete promotion
  ---------- ---------- ---------- ---------- ----------
*/

.delete-modal {
  text-align: center;

  .content-delete {
    margin-bottom: 55px;

    p {
      margin-bottom: 5px;
    }
  }

  .btn-content {
    display: flex;
    justify-content: center;

    button {
      min-width: 140px;
      margin: 0 6px;
      @media(min-width: 572px) {
        min-width: 220px;
      }
    }

    .border-yellow {
      background: transparent;

      &:hover {
        background: $yellow;
      }
    }
  }
}


.delete-button-promotion{
  color: red;
  background-color: transparent !important;
  &:hover {
    text-decoration: underline;
  }
}