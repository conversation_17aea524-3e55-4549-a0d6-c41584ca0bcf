div.scrollmenu {
  overflow: hidden;
  white-space: nowrap;

  a {
    display: inline-block;
    color: white;
    text-align: center;
    padding: 14px;
    text-decoration: none;

    &:hover {
      background-color: #777;
    }
  }
}

._inputItem_18w4i_1 {
  background: white !important;
  border-radius: 4px;
  border: 1px solid #d9d9d9 !important;
  color: hsl(0, 0%, 20%);
  padding: 5px;
}

._deleteButton_18w4i_11 {
  span {
    font-weight: bold;
    &:hover {
      background-color: #ffbdad;
      color: #de350b;
    }
  }
}

.multiple-value-text-input-item-container {
  p {
    display: flex;
    flex-wrap: wrap;
    span {
      margin: 2px;
    }
  }
}
