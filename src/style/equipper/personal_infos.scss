/*
  • personal infos
  ---------- ---------- ---------- ---------- ----------
*/

.personal-infos {
  background: $white;
  padding: 10px 0;
  position: relative;
  border-bottom: 1px solid $border-grey;


  &__image {
    border: 2px solid $near-grey;
    border-radius: 100%;
    width: 180px;
    height: 180px;
    overflow: hidden;
    @media(max-width: 992px) {
      width: 90px;
      height: 90px;
      margin: 0 auto;
    }

    img {
      width: 180px;
      height: 180px;
      @media(max-width: 992px) {
        width: 90px;
        height: 90px;
      }
    }
  }

  &_img {
    border-radius: 100px;
    margin-right: 40px;
    object-fit: cover;
  }

  &__content {
    @media(max-width: 992px) {
      margin-bottom: 15px;
    }

    p {
      margin-bottom: 5px;
    }
  }

  button {
    margin: 15px auto;
    display: block;
    @media(min-width: 992px) {
      /*position: absolute;
        top: 35px;*/
      margin: 0;
    }
  }
}
