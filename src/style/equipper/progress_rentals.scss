
.search-sort {
  margin-right: 20px;
}

.progress-rental-sort {
  @media(max-width: 992px) {
    margin-top: 20px;
  }

  .dropdown {
    min-width: 315px;

    .toggle-style {
      width: 100%;
    }
  }

  .dropdown-toggle {
    &::after {
      position: absolute;
      right: 16px;
      top: 18px;
    }
  }
}

.equipments-content {
  background: $white;

  &__bottom {
    margin-top: 40px;

    p {
      svg {
        color: $yellow;
        width: 20px;
      }
    }

    .rental__lodge {
      display: flex;
      align-items: center;

      img {
        width: 36px;
        height: 36px;
        border-radius: 36px;
      }

      span {
        margin-left: 5px;
      }
    }

    &.rental-content {
      margin-top: 20px;
      @media(min-width: 992px) {
        margin-top: 40px;
      }
    }
  }
}

.gery-bg {
  padding: 35px 0;
  border-radius: 7px;
  @media(min-width: 992px) {
    background: $light-grey;
    padding: 35px 20px;
  }

  &.rental-grey-box {
    padding: 20px 0;
    @media(min-width: 992px) {
      padding: 35px 20px;
    }
    @media(max-width: 992px) {
      p {
        margin-bottom: 0;
        padding-bottom: 10px;
      }
    }
  }
}

.rental-grey-box {
  span {
    background: $white;
    border-radius: 7px;
    padding: 10px 0;
    font-size: 12px;
    @media(min-width: 992px) {
      padding: 10px 20px;
      font-size: 16px;
    }
  }

  p {
    font-size: 12px;
    @media(min-width: 992px) {
      font-size: 16px;
    }
  }
}

.rental__rate {
  span {
    margin-left: 5px;
  }

  @media(max-width: 992px) {
    margin-left: 36px;
    p {
      margin-bottom: 0;
    }
  }
}
