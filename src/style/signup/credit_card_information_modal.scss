.modal-core {
    padding: 2% 3% !important;
}
.modal-core h3 {
    text-align: CENTER;
}
.modal-core .button-signup {
    width: 50%;
    margin-top: 40px;
}

.modal-core .css-1s2u09g-control {
    -webkit-align-items: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background-color: hsl(0, 0%, 100%);
    border-color: hsl(0, 0%, 80%);
    border-radius: 28px;
    border-style: solid;
    border-width: 1px;
    cursor: default;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    min-height: 45px;
    outline: 0 !important;
    position: relative;
    -webkit-transition: all 100ms;
    transition: all 100ms;
    box-sizing: border-box;
    margin-top: 5px;
}
.modal-core .css-1pahdxg-control {
    -webkit-align-items: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background-color: hsl(0, 0%, 100%);
    border-color: hsl(0, 0%, 80%);
    border-radius: 28px;
    border-style: solid;
    border-width: 1px;
    cursor: default;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    min-height: 45px;
    outline: 0 !important;
    position: relative;
    -webkit-transition: all 100ms;
    transition: all 100ms;
    box-sizing: border-box;
    margin-top: 5px;
    
}

.hint {
    margin-left: 15px;
    margin-top: 2px;
    color: gray;
    font-size: 12px;
}
.modal-core .Input-form {
    width: 100%;
}
.modal-core .Input-form-with-hint {
    display: flex;
    border-radius: 28px;
    margin: 0;
    width: 100%;
    padding: 1.5%;
    padding-left: 2%;
    border: 1px solid #bbb7b7;
    height: 45px;
}
.padding-10s {
    padding: 1.5% 2% !important;
}
