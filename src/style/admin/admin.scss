// Admin Area Styles
@import '../base/variables.scss';

.admin-login-container {
  background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
  min-height: 100vh;

  .card {
    border: none;
    border-radius: 15px;
    
    .card-body {
      border-radius: 15px;
    }
  }

  h2 {
    color: #333;
    font-weight: 600;
  }

  .text-muted {
    font-size: 0.9rem;
  }

  .form-control {
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 12px 15px;
    font-size: 0.95rem;

    &:focus {
      border-color: $primary-color;
      box-shadow: 0 0 0 0.2rem rgba(6, 28, 61, 0.25);
    }
  }

  .btn-primary {
    background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(6, 28, 61, 0.4);
    }

    &:disabled {
      transform: none;
      box-shadow: none;
    }
  }
}

// Admin Dashboard Styles
.admin-dashboard {
  background-color: #f8f9fa;
  min-height: 100vh;

  .card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .card-header {
      background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
      color: white;
      border-radius: 10px 10px 0 0 !important;
      border: none;
      font-weight: 600;
    }

    .card-body {
      padding: 1.5rem;
    }
  }

  // Stats cards
  .text-primary { color: $primary-color !important; }
  .text-success { color: #28a745 !important; }
  .text-info { color: #17a2b8 !important; }
  .text-warning { color: #ffc107 !important; }

  // Table styles
  .table {
    margin-bottom: 0;

    th {
      background-color: #f8f9fa;
      border-top: none;
      font-weight: 600;
      color: #495057;
      font-size: 0.9rem;
    }

    td {
      vertical-align: middle;
      font-size: 0.9rem;
    }

    .rounded {
      border-radius: 6px !important;
    }
  }

  .table-responsive {
    border-radius: 8px;
    overflow: hidden;
  }

  // Header styles
  h1, h3 {
    color: #333;
    font-weight: 600;
  }

  .btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
    border-radius: 8px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      background-color: #6c757d;
      border-color: #6c757d;
      transform: translateY(-1px);
    }
  }

  // Loading spinner
  .spinner-border {
    color: $primary-color;
  }

  // Alert styles
  .alert {
    border-radius: 8px;
    border: none;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .admin-login-container {
    .card-body {
      padding: 2rem !important;
    }
  }

  .admin-dashboard {
    .card-body {
      padding: 1rem;
    }

    .table-responsive {
      font-size: 0.8rem;
    }

    .d-flex.justify-content-between {
      flex-direction: column;
      gap: 1rem;

      .btn {
        align-self: flex-end;
      }
    }
  }
}

// Animation for cards
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.admin-dashboard .card {
  animation: fadeInUp 0.5s ease-out;
}

// Custom scrollbar for table
.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// Search and filter controls
.admin-dashboard {
  .input-group-text {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: $primary-color;
    font-size: 1.1rem;
  }

  .form-control, .form-select {
    &:focus {
      border-color: $primary-color;
      box-shadow: 0 0 0 0.2rem rgba(6, 28, 61, 0.25);
    }
  }

  // Enhanced search box styling
  .input-group {
    .form-control {
      border-radius: 0;
      transition: all 0.3s ease;

      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 0.2rem rgba(6, 28, 61, 0.15);
        transform: translateY(-1px);
      }
    }

    .input-group-text {
      border-radius: 8px 0 0 8px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-color: #dee2e6;
      transition: all 0.3s ease;
    }

    .btn {
      border-radius: 0 8px 8px 0;
      transition: all 0.3s ease;

      &:hover {
        background-color: #e9ecef;
        transform: translateY(-1px);
      }
    }
  }

  // Search results highlighting
  .badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
    border-radius: 6px;

    &.bg-info {
      background-color: #17a2b8 !important;
    }
  }

  // Table styling enhancements
  .table {
    th {
      background-color: #f8f9fa;
      border-color: #dee2e6;
      font-weight: 600;
      color: $primary-color;
    }

    td {
      vertical-align: middle;
    }
  }

  // Results summary
  .text-muted {
    font-size: 0.875rem;
  }

  // Clear filters button
  .btn-outline-secondary {
    border-color: $primary-color;
    color: $primary-color;

    &:hover {
      background-color: $primary-color;
      border-color: $primary-color;
    }
  }

}
