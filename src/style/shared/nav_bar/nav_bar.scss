.menuButton {
  background-color: transparent !important;
  box-shadow: none !important;
  border-color: transparent !important;
  border: 0 !important;
  border-radius: 100% !important;
  text-align: center !important;
  vertical-align: middle;
  width: 30px !important;
  padding: 0.7px 0 0 0 !important;
  height: 30px !important;
  content: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  content: none !important;

  svg {
    color: #333333;
  }
}

.menuButton:hover {
  content: none !important;
  background-color: transparent !important;
  box-shadow: none !important;
  border-color: transparent !important;
  border: 0 !important;
  border-radius: 100% !important;
  text-align: center !important;
  vertical-align: middle;
  //    horizontal-align: middle;
  width: 30px !important;
  padding: 0.7px 0px 0px 0px !important;
  height: 30px !important;
  content: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
}

.menuButton::after {
  content: none !important;
}

.userButtonBackground {
  background-color: rgb(0, 0, 0);
  border-radius: 100%;
  margin-right: 5px;
  border: solid 1px black;
  vertical-align: middle;
  size: 40px;
}



.nav-link-Login {
  display: block;
  padding:0px 5px 5px 0px;
  font-weight: 400;
  text-decoration: none;
  color: black;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
  border-color 0.15s ease-in-out;
}

.fr-lang {
  .nav-link-Login {
    font-size: 12px;
  }
}

.custom-dropdown-menu {
  position: absolute;
  z-index: 1000;
  display: none;
  padding: 0.5rem 0;
  font-size: 1rem;
  color: #212529;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: none;
  border-radius: 1rem;
  box-shadow: 1px 20px 50px -17px rgba(0, 0, 0, 0.32);

  .img {
    width: 26px;
    height: 26px;
    border: 2px solid #d1d1d1;
    border-radius: 7px;
    margin-right: 10px;
    display: inline-flex;
    align-items: center;
    justify-content: center;

    img {
      &.small-icon {
        width: 15px;
        height: 15px;
      }
    }
  }

  span {
    width: calc(100% - 40px);
  }

  .black {
    span {
      border-color: #302f2f;
    }
  }

  &.dropdown-menu {
    .dropdown-item {
      text-align: left;
      padding: 5px 10px;
      display: flex;
      align-items: center;
    }

    &.show {
      min-width: 200px;
      width: max-content;
    }
  }
}

.custom-dropdown-menu-position {
  margin-top: 3px !important;
}

.content-Align {
  justify-content: center !important;
}

@media (max-width: 800px) {
  .custom-dropdown-menu-position {
    margin-top: 0% !important;
    margin-left: 0% !important;
  }
}

.search-bar-size {
  margin-right: 115px !important;
  width: 70% !important;
}

@media (max-width: 1200px) and (min-width: 1000px) {
  .search-bar-size {
    margin-right: 60px !important;
  }
}

@media (max-width: 1000px) {
  .search-bar-size {
    margin-right: 60px !important;
    display: none !important;
  }
}

.mbt80 {
  margin-bottom: 80px !important;
}

.mbt40 {
  margin-bottom: 20px !important;
}
