.Container-Style {
    background-color: #ffffff;
    border-radius: 15px;
    height: 200px;
    width: 100%;
    vertical-align: middle;
    margin-top: 10px;
    @media (max-width: 768px) {
        background-color: #ffffff;
        border-radius: 15px;
        height: 400px;
        width: 100%;
        vertical-align: middle;
        margin-bottom: 10px;
        margin-top: 10px;
    }
}

.background-styles {
    background-color: #f1f1f1;
}

.title-style {
    font-size: 24px;
    font-weight: bold;
    color: #000000;
    margin-bottom: 20%;
}

.price-style {
    display: inline;
    text-align: end;
    font-size: 25px;
    font-weight: bold;
    color: #000000;
}

.period-style {
    display: inline;
    text-align: end;
    font-size: 16px;
    text-transform: uppercase;
    color: gray;
}

.details {
    position: relative;
    float: left;
    height: 70%;
    padding: 40% 0% 0% 0%;
    width: 100%;
    @media (max-width: 768px) {
        position: relative;
        float: left;
        height: 0px;
        padding: 0;
    }
}

.details button {
    left: 3%;
    border-radius: 30px;
    border: none;
    font-weight: bold;
    min-width: 120px;
    background-color: $yellow;
    padding: 10px;
    padding-left: 10px;
    padding-right: 10px;
    font-weight: bold !important;
    margin-left: -5%;
    @media (max-width: 768px) {
        border-radius: 30px;
        border: none;
        font-weight: bold;
        min-width: 150px;
        background-color: $yellow;

        padding-left: 10px;
        padding-right: 10px;
        font-weight: bold !important;
    }
}

.companyLogo {
    max-width: 110px;
}

.div-img-size {
    padding-top: 8px;
}

.img-size {
    height: 100%;
    width: 100%;
}

.div-img-size {
    height: 25px;
    width: 100px;
}

.item-footer-size {
    height: 40px;
    width: 100%;
}

.star-count {
    margin-left: -10px;
    margin-top: 10px;
}

.save-space {
    display: inline !important;
}

.fwxbold {
    font-weight: bold;
}

.category {
    content: none !important;
    width: 100%;
    text-align: start !important;
    font-size: 16px;
    font-weight: bold;
    color: #000000 !important;
    margin-bottom: 20%;
}

.category::after {
    content: none !important;
}

.category:hover {
    outline: none !important;
    box-shadow: none !important;
}

.category:active {
    outline: none !important;
    box-shadow: none !important;
}

.category:focus {
    outline: none !important;
    box-shadow: none !important;
}

/*----------------------- HITS SCSS ----------------------- */
.pname {
    margin-bottom: 0.5em;
}

.hit-description {
    color: #888;
    font-size: 14px;
    margin-bottom: 0.5em;
}

.equipment-item-img img {
    border-radius: 18px;
    border: solid 0.5px #d1d1d1;
}

.ais-Hits-item,
.ais-InfiniteHits-item {
    align-items: center !important;
    background: none !important;
    box-shadow: none !important;
    padding: 0 !important;
}

.title {
    font-size: 27px;
    font-weight: bold;
    color: #000000;
}

.bottom-bar {
    display: flex;
    flex-direction: row;
    justify-content: start;
    margin-top: 60px;
}

.bottom-bar span {
    margin-top: 8px;
    margin-right: 6px;
}

.bottom-bar svg {
    margin-top: 10px;
    margin-right: 4px;
}

.equipment-details {
    &.space-row {
        margin-bottom: 30px;
        margin-top: 15px;
    }

    .company-logo-details {
        @media (min-width: 992px) {
            margin-top: 0;
        }
    }

    .price {
        padding-bottom: 0;
    }
}
.display-column {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.equipment-details-box {
    &.RWC {
        height: auto;
    }
}
