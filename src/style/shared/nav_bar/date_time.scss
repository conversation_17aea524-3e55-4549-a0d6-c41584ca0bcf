.react-daterange-picker__wrapper {
  display: flex;
  flex-grow: 1;
  flex-shrink: 0;
  align-items: center;
  border: 0;
}

.react-daterange-picker__inputGroup {
  min-width: calc(17px + 4.32em + 0.434em);
  height: 100%;
  flex-grow: 1;
  padding: 0 38% 0 0;
  box-sizing: content-box;
}

.react-calendar {
  width: 350px;
  max-width: 100%;
  background: white;
  border: 1px solid white;
  border-radius: 20px;
  line-height: 1.125em;
  padding: 5%;
}

.react-calendar__tile--hasActive,
.react-calendar__tile--hasActive:hover,
.react-calendar__tile:hover {
  background: #ECA869 !important;
}

.react-calendar__tile--now:hover,
.react-calendar__tile--now {
  background-color: transparent !important;
}

.react-calendar__tile--active,
.react-calendar__tile--active:hover {
  background: #ECA869 !important;
  color: black;
}

.react-calendar button {
  margin: 0;
  border: 0;
  outline: none;
  border-radius: 20px;
}

.react-daterange-picker__calendar--open {
  inset: auto !important;
}

.react-calendar__tile:disabled,
.react-calendar__navigation button[disabled] {
  background-color: #ffffff;
}

.react-calendar__month-view__days__day--weekend {
  color: orange;
}

.react-daterange-picker__inputGroup__input:invalid {
  background: 0;
}

.react-daterange-picker__calendar {
  width: 310px;
  margin-top: 35px;
}
