
.our_story {
  @media(min-width: 992px) {
    margin-top: -20px;
  }

  &__banner {
    background: $primary-color;
    padding: 50px 35px;
    @media(min-width: 992px) {
      padding: 0;
    }
  }

  &__presentation {
    margin-top: 30px;
    @media(min-width: 992px) {
      margin-top: 35px;
    }

    &_row {
      margin: 32px 0;

      .marge-row {
        position: relative;
        padding: 20px;
        border: 1px solid $border-grey;
        border-radius: 12px;

        img {
          width: 135px;
          height: 135px;
          @media(max-width: 992px) {
            width: 90px;
            height: 90px;
          }
        }
      }

      .sep {
        margin: 0 40px;
        border: 2px solid $blue-grey;
        position: absolute;
        left: 90px;
        height: calc(100% - 50px);
        @media(min-width: 992px) {
          left: 160px;
          height: calc(100% - 80px);
        }
      }

      .content {
        margin-left: 60px;
        @media(min-width: 992px) {
          margin-left: 95px;
        }
        @media(max-width: 572px) {
          p {
            font-size: 10px;
            line-height: 130%;
          }
          h2 {
            font-size: 26px;
            line-height: 130%;
          }
        }
      }
    }

    @media only screen and (min-width: 1600px) {
      .xxl-flex {
        display: flex;
        height: 100%;
        width: 100%;
      }
      .our_story__presentation_row {
        width: 50%;
        margin: 16px;
        flex: 0 0 auto;

        .marge-row {
          height: 100%;
        }
      }
    }
  }

  &__team {
    margin-top: 48px;
    padding-bottom: 60px;
    @media(min-width: 992px) {
      padding-bottom: 120px;
    }

    .margin-bottom-30 {
      margin-bottom: 30px;
    }

    &_card {
      padding: 25px 15px;
      border-radius: 7px;
      border: 1px solid $border-grey;
      background: $white;
      box-shadow: 0 11px 40px 0 rgba(6, 28, 61, 0.07);
      margin-bottom: 24px;

      .image_card {
        margin-bottom: 20px;
        width: 150px;
        height: 150px;
        border-radius: 0 44px 0 44px;
        box-shadow: 0 11px 40px 0 rgba(6, 28, 61, 0.07);
      }

      .socials {
        border-radius: 5px;
        border: 1px solid $fake-black;
        background: $fake-black;
        width: 45px;
        height: 45px;
        padding: 12px;
        display: inline-flex;
        align-items: center;
        justify-content: center;

        svg {
          path {
            fill: $white;
          }
        }
      }

      p {
        min-height: 40px;
      }
    }

    &_container {
      position: relative;
      @media(max-width: 992px) {
        overflow-x: auto;
        display: flex;
        .card-width-mobile {
          width: 260px;
        }
      }
      @media(min-width: 992px) {
        &.after {
          &:after {
            content: url("../style/assets/img/our_story/Layer_ourStory.svg");
            position: absolute;
            right: -160px;
            top: -180px;
            z-index: -1;
          }
        }
      }
    }

    .card-advisor {
      @media(min-width: 992px) {
        width: 20%;
      }
    }
  }

  &__backed_by {

    h2 {
      margin-bottom: 60px;
    }

    .mb-logos {
      margin-bottom: 70px;
    }
  }
}