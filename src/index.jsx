import 'bootstrap/scss/bootstrap.scss';
import React from 'react';
import ReactDOM from 'react-dom';
import { BrowserRouter } from 'react-router-dom';
import App from './App';
import PostHogProvider from './shared/components/PostHogProvider';
import GoogleAnalyticsProvider from './shared/components/GoogleAnalyticsProvider';
import './shared/i18n';
import './style/style.scss';

ReactDOM.render(
  <React.StrictMode>
    <BrowserRouter>
      <PostHogProvider>
        <GoogleAnalyticsProvider>
          <App />
        </GoogleAnalyticsProvider>
      </PostHogProvider>
    </BrowserRouter>
  </React.StrictMode>,
  document.getElementById('root')
);
