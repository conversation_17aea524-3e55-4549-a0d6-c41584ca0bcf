import React, { useEffect, useRef, useState } from 'react';
import { Form, Formik } from 'formik';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/fontawesome-free-solid';
import RenderIf from '../../shared/components/Render_if';
import {
  prepareSecondaryApplicantInfo,
  prepareApplicantInfo,
  prepareAddress,
  prepareBankruptcyInfo,
  creditApplication
} from '../../shared/helpers/Credit_application';
import jsPDF from 'jspdf';
import { getCookies } from '../../shared/helpers/Cookies';
import RecursiveContainer from './RecursiveContainer';
import { isEmpty } from 'lodash';
import { useLocation } from 'react-router-dom';
import { UPLOAD_MY_CREDIT_CHECK_FORM } from '../../shared/helpers/Url_constants';
import axios from 'axios';

const LoadingModal = React.lazy(() =>
  import('../../shared/components/modals/Loading_modal')
);

const CustomButton = React.lazy(() =>
  import('../../shared/components/buttons/Custom_button')
);

export default function CreditApplication({
  show,
  onClose,
  submitCreditCheckForm,
  getCreditCheckFormAttachment,
  setSelectedCreditCheckForm,
  data,
  setFailureResponse,
  UpdateCreditCheckForm,
  showError,
  switchPlace,
  handleShow,
  handleClose,
  isForShow,
  t
}) {
  const [isPrinting, setIsPrinting] = useState(false);
  const [selectedCCF, setSelectedCCF] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const location = useLocation();
  const isCCF = location.pathname.includes('creditCheckForm');
  const isUS = getCookies('lodgerCountry') === 'United States';
  const { initialValues, validationSchema, formStructure, dataStructure } =
    creditApplication(isUS, t);
  const modalRef = useRef(null);

  async function uploadCreditCheckForm(file, id) {
    const header = {
      'Content-Type': 'multipart/form-data'
    };
    header.Authorization = `Bearer ${getCookies('token')}`;

    const data = new FormData();
    data?.set('file', file);

    const res = await await axios.post(
      `${
        import.meta.env.VITE_REACT_APP_BASE_URL
      }${UPLOAD_MY_CREDIT_CHECK_FORM}/${id}`,
      data,
      { headers: header }
    );
    if (res.status === 200 || res.status === 201) {
      handleShow();
    } else {
      setIsPrinting(false);
      showError();
      setFailureResponse(res);
    }
  }

  async function generatePDF(id) {
    setIsPrinting(true);
    const doc = new jsPDF('portrait', 'mm', [2600, 1800]);
    const generatePDFPromise = new Promise((resolve, reject) => {
      doc.html(modalRef.current, {
        callback(pdf) {
          resolve(pdf);
        },
        error(error) {
          reject(error);
        }
      });
    });
    try {
      const pdf = await generatePDFPromise;
      await uploadCreditCheckForm(pdf.output('blob'), id);
    } catch (error) {
      error.status = 400;
      error.message = 'message';
      setFailureResponse(error);
    }
  }

  async function uploadPDF(file, id, type) {
    const header = {
      'Content-Type': 'multipart/form-data'
    };
    header.Authorization = `Bearer ${getCookies('token')}`;

    const data = new FormData();
    data?.set('file', file);
    await axios.post(
      `${
        import.meta.env.VITE_REACT_APP_BASE_URL
      }${UPLOAD_MY_CREDIT_CHECK_FORM}/${id}/${type}`,
      data,
      { headers: header }
    );
  }

  const onSubmit = async (values) => {
    setIsLoading(true);
    const datetime = new Date();
    const att = values?.attachments;
    values.bond.date = datetime;
    values.company.number_of_employees = parseInt(
      values.company.number_of_employees
    );
    values.bond.has_other_accounts = values.bond.has_other_accounts
      ? values.bond.has_other_accounts_reason
      : 'No';
    values.company.tax_free_entity = values.company.tax_free_entity
      ? values.company.tax_free_entity_reason
      : 'No';
    values.company.property_insurance = values.company.property_insurance
      ? values.company.property_insurance_reason
      : 'No';
    values.company.required_project_number = values.company
      .required_project_number
      ? values.company.required_project_number_reason
      : 'No';

    values.company.year_of_location =
      values.company.year_of_location.toString();

    values.company.address = prepareAddress(values.company.address);
    values.accounts_payable = values?.accounts_payable?.map((item) => ({
      ...item,
      address: {
        ...prepareAddress(item.address),
        address: item.account_address,
        zip_code: item.zip_code
      }
    }));
    values.credit_references = values?.credit_references?.map((item) => ({
      ...item,
      address: {
        ...prepareAddress(item.address),
        address: item.credit_address,
        zip_code: item.zip_code
      }
    }));
    values.attachments = {
      applicant_userID: values.attachments.user_id?.name || '',
      secondary_applicant_userID:
        values.attachments.second_applicant_id?.name || '',
      partnership_agreement:
        values.attachments.partnership_agreement?.name || '',
      insurance: values.attachments.insurance?.name || ''
    };

    if (!isUS) {
      values.in_charge_of_rentals = values?.in_charge_of_rentals?.map(
        (item) => ({
          ...item,
          address: {
            ...prepareAddress(item.address),
            address: item.credit_address,
            zip_code: item.zip_code
          }
        })
      );
    }
    if (isUS) {
      values.us_credit_app_data = {
        ...prepareApplicantInfo(values),
        ...prepareSecondaryApplicantInfo(values),
        ...prepareBankruptcyInfo(values)
      };
      values.bankruptcy_equipment_info = values.bankruptcy_equipment_info?.map(
        (item) => ({
          ...item,
          condition: item?.condition?.value || ''
        })
      );
    }

    const isUpdating = !!data;
    values.created_at = isUpdating ? values.created_at : datetime;
    values.updated_at = datetime;

    let response = {};

    if (isUpdating) {
      values.updated_at = datetime;
      values.credit_check_form_path = '';
      values.attachments = {
        user_id: att?.user_id?.name || data.attachments?.user_id,
        second_applicant_id:
          att?.second_applicant_id?.name ||
          data.attachments?.second_applicant_id,
        partnership_agreement:
          att?.partnership_agreement?.name ||
          data.attachments?.partnership_agreement,
        insurance: att?.insurance?.name || data.attachments?.insurance
      };
      response = await UpdateCreditCheckForm(values, data?.id);
    } else {
      response = await submitCreditCheckForm(values);
    }

    if (response.status === 200 || response.status === 201) {
      if (att.user_id && typeof att.user_id !== 'string') {
        await uploadPDF(
          att?.user_id,
          isUpdating ? data?.id : response.data?.id,
          'applicant_userID'
        );
      }
      if (
        att.second_applicant_id &&
        typeof att.second_applicant_id !== 'string'
      ) {
        await uploadPDF(
          att?.second_applicant_id,
          isUpdating ? data?.id : response.data?.id,
          'secondary_applicant_userID'
        );
      }
      if (
        att.partnership_agreement &&
        typeof att.partnership_agreement !== 'string'
      ) {
        await uploadPDF(
          att?.partnership_agreement,
          isUpdating ? data?.id : response.data?.id,
          'partnership_agreement'
        );
      }
      if (att.insurance && typeof att.insurance !== 'string') {
        await uploadPDF(
          att.insurance,
          isUpdating ? data?.id : response.data?.id,
          'insurance'
        );
      }

      if (!switchPlace) {
        setTimeout(
          async () =>
            await generatePDF(isUpdating ? data?.id : response.data?.id),
          1000
        );
      } else {
        handleShow();
        setSelectedCreditCheckForm(null);
      }
      setSelectedCreditCheckForm(null);
    } else {
      setFailureResponse(response);
      setSelectedCreditCheckForm(null);
      showError();
      setIsLoading(false);
    }
  };

  useEffect(() => {
    async function updateAttachment(attachmentName, data) {
      if (data?.attachments && data?.attachments[attachmentName]) {
        const response = await getCreditCheckFormAttachment(
          data?.id,
          data?.attachments[attachmentName],
          '-'
        );
        return response?.data?.url || '';
      }
      return '';
    }

    if (!isEmpty(data) && isForShow) {
      (async () => {
        const [userId, secondApplicantId, insurance, partnershipAgreement] =
          await Promise.all([
            updateAttachment('user_id', data),
            updateAttachment('second_applicant_id', data),
            updateAttachment('insurance', data),
            updateAttachment('partnership_agreement', data)
          ]);

        setSelectedCCF({
          ...data,
          attachments: {
            insurance: insurance,
            partnership_agreement: partnershipAgreement,
            second_applicant_id: secondApplicantId,
            user_id: userId
          }
        });
      })();
    } else {
      setSelectedCCF(data);
    }
  }, [data]);

  const renderModal = () => {
    const isUpdating = !!data;
    if (isUpdating) {
      return show && selectedCCF;
    }
    if (isForShow) {
      return show;
    }
    return show;
  };

  return (
    <RenderIf condition={renderModal()}>
      <div className="modal">
        <div className="modal-content credit-check-modal">
          <button
            type="button"
            className="close-button"
            onClick={() => {
              setSelectedCreditCheckForm(null);
              setSelectedCCF(null);
              setIsLoading(false);
              onClose();
            }}
          >
            <FontAwesomeIcon icon={faTimes} color="c-primary-color" />
          </button>
          <div className="scrollbar">
            <div
              id="credit_check_form"
              ref={modalRef}
              style={isPrinting ? { margin: '20px' } : null}
            >
              <h2 className="t-header-h6 bold credit-title">
                {t('Credit_check_form')}
              </h2>
              <div className="send-btn mt-4 text-center"></div>
              <section className="credit-check-modal__section">
                <Formik
                  onSubmit={onSubmit}
                  initialValues={dataStructure(selectedCCF) || initialValues}
                  validationSchema={validationSchema}
                >
                  {(formik) => (
                    <Form>
                      <RecursiveContainer
                        config={formStructure}
                        formik={formik}
                        isPrinting={isPrinting}
                        t={t}
                        isUS={isUS}
                        isForShow={isForShow}
                      />
                      {isUS && (
                        <p className="lh-sm text-justify pt-4">
                          {t('Terms_sales_us')}
                        </p>
                      )}
                      {!isPrinting && (
                        <div className="send-btn mt-4 text-center fixed-button-modal">
                          <CustomButton
                            type="button"
                            onClick={() => {
                              setSelectedCreditCheckForm(null);
                              setSelectedCCF(null);
                              setIsLoading(false);
                              handleClose();
                            }}
                            color="white"
                            textButton={t('Close_button')}
                            className="button-signup fwb-700 round-button bg-transparent bold black mr-10"
                          />
                          <RenderIf condition={!isForShow}>
                            <CustomButton
                              type="submit"
                              color="white"
                              disabled={isLoading}
                              isLoading={isLoading}
                              textButton={data ? t('Update') : t('Save')}
                              textPopper={t('Required_fields')}
                              className="button-signup fwb-700 round-button bold c-primary-color yellow"
                            />
                          </RenderIf>
                          <RenderIf condition={isForShow && isCCF}>
                            {!switchPlace && (
                              <CustomButton
                                type="button"
                                onClick={async () =>
                                  await generatePDF(data?.id)
                                }
                                textButton={t('Download_pdf')}
                                className="button-signup fwb-700 round-button yellow bold c-primary-color"
                              />
                            )}
                          </RenderIf>
                        </div>
                      )}
                      <LoadingModal show={isPrinting} />
                    </Form>
                  )}
                </Formik>
              </section>
            </div>
          </div>
        </div>
      </div>
    </RenderIf>
  );
}
