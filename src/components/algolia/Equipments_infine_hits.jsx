import React, { useState } from 'react';
import EquipmentManagementTable from '../../shared/components/equipment/Equipment_management_table';
import RenderIf from '../../shared/components/Render_if';
import NoResults from '../search_result/No_results';
import FilterByCategorySubcategory from './Filterby_category_subcategory';
import Filter from '../../shared/components/modals/Filter_modal';
import { getSessionStorage } from '../../shared/helpers/Session_storage_helper';
import Swipe from '../../shared/components/swipe/Swipe';
import { CustomSpotlightMenu } from '../search_result/Custom_equipper_spotlight_menu';
import { connectInfiniteHits, connectStats } from 'react-instantsearch-dom';
import useResponsive from '../../shared/helpers/Responsive';
import EquipmentEmptyState from '../../style/assets/img/empty_state/Add_equipment_first.svg';

const InfiniteHits = ({
  hasInventory,
  searchState,
  t,
  mrpOptions,
  detectLanguage,
  showMRP
}) => {
  const category = getSessionStorage('category') || null;
  const subcategory = getSessionStorage('sub_category') || null;
  const status = getSessionStorage('status');
  const details = [
    {
      show: true,
      title: 'Subcategories',
      selected: getSessionStorage('sub_category'),
      attribute: 'sub_category'
    }
  ];

  const [itemsLength, setItemsLength] = useState(0);

  const { isMobile } = useResponsive();

  const Stats = ({ nbHits, t }) => (
    <h3 className="t-subheading-2 c-fake-black  m-2 p-2">
      {nbHits} {t('Equipment_found')}{' '}
    </h3>
  );

  const CustomStats = connectStats(Stats);

  if (!hasInventory) {
    return <NoResults t={t} image={EquipmentEmptyState} />;
  }

  return (
    <div className="row">
      {!isMobile ? (
        <FilterByCategorySubcategory t={t} isEquipmentManagement />
      ) : (
        <Filter t={t} isEquipmentManagement />
      )}
      <div className="col-lg-9">
        <RenderIf condition={mrpOptions}>
          <div className="d-flex">
            <button
              className="result-left-bottom col-lg-12 tag btn mb-2"
              onClick={() => showMRP()}
            >
              <p className="t-body-small c-blue-grey m-0">
                <strong className="t-body-regular bold ">
                  {t('Minimum_rental_period')}
                </strong>
              </p>
            </button>
            <CustomStats t={t} />
          </div>
        </RenderIf>

        <div
          id="accordion"
          className="accordion accordion-equipments-management"
        >
          <RenderIf condition={category}>
            <Swipe
              showArrow={itemsLength > 6}
              id="scrollmenu-equipments-management"
            >
              {details.map(
                ({ selected, show }) =>
                  show && (
                    <div className="d-flex sub-category__items">
                      <CustomSpotlightMenu
                        attribute="sub_category"
                        setItemsLength={setItemsLength}
                        selected={selected}
                        t={t}
                      />
                    </div>
                  )
              )}
            </Swipe>
          </RenderIf>

          <EquipmentManagementTable
            searchState={searchState}
            subcategory={subcategory}
            category={category}
            equipmentStatus={status}
            selectedStatus={status}
            t={t}
            detectLanguage={detectLanguage}
          />
        </div>
      </div>
    </div>
  );
};

export const CustomEquipmentsInfiniteHits = connectInfiniteHits(InfiniteHits);
