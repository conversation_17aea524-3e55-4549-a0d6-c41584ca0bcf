import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import 'rsuite/dist/rsuite.min.css';
import UseWindowDimensions from '../../../shared/hooks/Screen_size';
import BackgroundHome from '../../../style/assets/img/home-page.png';
import InstantSearchAlgolia from '../../search_result/Instant_search_algolia';
import DesktopSearch from './Desktop_search';
import MobileSearch from './Mobile_search';
import { navigateToSearchResult } from '../../../shared/helpers/Algolia_helper';

export default function Search({ t, detectLanguage }) {
  const navigate = useNavigate();
  const [startDate, setStartDate] = useState(new Date());
  const date = new Date();
  const [endDate, setEndDate] = useState(
    new Date(date.setDate(date.getDate() + 6))
  );
  const [equipmentName, setEquipmentName] = useState({
    requestedName: ''
  });
  const [location, setLocation] = useState({
    value: '',
    isSelected: false
  });
  const [equipmentID, setEquipmentID] = useState('');
  const [switchSearchWorkFlow, setSwitchSearchWorkFlow] = useState(
    window.innerWidth < 992
  );

  function handleSwitchSearchWorkFlow(prop) {
    setSwitchSearchWorkFlow(prop);
  }

  UseWindowDimensions({
    functions: handleSwitchSearchWorkFlow,
    dimension: 992
  });
  const currentTime = new Date();

  const handleStartDateChange = (date) => {
    date.setHours(
      currentTime.getHours(),
      currentTime.getMinutes(),
      currentTime.getSeconds(),
      currentTime.getMilliseconds()
    );

    setStartDate(date);
  };

  const handleEndDateChange = (date) => {
    date.setHours(
      currentTime.getHours(),
      currentTime.getMinutes(),
      currentTime.getSeconds(),
      currentTime.getMilliseconds()
    );

    setEndDate(date);
  };

  return (
    <InstantSearchAlgolia indexName={import.meta.env.VITE_ALGOLIA_INDEX_NAME}>
      <div className={`backImage ${switchSearchWorkFlow ? '' : ''}`}>
        <div className="searchContainer">
          <div className="search-content">
            <div className="search-content__head text-center">
              <h1 className="t-header-h2 c-fake-black">{t('SearchBar')}</h1>
              <p className="t-subheading-2 c-neutrals-gray mt-2">
                {t('SearchBar2')}
              </p>
            </div>
            <div className="container">
              <div className="searchBar">
                {!switchSearchWorkFlow ? (
                  <DesktopSearch
                    equipmentName={equipmentName}
                    setEquipmentName={setEquipmentName}
                    location={location}
                    setLocation={setLocation}
                    handleStartDateChange={handleStartDateChange}
                    handleEndDateChange={handleEndDateChange}
                    startDate={startDate}
                    detectLanguage={detectLanguage}
                    endDate={endDate}
                    t={t}
                    setEquipmentID={setEquipmentID}
                    equipmentID={equipmentID}
                    searchResult={async () =>
                      await navigateToSearchResult(
                        equipmentName,
                        location,
                        startDate,
                        endDate,
                        equipmentID,
                        navigate
                      )
                    }
                  />
                ) : (
                  <div id="mobile-search">
                    <MobileSearch
                      setEquipmentID={setEquipmentID}
                      equipmentID={equipmentID}
                      equipmentName={equipmentName}
                      setEquipmentName={setEquipmentName}
                      location={location}
                      setLocation={setLocation}
                      handleStartDateChange={handleStartDateChange}
                      handleEndDateChange={handleEndDateChange}
                      detectLanguage={detectLanguage}
                      startDate={startDate}
                      endDate={endDate}
                      t={t}
                      searchResult={async () =>
                        await navigateToSearchResult(
                          equipmentName,
                          location,
                          startDate,
                          endDate,
                          equipmentID,
                          navigate
                        )
                      }
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="search-image">
            <img src={BackgroundHome} alt="img" />
          </div>
        </div>
      </div>
    </InstantSearchAlgolia>
  );
}
