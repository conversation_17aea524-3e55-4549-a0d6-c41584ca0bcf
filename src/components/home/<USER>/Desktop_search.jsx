import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import React from 'react';
import { Col, Row } from 'react-bootstrap';
import { Label } from 'reactstrap';
import SearchInputAutoCompleteEquipment from '../../../features/search_result/Search_input_auto_complete_equipment';
import CustomButton from '../../../shared/components/buttons/Custom_button';
import DatePicker from '../../../shared/components/date_picker/Date_picker';
import { isDisabled } from '../../../shared/helpers/Date_helper';

export default function DesktopSearch({
  searchResult,
  equipmentName,
  setEquipmentName,
  location,
  setLocation,
  detectLanguage,
  handleStartDateChange,
  handleEndDateChange,
  startDate,
  endDate,
  equipmentID,
  t,
  setEquipmentID
}) {
  const isSearchButtonEnabled =
    equipmentID &&
    location.isSelected &&
    !isDisabled(startDate, endDate) &&
    startDate &&
    endDate;

  const popperText = !equipmentID
    ? t('Select_equipment')
    : !location.isSelected
    ? t('Set_your_location')
    : isDisabled(startDate, endDate)
    ? t('Date_error_msg')
    : t('Search');

  return (
    <form className="container height-100">
      <Row className="height-100">
        <Col
          sm={10}
          lg={4}
          className="center-mobile first_column border-gradient-right border-gradient-yellow"
        >
          <label
            htmlFor="input-field"
            className="fwb c-fake-black t-body-large"
          >
            {t('Equipment_name')}
          </label>
          <SearchInputAutoCompleteEquipment
            attribute="equipment"
            placeholder={t('Search_placeholder')}
            value={equipmentName.requestedName || ''}
            onChange={setEquipmentName}
            detectLanguage={detectLanguage}
            indexName={import.meta.env.VITE_ALGOLIA_INDEX_NAME_STANDARDS}
            className="search-input-auto-complete width-100 t-body-large"
            setEquipmentID={setEquipmentID}
            equipmentNameClassName="container-wrapper-equip-name"
            t={t}
            isEquipment
          />
        </Col>
        <Col
          sm={10}
          lg={3}
          className="center-mobile foxy border-gradient-right border-gradient-yellow set_location"
        >
          <label htmlFor="input-field" className="fwb c-fake-black">
            {t('Where')}
          </label>
          <SearchInputAutoCompleteEquipment
            attribute="location"
            placeholder={t('Set_your_location')}
            value={location.value || ''}
            indexName={import.meta.env.VITE_ALGOLIA_INDEX_COVERAGE_AREAS_NAME}
            onChange={setLocation}
            className="search-input-auto-complete width-100"
            locationIsSelected
            t={t}
          />
        </Col>
        <Col
          sm={10}
          lg={2}
          className="center-mobile border-gradient-right border-gradient-yellow"
        >
          <DatePicker
            label={t('Rent_on')}
            handleStartDateChange={handleStartDateChange}
            handleEndDateChange={handleEndDateChange}
            startDate={startDate}
            endDate={endDate}
            startDateClassName="searchInput-navbar no-border"
            endDateClassName="searchInput-navbar no-border"
          />
        </Col>
        <Col
          sm={10}
          lg={2}
          className="center-mobile adjustPicker border-gradient-right border-gradient-yellow border-gradient-far-right-adjustPicker"
        >
          <Label className="font-selecor fwb label-padding ">
            {t('Return_on')}
          </Label>
        </Col>
        <Col
          sm={10}
          lg={{
            span: 1
          }}
          className="center-mobile relative padding-lr-0"
        >
          <CustomButton
            textButton={
              <>
                <FontAwesomeIcon
                  icon="search"
                  className={isSearchButtonEnabled ? '' : 'c-near-grey'}
                />
                <span
                  className={`search-text ${
                    isSearchButtonEnabled ? '' : 'c-near-grey'
                  }`}
                  id="searchleft"
                >
                  {t('Search_button')}
                </span>
              </>
            }
            className={`button-search ${
              isSearchButtonEnabled ? '' : 'c-near-grey'
            }`}
            onClick={searchResult}
            textPopper={popperText}
            disabled={!isSearchButtonEnabled}
          />
        </Col>
      </Row>
    </form>
  );
}
