import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { Row, Col } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLinkedinIn, faFacebookF } from '@fortawesome/free-brands-svg-icons';
import Popup from '../../shared/components/modals/Popup';
import { PHONE_NUMBER_REGEX } from '../../shared/helpers/Regex';
import SuccessPopUp from '../../shared/components/modals/Success_pop_up';
import { useReclamation } from '../../shared/context/Reclamation_context';
import InputForm from '../../shared/components/inputs/Input_form';
import { isValid } from '../../shared/helpers/Data_helper';
import sendIcon from '../../style/assets/img/Icons/send.svg';

export default function NeedInformation({ t }) {
  const { Reclamation } = useReclamation();
  const [response, setResponse] = useState(null);
  const [show, setShow] = useState(false);
  const [showPopup, setShowPopup] = useState(false);

  const initialValues = {
    name: '',
    phone_number: '',
    email: ''
  };

  const validate = Yup.object({
    name: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .required(t('This_field_is_required')),
    phone_number: Yup.string()
      .max(15, t('SignUp_phone_number_max_length'))
      .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
      .required(t('This_field_is_required')),
    email: Yup.string()
      .email(t('Invalid_email'))
      .required(t('This_field_is_required'))
  });

  const handleSubmit = async (event) => {
    const res = await Reclamation({
      name: event.name,
      email: event.email,
      phone_number: event.phone_number
    });
    if (res.status === 200) {
      setShow(true);
    } else {
      setShowPopup(true);
      setResponse(res);
    }
  };

  const handleCloseModal = (formik) => {
    setShow(false);
    formik.resetForm();
  };
  const icons = [
    {
      icon: faLinkedinIn,
      path: 'https://www.linkedin.com/company/derental-inc'
    },
    { icon: faFacebookF, path: 'https://facebook.com/DerentalEquipments ' }
  ];

  return (
    <div>
      <div className="submit-form">
        <h2 className="t-header-h2 c-fake-black">{t('More_information')}</h2>
        <h3 className="subTitle t-body-regular c-neutrals-gray">
          {t('Help_you')}
        </h3>
      </div>
      <div className="row flex-lg-row flex-column-reverse">
        <div className="col-lg-4 submit-form" id="need-info">
          <div className="tel-information">
            <h3 className="t-body-regular c-neutrals-gray mb-2">
              {t('Need_more_info_text')}
            </h3>
            <div className="tel-information__details">
              <p className="t-header-h6 c-fake-black text-uppercase bold">
                USA
              </p>
              <a
                href="tel:+7077711733"
                className="t-header-h6 c-fake-black d-block bold"
              >
                ************
              </a>
            </div>
            <div className="tel-information__details">
              <p className="t-header-h6 c-fake-black text-uppercase bold">
                SAUDI
              </p>
              <a
                href="tel:+5149999578"
                className="t-header-h6 c-fake-black d-block bold"
              >
                59 980 8901
              </a>
            </div>
            <div className="tel-information__details">
              <p className="t-header-h6 c-fake-black text-uppercase bold">
                CANADA
              </p>
              <a
                href="tel:+5149999578"
                className="t-header-h6 c-fake-black d-block bold"
              >
                ************
              </a>
            </div>

            <div className="tel-information__socials mt-lg-0 mt-5">
              <h3 className="t-body-regular c-neutrals-gray mb-2">
                {t('Need_more_info_text_social')}
              </h3>
              <ul className="d-flex align-items-center">
                {icons.map((item, key) => (
                  <li>
                    <a
                      href={item.path}
                      className="me-2 text-reset"
                      key={key}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <FontAwesomeIcon
                        icon={item.icon}
                        color="c-neutrals-gray"
                      />
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
        <div className="col-lg-7">
          <div className="form_footer">
            <div className="form_footer__header mb-lg-5 mb-4">
              <h3 className="t-header-h6 c-fake-black mb-2">
                {t('Form_footer_h')}
              </h3>
              <p className="t-subheading-3 c-neutrals-gray">
                {t('Form_footer_text')}
              </p>
              <p></p>
            </div>
            <Formik
              initialValues={initialValues}
              onSubmit={handleSubmit}
              validationSchema={validate}
            >
              {(formik) => (
                <>
                  <Form>
                    <Row className="row-form">
                      <div className="form-group col-lg-6 col-sm-12 mb-lg-2">
                        <Col>
                          <label className="t-body-small c-fake-black mb-1">
                            {t('Name')}
                          </label>
                          <InputForm
                            name="name"
                            type="text"
                            placeholder={t('Name')}
                            className={`border-gradient-far-right rounded-corners form-control mb-2 ${isValid(
                              formik,
                              'name'
                            )}`}
                            isFormik
                          />
                        </Col>
                      </div>
                      <div className="form-group col-lg-6 col-sm-12 mb-lg-2">
                        <Col>
                          <label className="t-body-small c-fake-black mb-1">
                            {t('Email')}
                          </label>
                          <InputForm
                            name="email"
                            type="email"
                            placeholder={t('Email')}
                            className={`border-gradient-far-right rounded-corners form-control mb-2 ${isValid(
                              formik,
                              'email'
                            )}`}
                            isFormik
                          />
                        </Col>
                      </div>
                      <div className="form-group col-lg-12 mb-lg-2">
                        <Col>
                          <label className="t-body-small c-fake-black mb-1">
                            {t('Phone_number')}
                          </label>
                          <InputForm
                            name="phone_number"
                            type="text"
                            placeholder={t('Phone_number')}
                            className={`border-gradient-far-right rounded-corners form-control mb-2 ${isValid(
                              formik,
                              'phone_number'
                            )}`}
                            isFormik
                          />
                        </Col>
                      </div>
                      <div className="col-12 d-flex justify-content-lg-start justify-content-end">
                        <button
                          className="round-button yellow button-submit c-black hover_black   d-flex align-items-center justify-content-center"
                          type="submit"
                        >
                          <span className="">{t('Send')}</span>
                          <img src={sendIcon} alt="send icon" />
                        </button>
                      </div>
                    </Row>
                  </Form>
                  <SuccessPopUp
                    onClose={() => handleCloseModal(formik)}
                    message={t('Reclamation_success')}
                    show={show}
                  />
                  <Popup
                    t={t}
                    show={showPopup}
                    response={response}
                    onClose={() => {
                      setShowPopup(false);
                      formik.resetForm();
                    }}
                  />
                </>
              )}
            </Formik>
          </div>
        </div>
      </div>
      <div className="reach_out d-lg-flex align-items-center justify-content-center text-lg-start text-center">
        <h2 className="c-fake-black t-header-h3">{t('Reach_out')}</h2>
        <button className="round-button yellow hover_black c-black bold m-4">
          <a href="mailto:<EMAIL>" className="c-white">
            {t('Reach_out_button')}
          </a>
        </button>
      </div>
    </div>
  );
}
