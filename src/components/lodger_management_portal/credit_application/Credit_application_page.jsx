import React, { useEffect, useState } from 'react';
import { useCreditCheckFormContext } from '../../../shared/context/Credit_check_form_context';
import SuccessPopUp from '../../../shared/components/modals/Success_pop_up';
import Popup from '../../../shared/components/modals/Popup';
import ConfirmationModal from '../../../shared/components/modals/Confirmation_modal';
import ToloIsLoading from '../../../shared/components/cards/Tolo_is_loading';
import useWindowDimensions from '../../../shared/hooks/Screen_size';
import { getCookies } from '../../../shared/helpers/Cookies';
import CreditApplication from '../../credit_application/CreditApplication';
import CreditApplicationTab from './Credit_application_table';

export default function CreditApplicationPage({ t }) {
  const {
    AddCreditCheckForm,
    GetMyCreditCheckForm,
    DeleteCreditCheckForm,
    GetCreditCheckFormAttachment,
    UpdateCreditCheckForm
  } = useCreditCheckFormContext();
  const [show, setShow] = useState(false);
  const [isForShow, setIsForShow] = useState(false);
  const [selectedCreditCheckForm, setSelectedCreditCheckForm] = useState(null);
  const [myCreditCheckForm, setMyCreditCheckForm] = useState(null);
  const [showCreditFormModal, setShowCreditFormModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showFailurePopUp, setShowFailurePopUp] = useState(false);
  const [failureResponse, setFailureResponse] = useState(null);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [onAction, setOnAction] = useState(false);
  const [switchPlace, setSwitchPlace] = useState(window.innerWidth < 1200);
  const [userType, setUserType] = useState('');
  function handleSwitchPlace(prop) {
    setSwitchPlace(prop);
  }
  const refresh = () => {
    setIsInitialized(false);
  };

  useWindowDimensions({ functions: handleSwitchPlace, dimension: 1200 });

  function handleClose() {
    setShow(false);
    setShowCreditFormModal(false);
    setShowFailurePopUp(false);
    setShowConfirmationModal(false);
    refresh();
  }

  async function onDeleteCreditCheckForm() {
    setOnAction(true);
    const response = await DeleteCreditCheckForm(selectedCreditCheckForm.id);
    if (response.status === 200) {
      setSelectedCreditCheckForm(null);
      setMyCreditCheckForm(
        myCreditCheckForm.filter(
          (element) => element.id !== selectedCreditCheckForm.id
        )
      );
      setShow(true);
    } else {
      setFailureResponse(response);
      setShowConfirmationModal(false);
      setShowFailurePopUp(true);
    }
    setOnAction(false);
  }

  function handleCloseCredForm() {
    setShowCreditFormModal(false);
  }

  useEffect(() => {
    if (!isInitialized) {
      setIsInitialized(true);

      (async () => {
        setIsLoading(true);
        const response = await GetMyCreditCheckForm();

        if (response.status === 200 && response.data) {
          const attachmentsPromises = response?.data?.map(async (element) => {
            if (!element.credit_check_form_path) {
              return;
            }
            const { data, status } = await GetCreditCheckFormAttachment(
              element.id,
              element.credit_check_form_path,
              '-'
            );
            if (status === 200) {
              element.credit_check_form_path =
                data?.url || element.credit_check_form_path;
            }
          });

          await Promise.all(attachmentsPromises);

          setMyCreditCheckForm(response.data);
        }

        setUserType(getCookies('member').type);
        setIsLoading(false);
      })();
    }
  }, [isInitialized]);

  if (isLoading) {
    return <ToloIsLoading />;
  }
  return (
    <div className="panel" id="p8">
      <CreditApplicationTab
        t={t}
        myCreditCheckForm={myCreditCheckForm}
        setSelectedCreditCheckForm={setSelectedCreditCheckForm}
        setShowConfirmationModal={setShowConfirmationModal}
        setShowCreditFormModal={setShowCreditFormModal}
        setIsForShow={setIsForShow}
        switchPlace={switchPlace}
        userType={userType || 'admin'}
      />

      <CreditApplication
        show={showCreditFormModal}
        t={t}
        handleShow={() => setShow(true)}
        showError={() => setShowFailurePopUp(true)}
        isForShow={isForShow}
        submitCreditCheckForm={AddCreditCheckForm}
        getCreditCheckFormAttachment={GetCreditCheckFormAttachment}
        UpdateCreditCheckForm={UpdateCreditCheckForm}
        data={selectedCreditCheckForm}
        setSelectedCreditCheckForm={setSelectedCreditCheckForm}
        handleClose={handleCloseCredForm}
        setFailureResponse={setFailureResponse}
        switchPlace={switchPlace}
        onClose={handleCloseCredForm}
      />

      <Popup
        t={t}
        show={showFailurePopUp}
        response={failureResponse}
        onClose={() => {
          setShowFailurePopUp(false);
        }}
      />

      <ConfirmationModal
        onClose={() => {
          setShowConfirmationModal(false);
        }}
        cancelText={t('Cancel')}
        message={
          selectedCreditCheckForm &&
          `${t('Are_you_sure_you_want_to_delete')} : ${
            selectedCreditCheckForm.name
          } ?`
        }
        action={onDeleteCreditCheckForm}
        buttonText={t('Delete')}
        show={showConfirmationModal}
        isLoading={onAction}
        t={t}
      />

      <SuccessPopUp
        show={show}
        onClose={handleClose}
        onCloseIcon={handleClose}
      />
    </div>
  );
}
