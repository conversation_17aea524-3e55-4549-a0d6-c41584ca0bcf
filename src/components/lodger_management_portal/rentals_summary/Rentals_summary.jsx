import React, { useState, useEffect } from 'react';
import { useEquipment } from '../../../shared/context/Equipment_context';
import ToloIsLoading from '../../../shared/components/cards/Tolo_is_loading';
import RenderIf from '../../../shared/components/Render_if';
import SummaryTab from '../../../shared/components/management_portal/rentals_summary/Rentals_summary_tab';
import ViewMore from '../../../shared/components/buttons/View_more';
import { useProjects } from '../../../shared/context/Project_context';
import { isEmpty } from 'lodash';

export default function RentalsSummary({ t, role, detectLanguage }) {
  const { GetRentalInSummary } = useEquipment();
  const { GetProjects } = useProjects();

  const [equipments, setEquipments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [itemsPerPage, setItemsPerPage] = useState(3);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isEmptyData, setIsEmptyData] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      if (!isInitialized) {
        setIsInitialized(true);
        setIsLoading(true);
        const { data } = await GetProjects();

        const res = await GetRentalInSummary(itemsPerPage);
        if (res.status === 200 && !isEmpty(res.data)) {
          setEquipments(
            res.data.map((el) => ({
              ...el,
              project_name:
                data?.find((project) => el.project_id === project.id)?.name ||
                '-'
            }))
          );

          setItemsPerPage((prev) => prev + 3);
          setIsEmptyData(res.data.length === equipments.length);
        }
        setIsLoading(false);
      }
    };

    fetchData();
  }, [isInitialized, itemsPerPage, equipments]);

  function intiEquipmentList() {
    setIsInitialized(false);
  }

  function viewMore() {
    setItemsPerPage(itemsPerPage + 3);
    intiEquipmentList();
  }

  if (isLoading) {
    return <ToloIsLoading />;
  }

  return (
    <div className="panel" id="p4">
      <div className="white-bg">
        <div className="equipments-search">
          <div className="row">
            <div className="col-lg-6">
              <div className="form-group" />
            </div>
            <div className="col-lg-6 text-lg-end" />
          </div>
        </div>
        <SummaryTab
          equipments={equipments}
          t={t}
          role={role}
          detectLanguage={detectLanguage}
        />
        <RenderIf condition={equipments && equipments.length !== 0}>
          <div className="equipments-all">
            <ViewMore
              isLoadingButton={isLoading}
              onClick={viewMore}
              isEmptyMessage={t('No_more_equipments')}
              isEmpty={isEmptyData}
            />
          </div>
        </RenderIf>
      </div>
    </div>
  );
}
