import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import MultiSelect from '../../../shared/components/multi_select/Multi_select';
import DatePicker from '../../../shared/components/date_picker/Date_picker';
import CustomButton from '../../../shared/components/buttons/Custom_button';
import InputForm from '../../../shared/components/inputs/Input_form';
import { isDisabled } from '../../../shared/helpers/Date_helper';
import RenderIf from '../../../shared/components/Render_if';
import CustomLabel from '../../../shared/components/labels/Custom_label';
import GeoLocation from '../../../shared/components/inputs/Geonames';
import MultipleSelectCheckmarks from '../../../shared/components/multi_select/Multi_select_mui';

export default function ProjectForm({
  setSelectedCreditCheckForm,
  setSelectedLodgers,
  selectedCreditCheckForm,
  creditCheckFormOptions,
  setSelectedEquipments,
  selectedEquipments,
  equipmentOptions,
  setSelectedBidz,
  selectedLodgers,
  memberOptions,
  initialValues,
  selectedBidz,
  bidzOptions,
  buttonText,
  onSubmitFn,
  projectOwner,
  validate,
  isForShow,
  onClose,
  owner,
  type,
  isEdit,
  t
}) {
  const [isLoading, setIsLoading] = useState();

  const multiSelectValue = [
    {
      title: t('Affected_equipments'),
      name: 'equipments',
      handleChange: setSelectedEquipments,
      value: selectedEquipments,
      options: equipmentOptions,
      disabled: isForShow,
      placeholder: t('Select_equipments')
    },
    {
      title: t('Affected_members_emails'),
      name: 'members',
      handleChange: setSelectedLodgers,
      value: selectedLodgers,
      options: memberOptions,
      disabled: isForShow || (type === 'collaborator' && isEdit),
      placeholder: t('Select_members')
    },
    {
      title: t('Affected_bidz_equipments'),
      handleChange: setSelectedBidz,
      name: 'bidz_equipment',
      value: selectedBidz,
      options: bidzOptions,
      disabled: isForShow,
      placeholder: t('Select_bidz_equipments')
    }
  ];
  const currentTime = new Date();

  const handleStartDateChange = (formik, date) => {
    date.setHours(
      currentTime.getHours(),
      currentTime.getMinutes(),
      currentTime.getSeconds(),
      currentTime.getMilliseconds()
    );

    formik.setFieldValue('start_date', date);
  };

  const handleEndDateChange = (formik, date) => {
    date.setHours(
      currentTime.getHours(),
      currentTime.getMinutes(),
      currentTime.getSeconds(),
      currentTime.getMilliseconds()
    );
    formik.setFieldValue('end_date', date);
  };

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={
        isForShow
          ? onClose
          : (event) => {
              setIsLoading(true);
              onSubmitFn({
                ...event,
                billing_address: {
                  ...event.billing_address,
                  country: event.billing_address.country.label,
                  city: event.billing_address.city.label,
                  state: event.billing_address.state.label
                },
                delivery_address: {
                  ...event.delivery_address,
                  country: event.delivery_address.country.label,
                  city: event.delivery_address.city.label,
                  state: event.delivery_address.state.label
                }
              });
            }
      }
      validationSchema={validate}
    >
      {(formik) => {
        return (
          <Form>
            <div className="row">
              <div className="col-lg-12 padding-r-0">
                <RenderIf condition={!owner && isEdit && !isForShow}>
                  <CustomLabel
                    severity="info"
                    text={t('No_priveleges_to_edit')}
                    className="project-info-lbl"
                  />
                </RenderIf>
              </div>
            </div>

            <div className="row">
              <div className="col-lg-12 padding-r-0">
                <label className="t-body-regular bold mt-4">
                  {t('Project_name_LMP')}
                  <span className="c-red star-required">*</span>
                </label>
                <p className="form-group">
                  <InputForm
                    disabled={isForShow || (!owner && isEdit)}
                    name="name"
                    type="text"
                    className="form-control w-100 "
                    placeholder={t('Project_name_LMP')}
                    isFormik
                  />
                </p>
              </div>
            </div>
            <RenderIf condition={isForShow || isEdit}>
              <div className="row">
                <div className="col-lg-12 padding-r-0">
                  <label className="t-body-regular bold mt-4">
                    {t('Owner_text')}
                  </label>
                  <p className="form-group">
                    <InputForm
                      disabled
                      value={projectOwner?.full_name}
                      className="form-control w-100"
                    />
                  </p>
                </div>
              </div>
            </RenderIf>
            {isForShow && !selectedCreditCheckForm?.value ? null : (
              <div className="row">
                <div className="col-lg-12 padding-r-0">
                  <label className="t-body-regular bold mt-4">
                    {t('Credit_check_form_name_LMP')}
                  </label>
                  <p className="form-group">
                    <MultiSelect
                      handleChange={setSelectedCreditCheckForm}
                      value={selectedCreditCheckForm}
                      options={creditCheckFormOptions}
                      placeholder={t('Select_CCF')}
                      defaultValue={selectedCreditCheckForm}
                      t={t}
                      isClearable
                      disabled={isForShow || (!owner && isEdit)}
                    />
                  </p>
                </div>
              </div>
            )}
            <div className="row">
              <div className="col-lg-12 padding-r-0">
                <label className="t-body-regular bold mt-4">
                  {t('Billing_address_LMP')}{' '}
                  <span className="c-red star-required">*</span>
                </label>
                <p className="form-group">
                  <GeoLocation
                    formik={formik}
                    names={{
                      country: 'billing_address.country',
                      state: 'billing_address.state',
                      city: 'billing_address.city',
                      address: 'billing_address.address',
                      zip_code: 'billing_address.zip_code'
                    }}
                    disabled={isForShow || (!owner && isEdit)}
                    t={t}
                    initialValues={{
                      country: initialValues.billing_address.country,
                      state: initialValues.billing_address.state,
                      city: initialValues.billing_address.city,
                      address: initialValues.billing_address.address,
                      zip_code: initialValues.billing_address.zip_code
                    }}
                    isProject
                  />
                </p>
              </div>
            </div>
            <div className="row">
              <div className="col-lg-12 padding-r-0">
                <label className="t-body-regular bold mt-4">
                  {t('Delivery_address_LMP')}{' '}
                  <span className="c-red star-required">*</span>
                </label>
                <p className="form-group">
                  <GeoLocation
                    formik={formik}
                    t={t}
                    names={{
                      country: 'delivery_address.country',
                      state: 'delivery_address.state',
                      city: 'delivery_address.city',
                      address: 'delivery_address.address',
                      zip_code: 'delivery_address.zip_code'
                    }}
                    initialValues={{
                      country: initialValues.delivery_address.country,
                      state: initialValues.delivery_address.state,
                      city: initialValues.delivery_address.city,
                      address: initialValues.delivery_address.address,
                      zip_code: initialValues.delivery_address.zip_code
                    }}
                    disabled={isForShow || (!owner && isEdit)}
                    isProject
                  />
                </p>
              </div>
            </div>
            {multiSelectValue?.map((item, key) =>
              isForShow && item.value.length === 0 ? null : (
                <div className="row" key={key}>
                  <div className="col-lg-12 padding-r-0">
                    <label className="t-body-regular bold mt-4">
                      {item.title}
                    </label>
                    <p className="form-group">
                      <MultipleSelectCheckmarks
                        onChange={item.handleChange}
                        options={item.options}
                        value={item.value}
                        name={item.name}
                        defaultValue={item.value}
                        disabled={item.disabled}
                        placeholder={item.placeholder}
                        t={t}
                      />
                    </p>
                  </div>
                </div>
              )
            )}
            <div className="row">
              <div className="date-picker-project form-group">
                <div className=" date-label t-body-regular bold mt-4">
                  <label className="start-date-project ">
                    {t('Start_date')}
                  </label>
                  <label className="end-date-project ">{t('End_date')}</label>
                  <label className="mobile-label d-md-none d-block">
                    {t('Start_End_date')}
                  </label>
                </div>
                <DatePicker
                  isForShow={isForShow || (!owner && isEdit)}
                  handleStartDateChange={(date) =>
                    handleStartDateChange(formik, date)
                  }
                  handleEndDateChange={(date) =>
                    handleEndDateChange(formik, date)
                  }
                  startDate={formik.values.start_date}
                  endDate={formik.values.end_date}
                  startDateClassName="form-control w-100 "
                  endDateClassName="form-control w-100"
                />

                {isDisabled(
                  formik.values.start_date,
                  formik.values.end_date
                ) && <p className="error-message">{t('Date_error_msg')} </p>}
              </div>
            </div>

            <div className="send-btn fixed-button-modal">
              <CustomButton
                textButton={isForShow ? t('Close_button') : t(buttonText)}
                type="submit"
                isLoading={isLoading}
                className="round-button bold yellow"
                disabled={
                  isDisabled(
                    formik.values.start_date,
                    formik.values.end_date
                  ) || isLoading
                }
              />
            </div>
          </Form>
        );
      }}
    </Formik>
  );
}
