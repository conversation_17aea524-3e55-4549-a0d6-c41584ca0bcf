import React, { useState, useEffect } from 'react';
import ProjectForm from './Project_form';
import { removeOwnerFromList } from '../../../shared/helpers/Array_helpers';
import RenderIf from '../../../shared/components/Render_if';
import { getCookies } from '../../../shared/helpers/Cookies';
import { isEmpty } from 'lodash';
import ToloIsLoading from '../../../shared/components/cards/Tolo_is_loading';
import { getSessionStorage } from '../../../shared/helpers/Session_storage_helper';

export default function EditProjectModal({
  t,
  onClose,
  onEditClick,
  show,
  isForShow,
  bidzOptions,
  equipmentOptions,
  memberOptions,
  creditCheckFormOptions,
  validate,
  type,
  project
}) {
  const owner = project.creator_id.id === getCookies('userId');
  const ownerTeam = project.owner_id.id === getCookies('userId');

  const [selectedCreditCheckForm, setSelectedCreditCheckForm] = useState([]);
  const [selectedEquipments, setSelectedEquipments] = useState([]);
  const [selectedLodgers, setSelectedLodgers] = useState([]);
  const [selectedBidz, setSelectedBidz] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const initialValues = project && {
    name: project.name,
    billing_address: {
      address: project.billing_address.address,
      zip_code: project.billing_address.zip_code,
      city: {
        label: project.billing_address.city,
        value: project.billing_address.city
      },
      state: {
        label: project.billing_address.state,
        value: project.billing_address.state
      },
      country: {
        label: project.billing_address.country,
        value: project.billing_address.country
      }
    },
    delivery_address: {
      address: project.delivery_address.address,
      zip_code: project.delivery_address.zip_code,
      city: {
        label: project.delivery_address.city,
        value: project.delivery_address.city
      },
      state: {
        label: project.delivery_address.state,
        value: project.delivery_address.state
      },
      country: {
        label: project.delivery_address.country,
        value: project.delivery_address.country
      }
    },
    equipments: selectedEquipments,
    members: selectedLodgers,
    bidz_equipments: selectedBidz,
    start_date: new Date(Date.parse(project.start_date)),
    end_date: new Date(Date.parse(project.end_date))
  };

  const updateProject = async (event) => {
    onEditClick({
      ...event,
      id: project.id,
      equipments: selectedEquipments,
      credit_check_form_id: selectedCreditCheckForm?.value?.id,
      members: ! ownerTeam ? [...selectedLodgers, getSessionStorage('member_id')] :selectedLodgers ,
      credit_check_form: selectedCreditCheckForm
        ? selectedCreditCheckForm.value
        : {},
      bidz_equipment: selectedBidz
    });
  };
  useEffect(() => {
    if (!isEmpty(project.equipments)) {
      setSelectedEquipments(project.equipments?.map((item) => item.id));
    }

    if (!isEmpty(project.members)) {
      const projectMembers = removeOwnerFromList(
        project.members
          ?.filter((member) => member.email !== getCookies('email'))
          ?.map((item) => item.id),
        project.owner_id.id
      );
      setSelectedLodgers(
        projectMembers.filter((el) => el !== project.creator_id.member_id)
      );
    }

    if (!isEmpty(project.bidz_equipment)) {
      setSelectedBidz(project.bidz_equipment?.map((item) => item.id));
    }

    if (project.credit_check_form.name) {
      setSelectedCreditCheckForm({
        label: project.credit_check_form.name,
        value: project.credit_check_form
      });
    }
    setTimeout(() => {
      setIsLoading(false);
    }, 500);
  }, []);

  return (
    <RenderIf condition={show}>
      <div className="modal create-project-modal">
        <div className="modal-content no-title-margeTop add-memberModal">
          <button className="close-button" onClick={onClose} type="button">
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19.25 5.25L5.75 18.75"
                stroke="#333333"
                strokeWidth="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M19.25 18.75L5.75 5.25"
                stroke="#333333"
                strokeWidth="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
          <div className="row">
            <div className="col-lg-11 mx-auto">
              <div className={!isLoading ? 'scrollBarModal' : ''}>
                <h2 className="t-header-h5 c-fake-black credit-title">
                  {isForShow ? t('Project_details_LMP') : t('Edit_project_LMP')}
                </h2>
                {isLoading ? (
                  <ToloIsLoading />
                ) : (
                  <ProjectForm
                    validate={validate}
                    onSubmitFn={updateProject}
                    projectOwner={project.creator_id}
                    initialValues={initialValues}
                    equipmentOptions={equipmentOptions}
                    selectedEquipments={selectedEquipments}
                    setSelectedEquipments={setSelectedEquipments}
                    creditCheckFormOptions={creditCheckFormOptions}
                    selectedCreditCheckForm={selectedCreditCheckForm}
                    setSelectedCreditCheckForm={setSelectedCreditCheckForm}
                    memberOptions={memberOptions}
                    selectedLodgers={selectedLodgers}
                    setSelectedLodgers={setSelectedLodgers}
                    bidzOptions={bidzOptions}
                    selectedBidz={selectedBidz}
                    setSelectedBidz={setSelectedBidz}
                    buttonText={
                      isForShow ? t('Close_button') : t('Update_project_LMP')
                    }
                    isForShow={isForShow}
                    onClose={onClose}
                    owner={owner}
                    type={type}
                    isEdit
                    t={t}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </RenderIf>
  );
}
