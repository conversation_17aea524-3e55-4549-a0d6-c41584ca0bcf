import React from 'react';
import Modal from '../../../shared/components/modals/Modal';

export default function ProjectDeleteModal({
  t,
  project,
  onClose,
  onDelete,
  isLoading
}) {
  return (
    <Modal
      onClose={onClose}
      noScrollModal
      className="text-center t-header-medium c-grey-titles"
      description={`${t('Are_you_sure_you_want_to_delete_project')} ${
        project.name
      } ? `}
      t={t}
    >
      <div className="text-center btn-content fixed-button-modal">
        <button className="round-button black bold mr-10" onClick={onClose}>
          {t('I_m_not_sure')}
        </button>
        <button
          className={
            isLoading
              ? 'round-button c-near-grey bold '
              : 'round-button yellow bold'
          }
          disabled={isLoading}
          onClick={() => {
            onDelete(project);
          }}
        >
          {t('Delete')}
        </button>
      </div>
    </Modal>
  );
}
