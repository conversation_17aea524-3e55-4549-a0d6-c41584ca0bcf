import React, { useEffect, useState } from 'react';
import ProjectsTable from './Projects_table';
import RenderIf from '../../../shared/components/Render_if';
import TableCrudMenu from '../../../shared/components/management_portal/team_management/Table_crud_menu';
import TableSearchBar from '../../../shared/components/buttons/Table_search_bar';
import { getCookies } from '../../../shared/helpers/Cookies';
import ProjectEmptyState from '../../../style/assets/img/empty_state/Project_empty_state.svg';
import NoResults from '../../search_result/No_results';

export default function ProjectDataTable({
  handleShowEditProjectModal,
  handleShowCreateProjectModal,
  handleShowDeleteProjectModal,
  handleShowDetails,
  selectProject,
  selectedProject,
  projects,
  type,
  t
}) {
  const [isActive, setIsActive] = useState(false);

  useEffect(() => {
    setIsActive(
      type === 'admin' ||
        type === 'owner' ||
        (selectedProject &&
          selectedProject.creator_id.id === getCookies('userId'))
    );
  }, [selectedProject, type]);

  if (!projects) {
    return (
      <NoResults
        message={t('You_currently_have_no_projects_LMP')}
        image={ProjectEmptyState}
        buttonText={t('Create_project_LMP')}
        onClick={handleShowCreateProjectModal}
      />
    );
  }

  return (
    <div className="team-managements white-bg">
      <RenderIf condition={projects}>
        <RenderIf condition={type !== 'collaborator'}>
          <TableSearchBar
            buttonTitle={t('Create_project_LMP')}
            onClick={handleShowCreateProjectModal}
          />
        </RenderIf>
        <ProjectsTable
          projects={projects}
          selectProject={selectProject}
          t={t}
        />
        <TableCrudMenu
          t={t}
          isProjectManagement
          onDeleteClick={handleShowDeleteProjectModal}
          onEditClick={handleShowEditProjectModal}
          onDetailsClick={handleShowDetails}
          disabledDelete={!isActive || selectedProject === null}
          showTooltipProject={!isActive}
          disabledEdit={selectedProject === null}
          list={projects}
          type={type}
        />
      </RenderIf>
    </div>
  );
}
