import React, { useState, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import Box from '@mui/material/Box';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';
import TableSortLabel from '@mui/material/TableSortLabel';
import Checkbox from '@mui/material/Checkbox';
import { getComparator, stableSort } from '../../../shared/helpers/Data_table';
import { cutString } from '../../../shared/helpers/String_helps';
import CustomTooltip from '../../../shared/components/tooltips/Tooltip';

const headCells = [
  {
    id: 'name',
    numeric: false,
    disablePadding: true,
    label: 'Name'
  },
  {
    id: 'owner',
    numeric: false,
    disablePadding: true,
    label: 'Owner_text'
  },
  {
    id: 'dates',
    numeric: true,
    disablePadding: true,
    label: 'Dates'
  },
  {
    id: 'delivery_address',
    numeric: false,
    disablePadding: true,
    label: 'Delivery_address_LMP'
  },
  {
    id: 'billing_address',
    numeric: false,
    disablePadding: true,
    label: 'Billing_address_LMP'
  }
];

export function TableHeadComponent({
  order,
  orderBy,
  onRequestSort,
  t,
  headCells
}) {
  const createSortHandler = (property) => (event) => {
    onRequestSort(event, property);
  };

  return (
    <TableHead
      sx={{
        backgroundColor: '#f1f1f1',
        textAlign: 'center'
      }}
    >
      <TableRow>
        {headCells.map((headCell) => {
          const sort = headCell.id !== 'email' || headCell.id !== 'name';

          return (
            <TableCell key={headCell.id} sortDirection={sort ? order : false}>
              <div className="col-12">
                <TableSortLabel
                  active={headCell.id === 'email' || headCell.id === 'name'}
                  hideSortIcon={sort}
                  direction={orderBy === headCell.id ? order : 'asc'}
                  onClick={createSortHandler(headCell.id)}
                >
                  {t(headCell.label)}
                </TableSortLabel>
              </div>
            </TableCell>
          );
        })}
      </TableRow>
    </TableHead>
  );
}

TableHeadComponent.propTypes = {
  onRequestSort: PropTypes.func.isRequired,
  order: PropTypes.oneOf(['asc', 'desc']).isRequired,
  orderBy: PropTypes.string.isRequired
};

function DataTable({ rows, selectProject, t }) {
  const [order, setOrder] = useState('asc');
  const [orderBy, setOrderBy] = useState('name');
  const [selected, setSelected] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);

  const handleRequestSort = (_, property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleClick = (_, row) => {
    if (selected[0] === row.id) {
      setSelected([]);
      selectProject(null);
    } else {
      setSelected([row.id]);
      selectProject(row);
    }
  };

  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const visibleRows = useMemo(
    () =>
      stableSort(rows, getComparator(order, orderBy)).slice(
        page * rowsPerPage,
        page * rowsPerPage + rowsPerPage
      ),
    [order, orderBy, page, rowsPerPage, rows]
  );

  return (
    <Box sx={{ width: '100%' }}>
      <TableContainer>
        <Table sx={{ minWidth: 750 }} aria-labelledby="tableTitle">
          <TableHeadComponent
            numSelected={selected.length}
            order={order}
            t={t}
            headCells={headCells}
            orderBy={orderBy}
            onRequestSort={handleRequestSort}
            rowCount={rows.length}
          />
          <TableBody>
            {visibleRows.map((row, index) => {
              const labelId = `enhanced-table-checkbox-${index}`;

              return (
                <TableRow
                  hover
                  onClick={(event) => {
                    handleClick(event, row);
                  }}
                  role="checkbox"
                  aria-checked={row.id === selected[0]}
                  tabIndex={-1}
                  key={row.id}
                  selected={row.id === selected[0]}
                  sx={{ cursor: 'pointer' }}
                >
                  <TableCell className="col-3">
                    <Checkbox
                      color="primary"
                      checked={row.id === selected[0]}
                      inputProps={{
                        'aria-labelledby': labelId
                      }}
                      onChange={(event) => {
                        handleClick(event, row);
                      }}
                    />
                    <span className="t-body-regular  c-fake-black">
                      {row.name}
                    </span>
                  </TableCell>

                  <TableCell
                    align="left"
                    id={labelId}
                    className="col-lg-2 col-3"
                  >
                    <CustomTooltip
                      text={row?.creator_id.full_name}
                      placement="right"
                    >
                      <span className="owner-tag p-2 m-2">
                        {cutString(row?.creator_id.full_name, 15)}
                      </span>
                    </CustomTooltip>
                  </TableCell>
                  <TableCell align="left" id={labelId} className="col-2">
                    {t('From')} : {row.start_date?.slice(0, 10)} <br></br>{' '}
                    {t('To')} : {row?.end_date.slice(0, 10)}
                  </TableCell>

                  <TableCell align="left" id={labelId}>
                    {' '}
                    {` ${row.delivery_address.address} ${row.delivery_address.zip_code}
                                               ${row.delivery_address.state} ${row.delivery_address.city}`}
                  </TableCell>
                  <TableCell
                    align="left"
                    id={labelId}
                  >{` ${row.billing_address.address} ${row.billing_address.zip_code}
                                               ${row.billing_address.state} ${row.billing_address.city}`}</TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={rows.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Box>
  );
}

export default function ProjectsTable({ projects, t, selectProject }) {
  const [projectsData, setProjectsData] = useState([]);

  useEffect(() => {
    setProjectsData(projects);
  }, [projects]);

  return (
    <div className="mt-4">
      <DataTable rows={projectsData} selectProject={selectProject} t={t} />
    </div>
  );
}
