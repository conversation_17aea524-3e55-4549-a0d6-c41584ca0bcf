import React, { useCallback } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { connectInfiniteHits } from 'react-instantsearch-dom';
import { isUndefined, isEmpty } from 'lodash';
import { setSessionStorage } from '../../shared/helpers/Session_storage_helper';
import Typography from '@mui/material/Typography';
import { Link } from 'react-router-dom';

const EmptyDropdown = () => {
  return (
    <div style={{ paddingLeft: '20px' }}>
      <Typography fontFamily="inherit">
        <Trans i18nKey="Location_not_available">
          Location is not available? Please,
          <Link className="underline-yellow" to="/help">
            contact us
          </Link>{' '}
          to resolve it.
        </Trans>
      </Typography>
    </div>
  );
};

const EmptyDropdownContactUs = () => (
  <div style={{ paddingLeft: '20px' }}>
    <Typography fontFamily="inherit">
      <Trans i18nKey="Equipment_not_available">
        Equipment is not available? Please,
        <Link className="underline-yellow" to="/help">
          contact us
        </Link>{' '}
        to resolve it.
      </Trans>
    </Typography>
  </div>
);

const DropdownItem = ({ item, index, handleChange, name }) => {
  return (
    <li
      className="autocomplete-label"
      key={index}
      onClick={() => handleChange(item, name)}
    >
      <span>{name}</span>
    </li>
  );
};

const InfiniteHits = ({
  hits,
  onChange,
  isEquipment,
  setEquipmentID,
  onLocationChange,
  equipmentNameClassName,
  isAdvancedFilter,
  t,
  isAddEquipment,
  showDropdown
}) => {
  const {
    i18n: { language }
  } = useTranslation();

  const equipments = hits?.map((item) => {
    const targetLanguage = language === 'ar' ? 'en' : language;
    return {
      label: isUndefined(item[`alias.${targetLanguage}`])
        ? [item[`name_${targetLanguage}`]]
        : [
            ...item[`alias.${targetLanguage}`]?.map((alias) => alias),
            item[`name_${targetLanguage}`]
          ],
      name_en: item.name_en,
      name_fr: item.name_fr,
      objectID: item.objectID,
      description_fr: item.description_fr,
      description_en: item.description_en
    };
  });

  const noScrollBar = isEmpty(equipments) || isEmpty(hits);

  const handleChange = useCallback(
    (item, alias) => {
      if (isEquipment) {
        setEquipmentID(item.objectID);
        if (!isAddEquipment) {
          setSessionStorage('requestedName', alias);
          setSessionStorage('descriptionFr', item.description_fr);
          setSessionStorage('descriptionEn', item.description_en);
          setSessionStorage('nameFr', item.name_fr);
          setSessionStorage('nameEn', item.name_en);
          setSessionStorage('equipmentID', item.objectID);
        }
        onChange({
          requestedName: alias,
          name_en: item.name_en,
          name_fr: item.name_fr,
          description_fr: item.description_fr,
          description: item.description
        });
      } else {
        onChange({
          value: item.name,
          isSelected: true
        });
        isAdvancedFilter && onLocationChange(item.name);
      }
      // Close dropdown after selection
      showDropdown && showDropdown(false);
    },
    [isEquipment, isAdvancedFilter, showDropdown]
  );

  return (
    <div className={isEquipment ? equipmentNameClassName : 'container wrapper'}>
      <ul>
        <div className="autocomplete-options-menu">
          <div
            className={
              noScrollBar
                ? 'd-flex justify-content-center'
                : 'scroll-bar-options'
            }
          >
            {isEquipment ? (
              isEmpty(equipments) ? (
                <EmptyDropdownContactUs />
              ) : (
                equipments?.map((item, index) =>
                  item.label?.map((alias, i) => (
                    <DropdownItem
                      key={i}
                      item={item}
                      index={index}
                      handleChange={handleChange}
                      name={alias}
                    />
                  ))
                )
              )
            ) : isEmpty(hits) ? (
              <EmptyDropdown t={t} />
            ) : (
              hits?.map((item, index) => (
                <DropdownItem
                  key={index}
                  item={item}
                  index={index}
                  handleChange={handleChange}
                  name={item.name}
                />
              ))
            )}
          </div>
        </div>
      </ul>
    </div>
  );
};
export const InfiniteHitsDropdown = connectInfiniteHits(InfiniteHits);
