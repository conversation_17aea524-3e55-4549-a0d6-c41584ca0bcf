import React, { useState, useEffect } from 'react';
import { connectSearchBox } from 'react-instantsearch-dom';

const SearchBox = ({
  value,
  placeholder,
  refine,
  className,
  onChange,
  showDropdown,
  attribute,
  setEquipmentID,
  currentRefinement
}) => {
  // Local state to track the input value for immediate display
  const [inputValue, setInputValue] = useState(value || '');

  // Sync with parent value when it changes (e.g., when item is selected from dropdown)
  useEffect(() => {
    // Only sync if the parent value is different from our local state
    // This prevents unnecessary updates when user is typing
    if (value !== undefined && value !== inputValue) {
      setInputValue(value || '');
    }
  }, [value]);

  // Sync search refinement with parent value when needed
  useEffect(() => {
    if (value !== undefined && value !== currentRefinement) {
      refine(value);
    }
  }, [value, currentRefinement, refine]);

  const onInternalBlur = () => {
    setTimeout(() => {
      showDropdown(false);
    }, 300);
  };

  const handleInputChange = (event) => {
    const newValue = event.currentTarget.value;

    // Update local state immediately for responsive typing
    setInputValue(newValue);

    // Show dropdown when user starts typing
    showDropdown(true);

    // Update parent state based on attribute type
    if (attribute === 'equipment') {
      // For equipment, we need to update the requestedName field
      // while preserving other fields until a selection is made
      onChange({ requestedName: newValue });
      // Clear equipment ID when user types (they're searching for new equipment)
      setEquipmentID('');
    } else if (attribute === 'location') {
      onChange({ value: newValue, isSelected: false });
    }

    // Update Algolia search refinement to filter suggestions
    refine(newValue);
  };

  return (
    <li>
      <input
        type="search"
        className={className}
        placeholder={placeholder}
        value={inputValue}
        onBlur={onInternalBlur}
        required
        onChange={handleInputChange}
        onKeyDown={() => {
          // Clear equipment ID when user starts typing
          attribute === 'equipment' && setEquipmentID('');
        }}
      />
    </li>
  );
};
SearchBox.defaultProps = {
  onChange: () => {},
  showDropdown: () => {}
};

export const CustomSearchBox = connectSearchBox(SearchBox);
