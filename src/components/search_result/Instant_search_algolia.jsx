import React from 'react';
import { InstantSearch } from 'react-instantsearch-dom';
import { searchClient } from '../../shared/helpers/Algolia_helper';

export default function InstantSearchAlgolia({
  searchState,
  onSearchStateChange,
  children,
  indexName,
  createURL
}) {
  return (
    <InstantSearch
      searchState={searchState}
      onSearchStateChange={onSearchStateChange}
      indexName={indexName}
      searchClient={searchClient}
      createURL={createURL}
      refresh
    >
      {children}
    </InstantSearch>
  );
}
