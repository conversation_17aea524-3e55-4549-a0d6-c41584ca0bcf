import React, { useEffect, useState } from 'react';
import { connectMenu } from 'react-instantsearch-dom';
import CategoryItem from '../../shared/components/labels/Category_item';
import {
  categoriesOptions,
  status,
  subCategoriesOptions
} from '../../shared/helpers/Data_helper';
import {
  setSessionStorage,
  getSessionStorage
} from '../../shared/helpers/Session_storage_helper';

const Menu = ({
  items,
  refine,
  selected,
  attribute,
  isEquipmentManagement,
  t,
  setItemsLength,
  isEquipperSpotlight
}) => {
  const [data, setData] = useState([]);
  const category = getSessionStorage('category');
  const refineItem = (item) => {
    switch (attribute) {
      case 'category':
        if (selected === item.value) {
          refine('');
          setSessionStorage('category', '');
          setSessionStorage('sub_category', '');
        } else {
          refine(item.value);
          setSessionStorage('category', item.value);
          setSessionStorage('sub_category', '');
        }
        break;
      case 'sub_category':
        if (selected === item.value) {
          refine('');
        } else {
          refine(item.value);
          setSessionStorage('sub_category', item.value);
        }
        break;
      case 'status':
        if (selected === item.value) {
          refine('');
          setSessionStorage('status', '');
        } else {
          refine(item.value);
          setSessionStorage('status', item.value);
        }
        break;
      default:
        refine(item.value);
    }
  };

  useEffect(() => {
    if (attribute === 'sub_category' && category !== '') {
      setItemsLength(items.length || data.length);
    }
    switch (attribute) {
      case 'category':
        setData(
          isEquipperSpotlight || isEquipmentManagement
            ? items
            : categoriesOptions(t)
        );
        break;
      case 'sub_category':
        if (category !== '') {
          setData(items);
        } else {
          setData(subCategoriesOptions(t));
        }
        break;
      case 'status':
        setData(status(t));
        break;
      default:
        break;
    }
  }, [attribute, items, t]);

  return (
    <>
      <ul className="p-0">
        {data.map((item, key) => (
          <CategoryItem
            onClick={() => refineItem(item)}
            selected={
              (selected &&
                selected.toLowerCase() === item.label.toLowerCase()) ||
              (selected && selected.toLowerCase() === item.value.toLowerCase())
            }
            item={item}
            attribute={attribute}
            category={category}
            iconPath={category?.iconPath}
            t={t}
            key={key}
          />
        ))}
      </ul>
    </>
  );
};

export const CustomSpotlightMenu = connectMenu(Menu);
