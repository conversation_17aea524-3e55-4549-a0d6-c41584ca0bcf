import { isEmpty } from 'lodash';
import React, { useEffect, useState } from 'react';
import { connectMenu } from 'react-instantsearch-dom';
import {
  getSessionStorage,
  setSessionStorage
} from '../../shared/helpers/Session_storage_helper';

const Menu = ({ items, refine, attribute, t }) => {
  const [selectedValue, setSelectedValue] = useState('');
  const descriptionEn = getSessionStorage('descriptionEn');
  const descriptionFr = getSessionStorage('descriptionFr');

  const onRefine = (item) => {
    if (attribute === 'description_fr' || attribute === 'description') {
      setSelectedValue(item.value);
      if (attribute === 'description_fr') {
        setSessionStorage('descriptionFr', item.value);
      } else {
        setSessionStorage('descriptionEn', item.value);
      }
    }
  };

  const handleChange = (e, item) => {
    e.preventDefault();
    refine(item.value);
    onRefine(item);
    window.scrollTo(0, 0);
  };

  const defaultChecked = (item) => {
    if (attribute === 'category' || attribute === 'sub_category') {
      return true;
    }
    if (attribute === 'description_fr' || attribute === 'description') {
      return item.value === selectedValue;
    }
  };

  useEffect(() => {
    if (
      (attribute === 'description_fr' || attribute === 'description') &&
      items.length > 0
    ) {
      setSelectedValue(descriptionEn || descriptionFr);
    }
    return () => {
      setSelectedValue('');
    };
  }, [attribute, descriptionEn, descriptionFr, items]);

  return (
    <>
      {isEmpty(items) ? (
        <p>{t('Not_applicable')}</p>
      ) : (
        <div className="radio-box categories-box">
          <ul>
            {items?.map((item, index) => (
              <li key={item.value}>
                <input
                  type="radio"
                  id={attribute + index}
                  name={attribute + index}
                  onChange={(e) => handleChange(e, item)}
                  checked={item.isRefined || defaultChecked(item)}
                />
                <label
                  htmlFor={attribute + index}
                  className="check-label t-body-small c-blue-grey"
                  style={{ fontWeight: item.isRefined && 'bold' }}
                >
                  <span>
                    <div className="align-items">
                      {attribute === 'category' || attribute === 'sub_category'
                        ? t(item.label.replaceAll(' ', '_'))
                        : item.label}
                    </div>
                  </span>
                </label>
              </li>
            ))}
          </ul>
        </div>
      )}
    </>
  );
};

Menu.defaultProps = {
  items: [],
  attribute: ''
};

export const CustomRadioBoxMenu = connectMenu(Menu);
