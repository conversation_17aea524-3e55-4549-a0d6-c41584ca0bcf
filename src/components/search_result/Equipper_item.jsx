import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useSearchContext } from '../../shared/context/Search_context';
import { rating } from '../../shared/helpers/Rating_helper';
import { RatingCard } from '../../shared/components/cards/Rating_card';
import CustomImage from '../../shared/components/images/Custom_image';
import RenderIf from '../../shared/components/Render_if';
import { isEmpty } from 'lodash';
import Location from '../../style/assets/img/company_spotlight/location.svg';
import { cutString } from '../../shared/helpers/String_helps';
import CustomTooltip from '../../shared/components/tooltips/Tooltip';

function EquipperItem({ t, item, recommendationObjectID }) {
  const navigate = useNavigate();
  const { start_date, end_date, city, name_en } = useParams();
  const { getEquipperById } = useSearchContext();
  const [equipper, setEquipper] = useState(null);
  const [ratingData, setRatingData] = useState();

  function navigateToCompanySpotlight() {
    navigate(
      `/companySpotlight/${item.equipper_id}/${start_date}/${end_date}/${city}/${name_en}/${recommendationObjectID}`
    );
  }

  useEffect(() => {
    if (!isEmpty(item)) {
      (async () => {
        const res = await getEquipperById(item.equipper_id);
        if (res.status === 200) {
          setEquipper(res.data);
          setRatingData(rating(res.data?.user_name));
        }
      })();
    }
  }, []);

  return (
    <RenderIf condition={equipper && equipper.has_inventory}>
      <div className="result-box">
        <div className="container">
          <div className="row align-items-center">
            <div className="d-xl-flex d-lg-block d-flex result-box__image">
              <CustomImage
                imageUrl={equipper && equipper.photo_url}
                alt={t('Cant_load_image')}
                isUser
              />
              <div className="search-title">
                <h2 className="t-subheading-1 bold c-fake-black">
                  {equipper && equipper.company}
                </h2>
                <p className="t-caption-small c-new-green confirmation">
                  {t('Confirmation_under_30min')}
                </p>
              </div>
            </div>
            <div className="new-ui-search-bottom">
              <div className="result-box__bottom">
                <div className="bottom-content">
                  <CustomTooltip
                    text={`${equipper?.address?.address} ${equipper?.address?.city} ${equipper?.address?.state}`}
                    placement="left"
                  >
                    <p className="t-body-regular c-blue-grey mb-1 d-flex align-items-center">
                      <img
                        src={Location}
                        alt="location"
                        className="location-icon"
                      />
                      {cutString(
                        `${equipper?.address?.address} ${equipper?.address?.city} ${equipper?.address?.state}`,

                        40
                      )}
                    </p>
                  </CustomTooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="d-flex justify-content-between align-items-center">
          <div className="result-box__top">
            <div className="result-box__top-left">
              {ratingData && (
                <div className="result-box__rate">
                  <RatingCard t={t} ratingData={ratingData} />
                </div>
              )}
            </div>
          </div>
          <div className="top-content d-lg-inline-block ">
            <button
              className="round-button yellow c-black hover_black d-flex align-items-center with-arrow-white"
              onClick={navigateToCompanySpotlight}
            >
              {t('See_availability')}
            </button>
          </div>
        </div>
      </div>
    </RenderIf>
  );
}
export default EquipperItem;
