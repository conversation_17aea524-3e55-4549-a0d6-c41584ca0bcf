import React from 'react';
import { connectCurrentRefinements } from 'react-instantsearch-dom';

const ClearRefinements = ({ items, refine, t, setChangedSearchState }) => {
  items = filterRefinements(items);
  return (
    <span
      className={` 
          transparent t-header-medium title-filter  ${
            !items.length ? 'c-near-grey ' : 'c-yellow'
          }`}
      onClick={() => {
        sessionStorage.removeItem('category');
        sessionStorage.removeItem('sub_category');
        sessionStorage.setItem('descriptionFr', JSON.stringify(''));
        sessionStorage.setItem('descriptionEn', JSON.stringify(''));
        filterRefinements(items);
        refine(items);
        setChangedSearchState(false);
      }}
      disabled={!items.length}
    >
      {t('Clear_all')}
    </span>
  );
};

function filterRefinements(items) {
  return items.filter(
    (item) =>
      item.id !== 'coverage_area' &&
      item.id !== 'name' &&
      item.id !== 'name_fr' &&
      item.id !== 'available_from'
  );
}
export const CustomClearRefinements =
  connectCurrentRefinements(ClearRefinements);
