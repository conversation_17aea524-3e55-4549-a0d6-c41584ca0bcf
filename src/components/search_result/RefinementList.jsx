import React from 'react';
import { connectRefinementList } from 'react-instantsearch-dom';
import RenderIf from '../../shared/components/Render_if';
import { firstLetterUpperCase } from '../../shared/helpers/String_helps';

const RefinementList = ({ items, refine, attribute, t }) => {
  const refineItem = (item) => {
    if (item.isRefined) {
      refine('');
    }else{
      refine(item.value);
    }
    window.scrollTo(0, 0);

  };
  return (
    <RenderIf condition={items.length > 0}>
      <div>
        <p className="c-primary-color filters-more-filters">
          {t(firstLetterUpperCase(attribute))}
        </p>
        <ul>
          {items?.map((item, index) => (
            <span key={index}>
              <li style={{ display: 'flex', alignItems: 'center' }}>
                <input
                  type="checkbox"
                  id={attribute + index}
                  onChange={() => refineItem(item)}
                  checked={item.isRefined}
                  style={{ 
                    marginRight: '8px', 
                    minHeight: '34px', 
                    alignItems: 'center', 
                    cursor: 'pointer',
                    accentColor: '#ECA869'
                  }}
                />
                <label
                  htmlFor={attribute + index}
                  style={{
                    cursor: 'pointer',
                    fontWeight: item.isRefined && 'bold',
                    whiteSpace: 'nowrap',
                    overflowX: 'hidden', 
                    textOverflow: 'ellipsis',
                    marginBottom: 0,
                    fontSize: '15px'
                  }}
                >
                  {item.label} ({item.count})
                </label>
              </li>
            </span>
          ))}
        </ul>
      </div>
    </RenderIf>
  );
};

export const CustomRefinementList = connectRefinementList(RefinementList);
