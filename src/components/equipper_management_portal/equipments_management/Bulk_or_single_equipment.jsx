import React, { useState } from 'react';
import * as XLSX from 'xlsx';
import FileIcon from '../../../style/assets/img/file.svg';
import CustomButton from '../../../shared/components/buttons/Custom_button.jsx';
import Box from '@mui/material/Box';
import { faPlus } from '@fortawesome/fontawesome-free-solid';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useNavigate } from 'react-router-dom';
import { useEquipment } from '../../../shared/context/Equipment_context.jsx';
import Popup from '../../../shared/components/modals/Popup.jsx';
import SuccessPopUp from '../../../shared/components/modals/Success_pop_up.jsx';
import { UPLOAD_EQUIPMENTS } from '../../../shared/helpers/Url_prefixes.js';
import { setCookies, getCookies } from '../../../shared/helpers/Cookies.js';
import { userCurrency } from '../../../shared/helpers/Currency.js';
import { getSessionStorage } from '../../../shared/helpers/Session_storage_helper.js';
import RenderIf from '../../../shared/components/Render_if.jsx';
import FileIconAirtable from '../../../style/assets/img/airtable.svg';
import ConfirmationModal from '../../../shared/components/modals/Confirmation_modal.jsx';

export default function BulkOrSingleEquipment({ t }) {
  const { AddEquipments, AirTableSynchronization } = useEquipment();
  const [response, setResponse] = useState(null);
  const [onAction, setOnAction] = useState(false);
  const equipper = getSessionStorage('equipper');
  const [showAirTableConfirmationModal, setShowAirTableConfirmationModal] =
    useState(false);
  const [showActions, setShowActions] = useState({
    showSuccess: false,
    showError: false
  });
  const navigate = useNavigate();
  const [columns, setColumns] = useState(null);
  const [selectedFile, setSelectedFile] = useState({
    isSelected: false,
    file: null,
    fileName: ''
  });

  const submit = async () => {
    setOnAction(true);
    const data = new FormData();
    data?.append('file', selectedFile.file);
    const res = await AddEquipments(data);

    if (res.status === 200) {
      setTimeout(() => {
        setOnAction(false);
        setShowActions({ showSuccess: true, showError: false });
      }, 8000);
    } else {
      setResponse(res);
      setShowActions({ showSuccess: false, showError: true });
    }
  };

  function handleShowConfirmationModal() {
    setShowAirTableConfirmationModal(!showAirTableConfirmationModal);
  }
  const changeHandler = (event) => {
    const file = event.target.files[0];
    setSelectedFile({
      isSelected: true,
      file,
      fileName: file.name
    });
    const reader = new FileReader();
    reader.onload = (e) => {
      const data = XLSX.read(e.target.result, { type: 'binary' });
      const wsname = data?.SheetNames[0];
      const ws = data?.Sheets[wsname];
      const parsedData = XLSX.utils
        .sheet_to_json(ws, { header: 1 })
        .slice(1, 2);
      setColumns(parsedData[0]);
    };
    reader.readAsBinaryString(file);
  };

  const removeFile = () => {
    setSelectedFile({ isSelected: false, file: null, fileName: '' });
    setColumns(null);
    document.getElementById('formId').value = '';
  };

  const handleCloseSuccessPopUp = () => {
    setCookies('has_inventory', true);
    setShowActions({ showSuccess: false, showError: false });
    navigate('/equipperManagementPortal/equipmentManagement');
  };

  const handleClosePopUp = () => {
    setShowActions({ showSuccess: false, showError: false });
    navigate('/equipperManagementPortal/equipmentManagement');
  };

  async function handleSync() {
    setOnAction(true);
    const res = await AirTableSynchronization(
      userCurrency(getCookies('country'))
    );
    if (res.status === 200) {
      setShowActions({
        showSuccess: true,
        showError: false
      });
    } else {
      setResponse(res);
      setShowActions({
        showSuccess: false,
        showError: true
      });
    }

    setOnAction(false);
  }

  return (
    <>
      <Box margin="30px">
        <div className="row justify-content-center">
          <div className="col-lg-4">
            <div className="file_box">
              <div className="file_box__input d-lg-flex m-2">
                <label
                  htmlFor="formId"
                  className="t-base-small c-black d-flex"
                  style={{ cursor: 'pointer' }}
                >
                  <img src={FileIcon} alt="file icon" />
                  <label
                    htmlFor="formId"
                    className="t-body-large c-titles-color bold text-center mt-4"
                  >
                    <input
                      type="file"
                      accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                      id="formId"
                      hidden
                      onChange={changeHandler}
                    />
                    <div>
                      {selectedFile.isSelected
                        ? selectedFile.fileName
                        : t('Excel_csv_files')}
                      {selectedFile.isSelected && (
                        <span
                          className="c-primary-color m-2"
                          onClick={removeFile}
                        >
                          <svg
                            width="20"
                            height="20"
                            viewBox="0 0 25 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M19.25 5.25L5.75 18.75"
                              stroke="#333333"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <path
                              d="M19.25 18.75L5.75 5.25"
                              stroke="#333333"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </span>
                      )}
                    </div>
                  </label>
                </label>
              </div>
              <CustomButton
                className="round-button yellow bold mt-3"
                textPopper={
                  selectedFile.isSelected
                    ? t('Upload_excel_file_button')
                    : t('Choose_file')
                }
                isLoading={onAction}
                onClick={
                  selectedFile.isSelected
                    ? () => handleShowConfirmationModal()
                    : () => document.getElementById('formId').click()
                }
                disabled={columns && columns.length !== 69}
                textButton={
                  selectedFile.isSelected
                    ? t('Upload_excel_file_button')
                    : t('Choose_file')
                }
              />
              {columns && columns.length !== 69 && (
                <p className="error-message">{t('Invalid_file_upload')}</p>
              )}
            </div>
          </div>
          <div className="col-lg-4">
            <Box
              className="file_box"
              onClick={() =>
                navigate(
                  '/equipperManagementPortal/equipmentManagement/addSingleEquipment'
                )
              }
            >
              <Box className="file_box__input d-lg-flex m-2">
                <FontAwesomeIcon
                  style={{ marginRight: '20px' }}
                  icon={faPlus}
                />
                <div className="bold">{t('Add_single_equipment')}</div>
              </Box>
            </Box>
          </div>
          <RenderIf condition={equipper?.company === 'master.inventory'}>
            <div className="col-lg-4">
              <Box
                className="file_box"
                onClick={() => {
                  handleShowConfirmationModal();
                }}
              >
                <Box className="file_box__input d-lg-flex m-2">
                  <img src={FileIconAirtable} alt="file icon" />

                  <div className="bold margin-left-20">
                    {t('Upload_airtable')}
                  </div>
                </Box>
              </Box>
            </div>
          </RenderIf>
        </div>
      </Box>
      <ConfirmationModal
        show={showAirTableConfirmationModal}
        onClose={handleShowConfirmationModal}
        message={t('Are_you_sure_you_want_to_sync')}
        action={
          equipper?.company === 'master.inventory'
            ? () => handleSync()
            : () => submit()
        }
        isLoading={onAction}
        disabled={onAction}
        buttonText={t('Yes')}
        t={t}
      />
      <Popup
        t={t}
        show={showActions.showError}
        onClose={handleClosePopUp}
        response={response}
        prefix={UPLOAD_EQUIPMENTS}
      />
      <SuccessPopUp
        onClose={handleCloseSuccessPopUp}
        show={showActions.showSuccess}
        message={t('Success_excel_upload')}
      />
    </>
  );
}
