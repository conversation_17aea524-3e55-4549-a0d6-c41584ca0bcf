import React, { useEffect, useState } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { FieldArray } from 'formik';
import MultiSelect from '../../../../shared/components/multi_select/Multi_select.jsx';
import Input from '../../../../shared/components/forms/Input.jsx';
import Button from '@mui/material/Button';
import { attributes } from '../../../../shared/helpers/Data_helper.js';
import { firstLetterUpperCase } from '../../../../shared/helpers/String_helps.js';
import Grid from '@mui/material/Grid';
import PlusIcon from '../../../../style/assets/img/company_spotlight/Plus.svg';

export default function Specifications({ detectLanguage, t, formik }) {
  const options = attributes.map((item) => ({
    label: t(firstLetterUpperCase(item)).replaceAll(':', ''),
    value: item
  }));

  const [descriptionInputName, setDescriptionInputName] = useState('');
  const [isHidden, setIsHidden] = useState('');

  useEffect(() => {
    setDescriptionInputName(
      detectLanguage === 'fr' ? 'description_fr' : 'description'
    );
    formik.setFieldValue(
      'specifications.0.specification',
      detectLanguage === 'fr' ? 'description_fr' : 'description'
    );
    formik.setFieldValue(
      'specifications.0.value',
      detectLanguage === 'fr'
        ? formik.values.description_fr
        : formik.values.description
    );
  }, [detectLanguage]);

  useEffect(() => {
    setIsHidden(!isAnySpecificationOrValueEmpty());
  }, [formik.values]);

  useEffect(() => {
    formik?.values?.specifications?.forEach((e) => {
      formik.setFieldValue(e.specification, e.value);
    });
  }, [formik.values.specifications]);

  const isAnySpecificationOrValueEmpty = () => {
    for (let i = 1; i < formik.values?.specifications?.length; i++) {
      if (
        !formik.values?.specifications[i]?.value ||
        !formik.values?.specifications[i]?.specification
      ) {
        return true;
      }
    }
    return false;
  };

  return (
    <Box>
      <Typography fontFamily="inherit" fontWeight="bold" variant="h6">
        {t('Equipment_specifications')}
      </Typography>
      <Box fontFamily="inherit" color="red" hidden={isHidden}>
        {t('Please_fill_in_fields')}
      </Box>
      <FieldArray
        name="specifications"
        render={(arrayHelpers) => (
          <Box
            gap="35px"
            display="flex"
            flexDirection="column"
            marginTop="30px"
          >
            {formik.values?.specifications?.map((_, index) => (
              <div>
                <Box key={index} display="flex" gap="20px" flexDirection="row">
                  <Grid container spacing={{ xs: 2, md: 3 }}>
                    <Grid item xs={12} sm={6}>
                      <Box paddingTop="9px" className="form-group">
                        <label className="label-input t-body-regular c-fake-black">
                          {t('Specification')}
                        </label>

                        <MultiSelect
                          options={
                            index === 0
                              ? [
                                  {
                                    label: t('Description'),
                                    value: descriptionInputName
                                  }
                                ]
                              : options?.filter(
                                  (item) =>
                                    !formik.values?.specifications
                                      ?.map((item) => item.specification)
                                      .includes(item.value)
                                ) || []
                          }
                          disabled={index === 0}
                          t={t}
                          isGeoLocation
                          name={`specifications.${index}.specification`}
                          handleChange={(item) => {
                            formik.setFieldValue(
                              index === 0
                                ? descriptionInputName
                                : `specifications.${index}.specification`,
                              item?.value
                            );
                            _.specification = item?.value;
                          }}
                          defaultValue={
                            index === 0
                              ? [
                                  {
                                    label: t('Description'),
                                    value: descriptionInputName
                                  }
                                ]
                              : []
                          }
                          value={
                            index === 0
                              ? {
                                  label: t('Description'),
                                  value: descriptionInputName
                                }
                              : options?.filter(
                                  (item) =>
                                    item.value ===
                                    formik.values.specifications[index]
                                      .specification
                                )[0]
                          }
                          placeholder={t('Choose_a_specification')}
                        />
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Box display="flex" flexDirection="row">
                        <div className="form-group width-100">
                          <Input
                            type="text"
                            label={t('Value')}
                            name={`specifications.${index}.value`}
                            className="form-control"
                            placeholder={t('Value')}
                            value={formik.values.specifications[index].value}
                            isNotRequired={index === 0}
                          />
                        </div>
                        {index > 0 && (
                          <button
                            type="button"
                            className="transparent w-auto mt-4 p-2"
                            onClick={() => {
                              formik.setFieldValue(
                                formik.values.specifications[index]
                                  .specification,
                                ''
                              );
                              arrayHelpers.remove(index);
                            }}
                          >
                            <svg
                              width="20"
                              height="20"
                              viewBox="0 0 25 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M19.25 5.25L5.75 18.75"
                                stroke="#333333"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M19.25 18.75L5.75 5.25"
                                stroke="#333333"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </button>
                        )}
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </div>
            ))}

            <Box>
              <div className="button_content m-2">
                <Button
                  style={{
                    fontSize: '17px',
                    fontStyle: 'normal',
                    fontWeight: 700,
                    lineHeight: '30px',
                    textTransform: 'capitalize',
                    background: 'none',
                    marginTop: '16px',
                    marginBottom: '16px',
                    position: 'relative',
                    color: '#ECA869',
                    fontFamily: 'inherit',
                    borderColor: 'unset',
                    border: 'unset'
                  }}
                  startIcon={<img src={PlusIcon} alt="plus icon" />}
                  className="c-yellow"
                  type="button"
                  onClick={() => {
                    arrayHelpers.push({
                      specification: '',
                      value: ''
                    });
                  }}
                >
                  {t('Add_specification')}
                </Button>
              </div>
            </Box>
          </Box>
        )}
      />
    </Box>
  );
}
