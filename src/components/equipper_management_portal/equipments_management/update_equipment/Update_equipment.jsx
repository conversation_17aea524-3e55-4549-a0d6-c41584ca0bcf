import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useEquipment } from '../../../../shared/context/Equipment_context.jsx';
import Box from '@mui/material/Box';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import EquipmentInformation from './Equipment_information.jsx';
import CustomButton from '../../../../shared/components/buttons/Custom_button.jsx';
import StatusPrice from './Status_price.jsx';
import Specifications from './Specifications.jsx';
import { attributes, format } from '../../../../shared/helpers/Data_helper.js';
import SuccessPopUp from '../../../../shared/components/modals/Success_pop_up.jsx';
import Popup from '../../../../shared/components/modals/Popup.jsx';
import { getCookies } from '../../../../shared/helpers/Cookies.js';
import axios from 'axios';
import { EQUIPMENT_UPLOAD_IMAGE } from '../../../../shared/helpers/Url_constants.js';
import { Grid } from '@mui/material';
import ToloIsLoading from '../../../../shared/components/cards/Tolo_is_loading.jsx';

export default function UpdateEquipment({ detectLanguage, t }) {
  const navigate = useNavigate();
  const { equipmentId } = useParams();
  const [equipment, setEquipment] = useState({});
  const [customImage, setCustomImage] = useState({ localImage: '' });
  const [specifications, setSpecifications] = useState([]);
  const { UpdateEquipment } = useEquipment();
  const validationSchema = Yup.object().shape({
    internal_id: Yup.string()
      .required(t('Internal_id_required'))
      .matches(/^(?!.*\|\|\|).*$/, t('Invalid_internal_id')),
    minimum_rental_period: Yup.number()
      .min(0, t('Minimum_rental_period_must_be_greater_than_0'))
      .required(t('This_field_is_required')),
    status: Yup.string().required(t('Status_is_required')),
    price: Yup.object({
      day: Yup.number()
        .required(t('Price_required'))
        .positive(t('Price_positive')),
      week: Yup.number()
        .required(t('Price_required'))
        .positive(t('Price_positive')),
      month: Yup.number()
        .required(t('Price_required'))
        .positive(t('Price_positive'))
    }),
    specifications: Yup.array()
      .of(
        Yup.object().shape({
          specification: Yup.string().test(
            'specification',
            t('Specifications_required'),
            (value, context) => {
              const { path } = context;
              const index = parseInt(path.split('[')[1].split(']')[0]);
              return index === 0 || value !== undefined;
            }
          ),
          value: Yup.string().test(
            'value',
            t('Specifications_value_required'),
            (value, context) => {
              const { path } = context;
              const index = parseInt(path.split('[')[1].split(']')[0]);
              return index === 0 || value !== undefined;
            }
          )
        })
      )
      .test('specifications', 'Specifications are required', (specs) => {
        if (!specs || specs.length === 0) {
          return true;
        }
        return specs.slice(1).every((spec) => spec.specification && spec.value);
      })
  });
  const { GetEquipmentByID } = useEquipment();

  function handleCancel() {
    navigate('/equipperManagementPortal/equipmentManagement');
  }

  const getOneEquipment = async () => {
    const { status, data } = await GetEquipmentByID(equipmentId);
    if (status === 200) {
      setEquipment({
        ...data,
        internal_id: format(data.internal_id)
      });
      setSpecifications([
        {
          specification:
            detectLanguage === 'fr' ? 'description_fr' : 'description',
          value:
            detectLanguage === 'fr' ? data?.description_fr : data?.description
        }
      ]);
      attributes.forEach((item) => {
        let value = data[item];
        // Skip if value is undefined, null, empty string, or array of only empty strings
        if (
          value === undefined ||
          value === null ||
          (Array.isArray(value) && value.filter((v) => v && v.trim() !== '').length === 0) ||
          value === ''
        ) {
          return;
        }
        if (Array.isArray(value)) {
          value = value.join(', ');
        }
        setSpecifications((oldSpecifications) => [
          ...oldSpecifications,
          {
            specification: item,
            value: value
          }
        ]);
      });
      setTimeout(() => {
        setIsLoading(false);
      }, 1000);
    }
  };

  useEffect(async () => {
    await getOneEquipment();
  }, [equipmentId]);

  async function getFileFromBlob(imageLink) {
    const response = await fetch(imageLink);
    const blob = await response?.blob();
    return new File([blob], 'image.jpg', { type: blob.type });
  }

  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showError, setShowError] = useState(false);
  const [serverResponse, setServerResponse] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const onCloseSuccess = () => {
    setShowSuccessModal(false);
    navigate(
      `/equipperManagementPortal/equipmentManagement/equipmentDetails/${equipmentId}`
    );
  };
  const onCloseError = () => {
    setShowError(false);
  };
  const handleSubmit = async (values, actions) => {
    let imageLink = null;
    if (customImage.localImage) {
      imageLink = await getFileFromBlob(customImage.localImage);
    }
    if (equipment.description !== values.description) {
      values.description_fr = '';
    } else if (equipment.description_fr !== values.description_fr) {
      values.description = '';
    }
    
    // Ensure type_of_propulsion is an array
    if (values.type_of_propulsion && typeof values.type_of_propulsion === 'string') {
      values.type_of_propulsion = values.type_of_propulsion
        .split(',')
        .map((v) => v.trim())
        .filter((v) => v.length > 0);
    } else if (!values.type_of_propulsion) {
      values.type_of_propulsion = [];
    }
    
    const res = await UpdateEquipment(values);
    if (res?.status === 200 && res?.data !== null) {
      if (imageLink) {
        await uploadImage(imageLink, values.id);
      }
      setShowSuccessModal(true);
      actions.setSubmitting(false);
    } else {
      setShowError(true);
      setServerResponse(res);
      actions.setSubmitting(false);
    }
  };
  const uploadImage = async (file, equipmetID) => {
    const formatData = new FormData();

    formatData.set('file', file);

    const header = {
      'Content-Type': 'multipart/form-data'
    };
    header.Authorization = `Bearer ${getCookies('token')}`;

    await axios.post(
      `${
        import.meta.env.VITE_REACT_APP_BASE_URL + EQUIPMENT_UPLOAD_IMAGE
      }/${equipmetID}`,
      formatData,
      { headers: header }
    );
  };

  if (isLoading) {
    return <ToloIsLoading />;
  }

  return (
    <Formik
      initialValues={{
        ...equipment,
        specifications: specifications
      }}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {(formik) => (
        <Form className="equipments-management white-bg">
          <Box
            padding="30px 0px"
            marginTop="10px"
            border="1px solid #E5ECF6"
            borderRadius="7px"
            gap="50px"
          >
            <Box padding="20px">
              <Grid container justifyContent="space-between" spacing={4}>
                <Grid item xs={12} sm={5.5}>
                  <EquipmentInformation
                    setCustomImage={setCustomImage}
                    formik={formik}
                    t={t}
                    detectLanguage={detectLanguage}
                  />
                </Grid>
                <Grid item xs={12} sm={5.5}>
                  <StatusPrice formik={formik} t={t} />
                  <br />
                  <br />
                  <Specifications
                    t={t}
                    formik={formik}
                    detectLanguage={detectLanguage}
                    equipment={equipment}
                  />
                </Grid>
              </Grid>
            </Box>
          </Box>
          <Box
            display="flex"
            justifyContent="center"
            gap="30px"
            marginTop="20px"
            flexWrap="wrap"
          >
            <CustomButton
              textButton={t('Cancel')}
              onClick={handleCancel}
              className="round-button transparent black"
            />
            <CustomButton
              textButton={t('Save_changes_text')}
              type="submit"
              variant="contained"
              className="round-button yellow c-black"
              color="primary"
              disabled={!formik.isValid || formik.isSubmitting}
              isLoading={formik.isSubmitting}
            />
          </Box>

          <SuccessPopUp show={showSuccessModal} onClose={onCloseSuccess} />
          <Popup
            show={showError}
            response={serverResponse}
            onClose={onCloseError}
            t={t}
          />
        </Form>
      )}
    </Formik>
  );
}
