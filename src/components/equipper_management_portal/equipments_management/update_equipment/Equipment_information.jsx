import React, { useRef } from 'react';
import Box from '@mui/material/Box';
import CustomButton from '../../../../shared/components/buttons/Custom_button.jsx';
import makeStyles from '@mui/styles/makeStyles';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import Chip from '@mui/material/Chip';
import Input from '../../../../shared/components/forms/Input.jsx';
import Typography from '@mui/material/Typography';
import RenderIf from '../../../../shared/components/Render_if.jsx';

const useStyles = makeStyles({
  root: {
    backgroundColor: '#e9ecef',
    minHeight: '48px',
    borderRadius: '5px',
    borderColor: '#e9ecef',
    '& .MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline': {
      borderColor: '#e9ecef !important',
      minHeight: '48px !important'
    },
    '& .MuiChip-label': {
      color: 'black !important',
      fontFamily: 'airbnb-cereal-medium'
    }
  }
});

export default function EquipmentInformation({
  detectLanguage,
  t,
  formik,
  setCustomImage
}) {
  const classes = useStyles();
  const hiddenFileInput = useRef(null);
  const onImageChange = (event) => {
    if (event.target.files && event.target.files[0]) {
      formik.setFieldValue(
        'equipper_equipment_picture',
        URL.createObjectURL(event.target.files[0])
      );
      setCustomImage({
        localImage: URL.createObjectURL(event.target.files[0])
      });
    }
  };
  const handleClick = (event) => {
    event.preventDefault();
    event.stopPropagation();
    hiddenFileInput.current.click();
  };
  const isHidden =
    ((detectLanguage === 'en' && formik?.values?.name !== '') ||
      (detectLanguage === 'fr' && formik?.values?.name_fr !== '') ||
      (detectLanguage === 'ar' && formik?.values?.name !== '')) &&
    formik?.values?.internal_id !== '';

  return (
    <>
      <Typography fontFamily="inherit" fontWeight="bold" variant="h6">
        {t('Equipment_information')}
      </Typography>
      <Box fontFamily="inherit" color="red" hidden={isHidden}>
        {t('Please_fill_in_fields')}
      </Box>

      <Box className="mt-2">
        <Box
          display="flex"
          flexDirection="row"
          className="d-flex flex-lg-row flex-column mt-4  mb-4"
        >
          <Box
            component="img"
            marginRight="20px"
            width="168px"
            height="168px"
            alt="Equipment image"
            src={
              formik.values.equipper_equipment_picture &&
              !formik.values.equipper_equipment_picture.includes(
                'equipment_library/Empty_state_equipment.png'
              )
                ? formik.values.equipper_equipment_picture
                : formik.values.image_link
            }
          />
          <Box display="flex" flexDirection="column">
            <Typography fontFamily="inherit" fontWeight="bold" variant="h6">
              {t('Equipment_picture')}
            </Typography>
            <Typography fontFamily="inherit" variant="h10">
              {t('Image_equipment_update')}
            </Typography>
            <CustomButton
              textButton={t('Upload_image')}
              type="file"
              variant="contained"
              className="round-button yellow c-black"
              color="primary"
              onClick={handleClick}
            />
            <input
              type="file"
              onChange={onImageChange}
              accept=".png, .jpg, .jpeg, .bmp, .tiff"
              ref={hiddenFileInput}
              style={{ display: 'none' }}
            />
          </Box>
        </Box>
        <br />
        <div className="form-group mt-2">
          <Input
            type="text"
            label={t('Derental_equipment_name')}
            disabled
            name={detectLanguage === 'fr' ? 'name_fr' : 'name'}
            className="form-control"
          />
        </div>
        <br />
        <div className="form-group mt-2">
          <Input
            type="text"
            label={t('Preferred_equipment_name')}
            name="preferred_equipment_name"
            className="form-control"
            isNotRequired
          />
        </div>
        <br />
        <div className="form-group mt-2">
          <Input
            type="text"
            label={t('Equipment_id')}
            name="internal_id"
            className="form-control"
          />
        </div>
        <br />
        <div className="form-group mt-2">
          <Input
            type="number"
            label={t('Minimum_rental_period')}
            name="minimum_rental_period"
            className="form-control"
            isNotRequired
          />
        </div>
        <br />

        <div className="form-group  mt-2">
          <label className="label-input d-lg-block">
            {t('Category')} <span className="c-red star-required">*</span>
          </label>
          <Autocomplete
            variant="standard"
            name="category"
            clearIcon={false}
            options={[]}
            value={formik.values.category || []}
            freeSolo
            className={classes.root}
            readOnly
            multiple
            renderTags={(value, props) =>
              value.map((option, index) => (
                <Chip label={option} {...props({ index })} />
              ))
            }
            renderInput={(params) => <TextField {...params} />}
          />
        </div>
        <br />
        <div className="form-group  mt-2">
          <label className="label-input d-lg-block">
            {t('Sub_category')} <span className="c-red star-required">*</span>
          </label>
          <Autocomplete
            variant="standard"
            name="sub_category"
            clearIcon={false}
            options={[]}
            value={formik.values.sub_category || []}
            freeSolo
            className={classes.root}
            readOnly
            multiple
            renderTags={(value, props) =>
              value.map((option, index) => (
                <Chip label={option} {...props({ index })} />
              ))
            }
            renderInput={(params) => <TextField {...params} />}
          />
        </div>
        <br />
        <RenderIf condition={detectLanguage === 'fr' && formik.values.alias.fr}>
          <Autocomplete
            variant="standard"
            name="alias.en"
            clearIcon={false}
            options={[]}
            value={formik.values.alias.fr || []}
            freeSolo
            className={classes.root}
            readOnly
            multiple
            renderTags={(value, props) =>
              value.map((option, index) => (
                <Chip label={option} {...props({ index })} />
              ))
            }
            renderInput={(params) => <TextField {...params} />}
          />
        </RenderIf>
      </Box>
    </>
  );
}
