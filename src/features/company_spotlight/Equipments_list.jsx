import { connectInfiniteHits, connectStats } from 'react-instantsearch-dom';
import ViewMore from '../../shared/components/buttons/View_more';
import CompanySpotlightEquipmentItem from '../../shared/components/equipment/Company_spotlight_equipment_Item';
import { useEffect, useState } from 'react';
import { usePromotions } from '../../shared/context/Promotion_context';
import { getCookies } from '../../shared/helpers/Cookies';
import ToloIsLoading from '../../shared/components/cards/Tolo_is_loading';
import { isEmpty } from 'lodash';

const EquipmentsList = ({
  hitsPerPage,
  setHitsPerPage,
  setSelectedEquipment,
  setIsOpen,
  setShowSignIn,
  detectLanguage,
  hits,
  t
}) => {
  const [promotion, setPromotion] = useState(0);
  const Stats = ({ nbHits, t }) => (
    <h3 className="t-subheading-2 c-fake-black  m-2 p-2">
      {nbHits} {t('Equipment_found')}{' '}
    </h3>
  );

  const { GetPromotionByEquipperAndLodgerID } = usePromotions();
  const role = getCookies('role');

  useEffect(async () => {
    if (role === 'lodger' && hits[0]?.equipper_id) {
      const res = await GetPromotionByEquipperAndLodgerID(hits[0]?.equipper_id);
      if (res.status === 200 && res.data) {
        setPromotion(res.data);
      }
    }
  }, [hits[0]?.equipper_id, role]);
  const CustomStats = connectStats(Stats);
  if (isEmpty(hits)) {
    return <ToloIsLoading />;
  }
  return (
    <div className="row">
      <div className="col-large-10 col-12 mx-auto">
        <div className="row company-spotlight--equipement result-search">
          <CustomStats t={t} />
          {hits &&
            hits
              ?.slice(0, hitsPerPage)
              ?.map((item, index) => (
                <CompanySpotlightEquipmentItem
                  item={item}
                  promotion={promotion}
                  selectedEquipment={setSelectedEquipment}
                  detectLanguage={detectLanguage}
                  isOpen={setIsOpen}
                  key={index}
                  t={t}
                  setShowSignIn={setShowSignIn}
                />
              ))}
          {hitsPerPage < hits.length ? (
            <ViewMore onClick={() => setHitsPerPage(hitsPerPage + 3)} />
          ) : (
            hits.length > 5 && (
              <ViewMore
                onClick={() => setHitsPerPage(5)}
                text={t('View_less')}
              />
            )
          )}
        </div>
      </div>
    </div>
  );
};

export const CustomInfiteHitsEquipments = connectInfiniteHits(EquipmentsList);
