import React, { useState, useEffect, Suspense } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import '../../style/equipper/equiper_management_portal.scss';
import PersonalInfo from '../../components/lodger_management_portal/personal_info/Personal_info';
import BreadCrumb from '../../shared/components/Bread_crumb';
import ScrollToTop from '../../shared/components/Scroll_to_top';
import Tab from '../../shared/components/tab/Tab';
import { useLodger } from '../../shared/context/Lodger_context';
import ToloIsLoading from '../../shared/components/cards/Tolo_is_loading';
import SharedProvider from '../../shared/context/Shared_provider';
import { clearCookies, setCookies } from '../../shared/helpers/Cookies';
import {
  getSessionStorage,
  setSessionStorage
} from '../../shared/helpers/Session_storage_helper';
import { useTeamContext } from '../../shared/context/Team_context';
import { getConnectedUser } from '../../shared/helpers/Team_helper';

export default function LodgerManagementPortal({ t }) {
  const { GetTeamLodger } = useTeamContext();
  const navigate = useNavigate();
  const history = useLocation();
  const { GetLodgerPersonalInfo } = useLodger();
  const [team, setTeam] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [loadedInfo, setLoadedInfo] = useState(null);
  const optionsLodgerTab = [
    {
      label: 'Requests_management_title',
      tab: 'REQUEST_MANAGEMENT',
      router: '/renterManagementPortal',
      show: true
    },

    {
      label: 'Manage_projects_title',
      tab: 'MANAGE_PROJECTS',
      router: '/renterManagementPortal/projectManagement',
      show: getSessionStorage('type') === 'pro'
    },
    {
      label: 'Team_management_details_title',
      tab: 'MANAGE_MEMBERS',
      router: '/renterManagementPortal/teamManagement',
      show: getSessionStorage('type') === 'pro'
    },
    {
      label: 'Rentals_summary_title',
      router: '/renterManagementPortal/rentalsSummary',
      tab: 'RENTALS',
      show: true
    },
    {
      label: 'Credit_check_form',
      tab: 'CREDIT_CHECK_FORM',
      router: '/renterManagementPortal/creditCheckForm',
      show: true
    },
    {
      label: 'Statistics_title',
      router: '/renterManagementPortal/analytics',
      tab: 'STATISTICS',
      show: true
    },
    {
      label: 'Comments_rating_title',
      router: '/renterManagementPortal/commentsAndRatings',
      tab: 'COMMENTS_AND_RATINGS',
      show: true
    },
    {
      label: 'Bidz_management_title',
      tab: 'BIDZ_MANAGEMENT',
      router: '/renterManagementPortal/bidzManagement',
      show: true
    }
  ];

  const selectedTab = !history.pathname.includes('underConstruction')
    ? t(
        optionsLodgerTab.find((item) => item.router === history.pathname)?.label
      )
    : t('Under_construction_tab');

  useEffect(() => {
    (async function handleDisabledActions() {
      const tempUser = getConnectedUser(team);
      setCookies('member', tempUser);
    })();
  }, [team]);

  useEffect(() => {
    (async () => {
      const res = await GetLodgerPersonalInfo();
      setIsLoading(true);
      if (res.data === '') {
        clearCookies();
        navigate('/');
        window.location.reload();
      } else {
        setSessionStorage('type', res.data?.type);
        setCookies('imageLink', res?.data.photo_url);
        setCookies('lodgerCountry', res.data?.address.country);
        setSessionStorage('member_id', res.data?.member_id);

        if (res.data?.member_of && res.data?.member_of !== '') {
          setSessionStorage('member_of', res.data?.member_of);
        }

        setLoadedInfo(res.data);
      }
      const response = await GetTeamLodger();
      if (response.status === 200 && response.data !== null) {
        setTeam(response.data);
      } else {
        setTeam({
          admins: [
            {
              ...loadedInfo,
              membership_status: 'owner',
              privilege_level: 10
            }
          ]
        });
      }
      setIsLoading(false);
    })();
  }, []);

  return (
    <Suspense fallback={<ToloIsLoading />}>
      <div className="management">
        <ScrollToTop>
          <BreadCrumb
            t={t}
            items={[
              {
                label: selectedTab,
                show: true
              }
            ]}
          />
          <SharedProvider>
            <PersonalInfo loadedInfo={loadedInfo} isLoading={isLoading} t={t} />
            <div className="container tabulation">
              <Tab optionsTab={optionsLodgerTab} t={t} />
              <Outlet context={[loadedInfo]} />
            </div>
          </SharedProvider>
        </ScrollToTop>
      </div>
    </Suspense>
  );
}
