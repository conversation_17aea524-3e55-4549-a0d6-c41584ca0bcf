import image_banner from '../../style/assets/img/our_story/Isolation_Mode.png';
import icon_mission from '../../style/assets/img/our_story/target.svg';
import icon_vision from '../../style/assets/img/our_story/telescope.svg';
import image_nab from '../../style/assets/img/our_story/nab.jpg';
import image_nab_laarif from '../../style/assets/img/our_story/nab-laarif.jpg';
import image_joerg from '../../style/assets/img/our_story/joerg.jpg';
import image_neil from '../../style/assets/img/our_story/nell.jpg';
import image_reggie from '../../style/assets/img/our_story/reggie.jpeg';
import image_donald from '../../style/assets/img/our_story/donald.jpeg';
import image_eliott from '../../style/assets/img/our_story/eliot.jpeg';
import logo_founder from '../../style/assets/img/our_story/founder.svg';
import logo_digihub from '../../style/assets/img/our_story/digihub.svg';
import logo_d3 from '../../style/assets/img/our_story/d3.svg';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLinkedinIn } from '@fortawesome/free-brands-svg-icons';
import React from 'react';
import Slider3D from '../../shared/components/3D_slider/3D_slider';
import { carouselItems } from '../../shared/helpers/Data_helper';

export default function OurStory({ t }) {
  const foundingTeam = [
    {
      key: 1,
      name: 'Nab Mansouri',
      position: 'CEO',
      src: image_nab,
      link: 'https://www.linkedin.com/in/nab-mansouri/'
    },
    {
      key: 2,
      name: 'Nabil Laarif',
      position: 'CTO',
      src: image_nab_laarif,
      link: 'https://www.linkedin.com/in/laarif-nabil/'
    }
  ];
  const advisoryTeam = [
    {
      key: 1,
      name: 'Joerg Storm',
      src: image_joerg,
      link: 'https://linkedin.com/in/joergstorm/'
    },
    {
      key: 2,
      name: 'Neil Cohen',
      src: image_neil,
      link: 'https://linkedin.com/in/neilcohen/'
    },
    {
      key: 3,
      name: 'Donald Charbonnet',
      src: image_donald,
      link: 'https://www.linkedin.com/in/donald-charbonnet/'
    },
    {
      key: 4,
      name: 'Elliott Vigil',
      src: image_eliott,
      link: 'https://www.linkedin.com/in/elliottvigil/'
    },
    {
      key: 5,
      name: 'Reggie Gray',
      src: image_reggie,
      link: 'https://www.linkedin.com/in/reggie-gray/'
    }
  ];

  return (
    <div className="our_story">
      <section className="our_story__banner">
        <div className="container">
          <div className="row">
            <div className="col-lg-8">
              <div className="d-flex flex-column justify-content-center full-height">
                <h1 className="t-header-h3 c-yellow mb-4">
                  {t('Revolutionizing')}
                  <br />
                  {t('tools_heavy_equipment_rental_process')}
                </h1>
                <p className="t-subheading-1 c-white">
                  {t('Through_the_tools_that_we_build')}
                </p>
              </div>
            </div>
            <div className="col-lg-4">
              <img src={image_banner} alt="banner" />
            </div>
          </div>
        </div>
      </section>
      <section className="our_story__presentation">
        <div className="container">
          <div className="row">
            <div className="col-lg-7 mx-auto xxl-flex">
              <div className="our_story__presentation_row">
                <div className="marge-row d-flex align-items-center">
                  <img src={icon_mission} alt="mission" />
                  <div className="sep"></div>
                  <div className="content">
                    <h2 className="t-header-h2 c-blue-grey  mb-2">
                      {t('Mission')}
                    </h2>
                    <p className="t-body-regular c-blue-grey ">
                      {t('Mission_description')}
                    </p>
                  </div>
                </div>
              </div>

              <div className="our_story__presentation_row">
                <div className="marge-row d-flex align-items-center">
                  <img src={icon_vision} alt="vision" />
                  <div className="sep"></div>
                  <div className="content">
                    <h2 className="t-header-h2 c-blue-grey mb-2">
                      {t('Vision')}
                    </h2>
                    <p className="t-body-regular c-blue-grey ">
                      {t('Vision_description')}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="our_story__team">
        <div className="container">
          <div className="row">
            <div className="col-lg-11 mx-auto">
              <div className="text-center margin-bottom-30">
                <h2 className="t-header-h2 c-fake-black mb-lg-4 mb-2">
                  {t('Founding_team')}
                </h2>
                <p className="t-body-regular c-blue-grey text-justify">
                  {t('Founding_team_description')}
                </p>
              </div>
              <div className="our_story__team_container">
                <div className="row d-flex flex-lg-wrap flex-nowrap offset-3 w-100">
                  {foundingTeam.map((member) => (
                    <div className="col-lg-3 card-width-mobile ">
                      <div className="our_story__team_card text-center">
                        <img
                          src={member.src}
                          alt="team_nab"
                          className="image_card"
                        />
                        <h2 className="t-subheading-2 c-fake-black mb-3">
                          {member.name}
                        </h2>
                        <p className="t-body-small c-blue-grey mb-4">
                          {member.position}
                        </p>
                        <a
                          href={member.link}
                          target="_blank"
                          rel="noreferrer"
                          className="socials"
                        >
                          <FontAwesomeIcon
                            icon={faLinkedinIn}
                            color="c-white"
                          />
                        </a>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="our_story__team">
        <div className="container">
          <div className="row">
            <div className="col-lg-12 mx-auto">
              <div className="text-center margin-bottom-30">
                <h2 className="t-header-h2 c-fake-black mb-lg-4 mb-2">
                  {t('Advisory_board')}
                </h2>
              </div>
              <div className="our_story__team_container after">
                <div className="row d-flex flex-lg-wrap flex-nowrap justify-content-center">
                  {advisoryTeam.map((member) => (
                    <div className="card-advisor card-width-mobile">
                      <div className="our_story__team_card text-center">
                        <img
                          src={member.src}
                          alt="team_nab"
                          className="image_card"
                        />
                        <h2 className="t-subheading-2 c-fake-black mb-3">
                          {member.name}
                        </h2>
                        <p className="t-body-small c-blue-grey mb-4">
                          {member.position}
                        </p>
                        <a
                          href={member.link}
                          target="_blank"
                          rel="noreferrer"
                          className="socials"
                        >
                          <FontAwesomeIcon
                            icon={faLinkedinIn}
                            color="c-white"
                          />
                        </a>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="our_story__backed_by">
        <div className="container">
          <h2 className="t-header-h2 c-fake-black text-center">
            {t('Backed_by')}
          </h2>
          <div className="row justify-content-center text-center">
            <div className="col-lg-4 col-md-2 mb-logos">
              <img src={logo_founder} alt="Founder institute" />
            </div>
            <div className="col-lg-4 col-md-2 mb-logos">
              <img src={logo_digihub} alt="Digihub Shawinigan" />
            </div>
            <div className="col-lg-4 col-md-2 mb-logos">
              <img src={logo_d3} alt="District 3" />
            </div>
          </div>
        </div>
      </section>
      <div className="container-categories mb-4">
        <div className="container">
          <div className="container-card horizontal-scroll-wrapper squares mt-5 rental_centers swiper_after">
            <div className="title_home bordred_title">
              <h2 className="t-header-h2 c-fake-black mb-1">
                {t('Partnering_with')}
              </h2>
            </div>
            <Slider3D carouselItems={carouselItems} />
          </div>
        </div>
      </div>
      <div className="reach_out d-lg-flex align-items-center justify-content-center text-lg-start text-center mt-4">
        <h2 className="c-fake-black t-header-h3">{t('Reach_out')}</h2>
        <button className="round-button yellow hover_black c-black bold m-4">
          <a href="mailto:<EMAIL>" className="c-white">
            {t('Reach_out_button')}
          </a>
        </button>
      </div>
    </div>
  );
}
