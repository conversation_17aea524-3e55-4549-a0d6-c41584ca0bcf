import React, { useEffect, useState } from 'react';
import { Configure, Menu } from 'react-instantsearch-dom';
import FilterByCategorySubcategory from '../../components/algolia/Filterby_category_subcategory';
import { CustomInventoryInfiniteHits } from '../../components/search_result/Custom_inventory_infinite_hits';
import InstantSearchAlgolia from '../../components/search_result/Instant_search_algolia';
import BreadCrumb from '../../shared/components/Bread_crumb';
import SpotlightHead from '../../shared/components/cards/Spotlight_head';
import Filter from '../../shared/components/modals/Filter_modal';
import RenderIf from '../../shared/components/Render_if';
import ScrollToTop from '../../shared/components/Scroll_to_top';
import {
  getSessionStorage,
  setSessionStorage
} from '../../shared/helpers/Session_storage_helper';
import { firstLetterUpperCase } from '../../shared/helpers/String_helps';
import useWindowDimensions from '../../shared/hooks/Screen_size';

export default function AvailableInventory({
  detectLanguage,
  ratingData,
  t,
  setShowFPModal,
  showFPModal,
  signIn,
  isEquipper,
  data,
  equipper,
  isEquipperSpotlight
}) {
  const [searchState, setSearchState] = useState({
    query: '',
    category: '',
    sub_category: '',
    status: '',
    nbHits: ''
  });

  const category = getSessionStorage('category') || '';
  const subCategory = getSessionStorage('sub_category') || '';
  const startDate = getSessionStorage('start_date') || '';

  const [switchPlace, setSwitchPlace] = useState(window.innerWidth < 992);
  const [indexName, setIndexName] = useState(
    import.meta.env.VITE_ALGOLIA_INDEX_NAME_STANDARDS
  );
  const [objectIDs, setObjectIDs] = useState([]);
  useWindowDimensions({ functions: handleSwitchPlace, dimension: 992 });

  function handleStateSwitch(searchState) {
    setSearchState(searchState);
  }

  function handleSwitchPlace(prop) {
    setSwitchPlace(prop);
  }
  useEffect(() => {
    if (category) {
      setSearchState({
        menu: {
          category: category || '',
          sub_category: subCategory || '',
          status: 'available',
          ...data
        }
      });
    } else {
      setSearchState({
        menu: {
          category: '',
          sub_category: subCategory || '',
          status: 'available',
          ...data
        },
        range: {
          available_from: {
            max: new Date(parseInt(startDate)) || new Date().getTime()
          }
        }
      });
    }
  }, [category, data, subCategory, startDate]);

  useEffect(() => {
    if (isEquipper) {
      setIndexName(import.meta.env.VITE_ALGOLIA_INDEX_NAME);
    } else {
      setIndexName(import.meta.env.VITE_ALGOLIA_INDEX_NAME_STANDARDS);
    }
    return () => {
      setSessionStorage('category', '');
      setSessionStorage('sub_category', '');
    };
  }, [data, isEquipper]);

  return (
    <ScrollToTop>
      <InstantSearchAlgolia
        searchState={searchState}
        onSearchStateChange={(searchState) => {
          handleStateSwitch(searchState);
        }}
        indexName={indexName}
      >
        {isEquipper && (
          <>
            <Menu className="hidden" attribute="equipper_id" />
            <Menu className="hidden" attribute="available_from" />
            <Menu className="hidden" attribute="is_active" />
          </>
        )}
        <Configure
          hitsPerPage={18}
          filters={
            isEquipper &&
            'is_active:true AND (status:available OR status:booked)'
          }
        />

        <div className="company-spotlight">
          <div
            className={
              isEquipperSpotlight
                ? 'equipper-spotlight equipper-spotlight-p-mob'
                : 'container-fluid padding-fluid'
            }
          >
            <div className="company-spotlight--similarProduct">
              <RenderIf condition={isEquipperSpotlight}>
                <SpotlightHead
                  equipper={equipper}
                  t={t}
                  ratingData={ratingData}
                />
              </RenderIf>
              <div className="row">
                {!switchPlace ? (
                  <FilterByCategorySubcategory
                    t={t}
                    isAvailableInventory
                    isEquipperSpotlight
                    setObjectIDs={setObjectIDs}
                  />
                ) : (
                  <div className="border-b-mobile">
                    <Filter
                      t={t}
                      isAvailableInventory
                      setObjectIDs={setObjectIDs}
                    />
                  </div>
                )}
                <div
                  className={
                    isEquipperSpotlight
                      ? 'col-lg-9 equipper-spotlight-cls width-lg-screen'
                      : 'col-lg-9 available_inventory_right'
                  }
                >
                  {!switchPlace && !isEquipper && (
                    <BreadCrumb
                      t={t}
                      items={[
                        {
                          label: t('Available_inventory')
                        },
                        {
                          label: t(
                            firstLetterUpperCase(category?.replaceAll(' ', '_'))
                          )
                        }
                      ]}
                    />
                  )}

                  <CustomInventoryInfiniteHits
                    detectLanguage={detectLanguage}
                    t={t}
                    isEquipperSpotlight={isEquipperSpotlight}
                    objectIDs={objectIDs}
                    isEquipper={isEquipper}
                    currency={equipper?.currency}
                    setShowFPModal={setShowFPModal}
                    showFPModal={showFPModal}
                    signIn={signIn}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </InstantSearchAlgolia>
    </ScrollToTop>
  );
}
