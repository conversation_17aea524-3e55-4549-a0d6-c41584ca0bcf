import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import ScrollToTop from '../../shared/components/Scroll_to_top';
import SignUpForm from '../../shared/components/forms/SignUp_form';
import SuccessPopUp from '../../shared/components/modals/Success_pop_up';
import { useAuth } from '../../shared/context/Auth_context';
import { userCurrency } from '../../shared/helpers/Currency';

export default function SignupEquipper({ showModal, t }) {
  const { SignUp } = useAuth();
  const navigate = useNavigate();
  const [error, setError] = useState(false);
  const [show, setShow] = useState(false);
  const [onAction, setOnAction] = useState('');

  const navigateToLogin = () => {
    showModal();
    setShow(false);
    navigate('/');
  };

  const handleSubmit = async (event) => {
    setOnAction(true);
    
    const storage = localStorage.getItem('countries');
    const countries = JSON.parse(storage);
    const country = countries.find(
      (item) => item.value === event.country?.value
    );

    const equipper = {
      ...event,
      country: event.country?.value || '',
      country_code: country?.data?.country_code || '',
      city: event.city?.value || '',
      categories: event.categories || [],
      coverage_area: event.coverage_area || [],
      state: event.state?.value || '',
      currency: userCurrency(event.country?.value) || 'usd',
      communication_preferences: 'english'
    };

    const { error } = await SignUp(equipper);

    if (error) {
      setError(`SignUp_${error.error.replaceAll(' ', '_')?.slice(0, 25)}`);
      setOnAction(false);
    } else {
      setError('');
      setShow(true);
      setOnAction(false);
    }
  };

  return (
    <ScrollToTop>
      <div className="container signup-equipper">
        <div className="signup-equipper__head">
          <SuccessPopUp
            onClose={() => {
              setShow(false);
            }}
            show={show}
            message={t('Signup_success')}
            buttonText={t('Login_button')}
            function={navigateToLogin}
          />
          <div className="row">
            <div className="col-lg-7">
              <h1 className="t-header-h5 c-fake-black">
                {t('EMP_welcome_text_equipper')}
              </h1>
              <p className="t--body-large c-fake-black">
                {t('EMP_signup_text')}
              </p>
            </div>
          </div>
        </div>
        <div className="signup-equipper-content">
          <div className="row">
            <SignUpForm
              handleSubmit={handleSubmit}
              error={error}
              onAction={onAction}
              t={t}
              isEquipper
            />
          </div>
        </div>
      </div>
    </ScrollToTop>
  );
}
