import React, { useState } from 'react';
import { Formik, Field, Form, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, Container, Row, Col, Card } from 'react-bootstrap';
import axios from 'axios';
import { setCookies } from '../../shared/helpers/Cookies';

const validationSchema = Yup.object({
  email: Yup.string()
    .email('Invalid email format')
    .matches(/@derentalequipment\.com$/, 'Email must be from @derentalequipment.com domain')
    .required('Email is required'),
  password: Yup.string()
    .min(6, 'Password must be at least 6 characters')
    .required('Password is required')
});

export default function AdminLogin() {
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (values) => {
    setIsLoading(true);
    setError('');

    try {
      const response = await axios.post(`${import.meta.env.VITE_REACT_APP_BASE_URL}/admin/signin`, {
        email: values.email,
        password: values.password
      });

      if (response.status === 200) {
        const { idToken, email, localId } = response.data;
        
        // Store admin authentication data
        setCookies('adminToken', idToken);
        setCookies('userType', 'admin');
        setCookies('email', email);
        setCookies('userId', localId);

        // Redirect to admin dashboard
        navigate('/admin/dashboard');
      }
    } catch (err) {
      if (err.response?.status === 403) {
        setError('Access restricted to @derentalequipment.com domain');
      } else if (err.response?.data?.error) {
        setError(err.response.data.error);
      } else {
        setError('Authentication failed. Please check your credentials.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Container fluid className="admin-login-container">
      <Row className="justify-content-center align-items-center min-vh-100">
        <Col md={6} lg={4}>
          <Card className="shadow">
            <Card.Body className="p-5">
              <div className="text-center mb-4">
                <h2 className="h3 mb-3">Admin Portal</h2>
                <p className="text-muted">
                  Access restricted to @derentalequipment.com domain
                </p>
              </div>

              {error && (
                <Alert variant="danger" className="mb-4">
                  {error}
                </Alert>
              )}

              <Formik
                initialValues={{ email: '', password: '' }}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
              >
                {({ isSubmitting }) => (
                  <Form>
                    <div className="mb-3">
                      <label htmlFor="email" className="form-label">
                        Email Address
                      </label>
                      <Field
                        type="email"
                        name="email"
                        className="form-control"
                        placeholder="<EMAIL>"
                        disabled={isLoading}
                      />
                      <ErrorMessage
                        name="email"
                        component="div"
                        className="text-danger small mt-1"
                      />
                    </div>

                    <div className="mb-4">
                      <label htmlFor="password" className="form-label">
                        Password
                      </label>
                      <Field
                        type="password"
                        name="password"
                        className="form-control"
                        placeholder="Enter your password"
                        disabled={isLoading}
                      />
                      <ErrorMessage
                        name="password"
                        component="div"
                        className="text-danger small mt-1"
                      />
                    </div>

                    <button
                      type="submit"
                      className="btn btn-primary w-100"
                      disabled={isLoading || isSubmitting}
                    >
                      {isLoading ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          Signing in...
                        </>
                      ) : (
                        'Sign In'
                      )}
                    </button>
                  </Form>
                )}
              </Formik>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
}
