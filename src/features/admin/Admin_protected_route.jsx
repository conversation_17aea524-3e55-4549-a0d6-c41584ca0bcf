import React, { useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';
import { getCookies } from '../../shared/helpers/Cookies';

export default function AdminProtectedRoute({ children }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuth = () => {
      const token = getCookies('adminToken');
      const userType = getCookies('userType');
      const email = getCookies('email');

      // Check if user is authenticated as admin and has @derentalequipment.com domain
      if (token && userType === 'admin' && email && email.endsWith('@derentalequipment.com')) {
        setIsAuthenticated(true);
      } else {
        setIsAuthenticated(false);
      }
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '100vh' }}>
        <div className="spinner-border" role="status">
          <span className="sr-only">Loading...</span>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/admin/login" replace />;
  }

  return children;
}
