import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import AdminLogin from './Admin_login';
import AdminDashboard from './Admin_dashboard';
import AdminProtectedRoute from './Admin_protected_route';

export default function AdminRoutes() {
  return (
    <Routes>
      <Route path="/login" element={<AdminLogin />} />
      <Route
        path="/dashboard"
        element={
          <AdminProtectedRoute>
            <AdminDashboard />
          </AdminProtectedRoute>
        }
      />
      <Route path="/" element={<Navigate to="/admin/dashboard" replace />} />
    </Routes>
  );
}
