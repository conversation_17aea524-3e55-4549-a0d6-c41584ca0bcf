import React from 'react';
import BidzProvider from '../../shared/context/Bidz_context';
import CreditCheckFormProvider from '../../shared/context/Credit_check_form_context';
import LodgerProvider from '../../shared/context/Lodger_context';
import InventoryProvider from '../../shared/context/Tooler_bidz_inventory_management_context';
import BidzCompanySpotlight from '../bidz_company_spotlight/Bidz_company_spotlight';
import SearchProvider from '../../shared/context/Search_context';

export default function BidzSearchResult({
  t,
  detectLanguage,
  setShowFPModal,
  showFPModal,
  signIn
}) {
  return (
    <SearchProvider>
    <CreditCheckFormProvider>
      <LodgerProvider>
        <BidzProvider>
          <InventoryProvider>
            <BidzCompanySpotlight
              t={t}
              detectLanguage={detectLanguage}
              setShowFPModal={setShowFPModal}
              showFPModal={showFPModal}
              signIn={signIn}
            />
          </InventoryProvider>
        </BidzProvider>
      </LodgerProvider>
    </CreditCheckFormProvider>
    </SearchProvider>
  );
}
