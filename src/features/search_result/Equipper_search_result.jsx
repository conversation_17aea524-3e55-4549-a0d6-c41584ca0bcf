import React, { useState } from 'react';
import EquipperItem from '../../components/search_result/Equipper_item';
import BreadCrumb from '../../shared/components/Bread_crumb';
import RenderIf from '../../shared/components/Render_if';
import AdvancedFilterMobile from '../../shared/components/filter/Advanced_filter_mobile';
import { CustomAdvancedFilters } from '../../shared/components/filter/Advanced_filter';
import { EquipmentCardSearchResults } from '../../shared/components/equipment/Equipment_card_search_results';
import { getSessionStorage } from '../../shared/helpers/Session_storage_helper';
import { connectInfiniteHits } from 'react-instantsearch-dom';
import { groupByKey } from '../../shared/helpers/Array_helpers';
import ToloIsLoading from '../../shared/components/cards/Tolo_is_loading';
import { isEmpty } from 'lodash';
import { CustomCurrentRefinements } from '../../components/search_result/Custom_current_refinement';

function EquipperSearchResult({
  hits,
  nameFr,
  nameEn,
  objectIDSetter,
  recommendationObjectID,
  setChangedSearchState,
  detectLanguage,
  switchPlace,
  changedSearchState,
  t
}) {
  const [hitsPerPage, setHitsPerPage] = useState(12);
  const [showAdvancedFilter, setShowAdvancedFilter] = useState(false);

  const equippersList = groupByKey(
    hits,
    (hit) => hit.equipper_id,
    objectIDSetter
  );

  const handleShowAdvancedFilter = () => {
    setShowAdvancedFilter(!showAdvancedFilter);
  };

  if (isEmpty(equippersList) || isEmpty(hits)) {
    return <ToloIsLoading />;
  }

  return (
    <div className="search-result margin-top--20">
      <div className="container-fluid padding-r-fluid">
        <div className="row">
          <RenderIf condition={switchPlace}>
            <BreadCrumb
              t={t}
              items={[
                {
                  label:
                    getSessionStorage('requestedName') ||
                    detectLanguage === 'fr'
                      ? nameFr
                      : nameEn
                }
              ]}
            />
            <div className="col-xl-4">
              <AdvancedFilterMobile
                detectLanguage={detectLanguage}
                handleShowAdvancedFilter={handleShowAdvancedFilter}
                nameEn={nameEn}
                nameFr={nameFr}
                showAdvancedFilter={showAdvancedFilter}
                t={t}
                setChangedSearchState={setChangedSearchState}
              />
            </div>
          </RenderIf>
          <div className="col-xl-3 padding-r-0">
            <div className="advanced-filter">
              <EquipmentCardSearchResults
                changedSearchState={changedSearchState}
                img={equippersList[0].equipment_list[0].image_link}
                detectLanguage={detectLanguage}
                nameEn={nameEn}
                nameFr={nameFr}
                t={t}
              />
              <RenderIf condition={!switchPlace}>
                <CustomAdvancedFilters
                  detectLanguage={detectLanguage}
                  t={t}
                  setChangedSearchState={setChangedSearchState}
                  nameEn={nameEn}
                  nameFr={nameFr}
                />
              </RenderIf>
            </div>
          </div>
          <div className="col-xl-9">
            <div className="result-search new-search">
              {!switchPlace && (
                <BreadCrumb
                  t={t}
                  items={[
                    {
                      label:
                        getSessionStorage('requestedName') ||
                        detectLanguage === 'fr'
                          ? nameFr
                          : nameEn
                    }
                  ]}
                />
              )}
              <div className="results">
                <h1 className="t-subheading-1 bold c-fake-black title-search">
                  {t('Matches_equippers_found')}
                </h1>
                <p className="t-body-large c-neutrals-gray mb-3">
                  {t('Matches_equippers_found_text')}
                </p>
                <div className="d-flex align-items-center">
                  {changedSearchState ? (
                    <h2 className="t-body-small fake-black bold mr-4">
                      {t('Equipment_specifications')}
                    </h2>
                  ) : (
                    ''
                  )}

                  {changedSearchState ? (
                    <CustomCurrentRefinements t={t} />
                  ) : (
                    <span className="c-near-grey equipper-box-text tag-equip-standard ">
                      {t('Equipment_card_standard_help_message')}
                    </span>
                  )}
                </div>

                <div className="row">
                  {equippersList?.slice(0, hitsPerPage)?.map((item, key) => (
                    <div className="col-xxl-4 col-xl-6">
                      <EquipperItem
                        item={item}
                        key={`equipper-${key}-${item.equipper_id}`}
                        recommendationObjectID={recommendationObjectID}
                        t={t}
                      />
                    </div>
                  ))}
                </div>

                <RenderIf condition={equippersList.length > hitsPerPage}>
                  <div className="load-more ">
                    <button
                      className="round-button c-white yellow hover_black d-inline-flex align-items-center"
                      onClick={() => {
                        setHitsPerPage(hitsPerPage + 1);
                      }}
                    >
                      {t('View_more_button')}
                    </button>
                  </div>
                </RenderIf>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export const CustomEquipperSearchResult =
  connectInfiniteHits(EquipperSearchResult);
