import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useSearchContext } from '../../shared/context/Search_context';
import BreadCrumb from '../../shared/components/Bread_crumb';
import ToloIsLoading from '../../shared/components/cards/Tolo_is_loading';
import AvailableInventory from '../inventory/Available_inventory';
import NotFoundPage from '../not_found/Not_found_page';
import { rating } from '../../shared/helpers/Rating_helper';
import { isEmpty } from 'lodash';
import RenderIf from '../../shared/components/Render_if';
import ScrollToTop from '../../shared/components/Scroll_to_top';

export default function EquipperSpotlight({ detectLanguage, t }) {
  const params = useParams();
  const { getEquipperByUserName } = useSearchContext();
  const [isLoading, setIsLoading] = useState(false);
  const [items, setItems] = useState([]);
  const [ratingData, setRatingData] = useState();
  const [equipper, setEquipper] = useState(null);

  useEffect(() => {
    (async () => {
      setIsLoading(true);
      const res = await getEquipperByUserName(params.userName);
      if (res.status === 200 && res.data != null) {
        setEquipper(res.data);
        setRatingData(rating(res.data?.user_name));
        setItems([{ label: res.data?.company }]);
      }
      setIsLoading(false);
    })();
  }, [params]);

  if (isLoading) {
    return <ToloIsLoading />;
  }

  if (isEmpty(equipper)) {
    return <NotFoundPage t={t}  text="Not_found_page_text"/>;
  }

  return (
    <ScrollToTop>
      <div className="company-spotlight">
        <BreadCrumb items={items} t={t}/>
        <RenderIf condition={equipper}>
          <div className="container-fluid padding-l-desk-0">
            <div className="company-spotlight--similarProduct">
              <div className="row">
                <AvailableInventory
                  t={t}
                  detectLanguage={detectLanguage}
                  ratingData={ratingData}
                  isEquipper
                  data={{ equipper_id: equipper.id }}
                  equipper={equipper}
                  isEquipperSpotlight
                />
              </div>
            </div>
          </div>
        </RenderIf>
      </div>
    </ScrollToTop>
  );
}
