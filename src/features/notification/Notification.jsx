import React from 'react';
import notif1 from '../../style/assets/img/notif_yellow1.svg';
import notif2 from '../../style/assets/img/notif2.svg';

export default function Notification() {
  return (
    <div className="notification-container">
      <div className="container">
        <h1 className="t-title-extra-large bold">Notification</h1>
        <div className="notification-content">
          <div className="notification-content__sections">
            <div className="notification-content__section">
              <p className="t-base-medium c-grey-titles bold border-content">
                Today
              </p>
              <div className="notification-box">
                <div className="notification-box__image">
                  <img src={notif1} alt="notif" />
                </div>
                <div className="notification-box__content">
                  <p className="t-base-medium c-primary-color">
                    You have a new booking request from “Lodgername”on October
                    07 ,2021 with the cost of 234$
                  </p>
                </div>
                <div className="notification-box__right">
                  <p className="t-base-medium c-near-grey">20 min</p>
                  <button className="round-button black bold shadow">
                    View details
                  </button>
                </div>
              </div>
            </div>
            <div className="notification-content__section">
              <p className="t-base-medium c-grey-titles bold border-content">
                Yesterday
              </p>
              <div className="notification-box state">
                <div className="notification-box__image">
                  <img src={notif2} alt="can't load at the moment" />
                </div>
                <div className="notification-box__content">
                  <p className="t-base-medium c-primary-color">
                    You have a new booking request from “Lodgername”on September
                    30 ,2021 with the cost of 240$
                  </p>
                </div>
                <div className="notification-box__right">
                  <p className="t-base-medium c-near-grey">08:45</p>
                  <button className="round-button black bold shadow">
                    View details
                  </button>
                </div>
              </div>
              <div className="notification-box state">
                <div className="notification-box__image">
                  <img src={notif2} alt="can't load at the moment" />
                </div>
                <div className="notification-box__content">
                  <p className="t-base-medium c-primary-color">
                    You have a new booking request from “Lodgername”on June 14
                    ,2021 with the cost of 334$
                  </p>
                </div>
                <div className="notification-box__right">
                  <p className="t-base-medium c-near-grey">10:30</p>
                  <button className="round-button black bold shadow">
                    View details
                  </button>
                </div>
              </div>
            </div>
            <div className="view-all">
              <p className="t-label-medium c-primary-color">View all (30)</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
