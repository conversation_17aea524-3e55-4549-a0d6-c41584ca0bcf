import React, { useState } from 'react';
import { Alert } from 'react-bootstrap';
import * as Yup from 'yup';
import { Formik, ErrorMessage, Field, Form } from 'formik';
import { useAuth } from '../../shared/context/Auth_context';
import { isValid } from '../../shared/helpers/Data_helper';

export default function ForgetPassword({
  showFPModal,
  onClose,
  showCVModal,
  t
}) {
  const { FindUserByEmail } = useAuth();
  const [error, setError] = useState('');
  const validate = Yup.object({
    email: Yup.string()
      .email(t('Invalid_email'))
      .required(t('This_field_is_required'))
  });

  const handleSubmit = async (e) => {
    const res = await FindUserByEmail(e.email);
    if (res.status === 200 || res.status === 201) {
      setError('');
      sessionStorage.setItem('user', e.email);
      showCVModal();
    } else {
      res.error ? setError(res.error.error) : setError(t('Email_not_found'));
    }
  };

  if (!showFPModal) {
    return null;
  }

  return (
    <div className="modal">
      <div className="modal-content">
        <div className="row">
          <div className="col-lg-11 mx-auto text-center">
            <button className="close-button" onClick={onClose}>
              <svg
                width="25"
                height="24"
                viewBox="0 0 25 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M19.25 5.25L5.75 18.75"
                  stroke="#333333"
                  strokeWidth="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M19.25 18.75L5.75 5.25"
                  stroke="#333333"
                  strokeWidth="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
            <h3 className="t-header-h6 bold">{t('Forget_Password')}</h3>
            <p className="t-body-regular desc-limit">
              {t('Forget_password_text')}
            </p>
            <Formik
              initialValues={{
                email: ''
              }}
              onSubmit={handleSubmit}
              validationSchema={validate}
            >
              {(formik) => (
                <Form className="no-padding-row">
                  <p className="form-group m-2">
                    <Field
                      placeholder={t('Please_enter_your_email')}
                      type="email"
                      name="email"
                      className={`form-control full-width mt-4  ${isValid(
                        formik,
                        'email'
                      )}`}
                    />
                    <ErrorMessage
                      name="email"
                      component="span"
                      className="error-message"
                    />
                  </p>
                  {error && (
                    <Alert variant="danger" className="mt-4">
                      {t('Unable_to_find_user_by_email')}
                    </Alert>
                  )}
                  <button
                    className={`round-button bold width-200 ${
                      !formik.isValid || formik.isSubmitting
                        ? 'c-near-grey bg-neutrals-gray'
                        : 'yellow'
                    }
                      `}
                    disabled={!formik.isValid || formik.isSubmitting}
                    type="submit"
                  >
                    {t('Send')}
                  </button>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </div>
  );
}
