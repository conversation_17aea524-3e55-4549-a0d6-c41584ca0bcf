import React, { useState } from 'react';
import '../../shared/i18n';
import SuccessModal from '../../shared/components/modals/Success_pop_up';
import NewPasswordModal from './New_password_modal';
import VerifyCodeModal from './Verify_code_modal';
import CheckInboxModal from './Check_your_inbox_modal';
import { useAuth } from '../../shared/context/Auth_context';
import RenderIf from '../../shared/components/Render_if';

export default function CodeVerification({
  onClose,
  showCVModal,
  t,
  onCloseFPM
}) {
  const [changePassword, setChangePassword] = useState(false);
  const [verifyCode, setVerifyCode] = useState(false);
  const [code, setCode] = useState();
  const [error, setError] = useState('');
  const { GetVerificationCode, UpdatePasswordAPI } = useAuth();
  const [success, setSuccess] = useState(false);
  const email = sessionStorage.getItem('user');
  const [show, setShow] = useState(false);

  const updatePassword = async (e) => {
    try {
      const code = sessionStorage.getItem('code');
      await UpdatePasswordAPI(email, e.password, code);
      setSuccess(true);
      setShow(true);
    } catch (error) {
      setError(t('Failed_to_change_password'));
    }
  };

  const onAllClose = () => {
    onCloseFPM();
    onClose();
    window.location.reload();
  };

  const sendCode = () => {
    setChangePassword(true);
  };

  const handlePinChange = (value) => {
    setCode(value);
  };

  const getVerificationCode = async () => {
    const email = sessionStorage.getItem('user');
    const response = await GetVerificationCode(code, email);
    if (response.error || response.data?.error) {
      setError(t('Failed_to_verify_code'));
    } else {
      sessionStorage.setItem('code', code);
      setVerifyCode(true);
    }
  };

  return (
    <RenderIf condition={showCVModal}>
      <div className="modal">
        <div className="modal-content">
          {changePassword ? (
            <>
              {verifyCode ? (
                <>
                  {success ? (
                    <SuccessModal show={show} onClose={onAllClose} />
                  ) : (
                    <NewPasswordModal
                      onClose={onClose}
                      UpdatePassword={updatePassword}
                    />
                  )}
                </>
              ) : (
                <VerifyCodeModal
                  handlePinChange={handlePinChange}
                  code={code}
                  error={error}
                  verifyCode={getVerificationCode}
                  onClose={onClose}
                  t={t}
                />
              )}
            </>
          ) : (
            <CheckInboxModal sendCode={sendCode} onClose={onClose} t={t} />
          )}
        </div>
      </div>
    </RenderIf>
  );
}
