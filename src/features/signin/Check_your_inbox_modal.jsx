import React, { useState } from 'react';
import { Alert } from 'react-bootstrap';
import { useAuth } from '../../shared/context/Auth_context';
import Popup from '../../shared/components/modals/Popup';

export default function CheckYourInboxModal({ onClose, sendCode, t }) {
  const [response, setResponse] = useState(null);
  const [showPopup, setShowPopup] = useState(false);
  const [showMessage, setShowMessage] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { FindUserByEmail } = useAuth();
  const email = sessionStorage.getItem('user');

  const resendCode = async () => {
    setIsLoading(true);
    const res = await FindUserByEmail(email);
    if (res.status === 200) {
      setShowMessage(true);
      sessionStorage.setItem('user', email);
    } else {
      setShowPopup(true);
      setResponse(res);
    }
    setIsLoading(false);
  };

  return (
    <>
      <Popup
        show={showPopup}
        onClose={() => {
          setShowPopup(false);
        }}
        response={response}
        t={t}
      />
      <button className="close-button" onClick={onClose}>
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M19.25 5.25L5.75 18.75"
            stroke="#333333"
            strokeWidth="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M19.25 18.75L5.75 5.25"
            stroke="#333333"
            strokeWidth="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
      <div className="text-center">
        <h3 className="FPmodal-title t-header-h6 bold">
          {t('Check_your_inbox')}
        </h3>
        <p className="t-header-medium CVmodal-pragraph">
          {t('Description_code_verification')}
        </p>
        <p className="t-header-medium email-verified bold">{email}</p>
        <div className="container-btn-vc fixed-button-modal">
          {showMessage && (
            <Alert variant="success">
              {t('Verification code has been sent')}
            </Alert>
          )}
          <button
            className={
              isLoading
                ? 'btn-resend-code round-button border-yellow c-near-greay disabled'
                : 'btn-resend-code round-button border-yellow c-primary-color'
            }
            onClick={resendCode}
          >
            {t('Resend_code')}
          </button>
          <button
            className="btn-change-password round-button yellow c-primary-color"
            onClick={sendCode}
          >
            {t('Change_password')}
          </button>
        </div>
      </div>
    </>
  );
}
