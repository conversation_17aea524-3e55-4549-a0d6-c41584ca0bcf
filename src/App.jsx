import { library } from '@fortawesome/fontawesome-svg-core';
import {
  faAngleDown,
  faCheckCircle,
  faPaperPlane,
  faSearch,
  faTimesCircle
} from '@fortawesome/free-solid-svg-icons';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Route, Routes, useLocation, Navigate } from 'react-router-dom';
import NeedInformation from './components/home/<USER>';
import CompanySpotlight from './features/company_spotlight/Company_spotlight';
import EquipperManagementPortal from './features/equipper_management_portal/Equipper_management_portal';
import EquipperSpotlight from './features/equipper_spotlight/Equipper_spotlight';
import Footer from './features/footer/Footer';
import Home from './features/home/<USER>';
import AvailableInventory from './features/inventory/Available_inventory';
import LodgerManagementPortal from './features/lodger_management_portal/Lodger_management_portal';
import NotFoundPage from './features/not_found/Not_found_page';
import PrivacyPolicyEN from './features/legal_agreements/Privacy';
import PrivacyPolicyFR from './features/legal_agreements/Privacy_fr';
import PrivacyPolicyAR from './features/legal_agreements/Privacy_ar';
import SearchResult from './features/search_result/Search_result';
import Signup from './features/signup/Signup.jsx';
import SignupEquipper from './features/signup/Signup_equipper';
import TermsAndConditionsEN from './features/legal_agreements/Terms_conditions';
import TermsAndConditionsFR from './features/legal_agreements/Terms_conditions_fr';
import TermsAndConditionsAR from './features/legal_agreements/Terms_conditions_ar';
import ToolerBidzInventoryManagement from './features/tooler_bidz_inventory_management/Tooler_bidz_inventory_management';
import NavBar from './shared/components/nav_bar/NavBar';
import AuthProvider from './shared/context/Auth_context';
import CreditCheckFormProvider from './shared/context/Credit_check_form_context';
import EquipmentProvider from './shared/context/Equipment_context';
import EquipperProvider from './shared/context/Equipper_context';
import LodgerProvider from './shared/context/Lodger_context';
import ProjectProvider from './shared/context/Project_context';
import ReclamationProvider from './shared/context/Reclamation_context';
import RequestProvider from './shared/context/Requests_context';
import SearchProvider from './shared/context/Search_context';
import InventoryProvider from './shared/context/Tooler_bidz_inventory_management_context';
import TeamProvider from './shared/context/Team_context';
import BidzSearchResult from './features/search_result/Bidz_search_result';
import ErrorBoundary from './ErrorBoundary';
import { changeLanguage } from './shared/i18n';
import { getCookies, setCookies } from './shared/helpers/Cookies';
import OurStory from './features/our_story/Our_story';
import EquipmentDetails from './components/equipment_details/Equipment_details';
import AnalyticsVerificationPanel from './shared/components/AnalyticsVerificationPanel';
import RequestManagementSection from './shared/components/management_portal/request_management/Request_management_section';
import BidzManagement from './shared/components/management_portal/bidz_management/Bidz_management';
import ProjectManagement from './components/lodger_management_portal/project_management/Project_management';
import TeamManagement from './shared/components/management_portal/team_management/Team_management';
import RentalsSummary from './components/lodger_management_portal/rentals_summary/Rentals_summary';
import UnderConstruction from './shared/components/management_portal/under_construction/Under_construction';
import NoFound from './style/assets/img/no_found.png';
import PromotionProvider from './shared/context/Promotion_context.jsx';
import PricingAndMarketing from './components/equipper_management_portal/pricing_and_marketing/Pricing_and_marketing.jsx';
import AddEquipement from './components/equipper_management_portal/equipments_management/add_equipment/Add_equipement.jsx';
import BulkOrSingleEquipment from './components/equipper_management_portal/equipments_management/Bulk_or_single_equipment.jsx';
import EquipmentManagement from './components/equipper_management_portal/equipments_management/Equipments_management.jsx';
import OneEquipmentDetails from './components/equipper_management_portal/equipments_management/OneEquipmentDetails.jsx';
import UpdateEquipment from './components/equipper_management_portal/equipments_management/update_equipment/Update_equipment.jsx';
import CreditApplication from './components/lodger_management_portal/credit_application/Credit_application_page.jsx';
import LanguagePopup from './shared/components/modals/Language_popup.jsx';

library.add(faPaperPlane, faSearch, faAngleDown, faTimesCircle, faCheckCircle);

function App() {
  const [show, setShow] = useState(false);
  const [showFPModal, setShowFPModal] = useState(false);
  const [lang, setLang] = useState('');
  const [country, setCountry] = useState('');
  const [showLanguagePopup, setShowLanguagePopup] = useState(false);
  const { t } = useTranslation();
  const url = window.location.search;
  const Params = new URLSearchParams(url);
  const signIn = Params.get('sign_in');
  const location = useLocation();
  const role = getCookies('role');

  useEffect(() => {
    // Original language detection logic - restored for non-equipment-details pages
    const pathLang = location.pathname.split('/')[1];
    const validLangs = {
      qc: { lang: 'fr', country: 'CA' },
      us: { lang: 'en', country: 'US' },
      ca: { lang: 'en', country: 'CA' },
      ksa: { lang: 'ar', country: 'SA' }
    };

    if (validLangs[pathLang]) {
      const { lang: detectedLang, country } = validLangs[pathLang];
      setLang(detectedLang);
      setCountry(country);
      changeLanguage(detectedLang);
      setCookies('lang', detectedLang, 31536000); // 1 year in seconds
      setCookies('country', country, 31536000);
    } else {
      // If no language in route, use stored language from cookies
      const storedLang = getCookies('lang');
      const storedCountry = getCookies('country');
      if (storedLang && storedCountry) {
        setLang(storedLang);
        setCountry(storedCountry);
        changeLanguage(storedLang);
      } else {
        setShowLanguagePopup(true); // Show popup if cookies are not set
      }
    }
    // Check if cookies are erased
    const currentLang = getCookies('lang');
    const currentCountry = getCookies('country');
    if (!currentLang || !currentCountry) {
      setShowLanguagePopup(true); // Re-display popup if cookies are erased
    }
  }, [location.pathname]); // Update when route changes

  useEffect(() => {
    if (signIn === 'true') {
      setShow(true);
    }
  }, [signIn]);

  return (
    <div className={lang === 'fr' ? 'font-link fr-lang' : 'font-link en-lang'}>
      {showLanguagePopup && (
        <LanguagePopup
          setLang={setLang}
          setCountry={setCountry}
          changeLanguage={changeLanguage}
          setCookies={setCookies}
          setShowLanguagePopup={setShowLanguagePopup}
        />
      )}
      <NavBar
        setShowFPModal={setShowFPModal}
        showFPModal={showFPModal}
        signIn={signIn}
        show={show}
        setShow={setShow}
        t={t}
        detectLanguage={lang}
      />
      <div className="padding-lr-mobile">
        <ErrorBoundary t={t}>
          <Routes>
            <Route
              exact
              path="/"
              element={
                <EquipperProvider>
                  <PromotionProvider>
                    <Home
                      t={t}
                      detectLanguage={lang}
                      setShowFPModal={setShowFPModal}
                      showFPModal={showFPModal}
                      signIn={signIn}
                      country={country}
                    />
                  </PromotionProvider>
                </EquipperProvider>
              }
            />
            <Route
              path="/equipperManagementPortal"
              element={
                <EquipperProvider>
                  <EquipperManagementPortal detectLanguage={lang} t={t} />
                </EquipperProvider>
              }
            >
              <Route
                index
                element={
                  <RequestManagementSection
                    t={t}
                    detectLanguage={lang}
                    role={role}
                  />
                }
              />
              <Route
                path="bidzManagement"
                element={
                  <BidzManagement t={t} detectLanguage={lang} role={role} />
                }
              />

              <Route path="equipmentManagement">
                <Route
                  index
                  element={<EquipmentManagement t={t} detectLanguage={lang} />}
                />
                <Route
                  path="addEquipment"
                  element={<BulkOrSingleEquipment t={t} />}
                />
                <Route
                  path="addSingleEquipment"
                  element={
                    <InventoryProvider>
                      <AddEquipement t={t} detectLanguage={lang} />
                    </InventoryProvider>
                  }
                />
                <Route
                  path="equipmentDetails/:id"
                  element={
                    <LodgerProvider>
                      <RequestProvider>
                        <OneEquipmentDetails
                          t={t}
                          detectLanguage={lang}
                          equipmentByID
                        />
                      </RequestProvider>
                    </LodgerProvider>
                  }
                />

                <Route
                  path="updateEquipment/:equipmentId"
                  element={
                    <InventoryProvider>
                      <UpdateEquipment t={t} detectLanguage={lang} />
                    </InventoryProvider>
                  }
                />
              </Route>

              <Route
                path="teamManagement"
                element={<UnderConstruction t={t} />}
              />
              <Route
                path="pricingAndMarketing"
                element={
                  <LodgerProvider>
                    <PromotionProvider>
                      <PricingAndMarketing t={t} />
                    </PromotionProvider>
                  </LodgerProvider>
                }
              />
              <Route path="analytics" element={<UnderConstruction t={t} />} />

              <Route
                path="commentsAndRatings"
                element={<UnderConstruction t={t} />}
              />
            </Route>
            <Route
              path="/toolerBidzInventoryManagement"
              element={
                <InventoryProvider>
                  <EquipmentProvider>
                    <ToolerBidzInventoryManagement t={t} />
                  </EquipmentProvider>
                </InventoryProvider>
              }
            />

            <Route
              path="/availableInventory"
              element={
                <LodgerProvider>
                  <PromotionProvider>
                    <AvailableInventory
                      detectLanguage={lang}
                      t={t}
                      setShowFPModal={setShowFPModal}
                      showFPModal={showFPModal}
                      signIn={signIn}
                    />
                  </PromotionProvider>
                </LodgerProvider>
              }
            />

            <Route
              path="renterManagementPortal"
              element={
                <TeamProvider>
                  <LodgerProvider>
                    <LodgerManagementPortal t={t} detectLanguage={lang} />
                  </LodgerProvider>
                </TeamProvider>
              }
            >
              <Route
                index
                element={
                  <RequestManagementSection
                    t={t}
                    detectLanguage={lang}
                    role={role}
                  />
                }
              />
              <Route
                path="bidzManagement"
                element={
                  <BidzManagement t={t} detectLanguage={lang} role={role} />
                }
              />
              <Route
                path="teamManagement"
                element={<TeamManagement t={t} role={role} />}
              />
              <Route
                path="projectManagement"
                element={<ProjectManagement t={t} detectLanguage={lang} />}
              />
              <Route
                path="rentalsSummary"
                element={
                  <RentalsSummary t={t} detectLanguage={lang} role={role} />
                }
              />
              <Route
                path="creditCheckForm"
                element={<CreditApplication t={t} />}
              />
              <Route path="analytics" element={<UnderConstruction t={t} />} />

              <Route
                path="commentsAndRatings"
                element={<UnderConstruction t={t} />}
              />
            </Route>

            <Route
              path="/signUpAsLodger"
              element={
                <AuthProvider>
                  <Signup showModal={() => setShow(!show)} t={t} />
                </AuthProvider>
              }
            />

            <Route
              path="/searchResult/:name_fr/:name_en/:city/:start_date/:end_date/:id"
              element={
                <SearchProvider>
                  <SearchResult
                    t={t}
                    detectLanguage={lang}
                    setShowFPModal={setShowFPModal}
                    showFPModal={showFPModal}
                    signIn={signIn}
                    show={show}
                    setShow={setShow}
                  />
                </SearchProvider>
              }
            />

            <Route
              path="/bidzSearchResult/:name_fr/:name_en/:city/:start_date/:end_date/:id"
              element={
                <SearchProvider>
                  <RequestProvider>
                    <ProjectProvider>
                      <CreditCheckFormProvider>
                        <LodgerProvider>
                          <BidzSearchResult
                            t={t}
                            detectLanguage={lang}
                            setShowFPModal={setShowFPModal}
                            showFPModal={showFPModal}
                            signIn={signIn}
                          />
                        </LodgerProvider>
                      </CreditCheckFormProvider>
                    </ProjectProvider>
                  </RequestProvider>
                </SearchProvider>
              }
            />

            <Route
              path="/signUpAsEquipper"
              element={
                <AuthProvider>
                  <SignupEquipper showModal={() => setShow(!show)} t={t} />
                </AuthProvider>
              }
            />

            <Route
              path="/companySpotlight/:equipperId/:start_date/:end_date/:city/:name_en/:recommandationID"
              element={
                <SearchProvider>
                  <RequestProvider>
                    <PromotionProvider>
                      <ProjectProvider>
                        <CreditCheckFormProvider>
                          <LodgerProvider>
                            <CompanySpotlight
                              t={t}
                              setShowFPModal={setShowFPModal}
                              showFPModal={showFPModal}
                              signIn={signIn}
                              show={show}
                              setShow={setShow}
                              detectLanguage={lang}
                            />
                          </LodgerProvider>
                        </CreditCheckFormProvider>
                      </ProjectProvider>
                    </PromotionProvider>
                  </RequestProvider>
                </SearchProvider>
              }
            />

            <Route
              path="/equipperSpotlight/:userName"
              element={
                <SearchProvider>
                  <RequestProvider>
                    <ProjectProvider>
                      <CreditCheckFormProvider>
                        <LodgerProvider>
                          <PromotionProvider>
                            <EquipperSpotlight t={t} detectLanguage={lang} />
                          </PromotionProvider>
                        </LodgerProvider>
                      </CreditCheckFormProvider>
                    </ProjectProvider>
                  </RequestProvider>
                </SearchProvider>
              }
            />

            <Route
              path="/help"
              element={
                <ReclamationProvider>
                  <div className="m-4 p-4">
                    <NeedInformation t={t} />
                  </div>
                </ReclamationProvider>
              }
            />

            {lang === 'ar' ? (
              <>
                <Route
                  path="/terms&conditions"
                  element={<TermsAndConditionsAR />}
                />
                <Route path="/privacyPolicy" element={<PrivacyPolicyAR />} />
              </>
            ) : lang === 'en' ? (
              <>
                <Route
                  path="/terms&conditions"
                  element={<TermsAndConditionsEN />}
                />
                <Route path="/privacyPolicy" element={<PrivacyPolicyEN />} />
              </>
            ) : (
              <>
                <Route
                  path="/terms&conditions"
                  element={<TermsAndConditionsFR />}
                />
                <Route path="/privacyPolicy" element={<PrivacyPolicyFR />} />
              </>
            )}
            <Route path="/ourStory" element={<OurStory t={t} />} />
            <Route
              path="/equipmentDetails/:id"
              element={
                <SearchProvider>
                  <LodgerProvider>
                    <RequestProvider>
                      <PromotionProvider>
                        <EquipmentDetails
                          t={t}
                          setShowFPModal={setShowFPModal}
                          showFPModal={showFPModal}
                          signIn={signIn}
                          show={show}
                          setShow={setShow}
                          detectLanguage={lang}
                          role={role}
                        />
                      </PromotionProvider>
                    </RequestProvider>
                  </LodgerProvider>
                </SearchProvider>
              }
            />
            <Route path="/sa" element={<Navigate to="/" replace />} />
            <Route path="/us" element={<Navigate to="/" replace />} />
            <Route path="/fr" element={<Navigate to="/" replace />} />
            <Route path="/ca" element={<Navigate to="/" replace />} />
            <Route
              path="/*"
              element={
                <NotFoundPage
                  t={t}
                  text="Not_found_page_text"
                  img={NoFound}
                  description="Not_found_page_description"
                />
              }
            />
          </Routes>
        </ErrorBoundary>
      </div>
      {!location.pathname.includes('/searchResult') && (
        <ReclamationProvider>
          <div id="contactUS">
            <Footer
              t={t}
              detectLanguage={lang}
              setShowFPModal={setShowFPModal}
              showFPModal={showFPModal}
              signIn={signIn}
              show={show}
              setShow={setShow}
            />
          </div>
        </ReclamationProvider>
      )}
      <AnalyticsVerificationPanel />
    </div>
  );
}
export default App;
