export const ADMIN = 'admin';
export const COLLABORATOR = 'collaborator';
export const POWER_COLLABORATOR = 'power collaborator';
export const ACCOUNT_MANAGER = 'account manager';

export const OWNER_PRIVILEGE = 10;
export const ADMIN_PRIVILEGE = 4;
export const POWER_COLLABORATOR_PRIVILEGE = 3;
export const COLLABORATOR_PRIVILEGE = 2;
export const ACCOUNT_MANAGER_PRIVILEGE = 2;

export const EQUIPPER_TYPES = (t) => [
  {
    label: t('Account_manager'),
    value: ACCOUNT_MANAGER,
    privilege_level: ACCOUNT_MANAGER_PRIVILEGE
  },
  { label: t('Admin'), value: ADMIN, privilege_level: ADMIN_PRIVILEGE }
];

export const LODGER_TYPES = (t) => [
  {
    label: t('Owner'),
    value: 'owner',
    privilege_level: OWNER_PRIVILEGE
  },
  {
    label: t('Administrator'),
    value: ADMIN,
    privilege_level: ADMIN_PRIVILEGE
  },
  {
    label: t('Power_collaborator'),
    value: POWER_COLLABORATOR,
    privilege_level: POWER_COLLABORATOR_PRIVILEGE
  },
  {
    label: t('Collaborator'),
    value: COLLABORATOR,
    privilege_level: COLLABORATOR_PRIVILEGE
  }
];

export const ALL_TYPES = [
  { label: ADMIN, value: ADMIN, privilege_level: ADMIN_PRIVILEGE },
  {
    label: COLLABORATOR,
    value: COLLABORATOR,
    privilege_level: COLLABORATOR_PRIVILEGE
  },
  {
    label: 'power collaborator',
    value: POWER_COLLABORATOR,
    privilege_level: POWER_COLLABORATOR_PRIVILEGE
  },
  {
    Label: 'account manager',
    value: ACCOUNT_MANAGER,
    privilege_level: ACCOUNT_MANAGER_PRIVILEGE
  }
];

export function getSuggestionList(member, type, t, currentUser) {
  switch (type) {
    case 'equipper':
      return EQUIPPER_TYPES(t).filter(
        (item) =>
          item.value !== member.type &&
          item.privilege_level < currentUser.privilege_level
      );
    case 'lodger':
      return LODGER_TYPES(t).filter(
        (item) =>
          item.value !== member.type &&
          item.privilege_level < currentUser.privilege_level
      );
    default:
      return ALL_TYPES.filter(
        (item) =>
          item.value !== member.type &&
          item.privilege_level < currentUser.privilege_level
      );
  }
}
