import { setSessionStorage } from './Session_storage_helper';
import algoliasearch from 'algoliasearch';

export const searchClient = algoliasearch(
  `${import.meta.env.VITE_ALGOLIA_ID}`,
  `${import.meta.env.VITE_ALGOLIA_API_KEY}`
);

export function createURL(searchState, qs) {
  return qs.stringify(searchState, { addQueryPrefix: true });
}

export function searchStateToUrl({ location }, searchState, qs) {
  if (Object.keys(searchState).length === 0) {
    return '';
  }

  return `${location.pathname}${createURL(searchState, qs)}`;
}

export function urlToSearchState({ search }, qs) {
  return qs.parse(search.slice(1));
}

export const index = searchClient.initIndex(
  import.meta.env.VITE_ALGOLIA_INDEX_NAME
);

export const standardsIndex = searchClient.initIndex(
  import.meta.env.VITE_ALGOLIA_INDEX_NAME_STANDARDS
);

/**
 * Search for provider-specific equipment data
 * @param {string} equipmentId - The equipment object ID
 * @param {string} equipperId - The equipper/provider ID (optional)
 * @returns {Promise<Object|null>} - The provider-specific equipment data or null
 */
export const getProviderSpecificEquipment = async (
  equipmentId,
  equipperId = null
) => {
  try {
    if (!equipmentId || equipmentId.trim() === '') {
      return null;
    }

    // Search for provider-specific equipment using both equipmentId and equipperId
    // This ensures we get the correct provider's customized equipment data
    let searchFilters = `objectID:${equipmentId}`;
    if (equipperId) {
      searchFilters += ` AND equipper_id:${equipperId}`;
    }

    const providerSpecificSearch = await index.search('', {
      filters: searchFilters
    });

    if (providerSpecificSearch.hits && providerSpecificSearch.hits.length > 0) {
      return providerSpecificSearch.hits[0];
    }

    // Fallback: try searching by objectID only (without equipper filter)
    const fallbackSearch = await index.search('', {
      filters: `objectID:${equipmentId}`
    });

    if (fallbackSearch.hits && fallbackSearch.hits.length > 0) {
      return fallbackSearch.hits[0];
    }

    // Last resort: try searching by equipment name/text
    const { hits } = await index.search(equipmentId);
    if (hits && hits.length > 0) {
      return hits[0];
    }

    return null;
  } catch (error) {
    console.error('Error fetching provider-specific equipment:', error);
    return null;
  }
};
export const navigateToSearchResult = async (
  equipmentName,
  location,
  startDate,
  endDate,
  equipmentID,
  navigate
) => {
  const currentTime = new Date();

  startDate.setHours(
    currentTime.getHours(),
    currentTime.getMinutes(),
    currentTime.getSeconds(),
    currentTime.getMilliseconds()
  );

  endDate.setHours(
    currentTime.getHours(),
    currentTime.getMinutes(),
    currentTime.getSeconds(),
    currentTime.getMilliseconds()
  );

  setSessionStorage('location', location.value);
  setSessionStorage('start_date', startDate.getTime());
  setSessionStorage('end_date', endDate.getTime());
  setSessionStorage('nameEn', equipmentName?.name_en || equipmentName?.name);
  setSessionStorage('nameFr', equipmentName?.name_fr);

  const name = equipmentName?.name_en || equipmentName?.name;
  const data = await index.search(name, {
    facets: ['name', 'name_fr', 'coverage_area', 'status'],
    facetFilters: [`name:${name}`, `coverage_area:${location.value}`],
    filters: `available_from<=${startDate.getTime()} AND is_active:true AND (status:available OR status:booked)`
  });

  if (data?.hits.length > 0) {
    navigate(
      `/searchResult/${equipmentName?.name_fr}/${name}/${
        location.value
      }/${startDate.getTime()}/${endDate.getTime()}/${equipmentID}`
    );
  } else {
    navigate(
      `/bidzSearchResult/${equipmentName?.name_fr}/${name}/${
        location.value
      }/${startDate.getTime()}/${endDate.getTime()}/${equipmentID}`
    );
  }
};

export const navigateToCompanySpotlight = async (
  equipmentName,
  location,
  startDate,
  endDate,
  equipperId,
  equipmentID,
  navigate
) => {
  setSessionStorage('location', location.value);
  setSessionStorage('start_date', startDate.getTime());
  setSessionStorage('end_date', endDate.getTime());
  const name = equipmentName?.name_en || equipmentName?.name;
  const data = await index.search(name, {
    facets: ['name', 'name_fr', 'coverage_area', 'status'],
    facetFilters: [`name:${name}`, `coverage_area:${location.value}`],
    filters: `available_from<=${startDate.getTime()} AND is_active:true AND (status:available OR status:booked)`
  });

  if (data?.hits.length > 0) {
    navigate(
      `/companySpotlight/${equipperId}/${startDate.getTime()}/${endDate.getTime()}/${
        location.value
      }/${name}/${equipmentID}`
    );
  } else {
    navigate(
      `/bidzSearchResult/${equipmentName?.name_fr}/${name}/${
        location.value
      }/${startDate.getTime()}/${endDate.getTime()}/${equipmentID}`
    );
  }
};
