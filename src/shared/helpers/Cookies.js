import Cookies from 'universal-cookie';

const cookies = new Cookies();

export const removeCookies = () => {
  const allCookies = cookies.getAll();
  Object.keys(allCookies).forEach((cookie) => {
    if (cookie !== 'lang' && cookie !== 'country') {
      cookies.remove(cookie);
    }
  });
};
export const setCookies = (key, value, maxAge) => {
  cookies.set(key, value, { path: '/', maxAge: maxAge });
};
export const getCookies = (name) => {
  if (cookies.get(name)) {
    return cookies.get(name);
  }
  return null;
};
export const clearCookies = () => {
  const allCookies = cookies.getAll();
  Object.keys(allCookies).forEach((cookie) => {
    if (cookie !== 'lang' && cookie !== 'country') {
      cookies.remove(cookie);
    }
  });
};
