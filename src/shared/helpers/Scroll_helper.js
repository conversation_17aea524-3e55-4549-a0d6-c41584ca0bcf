export const scrollLeft = (elementId, setChevronRight) => {
    const scrollElement = document.getElementById(elementId);

    if (scrollElement) {
        scrollElement.scrollLeft += 50;
    }

    if (scrollElement.scrollLeft >= (scrollElement.scrollWidth - scrollElement.offsetWidth) - 1) {
        setChevronRight(false)
    }


};

export const scrollRight = (elementId, setChevronLeft) => {
    const scrollElement = document.getElementById(elementId);
    if (scrollElement) {
        scrollElement.scrollLeft -= 50;
    }

    if (scrollElement.scrollLeft <= 0) {
        setChevronLeft(false)
    }
};
