import { PHONE_NUMBER_DISPLAY_REGEX } from './Regex';

// this function is used to cut a string to a given length and add ... at the end
// if the string is longer than the given length
// @param {string} str - the string to cut
// @param {number} length - the length to cut the string to
// @return {string} - the cut string
export function cutString(str, length) {
    if (str.length > length) {
        return `${str.substring(0, length - 3)}...`;
    }
    return str;
}
export function firstLetterUpperCase(str) {
    if (typeof str !== 'string') {
        return '';
    }

    return str.charAt(0).toUpperCase() + str.slice(1);
}

// puts string in this format: (xxx) xxx-xxxx
export function formatPhoneNumber(phoneNumberString) {
    let cleaned = `${phoneNumberString}`.replace(/\D/g, '');
    if (cleaned.length >= 10) {
        cleaned = cleaned.slice(cleaned.length - 10, cleaned.length);
        const match = cleaned.match(PHONE_NUMBER_DISPLAY_REGEX);
        if (match) {
            return `(${match[1]}) ${match[2]}-${match[3]}`;
        }
    } else {
        return cleaned;
    }
}

export const isEmptyValue = (value, t) => {
    return value === null || value === undefined || value === ''
        ? 'N/A'
        : t
        ? t(firstLetterUpperCase(value))
        : value;
};
