const SIGNUP = '/public/signup';
const SIGNIN = '/public/signin';
const GET_VERIFICATION_CODE = '/public/password/validate';
const FIND_USER_BY_EMAIL = '/public/password/forgot/';
const RESET_PASSWORD = '/public/password/reset';
const VERIFY_CREDIT_CHECK_FORM = '/VerifyCreditCheck';
const GET_EQUIPMENT_BY_ID = '/public/equipment/';
const ADD_EQUIPMENT = '/equipments/upload';
const EQUIPMENT = '/equipment';
const EQUIPMENT_UPLOAD_IMAGE = '/equipment/upload_image';
const UPDATE_BOOKING_STATUS = '/equipment/book';
const GET_ALL_EQUIPMENTS = '/equipper/equipments?';
const GET_RENTALS_IN_PROGRESS = '/equipper/booked';
const GET_COMMENT_BY_ID =
  'https://47597e85-f560-42e2-b996-525a500e4e55.mock.pstmn.io/comments';
const RATE_LODGER =
  'https://d80ed529-d502-4e2b-a730-5a465a2ff426.mock.pstmn.io/rateLodger';
const GET_EQUIPMENT_PROMOTION =
  'https://bb0c5d92-1d00-4844-a6cf-3f109a117089.mock.pstmn.io/getEquipmentsPromotions';
const ADD_CREDIT_CARD =
  'https://6922e6fc-20dc-44bb-a7b0-b0aa6b4a7148.mock.pstmn.io/addCreditCard';
const GET_TEAM_LODGER = '/lodger/member';
const GET_TEAM_EQUIPPER = '/equipper/member';
const DELETE_MEMBER = '/member';
const EDIT_MEMBER = '/member/';
const VERIFY_EMAIL = '/member/verify_email/';
const ADD_MEMBER = '/member';
const GET_SELECTION_LIST = '/member/selection_list/';
const TOTAL_RENTED_EQUIPMENTS =
  'https://666238b5-c4ba-4372-9851-47ff2dccdf2a.mock.pstmn.io/total_rented_equipments';
const TOTAL_RENTED_INCOME =
  'https://666238b5-c4ba-4372-9851-47ff2dccdf2a.mock.pstmn.io/total_rented_income';
const TOP_RENTED_EQUIPMENTS =
  'https://666238b5-c4ba-4372-9851-47ff2dccdf2a.mock.pstmn.io/top_rented_equipments';
const TOP_RENTED_EQUIPMENTS_PER_LOCATION =
  'https://be46d772-4156-406b-98ef-ae6af1501126.mock.pstmn.io/top_rented_equipments_per_location';
const TOP_LODGERS =
  'https://666238b5-c4ba-4372-9851-47ff2dccdf2a.mock.pstmn.io/top_lodger';
const HISTORY_OF_RENTED_EQUIPMENT =
  'https://666238b5-c4ba-4372-9851-47ff2dccdf2a.mock.pstmn.io/history_of_rented_equipments';
const GET_EQUIPPER_INFO = '/equipper';
const GET_LODGER_REQUESTS = '/lodger/booked/';
const GET_EQUIPPER_REQUESTS = '/equipper/booked';
const BOOK_EQUIPMENT = '/equipment/book';
const UPDATE_EQUIPPER_INFO = '/equipper';
const UPDATE_LODGER_INFO = '/lodger';
const GET_ALL_LODGERS = '/lodger/all';
const GET_PROJECT = '/projects';
const PROJECT = '/project';
const GET_LODGER_INFO = '/lodger';
const SEND_RECLAMATION = '/public/lead';
const GET_RENTAL_IN_SUMMARY = '/lodger/booked/';
const UPLOAD_PROFILE_PHOTO_EQUIPPER = '/equipper/picture/upload';
const UPLOAD_PROFILE_PHOTO_LODGER = '/lodger/picture/upload';
const GET_EQUIPPER_BY_ID = '/public/equipper/';
const GET_EQUIPPER_BY_EMAIL = '/public/equipper/email/';
const ADD_CREDIT_CHECK_FORM = '/credit_check_form/add';
const GET_MY_CREDIT_CHECK_FORM = '/credit_check_form';
const UPLOAD_MY_CREDIT_CHECK_FORM =
  '/credit_check_form/upload_credit_check_form';
const IMG_BASE_URL = import.meta.env
  .VITE_DERENTAL_BIDZ_STORAGE_EQUIPMENTS_IMG_PATH;
const ADD_BIDZ_REQUEST = '/request_bids/send_bits_request';
const GET_BIDZ_OFFER_BY_RENTER_ID = '/offer_bids/lodger';
const GET_BIDZ_OFFER_BY_EQUIPPER_ID = '/offer_bids/equipper';
const GET_BIDZ_REQUEST_LODGER = '/request_bids/lodger';
const GET_BIDZ_REQUEST_EQUIPPER = '/request_bids/equipper';
const BIDZ_REQUEST = '/request_bids/';
const BIDZ_OFFER = '/offer_bids/';
const SET_BID_OFFER = '/offer_bids/send_bits_offer';
const TOOLER_BIDZ_INVENTORY = '/tooler_bidz_equipment';
const PUBLIC_TOOLER_BIDZ_INVENTORY = '/public/tooler_bidz_equipment';
const GET_EQUIPPER_BY_USER_NAME = '/public/equipper/user_name/';
const AIRTABLE_SYNC = '/equipment/synchronization';
const DELETE_ALL_EQUIPMENTS = '/equipment/all';
const PROMOTION = '/promotion';
const GET_IP_INFO = '/public/ip-info';

export {
  SIGNUP,
  BIDZ_OFFER,
  UPLOAD_MY_CREDIT_CHECK_FORM,
  DELETE_ALL_EQUIPMENTS,
  GET_EQUIPPER_BY_USER_NAME,
  ADD_CREDIT_CHECK_FORM,
  GET_MY_CREDIT_CHECK_FORM,
  GET_BIDZ_REQUEST_EQUIPPER,
  SIGNIN,
  VERIFY_EMAIL,
  UPLOAD_PROFILE_PHOTO_EQUIPPER,
  UPLOAD_PROFILE_PHOTO_LODGER,
  GET_VERIFICATION_CODE,
  FIND_USER_BY_EMAIL,
  RESET_PASSWORD,
  ADD_CREDIT_CARD,
  VERIFY_CREDIT_CHECK_FORM,
  GET_EQUIPMENT_BY_ID,
  ADD_EQUIPMENT,
  GET_ALL_EQUIPMENTS,
  GET_RENTALS_IN_PROGRESS,
  GET_COMMENT_BY_ID,
  RATE_LODGER,
  GET_EQUIPMENT_PROMOTION,
  GET_TEAM_EQUIPPER,
  GET_TEAM_LODGER,
  DELETE_MEMBER,
  EDIT_MEMBER,
  ADD_MEMBER,
  TOTAL_RENTED_EQUIPMENTS,
  PROMOTION,
  TOTAL_RENTED_INCOME,
  TOP_RENTED_EQUIPMENTS,
  TOP_RENTED_EQUIPMENTS_PER_LOCATION,
  TOP_LODGERS,
  HISTORY_OF_RENTED_EQUIPMENT,
  GET_EQUIPPER_INFO,
  GET_LODGER_INFO,
  GET_EQUIPPER_REQUESTS,
  GET_LODGER_REQUESTS,
  BOOK_EQUIPMENT,
  UPDATE_EQUIPPER_INFO,
  UPDATE_LODGER_INFO,
  GET_ALL_LODGERS,
  GET_PROJECT,
  PROJECT,
  SEND_RECLAMATION,
  GET_RENTAL_IN_SUMMARY,
  GET_EQUIPPER_BY_ID,
  GET_EQUIPPER_BY_EMAIL,
  IMG_BASE_URL,
  ADD_BIDZ_REQUEST,
  GET_BIDZ_OFFER_BY_RENTER_ID,
  GET_BIDZ_REQUEST_LODGER,
  GET_BIDZ_OFFER_BY_EQUIPPER_ID,
  UPDATE_BOOKING_STATUS,
  BIDZ_REQUEST,
  SET_BID_OFFER,
  TOOLER_BIDZ_INVENTORY,
  PUBLIC_TOOLER_BIDZ_INVENTORY,
  AIRTABLE_SYNC,
  EQUIPMENT,
  GET_SELECTION_LIST,
  EQUIPMENT_UPLOAD_IMAGE,
  GET_IP_INFO
};
