import * as Yup from 'yup';
import { ALL_NUMBER, PHONE_NUMBER_REGEX } from './Regex';
import { subDays } from 'rsuite/esm/utils/dateUtils';

const creditApp = [
  {
    type: 'text',
    field: 'name',
    is_required: true,
    label: 'Credit_check_form_title0'
  },
  {
    type: 'array',
    field: 'company',
    label: 'Company',
    children: [
      {
        type: 'text',
        field: 'company.company_name',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Company_name',
        is_required: true
      },
      {
        type: 'year',
        field: 'company.year_of_location',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Year_of_creation',
        is_required: true
      },
      {
        type: 'text',
        field: 'company.telephone',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Telephone',
        is_required: true
      },
      {
        type: 'text',
        field: 'company.fax',
        col: 'col-lg-3 col-6 ',
        label: 'Fax'
      },
      {
        type: 'text',
        field: 'company.company_president',
        label: 'President_name',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true
      },
      {
        type: 'email',
        field: 'company.company_president_email',
        label: 'President_email',
        is_required: true,
        col: 'col-lg-3 col-sm-6 '
      },
      {
        label: 'Address',
        field: 'company.address.address',
        col: 'col-lg-6 col-sm-6',
        is_required: true,
        placeholder: 'Address',
        type: 'text'
      },
      {
        label: 'Zip_code',
        field: 'company.address.zip_code',
        col: 'col-lg-2 col-sm-6 ',
        is_required: true,
        placeholder: 'Zip_code',
        type: 'text'
      },
      {
        label: '',
        type: 'geolocation',
        field: 'company.address',
        key: 'company',
        col: 'col-lg-7 col-sm-6 ',
        placeholder: ''
      },
      {
        type: 'number',
        field: 'company.number_of_employees',
        label: 'Number_of_employees',
        col: 'col-lg-3 col-sm-6 '
      },
      {
        type: 'checkbox',
        field: 'company.property_insurance',
        placeholder: 'Property_insurance_placeholder',
        is_required: false,
        col: 'col-lg-6 col-sm-6 ',
        label: 'Property_insurance'
      },
      {
        type: 'checkbox',
        field: 'company.tax_free_entity',
        placeholder: 'Tax_free_entity_placeholder',
        label: 'Credit_check_form_text4',
        col: 'col-lg-6 col-sm-6 ',
        is_required: false
      },
      {
        type: 'checkbox',
        field: 'company.required_project_number',
        placeholder: 'Required_project_number_placeholder',
        label: 'Required_project_number',
        col: 'col-lg-6 col-sm-6 ',
        is_required: false
      },
      {
        type: 'checkbox',
        field: 'company.required_order_number',
        col: 'col-lg-6 col-sm-6 ',
        label: 'Credit_check_form_text2',
        is_required: false
      }
    ]
  },
  {
    type: 'array',
    field: 'in_charge_of_rentals',
    label: 'In_charge_of_rentals',
    children: [
      {
        type: 'text',
        field: 'name',
        is_required: true,
        col: 'col-lg-3 col-sm-6 ',
        label: 'Name'
      },
      {
        type: 'email',
        field: 'email',
        col: 'col-lg-3 col-sm-6 ',

        is_required: true,
        label: 'Email'
      },
      {
        type: 'text',
        field: 'telephone',
        is_required: true,
        col: 'col-lg-3 col-sm-6 ',

        label: 'Telephone'
      },
      {
        type: 'text',
        field: 'cell',
        is_required: true,
        col: 'col-lg-3 col-sm-6 ',
        label: 'Cell'
      },
      {
        type: 'text',
        field: 'title',
        is_required: true,
        col: 'col-lg-3 col-sm-6 ',
        label: 'Title'
      },
      {
        type: 'text',
        field: 'ext',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Ext'
      }
    ],
    is_extandable: true,
    max: 10
  },
  {
    type: 'array',
    field: 'accounts_payable',
    label: 'Accounts_payable',
    children: [
      {
        type: 'text',
        field: 'name',
        label: 'Name',
        is_required: true,
        col: 'col-lg-3 col-sm-6 '
      },
      {
        type: 'text',
        field: 'title',
        label: 'Title',
        is_required: true,

        col: 'col-lg-3 col-sm-6 '
      },
      {
        type: 'text',
        field: 'cell',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Cell',
        is_required: true
      },
      {
        type: 'email',
        field: 'email',
        label: 'Email',
        is_required: true,
        col: 'col-lg-3 col-sm-6 '
      },
      {
        type: 'text',
        field: 'telephone',
        label: 'Telephone',
        is_required: true,

        col: 'col-lg-3 col-sm-6 '
      },
      {
        type: 'text',
        field: 'ext',
        col: 'col-lg-2 col-sm-6 ',
        label: 'Ext'
      }
    ],
    is_extandable: true,
    max: 10
  },
  {
    type: 'array',
    field: 'credit_references',
    label: 'Credit_references',
    description: 'Credit_check_approval',
    children: [
      {
        type: 'text',
        field: 'equipper',
        label: 'Supplier',
        col: 'col-lg-3 col-sm-6 '
      },
      {
        type: 'text',
        field: 'telephone',
        col: 'col-lg-3 col-sm-6 ',

        label: 'Telephone'
      },
      {
        type: 'email',
        field: 'email',
        col: 'col-lg-3 col-sm-6 ',

        label: 'Email'
      }
    ],
    is_extandable: true,
    max: 10
  },
  {
    type: 'array',
    field: 'bond',
    label: 'Credit_check_form_title5',
    children: [
      {
        type: 'text',
        field: 'bond.name',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,
        label: 'Name'
      },
      {
        type: 'text',
        field: 'bond.function',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,
        label: 'Function'
      },
      {
        type: 'email',
        field: 'bond.email',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,
        label: 'Email'
      },
      {
        type: 'text',
        field: 'bond.telephone',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,
        label: 'Telephone'
      },
      {
        type: 'text',
        field: 'bond.ext',
        col: 'col-lg-2 col-sm-6 ',
        label: 'Ext'
      },
      {
        type: 'text',
        field: 'bond.cell',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Cell',
        is_required: true
      },
      {
        type: 'text',
        field: 'bond.signature',
        col: 'col-lg-2 col-sm-6 ',
        is_required: true,
        label: 'Signature'
      },
      {
        type: 'date',
        field: 'bond.date',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Date',
        is_required: true
      },
      {
        type: 'checkbox',
        field: 'bond.has_other_accounts',
        placeholder: 'Has_other_accounts_placeholder',
        col: 'col-lg-6 col-8 ',
        is_required: false,
        label: 'Has_other_accounts'
      }
    ]
  },
  {
    type: 'array',
    field: 'attachments',
    label: 'Attachments',
    children: [
      {
        type: 'file',
        field: 'attachments.user_id',
        col: 'col-lg-3 col-sm-6 ',
        label: 'UserID'
      },
      {
        type: 'file',
        field: 'attachments.insurance',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Insurance'
      },
      {
        type: 'file',
        field: 'attachments.partnership_agreement',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Partnership_agreement'
      }
    ]
  }
];

const initialValuesCreditApp = {
  name: '',
  company: {
    company_name: '',
    year_of_location: new Date().getFullYear().toString(),
    address: {
      address: '',
      city: '',
      zip_code: '',
      state: '',
      country: ''
    },
    telephone: '',
    fax: '',
    number_of_employees: 0,
    tax_free_entity: '',
    company_president: '',
    company_president_email: '',
    property_insurance: '',
    required_project_number: '',
    required_order_number: false
  },
  in_charge_of_rentals: [
    {
      name: '',
      email: '',
      telephone: '',
      cell: '',
      title: '',
      ext: '',
      type: ''
    }
  ],
  accounts_payable: [
    {
      name: '',
      title: '',
      email: '',
      telephone: '',
      ext: '',
      cell: ''
    }
  ],
  bond: {
    function: '',
    signature: '',
    has_other_accounts: '',
    date: new Date(),
    name: '',
    telephone: '',
    ext: '',
    email: '',
    cell: ''
  },
  credit_references: [
    {
      equipper: '',
      telephone: '',
      email: ''
    }
  ],
  attachments: {
    user_id: '',
    partnership_agreement: '',
    insurance: ''
  }
};

const creditAppValidate = (t) =>
  Yup.object().shape({
    name: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .required(t('This_field_is_required')),
    company: Yup.object({
      company_name: Yup.string()
        .min(3, t('Company_Name_can_not_be_less_than_3_letters'))
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('This_field_is_required')),

      address: Yup.object({
        address: Yup.string()
          .min(5, t('Address_min_length'))
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
          .required(t('Address_required')),
        city: Yup.object().required(t('City_required')).nullable(),
        state: Yup.object().required(t('Prov_required')).nullable(),
        country: Yup.object().required(t('Country_required')).nullable(),
        zip_code: Yup.string()
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
          .required(t('Postal_code_required'))
      }),
      number_of_employees: Yup.number().min(
        0,
        t('Number_of_employees_must_be_positive')
      ),
      fax: Yup.string().matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number')),
      telephone: Yup.string()
        .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
        .required(t('Phone_number_required')),
      company_president: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('President_name_length')),
      company_president_email: Yup.string()
        .email(t('President_email_invalid'))
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('President_email_required')),
      tax_free_entity_reason: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .when('tax_free_entity', {
          is: true,
          then: Yup.string().required(t('This_field_is_required'))
        }),
      property_insurance_reason: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .when('property_insurance', {
          is: true,
          then: Yup.string().required(t('This_field_is_required'))
        }),
      required_project_number_reason: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .when('required_project_number', {
          is: true,
          then: Yup.string().required(t('This_field_is_required'))
        })
    }),

    credit_references: Yup.array().of(
      Yup.object({
        equipper: Yup.string()
          .min(3, t('Supplier_min_length'))
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),
        email: Yup.string().email(t('Invalid_email')),
        telephone: Yup.string().matches(
          PHONE_NUMBER_REGEX,
          t('Invalid_phone_number')
        )
      })
    ),
    in_charge_of_rentals: Yup.array().of(
      Yup.object().shape({
        name: Yup.string()
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
          .required(t('Name_required')),
        email: Yup.string()
          .email(t('Invalid_email'))
          .required(t('Email_required')),
        telephone: Yup.string()
          .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
          .required(t('Phone_number_required')),

        cell: Yup.string()
          .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
          .required(t('Phone_number_required')),
        ext: Yup.string().min(1, t('Ext_length')),
        title: Yup.string()
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
          .required(t('Title_required'))
      })
    ),
    accounts_payable: Yup.array().of(
      Yup.object().shape({
        name: Yup.string()
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
          .min(3, t('Name_must_be_at_least_3_characters'))
          .required(t('Name_required')),
        email: Yup.string()
          .email(t('Invalid_email'))
          .required(t('Email_required')),
        telephone: Yup.string()
          .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
          .required(t('Phone_number_required')),
        ext: Yup.string().min(1, t('Ext_length')),
        title: Yup.string()
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
          .required(t('Title_required')),
        cell: Yup.string()
          .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
          .required(t('Phone_number_required'))
      })
    ),
    bond: Yup.object().shape({
      name: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .min(3, t('Name_must_be_at_least_3_characters'))
        .required(t('Name_required')),
      email: Yup.string()
        .email(t('Invalid_email'))
        .required(t('Email_required')),
      telephone: Yup.string()
        .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
        .required(t('Phone_number_required')),
      cell: Yup.string()
        .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
        .required(t('Phone_number_required')),
      date: Yup.date().required(t('Date_required')),
      ext: Yup.string().min(1, t('Ext_length')),
      function: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('Function_required')),
      signature: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('Signature_required'))
    })
  });

const creditAppUSValidate = (t) =>
  Yup.object().shape({
    name: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .required(t('This_field_is_required')),
    applicant_info: Yup.object().shape({
      applicant_name: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('This_field_is_required')),
      SSN: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('This_field_is_required')),
      date_of_birth: Yup.string()
        .nullable()
        .test('valid-date', t('Invalid_birth_date'), (value) => {
          if (value) {
            const selectedDate = new Date(value);
            const currentDate = new Date();

            selectedDate.setHours(0, 0, 0, 0);
            currentDate.setHours(0, 0, 0, 0);

            const isDateValid = selectedDate < currentDate;

            return isDateValid;
          }

          return true;
        }),
      phone: Yup.string()
        .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
        .required(t('Phone_number_required')),
      expirationID_date: Yup.string()
        .nullable()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .test('valid-date', t('Invalid_expiration_date'), (value) => {
          if (value) {
            const selectedDate = new Date(value);
            const currentDate = new Date();
            selectedDate.setHours(0, 0, 0, 0);
            currentDate.setHours(0, 0, 0, 0);
            return selectedDate > currentDate;
          }
          return true;
        }),
      occupation: Yup.object().required(t('This_field_is_required')).nullable(),
      email: Yup.string()
        .email(t('Invalid_email'))
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('This_field_is_required')),
      userID: Yup.object().required(t('This_field_is_required')).nullable(),
      type: Yup.object().required(t('This_field_is_required')).nullable(),
      usage: Yup.object().nullable().required(t('This_field_is_required'))
    }),
    // second applicant object is required if second_applicant is true
    secondary_applicant_info: Yup.object().when(
      'applicant_info.second_applicant',
      {
        is: true,
        then: Yup.object().shape({
          applicant_name: Yup.string()
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
            .required(t('This_field_is_required')),
          SSN: Yup.string()
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
            .required(t('This_field_is_required')),
          date_of_birth: Yup.string()
            .nullable()
            .test('valid-date', t('Invalid_birth_date'), (value) => {
              if (value) {
                const selectedDate = new Date(value);
                const currentDate = new Date();

                selectedDate.setHours(0, 0, 0, 0);
                currentDate.setHours(0, 0, 0, 0);

                const isDateValid = selectedDate < currentDate;

                return isDateValid;
              }

              return true;
            }),
          phone: Yup.string()
            .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
            .required(t('Phone_number_required')),
          expirationID_date: Yup.string()
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
            .nullable()
            .test('valid-date', t('Invalid_expiration_date'), (value) => {
              if (value) {
                const selectedDate = new Date(value);
                const currentDate = new Date();
                selectedDate.setHours(0, 0, 0, 0);
                currentDate.setHours(0, 0, 0, 0);
                return selectedDate > currentDate;
              }
              return true;
            }),
          email: Yup.string()
            .email(t('Invalid_email'))
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
            .required(t('This_field_is_required')),
          userID: Yup.object().required(t('This_field_is_required')).nullable(),
          type: Yup.object().required(t('This_field_is_required')).nullable(),
          company_name: Yup.string()
            .min(3, t('Company_Name_can_not_be_less_than_3_letters'))
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
            .required(t('This_field_is_required')),
          address: Yup.object({
            address: Yup.string()
              .min(5, t('Address_min_length'))
              .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
              .required(t('Address_required')),
            city: Yup.object().required(t('City_required')).nullable(),
            state: Yup.object().required(t('Prov_required')).nullable(),
            country: Yup.object().required(t('Country_required')).nullable(),
            zip_code: Yup.string()
              .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
              .required(t('Postal_code_required'))
          }),
          telephone: Yup.string()
            .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
            .required(t('Phone_number_required')),
          tax_ID: Yup.string()
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
            .required(t('Tax_ID_required')),
          state_formed: Yup.string()
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
            .required(t('This_field_is_required')),
          role: Yup.object().required(t('This_field_is_required')).nullable(),
          signature: Yup.string()
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
            .required(t('Signature_required')),
          signature_date: Yup.date(),
          title: Yup.string()
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
            .required(t('Title_required'))
        })
      }
    ),
    company: Yup.object({
      company_name: Yup.string()
        .min(3, t('Company_Name_can_not_be_less_than_3_letters'))
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('This_field_is_required')),

      address: Yup.object({
        address: Yup.string()
          .min(5, t('Address_min_length'))
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
          .required(t('Address_required')),
        city: Yup.object().required(t('City_required')).nullable(),
        state: Yup.object().required(t('Prov_required')).nullable(),
        country: Yup.object().required(t('Country_required')).nullable(),
        zip_code: Yup.string()
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
          .required(t('Postal_code_required'))
      }),
      telephone: Yup.string()
        .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
        .required(t('Phone_number_required')),
      tax_ID: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('Tax_ID_required')),
      state_formed: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('This_field_is_required')),
      bank_name: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('This_field_is_required')),
      bank_contact: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('This_field_is_required')),
      bank_phone: Yup.string()
        .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
        .required(t('Phone_number_required'))
    }),
    credit_references: Yup.array().of(
      Yup.object({
        equipper: Yup.string()
          .min(3, t('Supplier_min_length'))
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
          .required(t('This_field_is_required')),
        email: Yup.string()
          .email(t('Invalid_email'))
          .required(t('This_field_is_required')),
        telephone: Yup.string()
          .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
          .required(t('Phone_number_required')),
        address: Yup.object({
          city: Yup.object().required(t('City_required')).nullable(),
          state: Yup.object().required(t('Prov_required')).nullable(),
          country: Yup.object().required(t('Country_required')).nullable()
        }),
        credit_address: Yup.string().required(t('This_field_is_required')),
        zip_code: Yup.string().required(t('This_field_is_required'))
      })
    ),
    accounts_payable: Yup.array().of(
      Yup.object().shape({
        name: Yup.string()
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
          .required(t('Name_required')),
        email: Yup.string()
          .email(t('Invalid_email'))
          .required(t('Email_required')),
        telephone: Yup.string()
          .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
          .required(t('Phone_number_required')),
        ext: Yup.string().min(1, t('Ext_length')),
        title: Yup.string()
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
          .required(t('Title_required')),
        address: Yup.object({
          city: Yup.object().required(t('This_field_is_required')).nullable(),
          state: Yup.object().required(t('This_field_is_required')).nullable(),
          country: Yup.object().required(t('This_field_is_required')).nullable()
        }),
        account_address: Yup.string().required(t('This_field_is_required')),
        zip_code: Yup.string().required(t('This_field_is_required'))
      })
    ),
    bond: Yup.object().shape({
      name: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('Name_required')),
      email: Yup.string()
        .email(t('Invalid_email'))
        .required(t('Email_required')),
      telephone: Yup.string()
        .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
        .required(t('Phone_number_required')),
      cell: Yup.string()
        .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
        .required(t('Phone_number_required')),
      ext: Yup.string().min(1, t('Ext_length')),

      function: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('Function_required')),
      signature: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('Signature_required'))
    }),
    // bankruptcy_equipment_info is required if bankruptcy is true
    bankruptcy_equipment_info: Yup.array().when('more_info.bankruptcy', {
      is: true,
      then: Yup.array().of(
        Yup.object().shape({
          year: Yup.string()
            .matches(ALL_NUMBER, t('Invalid_year'))
            .test('valid-year', t('Invalid_year'), (value) => {
              if (value) {
                const year = parseInt(value, 10);
                return year <= new Date().getFullYear();
              }
              return true;
            })
            .required(t('This_field_is_required')),
          hours: Yup.string()
            .matches(ALL_NUMBER, t('Invalid_hours'))
            .required(t('This_field_is_required')),
          serial: Yup.string()
            .required(t('This_field_is_required'))
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),

          salesPrice: Yup.string()
            .required(t('This_field_is_required'))
            .matches(ALL_NUMBER, t('Sales_price_must_be_greater_than_0'))
            .matches(/^(?!\s*$).+/, t('Space_error_msg')),

          condition: Yup.object()
            .required(t('This_field_is_required'))
            .nullable(),
          equipmentManufacturerDescription: Yup.string()
            .required(t('This_field_is_required'))
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),

          model: Yup.string()
            .required(t('This_field_is_required'))
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        })
      )
    }),

    bankruptcy_trade_info: Yup.array().when('more_info.bankruptcy', {
      is: true,
      then: Yup.array().of(
        Yup.object().shape({
          tradeInYear: Yup.string()
            .matches(ALL_NUMBER, t('Invalid_year'))
            .test('valid-year', t('Invalid_year'), (value) => {
              if (value) {
                const year = parseInt(value, 10);
                return year <= new Date().getFullYear();
              }
              return true;
            })
            .required(t('This_field_is_required')),
          tradeInHours: Yup.string()
            .matches(ALL_NUMBER, t('Invalid_hours'))
            .required(t('This_field_is_required'))
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),
          tradeInSerial: Yup.string()
            .required(t('This_field_is_required'))
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),
          tradeInAllowance: Yup.string()
          .matches(ALL_NUMBER, t('Value_must_be_a_positive_number'))
            .required(t('This_field_is_required'))
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),
          amountOwedOnTradeIn: Yup.string()
          .matches(ALL_NUMBER, t('Value_must_be_a_positive_number'))
            .required(t('This_field_is_required'))
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),
          netTradeIn: Yup.string()
          .matches(ALL_NUMBER, t('Value_must_be_a_positive_number'))
            .required(t('This_field_is_required'))
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),
          tradeInModel: Yup.string()
            .required(t('This_field_is_required'))
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),
          owedToAccountNumber: Yup.string()
          .matches(ALL_NUMBER, t('Value_must_be_a_positive_number'))
            .required(t('This_field_is_required'))
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        })
      )
    }),
    bankruptcy_cashDown_info: Yup.object().when('more_info.bankruptcy', {
      is: true,
      then: Yup.object().shape({
        cashDownProgramNumber: Yup.string()
          .required(t('This_field_is_required'))
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),
        cashDownProgramDescription: Yup.string()
          .required(t('This_field_is_required'))
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),
        cashDownEffectiveDate: Yup.string()
          .required(t('This_field_is_required'))
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),
        cashDownInterestStartDate: Yup.string()
          .required(t('This_field_is_required'))
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),
        cashDownFirstPaymentDate: Yup.string()
          .required(t('This_field_is_required'))
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),
        cashDownTerm: Yup.string()
        .matches(ALL_NUMBER, t('Value_must_be_a_positive_number'))
          .required(t('This_field_is_required'))
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),
        cashDownFrequency: Yup.string()
        .matches(ALL_NUMBER, t('Value_must_be_a_positive_number'))
          .required(t('This_field_is_required'))
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),
        estimatedAmountFinanced: Yup.string()
        .matches(ALL_NUMBER, t('Value_must_be_a_positive_number'))
          .required(t('This_field_is_required'))
          .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      })
    })
  });

const initialValuesCreditAppUS = {
  name: '',
  applicant_info: {
    applicant_name: '',
    SSN: '',
    date_of_birth: subDays(new Date(), 1),
    phone: '',
    userID: null,
    expirationID_date: subDays(new Date(), -1),
    occupation: null,
    email: '',
    type: null,
    usage: null,
    second_applicant: false
  },
  secondary_applicant_info: {
    applicant_name: '',
    SSN: '',
    date_of_birth: subDays(new Date(), 1),
    year_of_location: new Date().getFullYear().toString(),
    phone: '',
    userID: null,
    expirationID_date: subDays(new Date(), -1),
    email: '',
    type: null,
    role: null,
    company_name: '',
    address: {
      address: '',
      city: null,
      zip_code: '',
      state: null,
      country: null
    },
    telephone: '',
    state_formed: '',
    tax_ID: '',
    signature: '',
    title: '',
    signature_date: new Date()
  },

  more_info: {
    equipment_outside_us: '',
    payments_non_domestic_location: '',
    operations_outside_us: '',
    bankruptcy: ''
  },
  company: {
    company_name: '',
    year_of_location: new Date().getFullYear().toString(),
    address: {
      address: '',
      city: null,
      zip_code: '',
      state: null,
      country: null
    },
    telephone: '',
    state_formed: '',
    tax_ID: '',
    bank_name: '',
    bank_contact: '',
    bank_phone: ''
  },
  accounts_payable: [
    {
      name: '',
      title: '',
      email: '',
      telephone: '',
      account_address: '',
      zip_code: '',
      address: {
        city: null,
        state: null,
        country: null
      }
    }
  ],
  bond: {
    function: '',
    signature: '',
    date: new Date(),
    telephone: '',
    cell: '',
    email: '',
    name: ''
  },
  credit_references: [
    {
      equipper: '',
      telephone: '',
      email: '',
      credit_address: '',
      zip_code: '',
      address: {
        city: null,
        state: null,
        country: null
      }
    }
  ],
  bankruptcy_equipment_info: [
    {
      condition: '',
      year: new Date().getFullYear().toString(),
      equipmentManufacturerDescription: '',
      model: '',
      hours: '',
      serial: '',
      salesPrice: ''
    }
  ],
  bankruptcy_trade_info: [
    {
      tradeInYear: new Date().getFullYear().toString(),
      tradeInModel: '',
      tradeInHours: '',
      tradeInSerial: '',
      tradeInAllowance: '',
      amountOwedOnTradeIn: '',
      netTradeIn: '',
      owedToAccountNumber: ''
    }
  ],
  bankruptcy_cashDown_info: {
    cashDownProgramNumber: '',
    cashDownProgramDescription: '',
    cashDownEffectiveDate: new Date(),
    cashDownInterestStartDate: new Date(),
    cashDownFirstPaymentDate: new Date(),
    cashDownTerm: '',
    cashDownFrequency: '',
    estimatedAmountFinanced: ''
  },
  attachments: {
    user_id: '',
    second_applicant_id: '',
    partnership_agreement: '',
    insurance: ''
  }
};

const creditAppUS = [
  // Name
  {
    type: 'text',
    field: 'name',
    col: 'col-lg-12 ',
    is_required: true,
    label: 'Credit_check_form_title0'
  },
  // Applicant info
  {
    type: 'array',
    field: 'applicant_info',
    label: 'Applicant_info',
    children: [
      {
        type: 'text',
        field: 'applicant_info.applicant_name',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,
        label: 'Applicant_name'
      },

      {
        type: 'date',
        col: 'col-lg-3 col-sm-6 ',
        field: 'applicant_info.date_of_birth',
        is_required: true,
        label: 'Date_of_birth'
      },
      {
        type: 'text',
        field: 'applicant_info.phone',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,
        label: 'Telephone'
      },
      {
        type: 'email',
        field: 'applicant_info.email',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,
        label: 'Email'
      },

      {
        type: 'select',
        field: 'applicant_info.userID',
        col: 'col-lg-4 col-sm-6 ',
        is_required: true,
        label: 'User_ID',
        options: [
          { value: 'Driver’s License', label: 'Drivers_License' },
          { value: 'Passport', label: 'Passport' },
          {
            value: 'Other Government Issued ID ',
            label: 'Other_government_issued_ID'
          }
        ]
      },
      {
        type: 'date',
        field: 'applicant_info.expirationID_date',
        is_required: true,
        col: 'col-lg-3 col-sm-6 ',
        label: 'Expiration_ID_date'
      },

      {
        type: 'text',
        field: 'company.company_name',
        col: 'col-lg-5 col-sm-6 ',

        label: 'Company_name',
        is_required: true
      },
      {
        type: 'year',
        field: 'company.year_of_location',
        col: 'col-lg-3 col-sm-6 ',

        label: 'Year_of_creation',
        is_required: true
      },
      {
        type: 'select',
        field: 'applicant_info.occupation',
        col: 'col-lg-4 col-sm-6 ',
        is_required: true,
        label: 'Occupation',
        options: [
          { value: 'Full-time Farmer', label: 'Fulltime_farmer' },
          { value: 'Building Contractor', label: 'Building_contractor' },
          { value: 'Road & Street', label: 'Road_street' },
          { value: 'Rental Yard', label: 'Rental_yard' },
          { value: 'Part-time Farmer', label: 'Parttime_farmer' },
          { value: 'Excavating/Trenching', label: 'Excavating_trenching' },
          { value: 'Construction', label: 'Construction' },
          { value: 'Logging', label: 'Logging' },
          { value: 'Custom Operator', label: 'Custom_operator' },
          { value: 'Lawn & Landscape', label: 'Lawn_landscape' }
        ]
      },
      {
        type: 'select',
        col: 'col-lg-4 col-sm-6 ',
        field: 'applicant_info.type',
        is_required: true,
        options: [
          { value: 'Individual', label: 'Individual' },
          { value: 'Corporation', label: 'Corporation' },
          { value: 'Partnership', label: 'Partnership' },
          {
            value: 'Limited Liability Company',
            label: 'Limited_Liability_Company'
          },
          {
            value: 'Limited Liability Partnership',
            label: 'Limited_Liability_Partnership'
          },
          { value: 'Municipality', label: 'Municipality' }
        ],
        label: 'Type'
      },

      {
        type: 'select',
        field: 'applicant_info.usage',
        col: 'col-lg-4 col-sm-6 ',
        options: [
          { value: 'AG', label: 'AG' },
          { value: 'CE', label: 'CE' },
          {
            value: 'non AG business pruposes',
            label: 'Non_ag_business_purposes'
          }
        ],
        is_required: true,
        label: 'Usage'
      },
      {
        type: 'text',
        field: 'company.telephone',
        col: 'col-lg-3 col-sm-6 ',

        label: 'Telephone',
        is_required: true
      },
      {
        label: 'Address',
        is_required: true,
        field: 'company.address.address',
        col: 'col-lg-6 col-sm-6 ',
        placeholder: 'Address',
        type: 'text'
      },
      {
        label: 'Zip_code',
        is_required: true,
        field: 'company.address.zip_code',
        col: 'col-lg-2 col-sm-6 ',
        placeholder: 'Zip_code',
        type: 'text'
      },
      {
        label: '',
        type: 'geolocation',
        field: 'company.address',
        key: 'company',
        col: 'col-lg-8 col-sm-6 ',
        placeholder: ''
      },
      {
        type: 'text',
        field: 'applicant_info.SSN',
        col: 'col-lg-2 col-sm-6 ',
        is_required: true,
        label: 'SSN'
      },

      {
        label: 'State_formed',
        field: 'company.state_formed',
        is_required: true,
        col: 'col-lg-3 col-sm-6 ',
        placeholder: 'State_formed',
        type: 'text'
      },
      {
        label: 'Tax_ID',
        is_required: true,
        field: 'company.tax_ID',
        col: 'col-lg-3 col-sm-6 ',
        placeholder: 'Tax_ID',
        type: 'text'
      },

      {
        label: 'Bank_name',
        field: 'company.bank_name',
        is_required: true,
        col: 'col-lg-3 col-sm-6 ',
        placeholder: 'Bank_name',
        type: 'text'
      },
      {
        label: 'Bank_contact',
        field: 'company.bank_contact',
        is_required: true,
        col: 'col-lg-3 col-sm-6 ',
        placeholder: 'Bank_contact',
        type: 'text'
      },
      {
        label: 'Bank_phone',
        field: 'company.bank_phone',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,
        placeholder: 'Bank_phone',
        type: 'text'
      },
      {
        type: 'checkbox',
        field: 'applicant_info.second_applicant',
        col: 'col-lg-12 col-sm-6 ',
        placeholder: 'Second_applicant',
        label: 'Second_applicant'
      }
    ],
    is_extandable: false
  },
  // Secondary applicant info
  {
    type: 'array',
    field: 'secondary_applicant_info',
    label: 'Secondary_applicant_info',
    children: [
      {
        type: 'text',
        is_required: true,
        field: 'secondary_applicant_info.applicant_name',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Applicant_name'
      },

      {
        type: 'date',
        is_required: true,
        field: 'secondary_applicant_info.date_of_birth',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Date_of_birth'
      },
      {
        type: 'text',
        is_required: true,
        field: 'secondary_applicant_info.phone',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Telephone'
      },
      {
        type: 'text',
        field: 'secondary_applicant_info.email',
        is_required: true,
        col: 'col-lg-3 col-sm-6 ',
        label: 'Email'
      },
      {
        type: 'select',
        is_required: true,
        field: 'secondary_applicant_info.userID',
        col: 'col-lg-4 col-sm-6 ',
        options: [
          { value: 'Driver’s License', label: 'Drivers_License' },
          { value: 'Passport', label: 'Passport' },
          {
            value: 'Other Government Issued ID ',
            label: 'Other_government_issued_ID'
          }
        ],
        label: 'User_ID'
      },
      {
        type: 'date',
        is_required: true,
        field: 'secondary_applicant_info.expirationID_date',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Expiration_ID_date'
      },
      {
        type: 'text',
        is_required: true,
        field: 'secondary_applicant_info.company_name',
        col: 'col-lg-5 col-sm-6 ',
        label: 'Company_name'
      },
      {
        label: 'Year_of_creation',
        is_required: true,
        field: 'secondary_applicant_info.year_of_location',
        col: 'col-lg-3 col-sm-6 ',
        type: 'year'
      },
      {
        label: 'Telephone',
        is_required: true,
        col: 'col-lg-3 col-sm-6 ',
        field: 'secondary_applicant_info.telephone',
        placeholder: 'Telephone',
        type: 'text'
      },
      {
        label: 'Address',
        is_required: true,
        field: 'secondary_applicant_info.address.address',
        col: 'col-lg-6 col-sm-6 ',
        placeholder: 'Address',
        type: 'text'
      },
      {
        label: 'Zip_code',
        is_required: true,
        field: 'secondary_applicant_info.address.zip_code',
        col: 'col-lg-2 col-sm-6 ',
        placeholder: 'Zip_code',
        type: 'text'
      },
      {
        label: '',
        type: 'geolocation',
        key: 'secondary_applicant_info',
        field: 'secondary_applicant_info.address',
        col: 'col-lg-8 col-sm-6 ',
        placeholder: ''
      },
      {
        type: 'text',
        is_required: true,
        field: 'secondary_applicant_info.SSN',
        col: 'col-lg-2 col-sm-6 ',
        label: 'SSN'
      },
      {
        label: 'State_formed',
        is_required: true,
        field: 'secondary_applicant_info.state_formed',
        col: 'col-lg-2 col-sm-6 ',
        placeholder: 'State_formed',
        type: 'text'
      },
      {
        label: 'Tax_ID',
        is_required: true,
        field: 'secondary_applicant_info.tax_ID',
        col: 'col-lg-2 col-sm-6 ',
        placeholder: 'Tax_ID',
        type: 'text'
      },

      {
        type: 'select',
        is_required: true,
        field: 'secondary_applicant_info.type',
        col: 'col-lg-5 col-sm-6 ',
        label: 'Type',
        options: [
          { value: 'Individual', label: 'Individual' },
          { value: 'Corporation', label: 'Corporation' },
          { value: 'Partnership', label: 'Partnership' },
          {
            value: 'Limited Liability Company',
            label: 'Limited_Liability_Company'
          },
          {
            value: 'Limited Liability Partnership',
            label: 'Limited_Liability_Partnership'
          },
          { value: 'Municipality', label: 'Municipality' }
        ]
      },

      {
        type: 'select',
        is_required: true,
        field: 'secondary_applicant_info.role',
        col: 'col-lg-3 col-sm-6 ',
        options: [
          {
            value: 'co-applicant',
            label: 'Coapplicant'
          },
          {
            value: 'guarantor',
            label: 'Guarantor'
          },
          {
            value: 'officer',
            label: 'Officer'
          },
          {
            value: 'partner',
            label: 'Partner'
          }
        ],
        label: 'Role'
      },

      {
        label: 'Title',
        is_required: true,
        field: 'secondary_applicant_info.title',
        col: 'col-lg-3 col-sm-6 ',
        placeholder: 'Title',
        type: 'text'
      },
      {
        label: 'Signature',
        is_required: true,
        field: 'secondary_applicant_info.signature',
        col: 'col-lg-3 col-sm-6 ',
        placeholder: 'Signature',
        type: 'text'
      },
      {
        label: 'Date',
        is_required: true,
        field: 'secondary_applicant_info.signature_date',
        col: 'col-lg-3 col-sm-6 ',
        placeholder: 'Signature_date',
        type: 'date'
      }
    ],
    is_extandable: false
  },
  // More info
  {
    type: 'array',
    field: 'more_info',
    label: 'More_info',
    children: [
      {
        type: 'checkbox',
        field: 'more_info.equipment_outside_us',
        col: 'col-lg-4 col-sm-6 ',
        label: 'Equipment_outside_us'
      },
      {
        type: 'checkbox',
        field: 'more_info.payments_non_domestic_location',
        col: 'col-lg-8 col-sm-6 ',
        label: 'Payments_non_domestic_location'
      },
      {
        type: 'checkbox',
        field: 'more_info.operations_outside_us',
        col: 'col-lg-4 col-sm-6 ',
        label: 'Operations_outside_us'
      },
      {
        type: 'checkbox',
        field: 'more_info.bankruptcy',
        col: 'col-lg-8 col-sm-6 ',
        label: 'Bankruptcy'
      }
    ],
    is_extandable: false
  },
  // Bankruptcy_equipment_info
  {
    type: 'array',
    field: 'bankruptcy_equipment_info',
    label: 'Bankruptcy_equipment_info',
    children: [
      {
        type: 'select',
        field: 'condition',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,
        label: 'Condition',
        options: [
          { value: 'New', label: 'New' },
          { value: 'Used', label: 'Used' }
        ]
      },
      {
        type: 'year',
        field: 'year',
        is_required: true,
        col: 'col-lg-3 col-sm-6 ',
        label: 'Year'
      },
      {
        type: 'text',
        field: 'equipmentManufacturerDescription',
        is_required: true,
        col: 'col-lg-6 col-sm-6 ',
        label: 'EquipmentManufacturerDescription'
      },
      {
        type: 'text',
        field: 'model',
        is_required: true,

        col: 'col-lg-3 col-sm-6 ',
        label: 'Model'
      },
      {
        type: 'text',
        field: 'hours',
        is_required: true,

        col: 'col-lg-3 col-sm-6 ',
        label: 'Hours'
      },
      {
        type: 'text',
        field: 'serial',
        is_required: true,

        col: 'col-lg-3 col-sm-6 ',
        label: 'Serial'
      },
      {
        type: 'text',
        field: 'salesPrice',
        is_required: true,

        col: 'col-lg-3 col-sm-6 ',
        label: 'SalesPrice'
      }
    ],
    max: 3,
    is_extandable: true
  },
  // Bankruptcy_trade_info
  {
    type: 'array',
    field: 'bankruptcy_trade_info',
    label: 'bankruptcy_trade_info',
    children: [
      {
        type: 'year',
        field: 'tradeInYear',
        is_required: true,
        col: 'col-lg-3 col-sm-6 ',
        label: 'Trade_in_year'
      },
      {
        type: 'text',
        field: 'tradeInModel',
        is_required: true,

        col: 'col-lg-3 col-sm-6 ',
        label: 'Model'
      },
      {
        type: 'text',
        field: 'tradeInHours',
        is_required: true,

        col: 'col-lg-3 col-sm-6 ',
        label: 'TradeInHours'
      },
      {
        type: 'text',
        field: 'tradeInSerial',
        is_required: true,

        col: 'col-lg-3 col-sm-6 ',
        label: 'Serial'
      },
      {
        type: 'text',
        field: 'tradeInAllowance',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,

        label: 'Allowance'
      },
      {
        type: 'text',
        field: 'amountOwedOnTradeIn',
        is_required: true,

        col: 'col-lg-3 col-sm-6 ',

        label: 'Amount_owed_on_trade_in'
      },
      {
        type: 'text',
        field: 'netTradeIn',
        is_required: true,

        col: 'col-lg-3 col-sm-6 ',
        label: 'Net_trade_in'
      },
      {
        type: 'text',
        field: 'owedToAccountNumber',
        is_required: true,

        col: 'col-lg-3 col-sm-6 ',
        label: 'Owed_to_account_number'
      }
    ],
    is_extandable: true,
    max: 2
  },
  // Bankruptcy_cashDown_info
  {
    type: 'array',
    field: 'bankruptcy_cashDown_info',
    label: 'Bankruptcy_cashDown_info',
    children: [
      {
        type: 'text',
        field: 'bankruptcy_cashDown_info.cashDownProgramNumber',
        is_required: true,

        col: 'col-lg-6 col-sm-6 ',
        label: 'Cash_down_program_number'
      },
      {
        type: 'text',
        is_required: true,

        field: 'bankruptcy_cashDown_info.cashDownProgramDescription',
        col: 'col-lg-6 col-sm-6 ',
        label: 'Cash_down_program_description'
      },
      {
        type: 'date',
        is_required: true,

        field: 'bankruptcy_cashDown_info.cashDownEffectiveDate',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Cash_down_effective_date'
      },
      {
        type: 'date',
        is_required: true,

        field: 'bankruptcy_cashDown_info.cashDownInterestStartDate',
        col: 'col-lg-4 col-sm-6 ',
        label: 'Cash_down_interest_start_date'
      },
      {
        type: 'date',
        is_required: true,

        field: 'bankruptcy_cashDown_info.cashDownFirstPaymentDate',
        col: 'col-lg-4 col-sm-6 ',
        label: 'Cash_down_first_payment_date'
      },
      {
        type: 'text',
        field: 'bankruptcy_cashDown_info.cashDownTerm',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,

        label: 'Cash_down_term'
      },
      {
        type: 'text',
        field: 'bankruptcy_cashDown_info.cashDownFrequency',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,

        label: 'Cash_down_frequency'
      },
      {
        type: 'text',
        field: 'bankruptcy_cashDown_info.estimatedAmountFinanced',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,

        label: 'Estimated_amount_financed'
      }
    ],
    is_extandable: false
  },
  // Attachments
  {
    type: 'array',
    fiels: 'attachments',
    label: 'Attachments',
    children: [
      {
        type: 'file',
        field: 'attachments.user_id',
        col: 'col-lg-3 col-sm-6 ',
        label: 'User_ID'
      },
      {
        type: 'file',
        field: 'attachments.insurance',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Insurance'
      },
      {
        type: 'file',
        field: 'attachments.partnership_agreement',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Partnership_agreement'
      },
      {
        type: 'file',
        field: 'attachments.second_applicant_id',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Secondary_applicant_userID'
      }
    ]
  },
  // Bond
  {
    type: 'array',
    field: 'bond',
    label: 'Credit_check_form_title5',
    description: 'Terms_caf_us',
    children: [
      {
        type: 'text',
        field: 'bond.name',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,

        label: 'Name'
      },
      {
        type: 'text',
        field: 'bond.function',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,

        label: 'Function'
      },
      {
        type: 'email',
        field: 'bond.email',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,

        label: 'Email'
      },
      {
        type: 'text',
        field: 'bond.telephone',
        col: 'col-lg-3 col-sm-6 ',

        is_required: true,

        label: 'Telephone'
      },
      {
        type: 'text',
        field: 'bond.ext',
        col: 'col-lg-1 col-6 ',
        label: 'Ext'
      },
      {
        type: 'text',
        field: 'bond.cell',
        col: 'col-lg-3 col-sm-6 ',
        label: 'Cell',
        is_required: true
      },
      {
        type: 'text',
        field: 'bond.signature',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,
        label: 'Signature'
      },
      {
        type: 'date',
        field: 'bond.date',
        col: 'col-lg-2 col-sm-6 ',
        is_required: true,
        label: 'Date'
      }
    ],
    is_extandable: false
  },
  // Credit references
  {
    type: 'array',
    field: 'credit_references',
    label: 'Credit_references',
    children: [
      {
        type: 'text',
        field: 'equipper',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,
        label: 'Supplier'
      },
      {
        type: 'text',
        field: 'telephone',
        is_required: true,
        col: 'col-lg-3 col-sm-6 ',
        label: 'Telephone'
      },
      {
        type: 'email',
        field: 'email',
        is_required: true,
        col: 'col-lg-3 col-sm-6 ',
        label: 'Email'
      },
      {
        type: 'text',
        field: 'credit_address',
        is_required: true,
        col: 'col-lg-3 col-sm-6 ',
        label: 'Address'
      },
      {
        type: 'text',
        field: 'zip_code',
        is_required: true,
        col: 'col-lg-3 col-sm-6 ',
        label: 'Zip_code'
      },
      {
        type: 'geolocation',
        field: 'address',
        col: 'col-lg-8 col-sm-6 ',
        label: ''
      }
    ],
    is_extandable: true,
    max: 10
  },
  // Accounts payable
  {
    type: 'array',
    field: 'accounts_payable',
    label: 'Accounts_payable',
    children: [
      {
        type: 'text',
        field: 'name',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,
        label: 'Name'
      },
      {
        type: 'text',
        field: 'title',
        label: 'Title',
        is_required: true,
        col: 'col-lg-3 col-sm-6 '
      },
      {
        type: 'text',
        field: 'telephone',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,
        label: 'Telephone'
      },
      {
        type: 'email',
        field: 'email',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,
        label: 'Email'
      },
      {
        label: 'Address',
        field: 'account_address',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,
        placeholder: 'Address',
        type: 'text'
      },
      {
        label: 'Zip_code',
        field: 'zip_code',
        col: 'col-lg-3 col-sm-6 ',
        is_required: true,
        placeholder: 'Zip_code',
        type: 'text'
      },
      {
        label: '',
        type: 'geolocation',
        field: 'address',
        col: 'col-lg-6 col-sm-6 ',
        placeholder: ''
      }
    ],
    is_array: true
  }
];
const usDataStructure = (data) => {
  if (data) {
    return {
      ...data,
      applicant_info: {
        applicant_name: data?.us_credit_app_data?.applicant_name,
        SSN: data?.us_credit_app_data?.applicant_ssn,
        userID: {
          value: data?.us_credit_app_data?.applicant_ID,
          label: data?.us_credit_app_data?.applicant_ID
        },
        date_of_birth: data?.us_credit_app_data?.applicant_date_of_birth,
        occupation: {
          value: data?.us_credit_app_data?.occupation,
          label: data?.us_credit_app_data?.occupation
        },
        expirationID_date:
          data?.us_credit_app_data?.applicant_expirationID_date,
        email: data?.us_credit_app_data?.applicant_email,
        phone: data?.us_credit_app_data?.applicant_phone_number,
        usage: {
          value: data?.us_credit_app_data?.usage,
          label: data?.us_credit_app_data?.usage
        },
        type: {
          value: data?.us_credit_app_data?.type,
          label: data?.us_credit_app_data?.type
        },
        second_applicant: data?.us_credit_app_data?.secondary_applicant_name
      },
      secondary_applicant_info: {
        applicant_name: data?.us_credit_app_data?.secondary_applicant_name,
        SSN: data?.us_credit_app_data?.secondary_applicant_ssn,
        userID: data?.us_credit_app_data?.secondary_applicant_ID
          ? {
              value: data?.us_credit_app_data?.secondary_applicant_ID,
              label: data?.us_credit_app_data?.secondary_applicant_ID
            }
          : null,
        date_of_birth:
          data?.us_credit_app_data?.secondary_applicant_date_of_birth,
        expirationID_date:
          data?.us_credit_app_data?.secondary_applicant_expirationID_date,
        email: data?.us_credit_app_data?.secondary_applicant_email,
        phone: data?.us_credit_app_data?.secondary_applicant_phone_number,
        role: data?.us_credit_app_data?.secondary_applicant_role
          ? {
              value: data?.us_credit_app_data?.secondary_applicant_role,
              label: data?.us_credit_app_data?.secondary_applicant_role
            }
          : null,
        type: data?.us_credit_app_data?.secondary_applicant_type
          ? {
              value: data?.us_credit_app_data?.secondary_applicant_type,
              label: data?.us_credit_app_data?.secondary_applicant_type
            }
          : null,
        address: {
          address: data?.us_credit_app_data?.secondary_applicant_address,
          city: data?.us_credit_app_data?.secondary_applicant_city,
          zip_code: data?.us_credit_app_data?.secondary_applicant_zip_code,
          state: data?.us_credit_app_data?.secondary_applicant_state,
          country: data?.us_credit_app_data?.secondary_applicant_country
        },
        company_name: data?.us_credit_app_data?.secondary_applicant_company,
        year_of_location:
          data?.us_credit_app_data
            ?.secondary_applicant_company_year_of_location,
        state_formed: data?.us_credit_app_data?.secondary_state_formed,
        tax_ID: data?.us_credit_app_data?.secondary_applicant_tax_id,
        telephone: data?.us_credit_app_data?.secondary_applicant_phone,
        title: data?.us_credit_app_data?.secondary_applicant_title,
        signature_date:
          data?.us_credit_app_data?.secondary_applicant_signature_date,
        signature: data?.us_credit_app_data?.secondary_applicant_signature
      },

      company: {
        ...data?.company,
        bank_contact: data?.us_credit_app_data?.bank_contact,
        bank_name: data?.us_credit_app_data?.bank_name,
        bank_phone: data?.us_credit_app_data?.bank_phone,
        state_formed: data?.us_credit_app_data?.state_formed,
        tax_ID: data?.us_credit_app_data?.tax_id
      },
      accounts_payable: data?.accounts_payable?.map((item) => {
        return {
          ...item,
          address: {
            city: {
              label: item.address?.city,
              value: item.address?.city
            },
            state: {
              label: item.address?.state,
              value: item.address?.state
            },
            country: {
              label: item.address?.country,
              value: item.address?.country
            }
          },
          account_address: item.address?.address,
          zip_code: item.address?.zip_code
        };
      }),
      credit_references: data?.credit_references?.map((item) => {
        return {
          ...item,

          credit_address: item.address?.address,
          zip_code: item.address?.zip_code
        };
      }),
      more_info: {
        equipment_outside_us:
          data?.us_credit_app_data?.equipment_outside_us === 'Yes',
        payments_non_domestic_location:
          data?.us_credit_app_data?.payments_non_domestic_location === 'Yes',
        operations_outside_us:
          data?.us_credit_app_data?.operations_outside_us === 'Yes',
        bankruptcy: data?.us_credit_app_data?.bankruptcy === 'Yes'
      },
      bankruptcy_equipment_info: data?.bankruptcy_equipment_info
        ? data?.bankruptcy_equipment_info.map((item) => {
            return {
              ...item,
              condition: {
                value: item.condition,
                label: item.condition
              }
            };
          })
        : [
            {
              condition: null,
              year: new Date().getFullYear().toString(),
              equipmentManufacturerDescription: '',
              model: '',
              hours: '',
              serial: '',
              salesPrice: ''
            }
          ],
      bankruptcy_trade_info: data?.bankruptcy_trade_info
        ? data?.bankruptcy_trade_info
        : [
            {
              tradeInYear: new Date().getFullYear().toString(),
              tradeInModel: '',
              tradeInHours: '',
              tradeInSerial: '',
              tradeInAllowance: '',
              amountOwedOnTradeIn: '',
              netTradeIn: '',
              owedToAccountNumber: ''
            }
          ],
      bankruptcy_cashDown_info: data?.bankruptcy_cashDown_info
        ? data?.bankruptcy_cashDown_info
        : {
            cashDownProgramNumber: '',
            cashDownProgramDescription: '',
            cashDownEffectiveDate: '',
            cashDownInterestStartDate: '',
            cashDownFirstPaymentDate: '',
            cashDownTerm: '',
            cashDownFrequency: '',
            estimatedAmountFinanced: ''
          }
    };
  }
};

const dataStructure = (data) => {
  if (data) {
    return {
      ...data,
      accounts_payable: data?.accounts_payable?.map((item) => {
        return {
          ...item,
          account_address: item.address?.address,
          zip_code: item.address?.zip_code,
          address: {
            city: {
              label: item.address?.city,
              value: item.address?.city
            },
            state: {
              label: item.address?.state,
              value: item.address?.state
            },
            country: {
              label: item.address?.country,
              value: item.address?.country
            }
          }
        };
      }),
      credit_references: data?.credit_references?.map((item) => {
        return {
          ...item,
          credit_address: item.address?.address,
          zip_code: item.address?.zip_code
        };
      }),
      in_charge_of_rentals: data?.in_charge_of_rentals
        ? data?.in_charge_of_rentals?.map((item) => {
            return {
              ...item,
              account_address: item.address?.address,
              zip_code: item.address?.zip_code,
              name: item.name,
              address: {
                city: {
                  label: item.address?.city,
                  value: item.address?.city
                },
                state: {
                  label: item.address?.state,
                  value: item.address?.state
                },
                country: {
                  label: item.address?.country,
                  value: item.address?.country
                }
              }
            };
          })
        : [
            {
              name: '',
              title: '',
              email: '',
              telephone: '',
              cell: '',
              ext: '',
              type: ''
            }
          ],
      bond: {
        ...data.bond,
        has_other_accounts_reason:
          data?.bond?.has_other_accounts !== 'No'
            ? data?.bond?.has_other_accounts
            : ''
      },

      company: {
        ...data.company,
        required_project_number_reason:
          data?.company?.required_project_number !== 'No'
            ? data?.company?.required_project_number
            : '',
        property_insurance_reason:
          data?.company?.property_insurance !== 'No'
            ? data?.company?.property_insurance
            : '',
        tax_free_entity_reason:
          data?.company?.tax_free_entity !== 'No'
            ? data?.company?.tax_free_entity
            : ''
      }
    };
  }
};

const prepareAddress = (address) => ({
  ...address,
  city: address?.city?.value,
  state: address?.state?.value,
  country: address?.country?.value
});

const prepareApplicantInfo = (values) => ({
  bank_contact: values?.company.bank_contact,
  bank_name: values?.company.bank_name,
  bank_phone: values?.company.bank_phone,
  state_formed: values?.company.state_formed,
  usage: values?.applicant_info?.usage?.value,
  type: values?.applicant_info?.type?.value,
  occupation: values?.applicant_info?.occupation?.value,
  tax_id: values?.company.tax_ID,
  applicant_ssn: values?.applicant_info?.SSN,
  applicant_email: values?.applicant_info?.email,
  applicant_phone_number: values?.applicant_info?.phone,
  applicant_date_of_birth: values?.applicant_info?.date_of_birth,
  applicant_ID: values?.applicant_info?.userID?.value,
  applicant_name: values?.applicant_info?.applicant_name,
  applicant_expirationID_date: values?.applicant_info?.expirationID_date,
  userIDLink: values?.applicant_info?.userIDLink,
  second_applicant: values?.secondary_applicant_info?.state_formed
});

const prepareSecondaryApplicantInfo = (values) => ({
  secondary_state_formed: values?.secondary_applicant_info?.state_formed,
  secondary_applicant_type: values?.secondary_applicant_info?.type?.value,
  secondary_applicant_tax_id: values?.secondary_applicant_info?.tax_ID,
  secondary_applicant_ssn: values?.secondary_applicant_info?.SSN,
  secondary_applicant_email: values?.secondary_applicant_info?.email,
  secondary_applicant_phone_number: values?.secondary_applicant_info?.phone,
  secondary_applicant_date_of_birth:
    values?.secondary_applicant_info?.date_of_birth,
  secondary_applicant_ID: values?.secondary_applicant_info?.userID?.value,
  secondary_applicant_name: values?.secondary_applicant_info?.applicant_name,
  secondary_applicant_expirationID_date:
    values?.secondary_applicant_info?.expirationID_date,
  secondary_userIDLink: values?.secondary_applicant_info?.userIDLink,
  secondary_applicant_phone: values?.secondary_applicant_info?.phone,
  secondary_applicant_city:
    values?.secondary_applicant_info?.address?.city?.value,
  secondary_applicant_state:
    values?.secondary_applicant_info?.address?.state?.value,
  secondary_applicant_country:
    values?.secondary_applicant_info?.address?.country?.value,
  secondary_applicant_address:
    values?.secondary_applicant_info?.address?.address,
  secondary_applicant_zip_code:
    values?.secondary_applicant_info?.address?.zip_code,
  secondary_applicant_role: values?.secondary_applicant_info?.role?.value,
  secondary_applicant_company: values?.secondary_applicant_info?.company_name,
  secondary_applicant_company_phone_number:
    values?.secondary_applicant_info?.company_phone_number,
  secondary_applicant_company_year_of_location:
    values?.secondary_applicant_info?.year_of_location?.toString(),
  secondary_applicant_signature: values?.secondary_applicant_info?.signature,
  secondary_applicant_signature_date: new Date(),
  secondary_applicant_title: values?.secondary_applicant_info?.title
});

function getNestedValue(obj, path) {
  const keys = path.split('.');
  let value = obj;

  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key];
    }
    if (value && typeof value === 'object' && key.includes('[')) {
      const index = extractNumberFromString(key);
      value = value[key.split('[')[0]][index];
    }
  }

  return value || null;
}
function extractNumberFromString(inputString) {
  const match = inputString.match(/\d+/);
  return match ? parseInt(match[0], 10) : '';
}
function getGeoLocationInitialValues(formik, individualConfig) {
  const fieldName = individualConfig?.field;
  const index = extractNumberFromString(fieldName);
  const addressData =
    index !== ''
      ? formik?.values[individualConfig.key][index].address
      : formik?.values[individualConfig.key]?.address;
  return {
    city: addressData?.city || '',
    state: addressData?.state || '',
    country: addressData?.country || '',
    zip_code: addressData?.zip_code || '',
    address: addressData?.address || ''
  };
}

function creditApplication(isUs, t) {
  if (isUs) {
    return {
      initialValues: initialValuesCreditAppUS,
      validationSchema: creditAppUSValidate(t),
      formStructure: creditAppUS,
      dataStructure: usDataStructure
    };
  }
  return {
    initialValues: initialValuesCreditApp,
    validationSchema: creditAppValidate(t),
    formStructure: creditApp,
    dataStructure: dataStructure
  };
}

const prepareBankruptcyInfo = (values) => ({
  bankruptcy: values?.more_info?.bankruptcy ? 'Yes' : 'No',
  equipment_outside_us: values?.more_info?.equipment_outside_us ? 'Yes' : 'No',
  operations_outside_us: values?.more_info?.operations_outside_us
    ? 'Yes'
    : 'No',
  payments_non_domestic_location: values?.more_info
    ?.payments_non_domestic_location
    ? 'Yes'
    : 'No'
});

export {
  prepareAddress,
  prepareApplicantInfo,
  prepareBankruptcyInfo,
  getNestedValue,
  prepareSecondaryApplicantInfo,
  getGeoLocationInitialValues,
  creditApplication
};
