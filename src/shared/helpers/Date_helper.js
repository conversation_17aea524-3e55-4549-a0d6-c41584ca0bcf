export default function formatDate(date) {
  if (!date) {
    return new Date();
  }
  if (typeof date.getDate !== 'function') {
    date = new Date(parseInt(date));
  }
  const day = `${date.getDate()}`;
  const month = `${date.getMonth() + 1}`;
  const year = `${date.getFullYear()}`;
  date = `${month}/${day}/${year}`;
  return date;
}
export function formatDateBIDZ(date) {
  if (typeof date.getDate !== 'function') {
    date = new Date(parseInt(date));
  }
  const day = `${date.getDate()}`;
  const month = `${date.getMonth() + 1}`;
  const year = `${date.getFullYear()}`;
  date = `${day}/${month}/${year}`;
  return date;
}
export function isDisabled(startDate, endDate) {
  if (
    new Date(startDate)?.setHours(0, 0, 0, 0) >
    new Date(endDate)?.setHours(0, 0, 0, 0)
  ) {
    return true;
  }
  return false;
}

export const convertDate = (date) => {
  const day = `${date.getDate()}`;
  const month = `${date.getMonth() + 1}`;
  const year = `${date.getFullYear()}`;
  return `${day}/${month}/${year}`;
};
