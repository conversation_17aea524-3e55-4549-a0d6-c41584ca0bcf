export function UncheckCheckbox() {
  document
    .querySelectorAll('input[type=checkbox]')
    .forEach(el => (el.checked = false));
}
export function checkAllByName(name, value) {
  document
    .querySelectorAll(`input[name=${name}]`)
    .forEach(el => (el.checked = value));
}
export function checkOneById(id, value) {
  document
    .querySelectorAll(`input[id=${id}]`)
    .forEach(el => (el.checked = value));
}


