const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#/?$%^&*])(?=.{8,})/;
const PHONE_NUMBER_REGEX = /^\s*(?:\+?(\d{1,3}))?[-. (]*(\d{3})[-. )]*(\d{3})[-. ]*(\d{4})(?: *x(\d+))?\s*$/;
const PHONE_NUMBER_DISPLAY_REGEX = /^(\d{3})(\d{3})(\d{4})$/;
const SPECIAL_CHAR = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/;
const ALL_NUMBER = /^[0-9]*$/;
const DATE_REGEX = /^(0[1-9]|[12][0-9]|3[01])[- /.](0[1-9]|1[012])[- /.](19|20)\d\d$/;
export {
  PASSWORD_REGEX,
  PHONE_NUMBER_REGEX,
  SPECIAL_CHAR,
  PHONE_NUMBER_DISPLAY_REGEX,
  ALL_NUMBER,
  DATE_REGEX
};
