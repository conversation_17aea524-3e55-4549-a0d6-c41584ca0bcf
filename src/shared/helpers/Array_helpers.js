export function groupBy(values, key) {
  const result = {};
  values
    && values.length
    && values.forEach((value) => {
      if (!result[value[key]]) {
        result[value[key]] = [];
      }
      result[value[key]].push(value);
    });
  return result;
}
// remove redundancy from the array of users
export const removeRedundancy = (array) => {
  const newArray = [];
  array.forEach((item) => {
    if (newArray.findIndex(i => i.email === item.email) === -1) {
      newArray.push(item);
    }
  });
  return newArray;
};
export const removeOwnerFromList = (memberOptions, id) => {
  const options = [];
  for (let i = 0; i < memberOptions.length; i++) {
    if (memberOptions[i].value !== id) {
      options.push(memberOptions[i]);
    }
  }
  return options;
};
export const extractFromList = (array) => {
  const items = array.map(item => item.value);
  return items;
};
export const groupByKey = (list, keyGetter, objectIDSetter) => {
  if (objectIDSetter && list.length > 0) {
    objectIDSetter(list[0].objectID);
  }
  const map = new Map();
  list.forEach((item) => {
    const key = keyGetter(item);
    const collection = map.get(key);
    if (!collection) {
      map.set(key, [item]);
    } else {
      collection.push(item);
    }
  });
  const equipper_list = Array.from(map, ([key, value]) => ({
    equipper_id: key,
    equipment_list: value
  }));
  return equipper_list;
};


// array to string conversion separated by comma
export function arrayToString(array) {
  if (!array || array.length === 0) {
    return '';
  }
  return array.join(', ');
}

export function groupByEquipmentName(data) {
  const dataList = data?.reduce((r, a) => {
    r[a.equipment_name] = [...(r[a.equipment_name] || []), a];
    return {
      ...r,
      dataLength: data?.length
    };
  }, {});
  return dataList;
}
