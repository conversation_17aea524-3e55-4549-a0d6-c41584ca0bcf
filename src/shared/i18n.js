import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import Backend from 'i18next-http-backend';
// TEMPORARILY DISABLED - Language Detector Import
// import LanguageDetector from 'i18next-browser-languagedetector';
import { TR_EN } from './i18n/en/translation';
import { TR_AR } from './i18n/ar/translation';
import { TR_FR } from './i18n/fr/translation';
import { setCookies } from './helpers/Cookies';
// don't want to use this?
// have a look at the Quick start guide
// for passing in lng and translations on init

i18n
  // load translation using http -> see /public/locales (i.e. https://github.com/i18next/react-i18next/tree/master/example/react/public/locales)
  // learn more: https://github.com/i18next/i18next-http-backend
  // want your translations to be loaded from a professional CDN? => https://github.com/locize/react-tutorial#step-2---use-the-locize-cdn
  .use(Backend)
  // TEMPORARILY DISABLED - Language Detection
  // TODO: Re-enable when full internationalization is properly configured
  // detect user language
  // learn more: https://github.com/i18next/i18next-browser-languageDetector
  // .use(LanguageDetector)
  // pass the i18n instance to react-i18next.
  .use(initReactI18next)
  // init i18next
  // for all options read: https://www.i18next.com/overview/configuration-options
  .init({
    lng: 'en', // Default language
    fallbackLng: 'en', // Fallback to English
    debug: false,
    detection: {
      order: ['cookie', 'htmlTag', 'sessionStorage'],
      caches: ['cookie']
    },
    interpolation: {
      escapeValue: false // not needed for react as it escapes by default
    },
    resources: {
      en: {
        translation: TR_EN
      },
      fr: {
        translation: TR_FR
      },
      ar: {
        translation: TR_AR
      }
    },
    react: {
      useSuspense: false
    }
  });

export default i18n;
export function changeLanguage(lng) {
  setCookies('lang', lng);
  return i18n.changeLanguage(lng);
}
