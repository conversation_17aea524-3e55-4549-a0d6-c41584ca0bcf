/**
 * Google Analytics Verification Utility
 * This script helps verify that Google Analytics is properly integrated and working
 */

export class AnalyticsVerification {
  constructor() {
    this.results = [];
    this.isProduction = import.meta.env.VITE_ENV === 'production';
    this.googleAnalyticsId = import.meta.env.VITE_GOOGLE_ANALYTICS_ID;
  }

  log(message, type = 'info', data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      type,
      message,
      data
    };
    
    this.results.push(logEntry);
    
    const style = {
      info: 'color: #2196F3',
      success: 'color: #4CAF50; font-weight: bold',
      warning: 'color: #FF9800; font-weight: bold',
      error: 'color: #F44336; font-weight: bold'
    };
    
    console.log(`%c[GA Verification] ${message}`, style[type], data || '');
  }

  // Check if Google Analytics script is loaded
  checkScriptLoading() {
    this.log('🔍 Checking Google Analytics script loading...');
    
    const gaScript = document.querySelector(`script[src*="googletagmanager.com/gtag/js?id=${this.googleAnalyticsId}"]`);
    
    if (gaScript) {
      this.log('✅ Google Analytics script found in DOM', 'success', {
        src: gaScript.src,
        async: gaScript.async
      });
      return true;
    } else {
      this.log('❌ Google Analytics script not found in DOM', 'error');
      return false;
    }
  }

  // Check if gtag function is available
  checkGtagFunction() {
    this.log('🔍 Checking gtag function availability...');
    
    if (typeof window.gtag === 'function') {
      this.log('✅ gtag function is available', 'success');
      return true;
    } else {
      this.log('❌ gtag function is not available', 'error');
      return false;
    }
  }

  // Check if dataLayer is initialized
  checkDataLayer() {
    this.log('🔍 Checking dataLayer initialization...');
    
    if (Array.isArray(window.dataLayer)) {
      this.log('✅ dataLayer is initialized', 'success', {
        length: window.dataLayer.length,
        contents: window.dataLayer.slice(0, 5) // Show first 5 entries
      });
      return true;
    } else {
      this.log('❌ dataLayer is not initialized', 'error');
      return false;
    }
  }

  // Check environment configuration
  checkEnvironmentConfig() {
    this.log('🔍 Checking environment configuration...');
    
    this.log(`Environment: ${this.isProduction ? 'Production' : 'Development'}`, 'info');
    this.log(`Google Analytics ID: ${this.googleAnalyticsId || 'Not set'}`, 'info');
    
    if (this.isProduction && this.googleAnalyticsId) {
      this.log('✅ Production environment with GA ID configured', 'success');
      return true;
    } else if (!this.isProduction) {
      this.log('⚠️ Development environment - GA should not load', 'warning');
      return true;
    } else {
      this.log('❌ Production environment but GA ID not configured', 'error');
      return false;
    }
  }

  // Test page view tracking
  testPageViewTracking() {
    this.log('🔍 Testing page view tracking...');
    
    if (!this.isProduction || !window.gtag) {
      this.log('⚠️ Skipping page view test (not production or gtag unavailable)', 'warning');
      return true;
    }

    try {
      // Simulate a page view
      window.gtag('config', this.googleAnalyticsId, {
        page_path: '/test-verification',
        page_title: 'Analytics Verification Test',
        page_location: window.location.origin + '/test-verification'
      });
      
      this.log('✅ Page view tracking test completed', 'success');
      return true;
    } catch (error) {
      this.log('❌ Page view tracking test failed', 'error', error.message);
      return false;
    }
  }

  // Check for PostHog compatibility
  checkPostHogCompatibility() {
    this.log('🔍 Checking PostHog compatibility...');
    
    const hasPostHog = typeof window.posthog !== 'undefined';
    const hasGtag = typeof window.gtag !== 'undefined';
    
    if (hasPostHog && hasGtag) {
      this.log('✅ Both PostHog and Google Analytics are loaded', 'success');
    } else if (hasPostHog) {
      this.log('✅ PostHog is loaded, GA not loaded (expected in dev)', 'success');
    } else if (hasGtag) {
      this.log('✅ Google Analytics is loaded, PostHog not loaded', 'success');
    } else {
      this.log('⚠️ Neither PostHog nor Google Analytics detected', 'warning');
    }
    
    return true;
  }

  // Run comprehensive verification
  async runFullVerification() {
    this.log('🚀 Starting comprehensive Google Analytics verification...', 'info');
    this.results = []; // Clear previous results
    
    const checks = [
      { name: 'Environment Config', fn: () => this.checkEnvironmentConfig() },
      { name: 'Script Loading', fn: () => this.checkScriptLoading() },
      { name: 'gtag Function', fn: () => this.checkGtagFunction() },
      { name: 'DataLayer', fn: () => this.checkDataLayer() },
      { name: 'Page View Tracking', fn: () => this.testPageViewTracking() },
      { name: 'PostHog Compatibility', fn: () => this.checkPostHogCompatibility() }
    ];
    
    const results = {};
    let passedChecks = 0;
    
    for (const check of checks) {
      try {
        results[check.name] = check.fn();
        if (results[check.name]) passedChecks++;
      } catch (error) {
        this.log(`❌ ${check.name} check failed with error`, 'error', error.message);
        results[check.name] = false;
      }
    }
    
    const summary = {
      totalChecks: checks.length,
      passedChecks,
      failedChecks: checks.length - passedChecks,
      successRate: Math.round((passedChecks / checks.length) * 100),
      environment: this.isProduction ? 'production' : 'development',
      googleAnalyticsId: this.googleAnalyticsId,
      results
    };
    
    this.log(`📊 Verification Summary: ${passedChecks}/${checks.length} checks passed (${summary.successRate}%)`, 
      summary.successRate >= 80 ? 'success' : 'warning', summary);
    
    return summary;
  }

  // Get verification results
  getResults() {
    return this.results;
  }

  // Export results as JSON
  exportResults() {
    const data = {
      timestamp: new Date().toISOString(),
      environment: this.isProduction ? 'production' : 'development',
      googleAnalyticsId: this.googleAnalyticsId,
      results: this.results
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ga-verification-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    this.log('📁 Verification results exported', 'success');
  }
}

// Global verification instance
export const analyticsVerification = new AnalyticsVerification();

// Make it available globally for console testing
if (typeof window !== 'undefined') {
  window.analyticsVerification = analyticsVerification;
}
