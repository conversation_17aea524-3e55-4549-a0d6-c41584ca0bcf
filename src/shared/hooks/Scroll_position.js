import { useState, useEffect } from 'react';

export const useScrollPosition = (isHome) => {
  const [scrollPosition, setScrollPosition] = useState(0);

  useEffect(() => {
    if (!isHome) return;
    const updatePosition = () => {
      setScrollPosition(window.pageYOffset);
    };
    window.addEventListener('scroll', updatePosition);
    updatePosition();
    return () => window.removeEventListener('scroll', updatePosition);
  }, []);

  return scrollPosition;
};
