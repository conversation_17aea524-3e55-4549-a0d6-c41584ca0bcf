import React, { useContext } from 'react';
import AxiosFactory, { METHOD_POST } from '../helpers/Context_helpers';
import { SEND_RECLAMATION } from '../helpers/Url_constants';

const ReclamationContext = React.createContext();
export function useReclamation() {
  return useContext(ReclamationContext);
}
export default function ReclamationProvider({ children }) {
  async function Reclamation(user) {
    return await AxiosFactory({
      url: SEND_RECLAMATION,
      method: METHOD_POST,
      data: user
    });
  }
  const value = {
    Reclamation
  };
  return (
    <ReclamationContext.Provider value={value}>
      {children}
    </ReclamationContext.Provider>
  );
}
