import React, { useContext } from 'react';
import AxiosFactory, {
  METHOD_GET,
  METHOD_POST
} from '../helpers/Context_helpers';
import {
  GET_LODGER_REQUESTS,
  GET_EQUIPPER_REQUESTS,
  BOOK_EQUIPMENT
} from '../helpers/Url_constants';

const RequestContext = React.createContext();

export function useRequest() {
  return useContext(RequestContext);
}

export default function RequestProvider({ children }) {
  async function GetEquipperRequests(itemsPerPage, status, lastId) {
    const url = lastId
      ? `${GET_EQUIPPER_REQUESTS
      }?limit=${itemsPerPage}&status=${status}&last_id=${lastId}`
      : `${GET_EQUIPPER_REQUESTS}?limit=${itemsPerPage}&status=${status}`;
    return await AxiosFactory({
      url,
      method: METHOD_GET
    });
  }
  async function GetLodgerRequests(itemsPerPage, status, lastId) {
    const url = lastId
      ? `${GET_LODGER_REQUESTS
      }?limit=${itemsPerPage}&status=${status}&last_id=${lastId}`
      : `${GET_LODGER_REQUESTS}?limit=${itemsPerPage}&status=${status}`;
    return await AxiosFactory({
      url,
      method: METHOD_GET
    });
  }

  async function BookEquipment(data) {
    return await AxiosFactory({
      url: BOOK_EQUIPMENT,
      method: METHOD_POST,
      data
    });
  }

  async function AcceptRequest(requestID, specialPrice) {
    return await AxiosFactory({
      url: `${BOOK_EQUIPMENT}/${requestID}/accept`,
      method: METHOD_POST,
      data: specialPrice
    });
  }

  async function RejectRequest(requestID) {
    return await AxiosFactory({
      url: `${BOOK_EQUIPMENT}/${requestID}/reject`,
      method: METHOD_POST
    });
  }

  async function CancelRequest(requestID, comment) {
    return await AxiosFactory({
      url: `${BOOK_EQUIPMENT}/${requestID}/cancel`,
      method: METHOD_POST,
      data: comment
    });
  }

  const value = {
    GetLodgerRequests,
    GetEquipperRequests,
    BookEquipment,
    AcceptRequest,
    RejectRequest,
    CancelRequest
  };

  return (
    <RequestContext.Provider value={value}>{children}</RequestContext.Provider>
  );
}
