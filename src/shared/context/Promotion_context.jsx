import React, { createContext, useContext } from 'react';
import AxiosFactory, {
  METHOD_DELETE,
  METHOD_GET,
  METHOD_POST
} from '../helpers/Context_helpers';
import { PROMOTION } from '../helpers/Url_constants';

const PromotionsContext = createContext();
export function usePromotions() {
  return useContext(PromotionsContext);
}
export default function PromotionProvider({ children }) {
  async function GetPromotions() {
    return await AxiosFactory({ url: PROMOTION, method: METHOD_GET });
  }
  async function CreatePromotion(project) {
    return await AxiosFactory({
      url: PROMOTION,
      method: METHOD_POST,
      data: project
    });
  }


  async function DeletePromotion(code) {
    return await AxiosFactory({
      url: `${PROMOTION}/${code}`,
      method: METHOD_DELETE
    });
  }

  async function GetPromotionByEquipperAndLodgerID(equipper_id) {
    return await AxiosFactory({
      url: `${PROMOTION}/${equipper_id}`,
      method: METHOD_GET
    });
  }
  const value = {
    GetPromotions,
    CreatePromotion,
    GetPromotionByEquipperAndLodgerID,
    DeletePromotion
  };

  return (
    <PromotionsContext.Provider value={value}>
      {children}
    </PromotionsContext.Provider>
  );
}
