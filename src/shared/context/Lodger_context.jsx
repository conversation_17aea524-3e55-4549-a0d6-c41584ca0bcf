import React, { useContext } from 'react';
import AxiosFactory, {
  METHOD_GET,
  METHOD_POST,
  METHOD_PUT
} from '../helpers/Context_helpers';
import {
  GET_ALL_LODGERS,
  GET_LODGER_INFO,
  UPDATE_LODGER_INFO,
  UPLOAD_PROFILE_PHOTO_LODGER
} from '../helpers/Url_constants';

const LodgerContext = React.createContext();

export function useLodger() {
  return useContext(LodgerContext);
}
export default function LodgerProvider({ children }) {
  async function GetLodgerPersonalInfo() {
    return await AxiosFactory({
      url: GET_LODGER_INFO,
      method: METHOD_GET
    });
  }
  async function UploadLodgerPhoto(data) {
    return await AxiosFactory({
      url: UPLOAD_PROFILE_PHOTO_LODGER,
      method: METHOD_POST,
      data
    });
  }
  async function UpdateLodgerPersonalInfo(data) {
    return await AxiosFactory({
      url: UPDATE_LODGER_INFO,
      method: METHOD_PUT,
      data
    });
  }
  async function getAllLodger() {
    return await AxiosFactory({
      url: GET_ALL_LODGERS,
      method: METHOD_GET
    });
  }
  async function getAllLodgerByCountry(country) {
    return await AxiosFactory({
      url: `/lodger/all/${country}`,
      method: METHOD_GET
    });
  }
  async function getLodgerById(id) {
    return await AxiosFactory({
      url: `/lodger/${id}`,
      method: METHOD_GET
    });
  }
  async function existByEmail(email) {
    return await AxiosFactory({
      url: `/public/exist/${email}`,
      method: METHOD_GET
    });
  }
  const value = {
    GetLodgerPersonalInfo,
    UpdateLodgerPersonalInfo,
    UploadLodgerPhoto,
    getLodgerById,
    existByEmail,
    getAllLodger,
    getAllLodgerByCountry
  };

  return (
    <LodgerContext.Provider value={value}>{children}</LodgerContext.Provider>
  );
}
