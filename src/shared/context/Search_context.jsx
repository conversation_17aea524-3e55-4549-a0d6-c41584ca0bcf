import React, { useContext } from 'react';
import AxiosFactory, { METHOD_GET } from '../helpers/Context_helpers';
import {
  GET_EQUIPPER_BY_EMAIL,
  GET_EQUIPPER_BY_ID,
  GET_EQUIPPER_BY_USER_NAME
} from '../helpers/Url_constants';

export const SearchContext = React.createContext();
export function useSearchContext() {
  return useContext(SearchContext);
}
export default function SearchProvider(props) {
  function getEquipperById(id) {
    return AxiosFactory({
      url: GET_EQUIPPER_BY_ID + id,
      method: METHOD_GET
    });
  }
  function getEquipperByUserName(userName) {
    return AxiosFactory({
      url: GET_EQUIPPER_BY_USER_NAME + userName,
      method: METHOD_GET
    });
  }

  function getEquipperByEmail(email) {
    return AxiosFactory({
      url: GET_EQUIPPER_BY_EMAIL + email,
      method: METHOD_GET
    });
  }
  const value = {
    getEquipperById,
    getEquipperByEmail,
    getEquipperByUserName
  };
  return (
    <SearchContext.Provider value={value}>
      {props.children}
    </SearchContext.Provider>
  );
}
