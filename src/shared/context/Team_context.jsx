import React, { useContext } from 'react';
import AxiosFactory, {
  METHOD_POST,
  METHOD_PUT,
  METHOD_DELETE,
  METHOD_GET
} from '../helpers/Context_helpers';
import {
  DELETE_MEMBER,
  EDIT_MEMBER,
  ADD_MEMBER,
  GET_TEAM_LODGER,
  GET_TEAM_EQUIPPER,
  GET_SELECTION_LIST,
  VERIFY_EMAIL
} from '../helpers/Url_constants';

const TeamContext = React.createContext();
export const useTeamContext = () => useContext(TeamContext);

export default function TeamProvider({ children }) {
  async function GetTeamLodger() {
    return await AxiosFactory({
      url: GET_TEAM_LODGER,
      method: METHOD_GET
    });
  }
  async function GetTeamEquipper() {
    return await AxiosFactory({
      url: GET_TEAM_EQUIPPER,
      method: METHOD_GET
    });
  }
  async function DeleteMember(memberIds) {
    return await AxiosFactory({
      url: DELETE_MEMBER,
      method: METHOD_DELETE,
      data: {
        member_ids: memberIds
      }
    });
  }
  async function EditMember(member) {
    return await AxiosFactory({
      url: EDIT_MEMBER + member.id,
      method: METHOD_PUT,
      data: member
    });
  }
  async function AddMember(member) {
    return await AxiosFactory({
      url: ADD_MEMBER,
      method: METHOD_POST,
      data: member
    });
  }
  async function GetSelectionList(memberID, lodgerID) {
    return await AxiosFactory({
      url: `${GET_SELECTION_LIST}${memberID}/${lodgerID}`,
      method: METHOD_GET
    });
  }

  async function CheckIfMemberExists(memberOf, email) {
    return await AxiosFactory({
      url: `${VERIFY_EMAIL + email}/${memberOf}`,
      method: METHOD_GET
    });
  }
  const value = {
    GetTeamLodger,
    GetTeamEquipper,
    GetSelectionList,
    DeleteMember,
    EditMember,
    CheckIfMemberExists,
    AddMember
  };
  return (
    <TeamContext.Provider value={value}>{children}</TeamContext.Provider>
  );
}
