import React, { useContext } from 'react';
import AxiosFactory, {
  METHOD_POST,
  METHOD_PUT,
  METHOD_DELETE,
  METHOD_GET
} from '../helpers/Context_helpers';
import {
  PUBLIC_TOOLER_BIDZ_INVENTORY,
  TOOLER_BIDZ_INVENTORY
} from '../helpers/Url_constants';

const InventoryContext = React.createContext();
export const useInventoryContext = () => useContext(InventoryContext);

export default function InventoryProvider({ children }) {
  async function getInventory() {
    return await AxiosFactory({
      url: TOOLER_BIDZ_INVENTORY,
      method: METHOD_GET
    });
  }
  async function deleteInventory(id) {
    return await AxiosFactory({
      url: `${TOOLER_BIDZ_INVENTORY}/${id}`,
      method: METHOD_DELETE
    });
  }

  async function editInventory(inventory) {
    return await AxiosFactory({
      url: TOOLER_BIDZ_INVENTORY,
      method: METHOD_PUT,
      data: inventory
    });
  }

  async function addInventory(inventory) {
    return await AxiosFactory({
      url: TOOLER_BIDZ_INVENTORY,
      method: METHOD_POST,
      data: inventory
    });
  }

  async function uploadEquipmentPhoto(data, id) {
    return await AxiosFactory({
      url: `${TOOLER_BIDZ_INVENTORY}/${id}`,
      method: METHOD_POST,
      data
    });
  }

  async function getEquipmentById(id) {
    return await AxiosFactory({
      url: `${PUBLIC_TOOLER_BIDZ_INVENTORY}/${id}`,
      method: METHOD_GET
    });
  }

  const value = {
    getInventory,
    deleteInventory,
    editInventory,
    addInventory,
    uploadEquipmentPhoto,
    getEquipmentById
  };
  return (
    <InventoryContext.Provider value={value}>
      {children}
    </InventoryContext.Provider>
  );
}
