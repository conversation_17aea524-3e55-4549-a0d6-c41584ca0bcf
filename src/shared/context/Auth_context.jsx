import React, { useContext } from 'react';
import axiosFactory, {
  METHOD_GET,
  METHOD_POST
} from '../helpers/Context_helpers';
import {
  SIGNIN,
  SIGNUP,
  FIND_USER_BY_EMAIL,
  GET_VERIFICATION_CODE,
  RESET_PASSWORD
} from '../helpers/Url_constants';

const AuthContext = React.createContext();

export function useAuth() {
  return useContext(AuthContext);
}
export default function AuthProvider({ children }) {
  async function SignUp(user, memberId, lodgerId) {
    const url = memberId && lodgerId
      ? `${SIGNUP}?member_id=${memberId}&lodger_id=${lodgerId}`
      : SIGNUP;
    return await axiosFactory({
      url,
      method: METHOD_POST,
      data: user
    });
  }
  async function Login(user) {
    return await axiosFactory({
      url: SIGNIN,
      method: METHOD_POST,
      data: user
    });
  }
  async function FindUserByEmail(email) {
    return await axiosFactory({
      url: FIND_USER_BY_EMAIL + email,
      method: METHOD_GET
    });
  }
  async function GetVerificationCode(code, email) {
    return await axiosFactory({
      url: GET_VERIFICATION_CODE,
      method: METHOD_POST,
      data: {
        email,
        token: code
      }
    });
  }
  async function UpdatePasswordAPI(email, password, code) {
    return await axiosFactory({
      url: RESET_PASSWORD,
      method: METHOD_POST,
      data: {
        email,
        password,
        token: code
      }
    });
  }
  const value = {
    SignUp,
    Login,
    FindUserByEmail,
    GetVerificationCode,
    UpdatePasswordAPI
  };
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
