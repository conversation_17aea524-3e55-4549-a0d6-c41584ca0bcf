import React from 'react';

export default function FilterTab({ option, setSelectedTab, t }) {
  const handleChangeTab = (selectedTab) => {
    setSelectedTab(selectedTab);
  };
  return (
    <p className="radio-box left">
      <input
        type="radio"
        id={option.id}
        name="radio-group"
        defaultChecked={option.defaultChecked}
      />
      <label
        htmlFor={option.id}
        className="check-label t-body-regular c-fake-black"
        onClick={() => handleChangeTab(option)}
      >
        <span>
          {' '}
          {t(option.label)}
        </span>
      </label>
    </p>
  );
}
