import React, { useEffect, useState } from 'react';
import { scrollLeft, scrollRight } from '../../helpers/Scroll_helper';
import { getSessionStorage } from '../../helpers/Session_storage_helper';
import { useLocation, useNavigate } from 'react-router-dom';
import caretIcon from '../../../style/assets/img/Icons/CaretLeftMP.svg';

export default function Tab({ optionsTab, t }) {
  const navigate = useNavigate();
  const location = useLocation();
  const [chevronLeft, setChevronLeft] = useState(false);
  const [chevronRight, setChevronRight] = useState(true);
  const [isActive, setIsActive] = useState(location.pathname);

  useEffect(() => {
    setIsActive(location.pathname);
  }, [location.pathname]);

  return (
    <div
      className={`tabulation__head ${
        getSessionStorage('type') === 'private'
          ? 'tabulation__head--centered'
          : ''
      }`}
    >
      {chevronLeft && (
        <div className="nav-tab chevron-left">
          <img
            src={caretIcon}
            onClick={() => {
              setChevronRight(true);
              scrollRight('scrollmenu', setChevronLeft);
            }}
            alt="icon"
          />
        </div>
      )}
      <div className="scrollmenu" id="scrollmenu">
        {optionsTab?.map(
          (option, key) =>
            option.show && (
              <span key={key}>
                <input
                  type="radio"
                  id={`tab${key}`}
                  onClick={() => {
                    setIsActive(option.router);
                    navigate(option.router);
                  }}
                  name="tab"
                  className="tab-input"
                />
                <label
                  htmlFor={`tab${key}`}
                  className={`t-subheading-2 c-fake-black tab-label ${
                    isActive === option.router || (option.subRouter && option.subRouter.some(subRoute => isActive.includes(subRoute))) ? 'active' : ''
                  }`}
                >
                  {t(option.label)}
                </label>
              </span>
            )
        )}
        <div className="line" />
      </div>
      {chevronRight && (
        <div className="nav-tab chevron-right">
          <img
            src={caretIcon}
            onClick={() => {
              setChevronLeft(true);
              scrollLeft('scrollmenu', setChevronRight);
            }}
            alt="iconn"
          />
        </div>
      )}
    </div>
  );
}
