import { render } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import GoogleAnalyticsProvider from '../GoogleAnalyticsProvider';

// Mock import.meta.env
const mockEnv = {
  VITE_ENV: 'development',
  VITE_GOOGLE_ANALYTICS_ID: undefined
};

// Mock import.meta
Object.defineProperty(import.meta, 'env', {
  value: mockEnv,
  writable: true,
  configurable: true
});

beforeEach(() => {
  // Clear any existing gtag
  delete window.gtag;
  delete window.dataLayer;

  // Clear any existing scripts
  const existingScripts = document.querySelectorAll(
    'script[src*="googletagmanager"]'
  );
  existingScripts.forEach((script) => script.remove());

  // Reset mock environment
  mockEnv.VITE_ENV = 'development';
  mockEnv.VITE_GOOGLE_ANALYTICS_ID = undefined;
});

afterEach(() => {
  // Restore original environment
  jest.restoreAllMocks();
});

describe('GoogleAnalyticsProvider', () => {
  it('should render children without errors', () => {
    const TestComponent = () => <div>Test Content</div>;

    const { getByText } = render(
      <BrowserRouter>
        <GoogleAnalyticsProvider>
          <TestComponent />
        </GoogleAnalyticsProvider>
      </BrowserRouter>
    );

    expect(getByText('Test Content')).toBeInTheDocument();
  });

  it('should not load Google Analytics in non-production environment', () => {
    // Mock non-production environment
    mockEnv.VITE_ENV = 'development';
    mockEnv.VITE_GOOGLE_ANALYTICS_ID = 'G-TEST123';

    render(
      <BrowserRouter>
        <GoogleAnalyticsProvider>
          <div>Test</div>
        </GoogleAnalyticsProvider>
      </BrowserRouter>
    );

    // Should not create Google Analytics script in development
    const gaScript = document.querySelector('script[src*="googletagmanager"]');
    expect(gaScript).toBeNull();
  });

  it('should not load Google Analytics without analytics ID', () => {
    // Mock production environment but no analytics ID
    mockEnv.VITE_ENV = 'production';
    mockEnv.VITE_GOOGLE_ANALYTICS_ID = undefined;

    render(
      <BrowserRouter>
        <GoogleAnalyticsProvider>
          <div>Test</div>
        </GoogleAnalyticsProvider>
      </BrowserRouter>
    );

    // Should not create Google Analytics script without ID
    const gaScript = document.querySelector('script[src*="googletagmanager"]');
    expect(gaScript).toBeNull();
  });

  it('should load Google Analytics in production with valid ID', () => {
    // Mock production environment with analytics ID
    mockEnv.VITE_ENV = 'production';
    mockEnv.VITE_GOOGLE_ANALYTICS_ID = 'G-TEST123';

    render(
      <BrowserRouter>
        <GoogleAnalyticsProvider>
          <div>Test</div>
        </GoogleAnalyticsProvider>
      </BrowserRouter>
    );

    // Should create Google Analytics script in production
    const gaScript = document.querySelector('script[src*="googletagmanager"]');
    expect(gaScript).toBeTruthy();
    expect(gaScript.src).toContain('G-TEST123');
    expect(gaScript.async).toBe(true);
  });
});
