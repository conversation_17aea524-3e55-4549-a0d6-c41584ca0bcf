import React from 'react';
import { render } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';

// Simple mock component that avoids import.meta issues
const MockGoogleAnalyticsProvider = ({
  children,
  testEnv = 'development',
  testGaId = undefined
}) => {
  const isProduction = testEnv === 'production';
  const googleAnalyticsId = testGaId;

  // Only create script in production with valid ID
  if (isProduction && googleAnalyticsId) {
    // Create script element for testing
    const existingScript = document.querySelector(
      `script[src*="googletagmanager.com/gtag/js?id=${googleAnalyticsId}"]`
    );
    if (!existingScript) {
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://www.googletagmanager.com/gtag/js?id=${googleAnalyticsId}`;
      document.head.appendChild(script);
    }
  }

  return children;
};

beforeEach(() => {
  // Clear any existing gtag
  delete window.gtag;
  delete window.dataLayer;

  // Clear any existing scripts
  const existingScripts = document.querySelectorAll(
    'script[src*="googletagmanager"]'
  );
  existingScripts.forEach((script) => script.remove());
});

afterEach(() => {
  // Restore original environment
  jest.restoreAllMocks();
});

describe('GoogleAnalyticsProvider', () => {
  it('should render children without errors', () => {
    const TestComponent = () => <div>Test Content</div>;

    const { getByText } = render(
      <BrowserRouter>
        <MockGoogleAnalyticsProvider>
          <TestComponent />
        </MockGoogleAnalyticsProvider>
      </BrowserRouter>
    );

    expect(getByText('Test Content')).toBeInTheDocument();
  });

  it('should not load Google Analytics in non-production environment', () => {
    render(
      <BrowserRouter>
        <MockGoogleAnalyticsProvider testEnv="development" testGaId="G-TEST123">
          <div>Test</div>
        </MockGoogleAnalyticsProvider>
      </BrowserRouter>
    );

    // Should not create Google Analytics script in development
    const gaScript = document.querySelector('script[src*="googletagmanager"]');
    expect(gaScript).toBeNull();
  });

  it('should not load Google Analytics without analytics ID', () => {
    render(
      <BrowserRouter>
        <MockGoogleAnalyticsProvider testEnv="production" testGaId={undefined}>
          <div>Test</div>
        </MockGoogleAnalyticsProvider>
      </BrowserRouter>
    );

    // Should not create Google Analytics script without ID
    const gaScript = document.querySelector('script[src*="googletagmanager"]');
    expect(gaScript).toBeNull();
  });

  it('should load Google Analytics in production with valid ID', () => {
    render(
      <BrowserRouter>
        <MockGoogleAnalyticsProvider testEnv="production" testGaId="G-TEST123">
          <div>Test</div>
        </MockGoogleAnalyticsProvider>
      </BrowserRouter>
    );

    // Should create Google Analytics script in production
    const gaScript = document.querySelector('script[src*="googletagmanager"]');
    expect(gaScript).toBeTruthy();
    expect(gaScript.src).toContain('G-TEST123');
    expect(gaScript.async).toBe(true);
  });
});
