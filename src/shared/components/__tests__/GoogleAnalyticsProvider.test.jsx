import React from 'react';
import { render } from '@testing-library/react';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import GoogleAnalyticsProvider from '../GoogleAnalyticsProvider';

// Mock environment variables
const originalEnv = import.meta.env;

beforeEach(() => {
  // Clear any existing gtag
  delete window.gtag;
  delete window.dataLayer;
  
  // Clear any existing scripts
  const existingScripts = document.querySelectorAll('script[src*="googletagmanager"]');
  existingScripts.forEach(script => script.remove());
});

afterEach(() => {
  // Restore original environment variables
  jest.restoreAllMocks();
});

describe('GoogleAnalyticsProvider', () => {
  it('should render children without errors', () => {
    const TestComponent = () => <div>Test Content</div>;
    
    const { getByText } = render(
      <BrowserRouter>
        <GoogleAnalyticsProvider>
          <TestComponent />
        </GoogleAnalyticsProvider>
  it('should not load Google Analytics in non-production environment', () => {
    // Mock non-production environment
    Object.defineProperty(import.meta.env, 'VITE_ENV', {
      value: 'development',
      writable: true,
      configurable: true,
    });
    Object.defineProperty(import.meta.env, 'VITE_GOOGLE_ANALYTICS_ID', {
    jest.spyOn(import.meta, 'env', 'get').mockReturnValue({
      ...import.meta.env,
      VITE_ENV: 'development',
      VITE_GOOGLE_ANALYTICS_ID: 'G-TEST123',
    });
    expect(gaScript).toBeNull();
  });

  it('should not load Google Analytics without analytics ID', () => {
    // Mock production environment but no analytics ID
    Object.defineProperty(import.meta.env, 'VITE_ENV', {
      value: 'production',
      writable: true,
      configurable: true,
    });
    Object.defineProperty(import.meta.env, 'VITE_GOOGLE_ANALYTICS_ID', {
    jest.spyOn(import.meta, 'env', 'get').mockReturnValue({
      ...import.meta.env,
      VITE_ENV: 'production',
      VITE_GOOGLE_ANALYTICS_ID: undefined,
    });
    jest.spyOn(import.meta, 'env', 'get').mockReturnValue({
      ...import.meta.env,
      VITE_ENV: 'production',
      VITE_GOOGLE_ANALYTICS_ID: undefined,
    });
    Object.defineProperty(import.meta.env, 'VITE_ENV', {
      value: 'production',
      writable: true,
      configurable: true,
    });
    Object.defineProperty(import.meta.env, 'VITE_GOOGLE_ANALYTICS_ID', {
    jest.spyOn(import.meta, 'env', 'get').mockReturnValue({
      ...import.meta.env,
      VITE_ENV: 'production',
      VITE_GOOGLE_ANALYTICS_ID: 'G-TEST123',
    });
      </BrowserRouter>
    );
    
    // Should create Google Analytics script in production
    const gaScript = document.querySelector('script[src*="googletagmanager"]');
    expect(gaScript).toBeTruthy();
    expect(gaScript.src).toContain('G-TEST123');
    expect(gaScript.async).toBe(true);
  });
    expect(gaScript).toBeNull();
  });

  it('should load Google Analytics in production with valid ID', () => {
    // Mock production environment with analytics ID
    Object.defineProperty(import.meta.env, 'VITE_ENV', {
      value: 'production',
      writable: false,
      configurable: true,
    });
    Object.defineProperty(import.meta.env, 'VITE_GOOGLE_ANALYTICS_ID', {
      value: 'G-TEST123',
      writable: false,
      configurable: true,
    });

    render(
      <BrowserRouter>
        <GoogleAnalyticsProvider>
          <div>Test</div>
        </GoogleAnalyticsProvider>
      </BrowserRouter>
    );
    
    // Should create Google Analytics script in production
    const gaScript = document.querySelector('script[src*="googletagmanager"]');
    jest.spyOn(import.meta, 'env', 'get').mockReturnValue({
      ...import.meta.env,
      VITE_ENV: 'production',
      VITE_GOOGLE_ANALYTICS_ID: 'G-TEST123',
    });
