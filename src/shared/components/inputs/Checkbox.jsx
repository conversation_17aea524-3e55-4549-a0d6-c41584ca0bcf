export default function CheckboxInput({
  label,
  name,
  id,
  onChange,
  style,
  defaultChecked,
  checked
}) {
  return (
    <div
      className={`d-flex label-check align-items-center marg-top-20 ${style}`}
    >
      <input
        name={name}
        type="checkbox"
        id={id}
        checked={checked}
        defaultChecked={defaultChecked}
        className="checkbox"
        onChange={onChange}
      />
      <label className="t-body-regular c-fake-black" htmlFor={id}>
        {label}
      </label>
    </div>
  );
}
