import { Field, ErrorMessage } from 'formik';
import React from 'react';
import DatePickerComponent from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import RenderIf from '../Render_if';
import { subDays } from 'rsuite/esm/utils/dateUtils';

export default function Insurance({ data, t, disabled, formik }) {
  const currentTime = new Date();

  return (
    <>
      <div className="row">
        {data?.map((item, key) => (
          <div className="col-lg-6  margin-top-10 addresses" key={key}>
            <RenderIf condition={item.type !== 'date'}>
              <Field
                disabled={disabled}
                name={item.name}
                type={item.type}
                autoComplete="off"
                className="form-control w-100"
                placeholder={t(item.placeholder)}
              />
              <ErrorMessage
                name={item.name}
                component="span"
                className="error-message"
              />
            </RenderIf>
            <RenderIf condition={item.type === 'date'}>
              <DatePickerComponent
                name={item.name}
                selected={formik.values?.insurance?.expiry_date_of_insurance}
                onChange={(date) =>
                  formik.setFieldValue(
                    item.name,
                    date.setHours(
                      currentTime.getHours(),
                      currentTime.getMinutes(),
                      currentTime.getSeconds(),
                      currentTime.getMilliseconds()
                    )
                  )
                }
                minDate={subDays(new Date(), 0)}
                className="form-control w-100"
                placeholderText={t(item.placeholder)}
              />
              <ErrorMessage
                name={item.name}
                component="span"
                className="error-message"
              />
            </RenderIf>
          </div>
        ))}
      </div>
    </>
  );
}
