import React from 'react';
import RenderIf from '../Render_if';
import CustomLabel from '../labels/Custom_label';

export default function RadioInput({
  label,
  radioInputs,
  onChange,
  t,
  isCreditAccount
}) {
  return (
    <>
      <div className="row-infos check-row margin-top-10">
        <p
          className={
            label === 'Assurance_renonciation_dommage_question'
              ? 't-base-medium assurance-width'
              : 't-base-medium'
          }
        >
          <span className="t-base-medium c-ui-black">{t(label)}</span>
        </p>
        <div className="checks-content margin-top-10">
          {radioInputs?.map((item, key) => (
            <div className="radio-box" key={key}>
              <input
                type="radio"
                id={item.id}
                defaultChecked={item.defaultChecked}
                name={item.name}
                onChange={onChange}
              />
              <label
                htmlFor={item.id}
                className="check-label t-body-small c-blue-grey"
              />
              <span>{t(item.label)}</span>
            </div>
          ))}
        </div>
      </div>
      <RenderIf condition={isCreditAccount && label === 'Payment_method'}>
        <CustomLabel
          text={t(
            'Credit_check_form_is_required_if_you_want_to_pay_with_a_credit_account'
          )}
          severity="info"
          className="brd-radius-50"
        />
      </RenderIf>
    </>
  );
}
