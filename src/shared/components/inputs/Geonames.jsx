import { useEffect, useState } from 'react';
import MultiSelect from '../multi_select/Multi_select';
import { getCountries } from '../../helpers/Geonames';
import { ErrorMessage, Field } from 'formik';
import { Grid } from '@mui/material';
import RenderIf from '../Render_if';

export default function GeoLocation({
  formik,
  t,
  isCCA,
  initialValues,
  className,
  names,
  isBooking,
  disabled,
  isProject
}) {
  const [countries, setCountries] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState(
    initialValues?.country
  );
  const [selectedState, setSelectedState] = useState(initialValues?.state);
  const [selectedCity, setSelectedCity] = useState(initialValues?.city);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const storage = localStorage.getItem('countries');
  useEffect(() => {
    const fetchData = async () => {
      const response = await getCountries();
      const countries = response.data?.map((item) => ({
        value: item.country,
        label: item.country,
        data: item
      }));
      localStorage.setItem('countries', JSON.stringify(countries));

      setCountries(countries);
    };
    if (!storage) {
      fetchData();
    } else {
      setCountries(JSON.parse(storage));
    }
  }, [storage]);

  const handleChange = (value, name) => {
    if (name.includes('country')) {
      formik.setFieldValue(name, {
        label: value?.label,
        value: value?.value
      });
      setSelectedCountry(value);
      setSelectedState(null);
      setSelectedCity(null);
      setCities([]);
      setStates([]);
      formik.setFieldValue(names.state, '');
      formik.setFieldValue(names.city, '');
    } else if (name.includes('state')) {
      formik.setFieldValue(name, {
        label: value?.label,
        value: value?.value
      });
      setSelectedState(value);
      setSelectedCity(null);
      setCities([]);
      formik.setFieldValue(names.city, '');
    } else {
      setSelectedCity(value);
      formik.setFieldValue(name, {
        label: value?.label,
        value: value?.value
      });
    }
  };

  useEffect(() => {
    if (selectedCountry) {
      setStates(
        selectedCountry?.data?.states?.map((state) => ({
          ...state,
          value: state?.state,
          label: state?.state
        }))
      );
    }
  }, [selectedCountry]);

  useEffect(() => {
    if (initialValues?.country?.label) {
      const country = countries.find(
        (item) => item.label === initialValues?.country.label
      );

      setStates(
        country?.data?.states?.map((state) => ({
          ...state,
          value: state?.state,
          label: state?.state
        }))
      );

      const cities = country?.data?.states
        .flatMap((item) =>
          item.state === initialValues?.state?.label ? item.cities : []
        )
        ?.map((city) => ({
          value: city,
          label: city
        }));

      setCities(cities);
    }
  }, [countries]);
  useEffect(() => {
    if (selectedState) {
      setCities(
        selectedState?.cities?.map((city) => ({
          value: city,
          label: city
        }))
      );
    }
  }, [selectedState]);

  useEffect(() => {
    if (!isProject) {
      if (
        initialValues?.country?.label &&
        initialValues?.state?.label &&
        initialValues?.city?.label
      ) {
        setSelectedCountry(initialValues.country);
        setSelectedState(initialValues.state);
        setSelectedCity(initialValues.city);
      } else if (initialValues.country) {
        setSelectedCountry({
          value: initialValues.country,
          label: initialValues.country
        });
        setSelectedState({
          value: initialValues.state,
          label: initialValues.state
        });
        setSelectedCity({
          value: initialValues.city,
          label: initialValues.city
        });
        formik.setFieldValue(names.country, {
          value: initialValues.country,
          label: initialValues.country
        });
        formik.setFieldValue(names.state, {
          value: initialValues.state,
          label: initialValues.state
        });
        formik.setFieldValue(names.city, {
          value: initialValues.city,
          label: initialValues.city
        });
      } else {
        setSelectedCountry(null);
        setSelectedState(null);
        setSelectedCity(null);
      }
    }
  }, []);

  useEffect(() => {}, [
    initialValues?.country?.label,
    initialValues?.state?.label,
    initialValues?.city?.label,
    isProject
  ]);

  return (
    <div className={`${className} w-100 ml`}>
      <Grid container spacing={4} marginTop={1}>
        <RenderIf condition={!isCCA}>
          <Grid item xs={12} sm={12} className="pl-0 pt-0">
            <Field
              placeholder={t('Address')}
              type="text"
              name={names.address}
              className="input-address form-control w-100"
              disabled={disabled}
            />
            <ErrorMessage
              name={names.address}
              component="span"
              className="error-message"
            />
          </Grid>
        </RenderIf>

        <Grid
          item
          xs={isCCA ? 4 : 6}
          sm={isCCA ? 4 : 6}
          className="pad-r pl-0  pt-0"
        >
          <RenderIf condition={isCCA}>
            <label className="t-base-medium bold m-2">
              {t('Country')} <span className="c-red star-required">*</span>
            </label>
          </RenderIf>
          <MultiSelect
            value={
              !isBooking ? selectedCountry || null : initialValues?.country
            }
            options={countries}
            name={names.country}
            isGeoLocation
            placeholder={t('Country')}
            t={t}
            disabled={disabled}
            handleChange={(value) => handleChange(value, names.country)}
          />
          <ErrorMessage
            name={names.country}
            component="span"
            className="error-message"
          />
        </Grid>
        <RenderIf condition={!isCCA}>
          <Grid
            item
            xs={isCCA ? 4 : 6}
            sm={isCCA ? 4 : 6}
            className="pl-0  pt-0"
          >
            <Field
              placeholder={t('Zip_code')}
              type="text"
              name={names.zip_code}
              className="input-address form-control w-100 mt-0"
              disabled={disabled}
            />
            <ErrorMessage
              name={names.zip_code}
              component="span"
              className="error-message"
            />
          </Grid>
        </RenderIf>
        <Grid
          item
          xs={isCCA ? 4 : 6}
          sm={isCCA ? 4 : 6}
          className="pad-r pl-0  pt-0"
        >
          <RenderIf condition={isCCA}>
            <label className="t-base-medium bold m-2">
              {t('State')} <span className="c-red star-required">*</span>
            </label>
          </RenderIf>
          <MultiSelect
            value={isBooking ? initialValues?.state : selectedState || null}
            options={states}
            name={names.state}
            t={t}
            isGeoLocation
            disabled={disabled}
            placeholder={t('State')}
            handleChange={(value) => handleChange(value, names.state)}
          />
          <ErrorMessage
            name={names.state}
            component="span"
            className="error-message"
          />
        </Grid>
        <Grid item xs={isCCA ? 4 : 6} sm={isCCA ? 4 : 6} className="pl-0  pt-0">
          <RenderIf condition={isCCA}>
            <label className="t-base-medium bold m-2">
              {t('City')} <span className="c-red star-required">*</span>
            </label>
          </RenderIf>
          <MultiSelect
            value={isBooking ? initialValues?.city : selectedCity || null}
            options={cities}
            t={t}
            disabled={disabled}
            placeholder={t('City')}
            name={names.city}
            isGeoLocation
            handleChange={(value) => handleChange(value, names.city)}
          />
          <ErrorMessage
            name={names.city}
            component="span"
            className="error-message"
          />
        </Grid>
      </Grid>
    </div>
  );
}

GeoLocation.defaultProps = {
  onChange: undefined,
  disabled: false,
  isCountry: false,
  isValid: undefined,
  isBooking: false
};
