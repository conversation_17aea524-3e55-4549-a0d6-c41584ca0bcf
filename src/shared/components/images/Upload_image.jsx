import React from 'react';
import CustomImage from './Custom_image';

export default function UploadImage({
  t,
  uploadImage,
  data,
  setShowModal,
  isEquipper
}) {
  const onImageChange = (event) => {
    if (event?.target?.files[0]) {
      uploadImage(event.target.files[0]);
    }
  };
  return (
    <div className="change-logo">
      <div className="row justify-content-center">
        <div className="col-3">
          <div className="personal-infos__image">
            <CustomImage
              imageUrl={data?.photo_url}
              alt="personal logo"
              className="personal-infos_img"
              isUser
            />
          </div>
        </div>
        <div className="col-md-9 col-12">
          <div className="change-logo__content">
            <h4 className="t-header-h6 c-fake-black">
              {t('Change_logo_text')}
            </h4>
            <p className="t-body-regular c-fake-black">
              {t('Upload_new_logo_text')}
              <span className="c-near-grey">{t('Recommended_size_text')}</span>
            </p>
            <div className={`${isEquipper ? 'd-flex' : ''}`}>
              <p className="form-group  margin-left-10">
                <label
                  htmlFor="formId"
                  className="round-button black shadow bold center"
                >
                  <input
                    type="file"
                    onChange={onImageChange}
                    accept=".png, .jpg, .jpeg, .bmp, .tiff"
                    id="formId"
                    hidden
                  />
                  {t('Upload_button')}
                </label>
              </p>

              {isEquipper && (
                <p className="form-group margin-left-10">
                  <a
                    href="#open-hours-btn"
                    className="round-button black shadow bold center"
                    type="button"
                    onClick={() => {
                      setShowModal(true);
                    }}
                  >
                    {t('Opening_hours_text')}
                  </a>
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
