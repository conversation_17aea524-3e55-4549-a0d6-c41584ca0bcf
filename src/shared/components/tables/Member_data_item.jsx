import Tooltip from '@mui/material/Tooltip';
import { cutString, formatPhoneNumber } from '../../helpers/String_helps';
import CustomImage from '../images/Custom_image';

export default function MemberDataItem({ member, t }) {
  return (
    <div className="row collab-table">
      <div className="d-lg-flex justify-content-around col-lg-8 border ">
        <div className="d-flex">
          <CustomImage
            imageUrl={member && member.photo_url}
            alt={t('Cant_load_image')}
            isUser
          />
          <div className="right-side">
            <Tooltip title={member.email} placement="bottom">
              <span className="t-body-regular bold c-fake-black pad-top-10 ">
                {cutString(member.email, 30)}
              </span>
            </Tooltip>
          </div>
        </div>

        <div className="col-lg-3 status-owner d-flex align-items-center justify-content-center">
          <span className="t-body-regular bold c-primary-color status-text justify-content-center d-inline-block text-center">
            {t('Owner')}
          </span>
        </div>
      </div>

      <div className="border col-lg-4">
        <div className="d-lg-inline-block d-flex">
          <span className="mobile-left mobile-pad t-body-regular bold c-fake-black d-lg-none d-inline-block">
            {t('Phone_number')}
          </span>

          <span className="t-body-regular mobile-pad c-fake-black">
            {formatPhoneNumber(member.phone_number)}
          </span>
        </div>
      </div>
    </div>
  );
}
