import {
  PostHogProvider as ReactPostHogProvider,
  usePostHog
} from 'posthog-js/react';
import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const isProduction = import.meta.env.VITE_ENV === 'production';

const InnerPostHogInitializer = ({ children }) => {
  const location = useLocation();
  const posthog = usePostHog();

  useEffect(() => {
    // Track page views on route change
    if (isProduction && posthog) {
      posthog.capture('$pageview', {
        $current_url: window.location.href,
        $pathname: location.pathname
      });
    }
  }, [location, posthog]);

  return <>{children}</>;
};

export const PostHogProvider = ({ children }) => {
  return (
    <ReactPostHogProvider
      apiKey={import.meta.env.VITE_PUBLIC_POSTHOG_KEY}
      options={{
        api_host: import.meta.env.VITE_PUBLIC_POSTHOG_HOST,
        capture_pageview: false, // We manually capture pageviews
        capture_pageleave: true,
        autocapture: true,
        persistence: 'localStorage+cookie',
        person_profiles: 'identified_only',
        capture_exceptions: true,
        debug: import.meta.env.MODE === 'development'
      }}
    >
      <InnerPostHogInitializer>{children}</InnerPostHogInitializer>
    </ReactPostHogProvider>
  );
};

export default PostHogProvider;
