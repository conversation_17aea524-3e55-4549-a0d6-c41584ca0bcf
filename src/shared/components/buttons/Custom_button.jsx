import React from 'react';
import PropTypes from 'prop-types';
import { Spinner } from 'react-bootstrap';
import CustomTooltip from '../tooltips/Tooltip';

export default function CustomButton({
  className,
  textButton,
  textPopper,
  isLoading,
  onClick,
  disabled,
  type
}) {
  return (
    <CustomTooltip text={textPopper}>
      <button
        type={type}
        onClick={onClick}
        className={className}
        disabled={disabled}
      >
        {textButton}
        {isLoading && (
          <span className="p-2">
            {' '}
            <Spinner animation="border" size="sm" />
          </span>
        )}
      </button>
    </CustomTooltip>
  );
}

CustomButton.propTypes = {
  className: PropTypes.string,
  textButton: PropTypes.node,
  textPopper: PropTypes.string,
  onClick: PropTypes.func,
  disabled: PropTypes.bool,
  type: PropTypes.string
};

CustomButton.defaultProps = {
  className: '',
  textButton: '',
  textPopper: '',
  onClick: () => {},
  disabled: false,
  type: 'button'
};
