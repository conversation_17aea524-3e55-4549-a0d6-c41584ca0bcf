import { useTranslation } from 'react-i18next';
import React from 'react';

export default function CrudOptions(props) {
  const { t } = useTranslation();

  return (
    <div className="row">
      <button
        type="submit"
        style={{
          backgroundColor: '#00bcd4',
          color: '#fff'
        }}
        color="white"
        className=" col-1 button-signup fwb-700 bold"
        onClick={() => {
          props.setSelectedItem(props.data);
          props.showUpdateModal();
        }}
      >
        {t('Update')}
      </button>
      <button
        type="submit"
        style={{
          backgroundColor: 'black',
          color: 'white'
        }}
        onClick={() => {
          navigator.clipboard.writeText(props.data?.image_link);
        }}
        color="white"
        className="col-1 button-signup fwb-700 bold"
      >
        {t('Copy_image_link')}
      </button>
      <button
        type="submit"
        style={{
          backgroundColor: '#D42000',
          color: '#fff'
        }}
        onClick={() => {
          props.setAction('delete');
          props.setSelectedItem(props.data);
          props.showConfirmationModal();
        }}
        color="white"
        className="col-1 button-signup fwb-700 bold"
      >
        {t('Delete')}
      </button>
    </div>
  );
}
