/*
 * TEMPORARILY DISABLED COMPONENT - Language Menu
 *
 * This component is currently disabled to provide English-only navigation.
 * The component is preserved for future restoration when full internationalization
 * is properly implemented.
 *
 * TODO for full restoration:
 * 1. Complete all translations (French, Arabic)
 * 2. Implement RTL (Right-to-Left) support for Arabic
 * 3. Add locale-specific formatting (dates, numbers, currency)
 * 4. Test all UI components in different languages
 * 5. Implement proper language routing
 * 6. Add language-specific SEO meta tags
 * 7. Test accessibility in all languages
 *
 * To restore:
 * 1. Uncomment imports in Nav_bar.jsx
 * 2. Uncomment LanguageMenu usage in Nav_bar.jsx
 * 3. Uncomment mobile language menu in Mobile_screen_nav_bar.jsx
 * 4. Restore language detection in App.jsx
 * 5. Restore LanguageDetector in i18n.js
 * 6. Enable language popup in App.jsx
 */

import React from 'react';
import { Dropdown } from 'react-bootstrap';
import languageIcon from '../../../style/assets/img/Icons/language.svg';

export default function LanguageMenu({ changeLanguage, t }) {
  return (
    <Dropdown>
      <Dropdown.Toggle className="menuButton c-fake-black">
        <img src={languageIcon} alt="language icon" />
      </Dropdown.Toggle>
      <Dropdown.Menu className="custom-dropdown-menu custom-dropdown-menu-position">
        <Dropdown.Item
          className="text-center"
          onClick={() => {
            changeLanguage('en');
          }}
        >
          {t('English')}
        </Dropdown.Item>
        <Dropdown.Item
          className="text-center"
          onClick={() => {
            changeLanguage('fr');
          }}
        >
          {t('French')}
        </Dropdown.Item>
        <Dropdown.Item
          className="text-center"
          onClick={() => {
            changeLanguage('ar');
          }}
        >
          {t('Arabic')}
        </Dropdown.Item>
      </Dropdown.Menu>
    </Dropdown>
  );
}
