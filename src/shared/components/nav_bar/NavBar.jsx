
import React from 'react';
import CustomNavbar from './Nav_bar';
import SignInModal from '../modals/Sign_in_modal';

export default function NavBar({
  detectLanguage,
  t,
  show,
  setShow,
  setShowFPModal,
  showFPModal,
  signIn
}) {
  return (
    <>
      <CustomNavbar
        showmodal={() => setShow(!show)}
        t={t}
        detectLanguage={detectLanguage}
      />
      <SignInModal
        setShowFPModal={setShowFPModal}
        setShow={setShow}
        showFPModal={showFPModal}
        show={show}
        signIn={signIn}
        t={t}
      />
    </>
  );
}
