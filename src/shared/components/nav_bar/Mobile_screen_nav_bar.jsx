import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faStar } from '@fortawesome/fontawesome-free-solid';
import notificationIcon from '../../../style/assets/img/Icons/notif_icon.svg';
import settingsIcon from '../../../style/assets/img/Icons/settings_icon.svg';
import signUpIcon from '../../../style/assets/img/Icons/sign-up-icon.svg';
import UnderConstructionModal from '../modals/Under_construction_modal';
import { getCookies } from '../../helpers/Cookies';
import CustomImage from '../images/Custom_image';

export default function MobileScreenNavBar({ t, logout, showmodal, token }) {
  const [show, setShow] = useState(false);
  const [auth, setAuth] = useState(false);
  const role = getCookies('role');
  const clearSessionStorage = () => {
    sessionStorage.clear();
    sessionStorage.setItem('change', false);
  };
  useEffect(() => {
    if (token) {
      setAuth(true);
    }
  }, [token]);
  return (
    <>
      <UnderConstructionModal
        show={show}
        onClose={() => setShow(false)}
        t={t}
      />
      <div className="container mobile-menu d-block">
        <ul className="mobile-menu__top">
          {!auth && (
            <li>
              <a
                href="/SignUpAsLodger"
                className="t-base-medium c-black text-left black"
                onClick={() => setShow(true)}
              >
                <span>
                  <img
                    src={signUpIcon}
                    alt="icon settings"
                    className="small-icon"
                  />
                </span>
                {t('SignUp')}
              </a>
            </li>
          )}

          {auth && (
            <>
              <li>
                <a
                  href={
                    role === 'equipper'
                      ? '/equipperManagementPortal'
                      : '/renterManagementPortal'
                  }
                  onClick={clearSessionStorage}
                  className="t-base-medium c-black text-left"
                >
                  <span>
                    <CustomImage
                      alt="profile"
                      isUser
                      className="profile-icon"
                    />
                  </span>
                  {role === 'equipper'
                    ? t('Equipper_management_portal')
                    : t('Renter_management_portal')}
                </a>
              </li>
              <li>
                <a
                  href="#/Notification"
                  className="t-base-medium c-black text-left black"
                  onClick={() => setShow(true)}
                >
                  <span>
                    <img src={notificationIcon} alt="icon notification" />
                  </span>
                  {t('Notifications')}
                </a>
              </li>
              <li>
                <a
                  href="#/Settings"
                  className="t-base-medium c-black text-left black"
                  onClick={() => setShow(true)}
                >
                  <span>
                    <img
                      src={settingsIcon}
                      alt="icon settings"
                      className="small-icon"
                    />
                  </span>
                  {t('Settings')}
                </a>
              </li>
            </>
          )}
          <li>
            <a
              href="#/RateAs"
              className="t-base-medium c-black text-left black"
              onClick={() => setShow(true)}
            >
              <span>
                <FontAwesomeIcon icon={faStar} color="c-primary-color" />
              </span>
              {t('Rate_us')}
            </a>
          </li>
          {/* TEMPORARILY DISABLED - Mobile Language Menu for Multi-language Support
              TODO: Re-enable when full internationalization is properly configured
              This includes: translations, RTL support, locale-specific formatting, etc.
          <ul className="mobile-lang-menu">
            <li>
              <a
                href="#/action-2"
                className="text-center"
                onClick={() => {
                  changeLanguage('en');
                  toggleNavbarCollapse();
                }}
              >
                {t('English')}
              </a>
            </li>
            <li>
              <a
                href="#/action-1"
                className="text-center"
                onClick={() => {
                  changeLanguage('fr');
                  toggleNavbarCollapse();
                }}
              >
                {t('French')}
              </a>
            </li>
            <li>
              <a
                href="#/action-1"
                className="text-center"
                onClick={() => {
                  changeLanguage('ar');
                  toggleNavbarCollapse();
                }}
              >
                {t('Arabic')}
              </a>
            </li>
          </ul>
          */}
        </ul>

        <button
          className="round-button yellow bold nav-link-Login"
          onClick={auth ? logout : showmodal}
        >
          {auth ? t('Logout') : t('Login')}
        </button>
      </div>
    </>
  );
}
