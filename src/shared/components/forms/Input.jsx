import { ErrorMessage, FastField } from 'formik';
import React from 'react';
import PropTypes from 'prop-types';

export default function Input({
  name,
  label,
  type,
  component,
  disabled,
  placeholder,
  isNotRequired
}) {
  return (
    <>
      <label className="label-input d-lg-block" htmlFor={`${name}_id`}>
        {label}
        {!isNotRequired && <span className="c-red star-required"> * </span>}
      </label>

      <FastField
        placeholder={placeholder || label}
        type={type}
        name={name}
        id={`${name}_id`}
        disabled={disabled}
        key={name + type + label}
        component={component || null}
        autoComplete="off"
        className="form-control w-100 "
      />
      <ErrorMessage name={name} component="span" className="error-message" />
    </>
  );
}

Input.defaultProps = {
  type: 'text',
  disabled: false,
  isNotRequired: false
};

PropTypes.InputField = {
  form: PropTypes.object.isRequired,
  name: PropTypes.string.isRequired,
  label: PropTypes.string,
  type: PropTypes.string,
  disabled: PropTypes.bool
};
