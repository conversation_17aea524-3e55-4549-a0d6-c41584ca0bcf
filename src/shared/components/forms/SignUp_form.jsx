import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import * as Yup from 'yup';
import { PASSWORD_REGEX, PHONE_NUMBER_REGEX } from '../../helpers/Regex';
import FormikForm from './Formik';
import cities from '../../helpers/Cities.json';
import { Alert } from 'react-bootstrap';
import GeoLocation from '../inputs/Geonames';
import Input from './Input';
import { categoriesOptions } from '../../helpers/Data_helper';
import CustomButton from '../buttons/Custom_button';
import { ErrorMessage } from 'formik';
import RenderIf from '../Render_if';
import SwitchButton from '../buttons/Switch_button';
import { PhoneInputField } from '../inputs/Phone_input_field';
import CustomTooltip from '../tooltips/Tooltip';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle } from '@fortawesome/fontawesome-free-solid';
import MultipleSelectCheckmarks from '../multi_select/Multi_select_mui.jsx';

const Form = ({
  formik,
  error,
  invitedMember,
  isEquipper,
  onChange,
  onAction,
  changeForm,
  t
}) => {
  return (
    <div className="register-form row mb-lg-0 mb-5">
      <div className={isEquipper ? 'col-lg-12 mx-auto' : 'col-lg-8 mx-auto'}>
        <div className="row">
          <RenderIf condition={!isEquipper}>
            <div className="typeofuser-container">
              <div className="row Row-pad align-items-center">
                <div className="col-lg-12 mx-auto">
                  <div className="Row-pad__right">
                    <SwitchButton
                      changeState={changeForm}
                      disableIndividual={
                        invitedMember?.member_id && invitedMember?.lodger_id
                      }
                      dataContentB={t('Professional')}
                      dataContentA={t('Individual')}
                      formik={formik}
                    />
                  </div>
                </div>
              </div>
            </div>
          </RenderIf>
          <div className={isEquipper ? 'col-lg-4' : 'col-lg-6'}>
            <RenderIf condition={formik.values.userType === 'pro'}>
              <div className="container-email form-group marg-top-16">
                <Input
                  name="company"
                  type="text"
                  label={t('Company_name')}
                  disabled={
                    invitedMember?.member_id && invitedMember?.lodger_id
                  }
                />
              </div>
            </RenderIf>
            <RenderIf condition={!isEquipper}>
              <div className="container-email form-group marg-top-16">
                <Input name="full_name" type="text" label={t('Full_name')} />
              </div>
            </RenderIf>

            <div className="container-email form-group marg-top-16">
              <Input
                name="user_name"
                type="text"
                placeholder={t('Username')}
                isNotRequired
                label={
                  <>
                    {t('Username')}{' '}
                    <span className="c-red star-required  ">*</span>{' '}
                    <CustomTooltip
                      text={t('Username_tooltip')}
                      placement="right"
                    >
                      <span className="c-near-grey">
                        <FontAwesomeIcon
                          icon={faInfoCircle}
                          className="c-near-grey"
                        />
                      </span>
                    </CustomTooltip>
                  </>
                }
              />
            </div>
            <div className="container-email form-group marg-top-16">
              <Input
                name="email"
                type="text"
                label={t('Email')}
                disabled={invitedMember?.member_id && invitedMember?.lodger_id}
              />
            </div>
            <div className="container-email form-group marg-top-16">
              <Input
                name="phone_number"
                type="phone_number"
                component={PhoneInputField}
                label={t('Phone_number')}
              />
            </div>
          </div>
          <div className={isEquipper ? 'col-lg-4' : 'col-lg-6'}>
            <div className="container-email form-group marg-top-16">
              <Input name="password" type="password" label={t('Password')} />
            </div>
            <div className="container-email form-group marg-top-16">
              <Input
                name="confPassword"
                type="password"
                label={t('Confirm_your_password')}
              />
              <RenderIf condition={!isEquipper}>
                <div className="container-email form-group marg-top-16">
                  <label className="label-input d-lg-block">
                    {t('Company_address')}
                    <span className="c-red star-required">*</span>
                  </label>
                  <GeoLocation
                    formik={formik}
                    names={{
                      address: 'address',
                      country: 'country',
                      state: 'state',
                      city: 'city',
                      zip_code: 'zip_code'
                    }}
                    disabled={
                      invitedMember?.member_id && invitedMember?.lodger_id
                    }
                    initialValues={{
                      address: formik?.values?.address,
                      country: formik?.values?.country,
                      state: formik?.values?.state,
                      city: formik?.values?.city,
                      zip_code: formik?.values?.zip_code
                    }}
                    t={t}
                  />
                </div>
              </RenderIf>
            </div>

            <RenderIf condition={isEquipper}>
              <div className="container-email form-group marg-top-16">
                <label className="label-input d-lg-block">
                  {t('Categories')}{' '}
                  <span className="c-red star-required  ">*</span>{' '}
                  <CustomTooltip
                    text={t('Categories_tooltip')}
                    placement="right"
                  >
                    <span className="c-near-grey">
                      <FontAwesomeIcon
                        icon={faInfoCircle}
                        className="c-near-grey"
                      />
                    </span>
                  </CustomTooltip>
                </label>
                <MultipleSelectCheckmarks
                  options={categoriesOptions(t)}
                  t={t}
                  name="categories"
                  onChange={(value) => onChange(value, 'categories', formik)}
                  value={formik.values.categories}
                  placeholder={t('Categories')}
                />
              </div>
              <div className="container-email form-group marg-top-16">
                <label className="label-input d-lg-block">
                  {t('Coverage_area')}

                  <span className="c-red star-required">*</span>
                </label>

                <MultipleSelectCheckmarks
                  options={cities}
                  t={t}
                  name="coverage_area"
                  onChange={(value) => onChange(value, 'coverage_area', formik)}
                  value={formik.values.coverage_area}
                  placeholder={t('Coverage_area')}
                />
              </div>
            </RenderIf>
          </div>

          <RenderIf condition={isEquipper}>
            <div className="col-lg-4">
              <div className="container-email form-group marg-top-16 p-0">
                <label className="label-input d-lg-block">
                  {t('Company_address')}
                  <span className="c-red star-required">*</span>
                </label>
                <GeoLocation
                  formik={formik}
                  className="pr-0"
                  names={{
                    address: 'address',
                    country: 'country',
                    state: 'state',
                    city: 'city',
                    zip_code: 'zip_code'
                  }}
                  initialValues={{
                    address: formik?.values?.address,
                    country: formik?.values?.country,
                    state: formik?.values?.state,
                    city: formik?.values?.city,
                    zip_code: formik?.values?.zip_code
                  }}
                  t={t}
                />
              </div>
            </div>
          </RenderIf>
        </div>
      </div>
      <div className="d-lg-flex flex-column align-items-center">
        <div className="d-flex label-check justify-content-center check-signup">
          <input
            name="terms"
            type="checkbox"
            id="terms"
            checked={formik.values.terms}
            className="checkbox"
            onChange={() => {
              formik.setFieldValue('terms', !formik.values.terms);
            }}
          />
          <label
            className="padding-rigth-2 d-sm-flex align-items-center t-body-regular c-fake-black"
            htmlFor="terms"
          >
            {t('Accept')}{' '}
            <a
              href="/terms&conditions"
              target="_blank"
              rel="noopener noreferrer"
              className="bold"
            >
              {t('Terms_and_conditions')}
            </a>
            <a
              href="/privacyPolicy"
              target="_blank"
              rel="noopener noreferrer"
              className="bold"
            >
              {t('Privacy_notice')}
            </a>
          </label>
        </div>
        <ErrorMessage name="terms" component="span" className="error-message" />
      </div>

      {error && (
        <Alert variant="danger" className="error-alert mt-4">
          {t(error)}
        </Alert>
      )}

      <div
        md={{ span: 8, offset: 4 }}
        lg={{ span: 8, offset: 4 }}
        className="btn-sighup-content text-center"
      >
        <CustomButton
          type="submit"
          isLoading={onAction}
          className="button-signup round-button bold yellow c-black hover_black "
          textButton={
            isEquipper
              ? t('Signup_equipper_btn')
              : t('Start_your_renting_journey')
          }
        />
      </div>
    </div>
  );
};
export default function SignUpForm({
  error,
  handleSubmit,
  invitedMemberEmail,
  invitedMember,
  isEquipper,
  onAction,
  setType,
  t
}) {
  const initialValues = {
    name: '',
    full_name: '',
    address: invitedMember?.address || '',
    company: invitedMember?.company || '',
    userType: 'pro',
    zip_code: invitedMember?.zip_code || '',
    country: invitedMember?.country || '',
    state: invitedMember?.state || '',
    city: invitedMember?.city || '',
    phone_number: '',
    user_name: '',
    email: invitedMember?.invitedMemberEmail || '',
    password: '',
    confPassword: '',
    coverage_area: '',
    categories: '',
    terms: false,
    isEquipper: isEquipper
  };

  const validate = Yup.object({
    full_name: Yup.string().when('isEquipper', {
      is: (val) => val === false,
      then: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('This_field_is_required'))
    }),
    company: Yup.string().when('userType', {
      is: (val) => val === 'pro',
      then: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('This_field_is_required'))
    }),
    address: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .required(t('This_field_is_required')),
    country: Yup.object().required(t('This_field_is_required')).nullable(),
    city: Yup.object().required(t('This_field_is_required')).nullable(),
    state: Yup.object().required(t('This_field_is_required')).nullable(),
    zip_code: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .required(t('This_field_is_required')),
    phone_number: Yup.string()
      .max(15, t('SignUp_phone_number_max_length'))
      .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
      .required(t('This_field_is_required')),
    user_name: Yup.string()
      .matches(/^[A-Z-a-z-0-9^\-s]+$/, t('SignUp_user_name_error'))
      .required(t('This_field_is_required')),
    email: invitedMemberEmail
      ? Yup.string()
      : Yup.string()
          .email(t('Invalid_email'))
          .required(t('This_field_is_required')),
    password: Yup.string()
      .required(t('This_field_is_required'))
      .matches(PASSWORD_REGEX, t('Password_tooltip')),
    confPassword: Yup.string()
      .oneOf([Yup.ref('password'), null], t('SignUp_password_match'))
      .required(t('This_field_is_required')),
    userType: Yup.string(),
    isEquipper: Yup.bool(),
    coverage_area: Yup.array().when('isEquipper', {
      is: (val) => val === true,
      then: Yup.array()
        .required(t('This_field_is_required'))
        .min(1, t('This_field_is_required'))
        .nullable()
    }),
    categories: Yup.array().when('isEquipper', {
      is: (val) => val === true,
      then: Yup.array()
        .required(t('This_field_is_required'))
        .min(1, t('This_field_is_required'))
        .nullable()
    }),
    terms: Yup.bool().oneOf([true], t('This_field_is_required')).required()
  });

  const changeForm = (state, formik) => {
    if (state) {
      setType('pro');
      formik.setFieldValue('userType', 'pro');
    } else {
      setType('private');
      formik.setFieldValue('userType', 'private');
      formik.setFieldValue('company', '');
    }
  };

  const onSubmit = useCallback(async (values, setSubmitting) => {
    await handleSubmit(values);
    setSubmitting(false);
  });

  const onChange = (value, name, formik) => {
    formik.setFieldValue(name, value);
  };

  return (
    <FormikForm
      initialValues={initialValues}
      onSubmit={onSubmit}
      validationSchema={validate}
      component={({ formik }) => (
        <Form
          formik={formik}
          error={error}
          onAction={onAction}
          invitedMember={invitedMember}
          isEquipper={isEquipper}
          onChange={onChange}
          changeForm={changeForm}
          t={t}
        />
      )}
    />
  );
}

SignUpForm.propTypes = {
  handleSubmit: PropTypes.func.isRequired,
  error: PropTypes.bool,
  invitedMemberEmail: PropTypes.string,
  isEquipper: PropTypes.bool
};

SignUpForm.defaultProps = {
  error: false,
  invitedMemberEmail: '',
  isEquipper: false,
  type: '',
  communicationPreferences: null,
  selectedCategories: [],
  coverageArea: []
};
