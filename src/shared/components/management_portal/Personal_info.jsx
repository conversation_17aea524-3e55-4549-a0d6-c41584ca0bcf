import React from 'react';
import { cutString, formatPhoneNumber } from '../../helpers/String_helps';
import { getCookies } from '../../helpers/Cookies';
import Location from '../../../style/assets/img/company_spotlight/location.svg';
import Phone from '../../../style/assets/img/company_spotlight/phone.svg';
import Email from '../../../style/assets/img/company_spotlight/mail.svg';
import CustomImage from '../images/Custom_image';
import CustomTooltip from '../tooltips/Tooltip';

export default function PersonalInfo({ t, loadedInfo, showModal }) {
  return (
    <div className="container">
      <div className="personal-infos">
        <div className="row align-items-center">
          <div className="col-lg-2 order-1">
            <div className="personal-infos__image">
              <CustomImage
                imageUrl={loadedInfo.photo_url}
                alt="toolo profile"
                isUser
                className="personal-infos_img"
              />
            </div>
          </div>
          <div className="col-lg-4 order-2">
            <div className="personal-infos__content">
              {loadedInfo.user_name && (
                <p className="t-subheading-1 c-fake-black bold">
                  <span className="t-subheading-1 ">
                    {getCookies('role') === 'equipper'
                      ? loadedInfo.user_name
                      : loadedInfo.full_name}
                  </span>
                </p>
              )}

              {loadedInfo && loadedInfo.type !== 'private' && (
                <p className="t-subheading-2 c-fake-black bold">
                  <span className="t-subheading-2 bold">
                    {loadedInfo.company}
                  </span>
                </p>
              )}
            </div>
          </div>
          <div className="col-lg-3 order-lg-3 order-4">
            <div className="personal-infos__content">
              <p className="t-body-regular c-blue-grey">
                <img
                  src={Location}
                  alt="location"
                  className="location-icon me-2"
                />
                <span className="t-body-regular c-blue-grey ">
                  {` ${loadedInfo.address.address}, ${loadedInfo.address.country}, ${loadedInfo.address.state}, ${loadedInfo.address.city}, ${loadedInfo.address.zip_code}`}
                </span>
              </p>
              <p className="t-body-regular c-blue-grey">
                <img src={Phone} alt="phone" className="me-2" />
                <span className="t-body-regular c-blue-grey ">
                  {formatPhoneNumber(loadedInfo.phone_number)}
                </span>
              </p>
              <CustomTooltip text={loadedInfo.email} placement="right">
                <p className="t-body-regular c-blue-grey">
                  <img src={Email} alt="email" className="me-2" />
                  <span className="t-body-regular c-blue-grey ">
                    {cutString(loadedInfo.email, 24)}
                  </span>
                </p>
              </CustomTooltip>
            </div>
          </div>
          <div className="col-lg-3 order-lg-4 order-3">
            <button
              className="round-button yellow c-fake-black with-arrow-white d-flex align-items-center justify-content-center w-lg-auto w-100"
              onClick={showModal}
            >
              {t('Edit_my_profile')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
