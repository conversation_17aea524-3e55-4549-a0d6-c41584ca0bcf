import React, { useEffect, useState } from 'react';
import NoResult from '../../../../components/search_result/No_results';
import UnderConstructionImg from '../../../../style/assets/img/empty_state/Under_construction.svg';
import ToloIsLoading from '../../cards/Tolo_is_loading';

export default function UnderConstruction({ t }) {
  const [isLoading, setIsLoading] = useState(true);
  useEffect(() => {
    setTimeout(() => {
      setIsLoading(false);
    }, 500);
  }, []);

  if (isLoading) {
    return <ToloIsLoading />;
  }

  return (
    <div className="panel" id="p7">
      <div className="white-bg mobile-noBg">
        <NoResult
          message={t('Under_construction')}
          image={UnderConstructionImg}
        />
      </div>
    </div>
  );
}
