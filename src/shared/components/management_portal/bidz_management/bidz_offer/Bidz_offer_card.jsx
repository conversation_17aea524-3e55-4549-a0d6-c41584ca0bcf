import React, { useState } from 'react';
import ConfirmationModal from '../../../modals/Confirmation_modal';
import BidzCancelModal from '../../../modals/Bidz_cancel_modal';
import BidzDetails from '../../../modals/Bidz_details';
import SuccessPopUp from '../../../modals/Success_pop_up';
import Popup from '../../../modals/Popup';
import { useBidzContext } from '../../../../context/Bidz_context';
import { ACCEPT_BIDZ_OFFER } from '../../../../helpers/Url_prefixes';
import BidzOfferItem from './Bidz_offer_item';

export default function BidzOfferCard({
  data,
  detectLanguage,
  t,
  role,
  refresh,
  member
}) {
  const { BidzOfferActions, BidzCanceledOffer } = useBidzContext();
  const [selectedBidz, setSelectedBidz] = useState({});
  const [bidzModal, setBidzModal] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [show, setShow] = useState(false);
  const [showError, setShowError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showCancel, setShowCancel] = useState(false);
  const [response, setResponse] = useState('');
  const [buttonText, setButtonText] = useState('');

  async function acceptBidzOffer(data) {
    setIsLoading(true);
    const res = await BidzOfferActions(`${data.id}/accept`);
    if (res.status === 200) {
      if (data.payment_method === 'credit card') {
        window.location.href = res.data.checkout_url;
      } else {
        setShow(true);
      }
    } else {
      setShowError(true);
      setResponse(res);
    }
    setIsLoading(false);
  }

  async function declineBidzOffer(id) {
    setIsLoading(true);
    const res = await BidzOfferActions(`${id}/reject`);
    if (res.status === 200) {
      setShow(true);
    } else {
      setResponse(res);
      setShowError(true);
    }
    setIsLoading(false);
  }

  async function cancelBidzOffer(id, bidzOffer) {
    setIsLoading(true);
    const res = await BidzCanceledOffer(id, bidzOffer);
    if (res.status === 200) {
      setShow(true);
    } else {
      setResponse(res);
      setShowError(true);
    }
    setIsLoading(false);
  }

  const declineConfirmationModal = (item) => {
    setSelectedBidz(item);
    setShowConfirmation(true);
    setButtonText(t('Decline'));
  };

  const cancelConfirmationModal = (item) => {
    setSelectedBidz(item);
    setShowCancel(true);
    setButtonText(t('Cancel'));
  };

  const confirmationAction = () => {
    if (role === 'lodger') {
      buttonText === t('Decline') && declineBidzOffer(selectedBidz?.id);
    }
  };

  const onClose = () => {
    setBidzModal(false);
    setShowConfirmation(false);
    refresh();
  };
  return (
    <>
      {Object.keys(data)?.map(
        (item, key) =>
          item !== 'dataLength' && (
            <div className="body-content" key={key}>
              <div className="body-content__top">
                <div className="d-flex align-items-center">
                  <h3 className="t-subheading-2">
                    <span>
                      {detectLanguage === 'fr'
                        ? data[item] && data[item][0].equipment_name_fr
                        : item}
                    </span>
                  </h3>
                </div>
              </div>
              {data[item] &&
                data[item]?.map((item, key) => (
                  <BidzOfferItem
                    declineConfirmationModal={declineConfirmationModal}
                    cancelConfirmationModal={cancelConfirmationModal}
                    setBidzModal={setBidzModal}
                    isLoading={isLoading}
                    setSelectedBidz={setSelectedBidz}
                    role={role}
                    key={key}
                    member={member}
                    item={item}
                    t={t}
                  />
                ))}
            </div>
          )
      )}
      <ConfirmationModal
        show={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        action={() => confirmationAction()}
        buttonText={buttonText}
        isLoading={isLoading}
        message={t('Are_you_sure_you_want_to_declined_this_offer')}
        t={t}
      />
      <BidzCancelModal
        show={showCancel}
        isLoading={isLoading}
        onClose={() => setShowCancel(false)}
        message={t('Are_you_sure_you_want_to_cancel_this_offer')}
        buttonText={buttonText}
        action={cancelBidzOffer}
        item={selectedBidz}
        t={t}
      />
      <BidzDetails
        bidzModal={bidzModal}
        setBidzModal={setBidzModal}
        acceptBidzOffer={acceptBidzOffer}
        detectLanguage={detectLanguage}
        data={selectedBidz}
        role={role}
        isOffer
        t={t}
      />

      <SuccessPopUp show={show} onClose={onClose} />

      <Popup
        onClose={onClose}
        show={showError}
        response={response}
        prefix={ACCEPT_BIDZ_OFFER}
        t={t}
      />
    </>
  );
}
