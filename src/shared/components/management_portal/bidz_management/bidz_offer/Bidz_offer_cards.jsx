import React from 'react';
import NoResults from '../../../../../components/search_result/No_results';
import ViewMore from '../../../buttons/View_more';
import BidzOfferCard from './Bidz_offer_card';
import BidzHeaderOffers from './Bidz_header_offers';
import BookingEmptyState from '../../../../../style/assets/img/empty_state/No_booking_requests.svg';

export default function BidzOfferCards({
  t,
  detectLanguage,
  viewMore,
  refresh,
  request,
  data,
  member,
  role
}) {
  const { requestData, status } = data;
  return (
    <div className={`request-box ${status.toLowerCase()}`}>
      <h2 className="t-header-h6 c-fake-black bold  d-flex align-items-center">
        {t(`${status}_text`)}
        <span className="c-near-grey t-header-h6">{requestData.length}</span>
      </h2>
      {!requestData || requestData.length === 0 ? (
        <NoResults
          message={t('Request_management_no_request')}
          image={BookingEmptyState}
        />
      ) : (
        <>
          <BidzHeaderOffers t={t} role={role} member={member} />
          <BidzOfferCard
            data={requestData}
            member={member}
            refresh={refresh}
            detectLanguage={detectLanguage}
            role={role}
            t={t}
          />
          <div className="text-center request-all">
            <ViewMore
              isEmpty={request[`isEmpty${status}List`]}
              onClick={() => viewMore(status)}
              isEmptyMessage={t('No_more_equipments')}
            />
          </div>
        </>
      )}
    </div>
  );
}
