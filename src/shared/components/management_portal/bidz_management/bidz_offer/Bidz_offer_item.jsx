import { isEmpty } from 'lodash';
import React from 'react';
import CustomImage from '../../../images/Custom_image';
import RenderIf from '../../../Render_if';

export default function BidzOfferItem({
  t,
  item,
  member,
  role,
  setSelectedBidz,
  declineConfirmationModal,
  cancelConfirmationModal,
  setBidzModal
}) {
  const noRole = role === 'equipper' ? 'lodger' : 'equipper';
  const handleClick = (item) => {
    setSelectedBidz(item);
    setBidzModal(true);
  };
  return (
    <div className="body-content__bottom">
      <div className="row">
        <div className="col-xl-10 offset-xl-2 bg-content">
          <div className="row align-items-center">
            <div className="col-xl-3 col-8 order-1">
              <div className="d-flex align-items-center">
                <CustomImage
                  imageUrl={item[`${noRole}_image_link`]}
                  alt="profile"
                  isUser
                />

                <p className="t-body-regular">{item[`${noRole}_name`]}</p>
              </div>
            </div>
            <RenderIf
              condition={
                role === 'lodger' &&
                member.type === 'admin' &&
                !isEmpty(sessionStorage.getItem('member_of'))
              }
            >
              <div className="col-xl-3 col-8 order-1">
                <div className="d-flex align-items-center">
                  <CustomImage
                    imageUrl={item[`${role}_image_link`]}
                    alt="profile"
                    isUser
                  />

                  <p className="t-body-regular">{item[`${role}_name`]}</p>
                </div>
              </div>
            </RenderIf>
            <div className="col-xl-3 order-xl-3 order-2">
              <p className="t-body-regular text-xl-center text-left mobile-ml">
                {`${item.start_date?.slice(0, 10)} -
                                     ${item.return_date?.slice(0, 10)}`}
              </p>
            </div>
            <div
              className={
                role === 'equipper'
                  ? 'col-xl-5  order-4'
                  : member.type === 'admin' && role === 'lodger'
                  ? 'col-xl-3  order-4'
                  : 'col-xl-6  order-4'
              }
            >
              <div className="actions bidz-actions">
                <RenderIf
                  condition={
                    role === 'lodger' &&
                    item.status === 'pending' &&
                    !item.created_by_member
                  }
                >
                  {' '}
                  <div className="d-flex">
                    <div className="d-flex">
                      <button
                        className="round-button black shadow c-black"
                        onClick={() => declineConfirmationModal(item)}
                      >
                        {t('Decline')}
                      </button>
                      <button
                        className="round-button yellow c-black"
                        onClick={() => handleClick(item)}
                      >
                        {t('Accept_booking')}
                      </button>
                    </div>
                  </div>
                </RenderIf>
                <RenderIf
                  condition={
                    (role === 'equipper' && item.price_per_day) ||
                    (role === 'lodger' &&
                      item.status !== 'pending' &&
                      item.price_per_day)
                  }
                >
                  <button
                    className="round-button black shadow c-black"
                    onClick={() => handleClick(item)}
                  >
                    {t('View_details')}
                  </button>
                </RenderIf>
                <RenderIf
                  condition={
                    role === 'lodger' &&
                    item.status === 'pending' &&
                    item.created_by_member
                  }
                >
                  <button
                    className="round-button black shadow c-black"
                    onClick={() => handleClick(item)}
                  >
                    {t('View_details')}
                  </button>
                </RenderIf>

                <RenderIf
                  condition={
                    role === 'equipper' &&
                    (item.status === 'accepted' || item.status === 'pending')
                  }
                >
                  <button
                    className="round-button yellow c-black"
                    onClick={() => cancelConfirmationModal(item)}
                  >
                    {t('Cancel')}
                  </button>
                </RenderIf>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
