import React, { useState, useEffect, useCallback } from 'react';
import { useBidzContext } from '../../../context/Bidz_context';
import BidzRequest from './bidz_request/Bidz_request_cards';
import ToloIsLoading from '../../cards/Tolo_is_loading';
import {
  getSentRequest,
  requestStatus,
  switchRequest
} from '../../../helpers/Requests';

export default function BidzRequests({
  t,
  detectLanguage,
  role,
  hasInventory
}) {
  const { GetBidzRequestByEquipperID, GetBidzRequestByRenterID } =
    useBidzContext();
  const [isLoading, setIsLoading] = useState(true);
  const [pending, setPending] = useState([]);
  const [accepted, setAccepted] = useState([]);
  const [rejected, setRejected] = useState([]);
  const [sent, setSent] = useState([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [request, setRequest] = useState({
    itemsPerPending: 6,
    itemsPerAccepted: 6,
    itemsPerRejected: 6,
    itemsPerSent: 6,
    isEmptyPendingList: false,
    isEmptyAcceptedList: false,
    isEmptyRejectedList: false,
    isEmptySentList: false
  });

  const requests =
    role === 'lodger' || (role === 'equipper' && hasInventory === 'false')
      ? [
          {
            requestData: pending,
            status: 'Pending'
          },
          {
            requestData: accepted,
            status: 'Accepted'
          },
          {
            requestData: rejected,
            status: 'Rejected'
          }
        ]
      : [
          {
            requestData: sent,
            status: 'Sent'
          }
        ];

  const getRequest = useCallback(
    (itemsPerPage, status) => {
      if (role === 'lodger') {
        return GetBidzRequestByRenterID(itemsPerPage, status);
      }
      return GetBidzRequestByEquipperID(itemsPerPage, status);
    },
    [GetBidzRequestByRenterID, GetBidzRequestByEquipperID, role]
  );

  const refresh = () => {
    setAccepted([]);
    setPending([]);
    setRejected([]);
    setSent([]);
    setIsInitialized(false);
  };

  const viewMore = async (status) => {
    setRequest({
      ...request,
      [`itemsPer${status}`]: request[`itemsPer${status}`] + 2
    });
    if (
      role === 'lodger' ||
      (role === 'equipper' && hasInventory === 'false')
    ) {
      const res = await getRequest(
        request[`itemsPer${status}`] + 2,
        status.toLowerCase()
      );
      if (res.status === 200 && res.data !== null) {
        switchRequest(
          status,
          res,
          pending,
          accepted,
          rejected,
          null,
          setRequest,
          request,
          null,
          setAccepted,
          setPending,
          setRejected
        );
      }
    } else {
      getSentRequest(getRequest, request, setRequest, sent, setSent);
    }
  };

  useEffect(() => {
    if (isInitialized === false) {
      (async () => {
        setIsInitialized(true);
        setIsLoading(true);
        if (
          role === 'lodger' ||
          (role === 'equipper' && hasInventory === 'false')
        ) {
          requestStatus?.map(async (status) => {
            const res = await getRequest(
              request[`itemsPer${status}`],
              status.toLowerCase()
            );
            if (res.status === 200 && res.data !== null) {
              switchRequest(
                status,
                res,
                pending,
                accepted,
                rejected,
                null,
                setRequest,
                request,
                null,
                setAccepted,
                setPending,
                setRejected
              );
            }
          });
        } else {
          getSentRequest(getRequest, request, setRequest, sent, setSent);
        }
        setIsLoading(false);
      })();
    }
  }, [isInitialized, hasInventory, role]);

  if (isLoading || !requests) {
    return <ToloIsLoading />;
  }

  return (
    <>
      {requests?.map((data, index) => (
        <BidzRequest
          refresh={refresh}
          isLoading={isLoading}
          data={data}
          key={index}
          hasInventory={hasInventory}
          detectLanguage={detectLanguage}
          viewMore={viewMore}
          request={request}
          role={role}
          t={t}
        />
      ))}
    </>
  );
}
