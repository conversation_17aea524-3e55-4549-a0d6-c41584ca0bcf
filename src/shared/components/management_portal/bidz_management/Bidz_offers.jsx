import React, { useState, useEffect, useCallback } from 'react';
import ToloIsLoading from '../../cards/Tolo_is_loading';
import { requestStatus, switchRequest } from '../../../helpers/Requests';
import BidzOfferCards from './bidz_offer/Bidz_offer_cards';
import { useBidzContext } from '../../../context/Bidz_context';
import { getCookies } from '../../../helpers/Cookies';

export default function BidzOffer({ t, detectLanguage, role }) {
  const member = getCookies('member');
  const { GetBidzOfferByEquipperID, GetBidzOfferByRenterID } = useBidzContext();
  const [isLoading, setIsLoading] = useState(true);
  const [pending, setPending] = useState([]);
  const [accepted, setAccepted] = useState([]);
  const [rejected, setRejected] = useState([]);
  const [canceled, setCanceled] = useState([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [request, setRequest] = useState({
    itemsPerPending: 6,
    itemsPerAccepted: 6,
    itemsPerRejected: 6,
    isEmptyPendingList: false,
    isEmptyAcceptedList: false,
    isEmptyRejectedList: false
  });

  const data = [
    {
      requestData: pending,
      status: 'Pending'
    },
    {
      requestData: accepted,
      status: 'Accepted'
    },
    {
      requestData: rejected,
      status: 'Rejected'
    },
    {
      requestData: canceled,
      status: 'Canceled'
    }
  ];

  const getRequest = useCallback(
    (itemsPerPage, status) => {
      if (role === 'lodger') {
        return GetBidzOfferByRenterID(itemsPerPage, status);
      }
      return GetBidzOfferByEquipperID(itemsPerPage, status);
    },
    [role]
  );

  const refresh = () => {
    setAccepted([]);
    setPending([]);
    setRejected([]);
    setCanceled([]);
    setIsInitialized(false);
  };

  const viewMore = async (status) => {
    setRequest({
      ...request,
      [`itemsPer${status}`]: request[`itemsPer${status}`] + 2
    });
    const res = await getRequest(
      request[`itemsPer${status}`] + 2,
      status.toLowerCase()
    );
    if (res.status === 200 && res.data !== null) {
      switchRequest(
        status,
        res,
        pending,
        accepted,
        rejected,
        canceled,
        setRequest,
        request,
        setCanceled,
        setAccepted,
        setPending,
        setRejected
      );
    }
  };

  useEffect(() => {
    if (isInitialized === false) {
      (async () => {
        setIsInitialized(true);
        setIsLoading(true);
        requestStatus?.map(async (status) => {
          const res = await getRequest(
            request[`itemsPer${status}`],
            status.toLowerCase()
          );
          if (res.status === 200 && res.data !== null) {
            switchRequest(
              status,
              res,
              pending,
              accepted,
              rejected,
              canceled,
              setRequest,
              request,
              setCanceled,
              setAccepted,
              setPending,
              setRejected
            );
          }
        });
        setIsLoading(false);
      })();
    }
  }, [accepted, isInitialized, pending, rejected, request, canceled]);

  if (isLoading) {
    return <ToloIsLoading />;
  }

  return (
    <>
      {data?.map((data, index) => (
        <BidzOfferCards
          refresh={refresh}
          isLoading={isLoading}
          data={data}
          member={member}
          key={index}
          detectLanguage={detectLanguage}
          viewMore={viewMore}
          request={request}
          role={role}
          t={t}
        />
      ))}
    </>
  );
}
