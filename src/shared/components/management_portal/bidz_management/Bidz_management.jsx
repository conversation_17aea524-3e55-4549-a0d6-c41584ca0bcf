import React, {useState, lazy} from 'react';
import FilterTab from '../../tab/Filter_tab';
import {optionsBidzManagementTab} from '../../../helpers/Requests';
import {getCookies} from '../../../helpers/Cookies';

const componentMap = {
    BIDZ_REQUESTS: lazy(() => import('./Bidz_requests')),
    BIDZ_OFFERS: lazy(() => import('./Bidz_offers'))
};
export default function BidzManagement({t, detectLanguage, role}) {
    const [selectedTab, setSelectedTab] = useState({
        selectedTab: 'BIDZ_REQUESTS'
    });
    const BidzComponentToDisplay = componentMap[selectedTab.selectedTab];
    const hasInventory = getCookies('has_inventory');
    return (
        <div className="panel" id="p1">
            {(role === 'lodger' ||
                (role === 'equipper' && hasInventory === 'false')) && (
                <div className="tabulation__filter-bar pb-4">
                    <div className="form-group filter-search"/>
                    {optionsBidzManagementTab?.map((option, index) => (
                        <FilterTab
                            option={option}
                            t={t}
                            key={index}
                            setSelectedTab={setSelectedTab}
                        />
                    ))}
                </div>
            )}
            <div className={`row tabulation__row ${selectedTab.selectedTab}`}>
                <BidzComponentToDisplay
                    t={t}
                    detectLanguage={detectLanguage}
                    hasInventory={hasInventory}
                    role={role}
                />
            </div>
        </div>
    );
}
