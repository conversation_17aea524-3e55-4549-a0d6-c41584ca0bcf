import { isEmpty } from 'lodash';
import React from 'react';
import RenderIf from '../../../Render_if';

export default function BidzHeaderRequests({ t, role, member }) {
  return (
    <div className="head-content text-center">
      <div className="row">
        <div className="col-xl-2 text-start">
          <h3 className="t-body-regular c-neutrals-gray bold">{t('Equipment_name')}</h3>
        </div>

        <div className="col-xl-2">
          <h3 className="t-body-regular c-neutrals-gray bold">
            {role === 'equipper' ? t('Renter_name') : t('Equipper_name')}
          </h3>
        </div>
        <RenderIf
          condition={
            role === 'lodger' &&
            member &&
            member.type === 'admin' &&
            !isEmpty(sessionStorage.getItem('member_of'))
          }
        >
          <div className="col-xl-2">
            <h3 className="t-body-regular c-neutrals-gray bold">{t('Owner')}</h3>
          </div>
        </RenderIf>
        <div className="col-xl-3">
          <h3 className="t-body-regular c-neutrals-gray bold">{t('Rental_date')}</h3>
        </div>

        <div
          className={
            role === 'equipper'
              ? 'col-xl-4'
              : member && member.type === 'admin'
              ? 'col-xl-3'
              : 'col-xl-5'
          }
        >
          <h3 className="t-body-regular c-neutrals-gray bold">{t('Actions')}</h3>
        </div>
      </div>
    </div>
  );
}
