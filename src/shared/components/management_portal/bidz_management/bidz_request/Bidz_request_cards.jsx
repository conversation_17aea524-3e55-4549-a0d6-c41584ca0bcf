import React, { useState } from 'react';
import BidzHeaderRequests from './Bidz_header_requests';
import BidzRequestCard from './Bidz_request_card';
import NoResults from '../../../../../components/search_result/No_results';
import { useBidzContext } from '../../../../context/Bidz_context';
import ViewMore from '../../../buttons/View_more';
import { getCookies } from '../../../../helpers/Cookies';
import { isEmpty } from 'lodash';
import BookingEmptyState from '../../../../../style/assets/img/empty_state/No_booking_requests.svg';

export default function BidzRequestCards({
  t,
  detectLanguage,
  role,
  hasInventory,
  viewMore,
  refresh,
  request,
  data
}) {
  const member = getCookies('member');
  const [show, setShow] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showError, setShowError] = useState(false);
  const { SetBidOffer, AcceptBidzRequest, DeclineBidzRequest } =
    useBidzContext();
  async function acceptRequest(id, bidzOffer) {
    setIsLoading(true);
    const res = await SetBidOffer(bidzOffer);
    if (res.status === 200 || res.status === 201) {
      const response = await AcceptBidzRequest(id);
      if (response.status === 200 || response.status === 201) {
        setShow(true);
      }
    } else {
      setShowError(true);
    }
    setIsLoading(false);
  }

  async function declineRequest(id, bidzRequest) {
    setIsLoading(true);
    const res = await DeclineBidzRequest(id, bidzRequest);
    if (res.status === 200) {
      setShow(true);
    } else {
      setShowError(true);
    }
    setIsLoading(false);
  }
  const { requestData, status } = data;

  return (
    <div
      className={`request-box ${
        role === 'equipper' && hasInventory === 'true'
          ? 'canceled'
          : status.toLowerCase()
      }`}
    >
      <h2 className="t-header-h6 c-fake-black bold  d-flex align-items-center">
        {role === 'equipper' && hasInventory === 'true'
          ? t('Bidz_requests')
          : t(`${status}_text`)}
        <span className="c-near-grey t-header-h6">
          {requestData.dataLength || 0}
        </span>
      </h2>
      {isEmpty(requestData) ? (
        <NoResults
          message={t('Request_management_no_request')}
          image={BookingEmptyState}
        />
      ) : (
        <>
          <BidzHeaderRequests t={t} role={role} member={member} />
          <BidzRequestCard
            member={member}
            data={requestData}
            acceptRequest={acceptRequest}
            declineRequest={declineRequest}
            show={show}
            role={role}
            hasInventory={hasInventory}
            setShow={setShow}
            t={t}
            detectLanguage={detectLanguage}
            showError={showError}
            setShowError={setShowError}
            refresh={refresh}
            isLoading={isLoading}
          />

          <div className="text-center request-all">
            <ViewMore
              isEmpty={request[`isEmpty${status}List`]}
              onClick={() => viewMore(status)}
              isEmptyMessage={t('No_more_equipments')}
            />
          </div>
        </>
      )}
    </div>
  );
}
