import React from 'react';
import RenderIf from '../../Render_if';

export default function TableCrudMenu({
  isProjectManagement,
  onDetailsClick,
  onEditClick,
  onDeleteClick,
  disabledEdit,
  disabledDelete,
  t,
  type
}) {
  return (
    <div className="col-12 d-flex">
      <div className="col-12 mt-4">
        <button
          onClick={onEditClick}
          disabled={disabledEdit}
          className={`round-button yellow shadow  crud-delete  ${
            disabledEdit && 'c-near-grey'
          }`}
        >
          {t('Edit')}
        </button>

        <RenderIf condition={type !== 'collaborator'}>
          <button
            disabled={disabledDelete}
            className={`round-button shadow crud-delete black ${
              disabledDelete ? ' c-near-grey' : 'black'
            }`}
            onClick={onDeleteClick}
          >
            {t('Delete')}
          </button>
        </RenderIf>
        <RenderIf condition={isProjectManagement}>
          <button
            onClick={onDetailsClick}
            disabled={disabledEdit}
            className={`round-button shadow crud-delete black ${
              disabledEdit && ' c-near-grey'
            }`}
          >
            {t('Project_details_LMP')}
          </button>
        </RenderIf>
      </div>
    </div>
  );
}
