import React from 'react';
import { ErrorMessage } from 'formik';
import { AiOutlineMinusCircle } from 'react-icons/ai';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus } from '@fortawesome/fontawesome-free-solid';
import RenderIf from '../../Render_if';
import InputForm from '../../inputs/Input_form';
import MultiSelect from '../../multi_select/Multi_select';

export default function InviteMemberForm({
  formik,
  t,
  projectsOptions,
  arrayHelpers
}) {
  const { users } = formik.values;
  return (
    <>
      <RenderIf condition={users && users.length > 0}>
        {users?.map((_, index) => (
          <div key={index}>
            <div
              className={`${
                users.length === 1 ? 'col-12 ' : 'col-11 '
              }form-group invite-input mb-4`}
              key={index}
            >
              <div className="col-lg-12 padding-r-0">
                <label className="t-base-medium bold margin-left-10 mb-3">
                  {t('Email')}
                  <span className="c-red star-required">*</span>
                </label>
              </div>
              <InputForm
                name={`users.${index}.email`}
                type="text"
                className={`form-control w-100    ${
                  (formik.touched &&
                    formik.errors.users &&
                    formik.errors.users[index] &&
                    formik.errors.users[index].email &&
                    'is-invalid') ||
                  (formik.dirty && !formik.errors.email && 'is-valid')
                }`}
                placeholder={t('Email_address')}
                isFormik
              />
            </div>
            <div className="d-flex align-items-center invite-row">
              <p
                className={`${
                  users.length === 1 ? 'col-12 ' : 'col-11 '
                }form-group invite-input`}
              >
                <div className="form-group">
                  <label className="t-base-medium bold margin-left-10 mb-3">
                    {t('Projects_LMP')}
                    <span className="c-red star-required">*</span>
                  </label>
                  <MultiSelect
                    options={projectsOptions || []}
                    handleChange={(value) => {
                      formik.setFieldValue(`users[${index}].projects`, value);
                    }}
                    isGeoLocation
                    t={t}
                    className={`form-control w-100 ${
                      (formik.touched &&
                        formik.errors.users &&
                        formik.errors.users[index] &&
                        formik.errors.users[index].projects &&
                        'is-invalid') ||
                      (formik.dirty && !formik.errors.projects && 'is-valid')
                    }`}
                    name={`users[${index}].projects`}
                    placeholder={t('Projects_LMP')}
                  />
                  <ErrorMessage
                    name={`users.${index}.projects`}
                    component="div"
                    className="error-message"
                  />
                </div>
              </p>
              <RenderIf condition={users.length !== 1}>
                <div className="col-md-1 ">
                  <AiOutlineMinusCircle
                    size={30}
                    className="minus-icon"
                    onClick={() => {
                      arrayHelpers.remove(index);
                    }}
                  />
                </div>
              </RenderIf>
            </div>
          </div>
        ))}
      </RenderIf>
      <span
        className="t-base-medium extraBold c-grey-titles add-member hidden"
        onClick={() => {
          arrayHelpers.push({
            email: '',
            projects: projectsOptions ? projectsOptions[0].id : ''
          });
        }}
      >
        <FontAwesomeIcon icon={faPlus} color="c-grey-titles hidden" />{' '}
        {t('Add_many_members_at_once')}
      </span>
    </>
  );
}
