import React, { useState, useEffect } from 'react';
import { splitTeamList } from '../../../helpers/Team_helper';
import { getSuggestionList } from '../../../helpers/Account_type';
import MultiSelect from '../../multi_select/Multi_select';
import { firstLetterUpperCase } from '../../../helpers/String_helps';

export default function EditMemberModal({
  show,
  handleClose,
  onEditClick,
  selectedMembers,
  type,
  editMemberTitle,
  t,
  role,
  currentUserType
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [member, setMember] = useState({});
  const [memberType, setMemberType] = useState(null);

  const editMember = () => {
    setMember({});
    setIsLoading(true);
    onEditClick({
      ...member,
      type: memberType.value
    });
  };

  useEffect(() => {
    setMember(splitTeamList(selectedMembers, type)[0]);
    setMemberType({
      label: t(firstLetterUpperCase(type)),
      value: type
    });
  }, [selectedMembers, type]);

  if (!show || !member) {
    return null;
  }

  return (
    <div className="modal">
      <div className="modal-content no-title-margeTop">
        <button className="close-button" onClick={handleClose}>
          <svg
            width="25"
            height="24"
            viewBox="0 0 25 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19.25 5.25L5.75 18.75"
              stroke="#333333"
              strokeWidth="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M19.25 18.75L5.75 5.25"
              stroke="#333333"
              strokeWidth="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
        <div className="row">
          <div className="col-lg-10 mx-auto">
            <h2 className="t-header-h5 c-fake-black credit-title">
              {editMemberTitle}
            </h2>
            <p className="form-group mb-2">
              <input
                value={member.email}
                className="form-control w-100"
                disabled
              />
            </p>
            <div className="mt-2">
              <MultiSelect
                options={getSuggestionList(member, role, t, currentUserType)}
                value={memberType}
                defaultValue={memberType}
                handleChange={setMemberType}
                isClearable={false}
                t={t}
              />
            </div>

            <div className="content-buttons fixed-button-modal">
              <button className="round-button black big" onClick={handleClose}>
                {t('Cancel')}
              </button>
              <button
                className={
                  isLoading
                    ? 'round-button bold c-near-grey disabled'
                    : 'round-button bold yellow '
                }
                disabled={isLoading}
                onClick={editMember}
              >
                {t('Validate')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
