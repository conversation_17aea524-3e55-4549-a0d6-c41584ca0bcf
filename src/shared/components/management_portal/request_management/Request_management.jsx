import React, { useState, useEffect } from 'react';
import { optionsRequestTab } from '../../../helpers/Requests';
import AllRequests from './All_Requests';
import AcceptedRequests from './Accepted_requests';
import FilterTab from '../../tab/Filter_tab';
import ToloIsLoading from '../../cards/Tolo_is_loading';
import { ShowFlashMessage } from '../../../helpers/Show_flash_message';
import {
  getSessionStorage,
  setSessionStorage
} from '../../../helpers/Session_storage_helper';

const ComponentToMap = {
  ALL_REQUESTS: AllRequests,
  REQUEST_BY_STATUS: AcceptedRequests,
  FILRED_TAB: FilterTab
};
export default function RequestManagement({
  pending,
  accepted,
  declined,
  canceled,
  error,
  detectLanguage,
  request,
  role,
  refresh,
  viewMore,
  t
}) {
  const [selectedTab, setSelectedTab] = useState({ lazy: 'ALL_REQUESTS' });
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const ComponentToDisplayBySelectedTab = ComponentToMap[selectedTab.lazy];

  useEffect(() => {
    setIsLoading(true);
    setSessionStorage('loaded', true);
    switch (selectedTab.selectedTab) {
      case 'PENDING':
        setData({
          requestData: pending,
          status: 'Pending'
        });
        break;
      case 'ACCEPTED':
        setData({
          requestData: accepted,
          status: 'Accepted'
        });
        break;
      case 'DECLINED':
        setData({
          requestData: declined,
          status: 'Rejected'
        });
        break;
      case 'CANCELED':
        setData({
          requestData: canceled,
          status: 'Canceled'
        });
        break;
      default:
        setData({
          requestData: pending,
          status: 'Pending'
        });
        break;
    }
    setIsLoading(false);
  }, [accepted, canceled, declined, pending, selectedTab]);

  const url = window.location.search;
  const params = new URLSearchParams(url);
  const isStripe = params.get('stripe');
  if (isStripe === 'true' && !getSessionStorage('loaded')) {
    ShowFlashMessage(t);
  }
  if (isLoading) {
    return <ToloIsLoading />;
  }

  return (
    <div className="panel" id="p1">
      <div className="tabulation__filter-bar pb-4">
        <div className="form-group filter-search" />
        {optionsRequestTab?.map((item, key) => (
          <ComponentToMap.FILRED_TAB
            option={item}
            t={t}
            key={key}
            setSelectedTab={setSelectedTab}
          />
        ))}
      </div>
      <div className={`row tabulation__row ${selectedTab.selectedTab}`}>
        <ComponentToDisplayBySelectedTab
          refresh={refresh}
          viewMore={viewMore}
          selectedTab={selectedTab}
          data={data}
          detectLanguage={detectLanguage}
          allRequests={[
            {
              requestData: pending,
              status: 'Pending'
            },
            {
              requestData: accepted,
              status: 'Accepted'
            },
            {
              requestData: declined,
              status: 'Rejected'
            },
            {
              requestData: canceled,
              status: 'Canceled'
            }
          ]}
          error={error}
          request={request}
          role={role}
          t={t}
        />
      </div>
    </div>
  );
}
