import React, { useEffect, useState } from 'react';
import RenderIf from '../../Render_if';
import RequestDetailsModal from './Request_details_modal';
import RequestCardItem from './Request_card_item';
import { useCreditCheckFormContext } from '../../../context/Credit_check_form_context';

export default function RequestCard({
  data,
  member,
  t,
  role,
  status,
  detectLanguage
}) {
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [requestDetailsModal, setRequestDetailsModal] = useState(false);

  const { GetCreditCheckFormAttachment } = useCreditCheckFormContext();

  useEffect(() => {
    const updateAttachment = async (
      attachmentName,
      selectedRequest,
      requestId
    ) => {
      const attachment =
        selectedRequest?.credit_check_form?.attachments[attachmentName];
      if (!attachment?.includes('storage') && attachment) {
        const response = await GetCreditCheckFormAttachment(
          selectedRequest?.credit_check_form.id,
          attachment,
          requestId
        );

        if (response) {
          selectedRequest.credit_check_form.attachments[attachmentName] =
            response.data?.url;
        }
      }
    };

    const updateCreditCheckFormPath = async (selectedRequest) => {
      const response = await GetCreditCheckFormAttachment(
        selectedRequest?.credit_check_form?.id,
        selectedRequest?.credit_check_form?.credit_check_form_path,
        selectedRequest?.id
      );
      if (response) {
        selectedRequest.credit_check_form.credit_check_form_path =
          response.data?.url ||
          selectedRequest?.credit_check_form?.credit_check_form_path;
      }
    };

    const updateSelectedRequest = async () => {
      const updatedRequest = { ...selectedRequest };

      await Promise.all([
        updateAttachment('user_id', updatedRequest, updatedRequest.id),
        updateAttachment(
          'second_applicant_id',
          updatedRequest,
          updatedRequest.id
        ),
        updateAttachment('insurance', updatedRequest, updatedRequest.id),
        updateAttachment(
          'partnership_agreement',
          updatedRequest,
          updatedRequest.id
        ),
        selectedRequest?.credit_check_form.credit_check_form_path &&
          updateCreditCheckFormPath(updatedRequest)
      ]);

      setSelectedRequest(updatedRequest);
    };
    updateSelectedRequest();
  }, [requestDetailsModal]);
  return (
    <>
      {Object.keys(data)
        .sort((a, b) => a.localeCompare(b))
        .map(
          (item, key) =>
            item !== 'dataLength' && (
              <div className="body-content" key={key}>
                <div className="body-content__top">
                  <div className="d-flex align-items-center">
                    <h3 className="t-subheading-2">
                      <span>
                        {detectLanguage === 'fr'
                          ? data[item] && data[item][0].equipment_name_fr
                          : item}
                      </span>
                    </h3>
                  </div>
                </div>
                {data[item] &&
                  data[item]
                    .sort(
                      (a, b) => new Date(b.start_date) - new Date(a.start_date)
                    )
                    .map((item, key) => (
                      <RequestCardItem
                        data={item}
                        key={key}
                        setRequestDetailsModal={setRequestDetailsModal}
                        setSelectedRequest={setSelectedRequest}
                        status={status}
                        role={role}
                        member={member}
                        t={t}
                      />
                    ))}
              </div>
            )
        )}

      <RenderIf condition={requestDetailsModal && selectedRequest}>
        <RequestDetailsModal
          showModal={requestDetailsModal}
          data={selectedRequest}
          status={status}
          detectLanguage={detectLanguage}
          onClose={() => {
            setSelectedRequest(null);
            setRequestDetailsModal(false);
          }}
          role={role}
          t={t}
        />
      </RenderIf>
    </>
  );
}
