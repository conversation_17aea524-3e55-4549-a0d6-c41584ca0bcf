import React, { useState, useEffect, useCallback } from 'react';
import { useRequest } from '../../../context/Requests_context';
import { switchRequest, requestStatus } from '../../../helpers/Requests';
import ToloIsLoading from '../../cards/Tolo_is_loading';
import RequestManagement from './Request_management';
import { isEmpty } from 'lodash';
import NoResults from '../../../../components/search_result/No_results';
import { useNavigate } from 'react-router-dom';
import BookingEmptyState from '../../../../style/assets/img/empty_state/No_booking_requests.svg';

export default function RequestManagementSection({ t, role, detectLanguage }) {
  const { GetLodgerRequests, GetEquipperRequests } = useRequest();
  const navigate = useNavigate();
  const [error, setError] = useState();
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  const [pending, setPending] = useState([]);
  const [accepted, setAccepted] = useState([]);
  const [rejected, setRejected] = useState([]);
  const [canceled, setCanceled] = useState([]);
  const [request, setRequest] = useState({
    itemsPerPending: 6,
    itemsPerAccepted: 6,
    itemsPerRejected: 6,
    itemsPerCanceled: 6,
    isEmptyPendingList: false,
    isEmptyAcceptedList: false,
    isEmptyRejectedList: false,
    isEmptyCanceledList: false
  });

  const getRequest = useCallback(
    (itemsPerPage, status) => {
      if (role === 'lodger') {
        return GetLodgerRequests(itemsPerPage, status);
      }
      return GetEquipperRequests(itemsPerPage, status);
    },
    [GetEquipperRequests, GetLodgerRequests, role]
  );

  const refresh = () => {
    setAccepted([]);
    setCanceled([]);
    setPending([]);
    setRejected([]);
    setIsInitialized(false);
  };

  const viewMore = async (status) => {
    setRequest({
      ...request,
      [`itemsPer${status}`]: request[`itemsPer${status}`] + 2
    });
    const res = await getRequest(
      request[`itemsPer${status}`] + 2,
      status.toLowerCase()
    );
    if (res.status === 200 && res.data !== null) {
      switchRequest(
        status,
        res,
        pending,
        accepted,
        rejected,
        canceled,
        setRequest,
        request,
        setCanceled,
        setAccepted,
        setPending,
        setRejected
      );
    } else {
      setError(res.data);
    }
  };

  useEffect(() => {
    if (isInitialized === false) {
      (async () => {
        setIsInitialized(true);

        requestStatus?.map(async (status) => {
          setIsLoading(true);
          const res = await getRequest(
            request[`itemsPer${status}`],
            status.toLowerCase()
          );
          if (res.status === 200 && res.data !== null) {
            switchRequest(
              status,
              res,
              pending,
              accepted,
              rejected,
              canceled,
              setRequest,
              request,
              setCanceled,
              setAccepted,
              setPending,
              setRejected
            );
            setIsLoading(false);
          } else {
            setError(res.error);
          }
          setIsLoading(false);
        });
      })();
    }
  }, [
    getRequest,
    accepted,
    canceled,
    isInitialized,
    pending,
    rejected,
    request
  ]);

  if (isLoading) {
    return <ToloIsLoading />;
  }

  if (
    isEmpty(accepted) &&
    isEmpty(pending) &&
    isEmpty(rejected) &&
    isEmpty(canceled)
  ) {
    return (
      <NoResults
        t={t}
        image={BookingEmptyState}
        message={
          role === 'equipper'
            ? t('You_have_no_requests')
            : t('Make_sure_to_book_an_equipment')
        }
        buttonText={role === 'equipper' ? '' : t('Make_your_first_booking')}
        onClick={() => {
          navigate('/availableInventory');
        }}
      />
    );
  }

  return (
    <RequestManagement
      refresh={refresh}
      error={error}
      isLoading={isLoading}
      pending={pending}
      accepted={accepted}
      declined={rejected}
      detectLanguage={detectLanguage}
      canceled={canceled}
      viewMore={viewMore}
      request={request}
      role={role}
      t={t}
    />
  );
}
