import React, { useState } from 'react';
import NoResults from '../../../../components/search_result/No_results';
import RequestCard from './Request_card';
import Header from './Header';
import ViewMore from '../../buttons/View_more';
import { getCookies } from '../../../helpers/Cookies';
import BookingEmptyState from '../../../../style/assets/img/empty_state/No_booking_requests.svg';
import ToloIsLoading from '../../cards/Tolo_is_loading';
import { useNavigate } from 'react-router-dom';

export default function RequestByStatus({
  t,
  data,
  error,
  refresh,
  request,
  detectLanguage,
  role,
  viewMore
}) {
  const { requestData, status } = data;
  const [isLoading, setIsLoading] = useState(true);
  const member = getCookies('member');
  const navigate = useNavigate();

  setTimeout(() => {
    setIsLoading(false);
  }, 1000);

  if (isLoading) {
    return <ToloIsLoading />;
  }

  return (
    <div className={`request-box ${status.toLowerCase()}`}>
      <h2 className="t-header-h6 bold mb-3 d-flex align-items-center">
        {t(`${status}_text`)}
        <span className="c-near-grey t-header-h6">
          ({requestData.dataLength || 0})
        </span>
      </h2>
      {error || !requestData || requestData.length === 0 ? (
        <NoResults
          message={
            role === 'equipper'
              ? t('You_have_no_requests')
              : t('Make_sure_to_book_an_equipment')
          }
          buttonText={role === 'equipper' ? '' : t('Make_your_first_booking')}
          image={BookingEmptyState}
          onClick={() => {
            navigate('/availableInventory');
          }}
        />
      ) : (
        <>
          <Header t={t} role={role} member={member} />
          <RequestCard
            refresh={refresh}
            member={member}
            data={requestData}
            status={status}
            role={role}
            detectLanguage={detectLanguage}
            t={t}
            action={role === 'lodger'}
          />
          <div className="text-center request-all">
            <ViewMore
              isEmpty={request[`isEmpty${status}List`]}
              onClick={() => viewMore(status)}
              isEmptyMessage={t('No_more_equipments')}
            />
          </div>
        </>
      )}
    </div>
  );
}
