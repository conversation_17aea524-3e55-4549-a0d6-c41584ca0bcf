import { Box, Paper, Typography } from '@mui/material';
import React from 'react';
import equipmentDetailsData from '../../helpers/Single_equipment_details_data_generation.js';

export default function DescriptionPanelSection({
  equipment,
  detectedLanguage,
  t
}) {
  // Find equipment data based on equipment name or preferred name
  const getEquipmentData = () => {
    if (!equipment || (!equipment.name && !equipment.preferred_equipment_name))
      return null;

    // Get the equipment name to search for (prioritize preferred name)
    const searchName = equipment.preferred_equipment_name || equipment.name;

    // Try to find exact match first
    let equipmentData = equipmentDetailsData.find(
      (item) =>
        item.equipment_name &&
        item.equipment_name.toLowerCase() === searchName.toLowerCase()
    );

    // If no exact match with preferred name, try with standard name
    if (
      !equipmentData &&
      equipment.preferred_equipment_name &&
      equipment.name
    ) {
      equipmentData = equipmentDetailsData.find(
        (item) =>
          item.equipment_name &&
          item.equipment_name.toLowerCase() === equipment.name.toLowerCase()
      );
    }

    // If still no exact match, try partial match with search name
    if (!equipmentData) {
      equipmentData = equipmentDetailsData.find(
        (item) =>
          item.equipment_name &&
          (searchName
            .toLowerCase()
            .includes(item.equipment_name.toLowerCase()) ||
            item.equipment_name
              .toLowerCase()
              .includes(searchName.toLowerCase()))
      );
    }

    // Last resort: try partial match with standard name if we used preferred name
    if (
      !equipmentData &&
      equipment.preferred_equipment_name &&
      equipment.name
    ) {
      equipmentData = equipmentDetailsData.find(
        (item) =>
          item.equipment_name &&
          (equipment.name
            .toLowerCase()
            .includes(item.equipment_name.toLowerCase()) ||
            item.equipment_name
              .toLowerCase()
              .includes(equipment.name.toLowerCase()))
      );
    }

    return equipmentData;
  };

  const equipmentData = getEquipmentData();

  // Default content if no specific data found
  const getDefaultContent = (section) => {
    const defaultContent = {
      en:
        section === 'section1'
          ? 'This equipment is designed for professional use in construction and industrial applications.'
          : 'Please ensure proper safety measures and training before operating this equipment.',
      fr:
        section === 'section1'
          ? 'Cet équipement est conçu pour un usage professionnel dans les applications de construction et industrielles.'
          : "Veuillez vous assurer des mesures de sécurité appropriées et de la formation avant d'utiliser cet équipement.",
      ar:
        section === 'section1'
          ? 'تم تصميم هذه المعدات للاستخدام المهني في تطبيقات البناء والصناعة.'
          : 'يرجى التأكد من اتخاذ تدابير السلامة المناسبة والتدريب قبل تشغيل هذه المعدات.'
    };
    return defaultContent[detectedLanguage] || defaultContent.en;
  };
  return (
    <>
      <Box sx={{ width: '100%' }}>
        <Paper elevation={0} sx={{ borderRadius: '6px' }}>
          <Box
            sx={{
              padding: '10.5px 17.5px',
              borderRadius: '6px 6px 0 0',
              bgcolor: 'white'
            }}
          >
            <Typography
              variant="h6"
              sx={{
                color: 'text.primary',
                fontWeight: 'bold',
                fontSize: 'var(--panel-header-typography-font-size)',
                letterSpacing: 'var(--panel-header-typography-letter-spacing)',
                lineHeight: 'var(--panel-header-typography-line-height)',
                fontStyle: 'var(--panel-header-typography-font-style)'
              }}
            >
              {t('Usages_of_this_equipment')}
            </Typography>
          </Box>

          <Box
            sx={{
              padding: '17.5px',
              bgcolor: 'background.paper',
              borderRadius: '0 0 6px 6px'
            }}
          >
            <Typography
              variant="body1"
              sx={{
                color: 'text.secondary',
                fontWeight: 'var(--panel-content-typography-font-weight)',
                fontSize: 'var(--panel-content-typography-font-size)',
                letterSpacing: 'var(--panel-content-typography-letter-spacing)',
                lineHeight: 'var(--panel-content-typography-line-height)',
                fontStyle: 'var(--panel-content-typography-font-style)'
              }}
            >
              {equipmentData?.section1?.en || getDefaultContent('section1')}
            </Typography>
          </Box>
        </Paper>
      </Box>
      <Box sx={{ width: '100%' }}>
        <Paper
          elevation={0}
          sx={{
            borderRadius: '6px 6px 0 0',
            overflow: 'hidden'
          }}
        >
          <Box
            sx={{
              padding: '10.5px 17.5px',
              bgcolor: 'white'
            }}
          >
            <Typography
              variant="h6"
              sx={{
                color: 'text.primary',
                fontWeight: 'bold',
                fontSize: 'var(--panel-header-typography-font-size)',
                letterSpacing: 'var(--panel-header-typography-letter-spacing)',
                lineHeight: 'var(--panel-header-typography-line-height)',
                fontStyle: 'var(--panel-header-typography-font-style)'
              }}
            >
              {t('Additional_important_information')}
            </Typography>
          </Box>

          <Box
            sx={{
              padding: '17.5px',
              bgcolor: 'var(--panelcontentbackground)',
              borderRadius: '0 0 6px 6px'
            }}
          >
            <Typography
              variant="body1"
              color="var(--panelcontentcolor)"
              sx={{
                fontFamily: 'var(--panel-content-typography-font-family)',
                fontWeight: 'var(--panel-content-typography-font-weight)',
                fontSize: 'var(--panel-content-typography-font-size)',
                letterSpacing: 'var(--panel-content-typography-letter-spacing)',
                lineHeight: 'var(--panel-content-typography-line-height)',
                fontStyle: 'var(--panel-content-typography-font-style)'
              }}
            >
              {equipmentData?.section2?.en || getDefaultContent('section2')}
            </Typography>
          </Box>
        </Paper>
      </Box>
    </>
  );
}
