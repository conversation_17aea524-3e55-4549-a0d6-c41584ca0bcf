import React, { useCallback, useEffect, useState } from 'react';
import { useEquipment } from '../../context/Equipment_context';
import ConfirmationModal from '../modals/Confirmation_modal';
import Popup from '../modals/Popup';
import SuccessPopUp from '../modals/Success_pop_up';
import RenderIf from '../Render_if';
import { isEmptyValue } from '../../helpers/String_helps';
import CustomImage from '../images/Custom_image';
import FormatPrice from '../../helpers/Price_helper';
import { priceData } from '../../helpers/Data_helper';
import CustomTooltip from '../tooltips/Tooltip';
import { useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrashAlt, faPencilAlt } from '@fortawesome/fontawesome-free-solid';
import Box from '@mui/material/Box';
import { getCookies } from '../../helpers/Cookies';
import NoResults from '../../../components/search_result/No_results';
import { isEmpty } from 'lodash';
import { index } from '../../helpers/Algolia_helper';
import ToloIsLoading from '../cards/Tolo_is_loading';
import ViewMore from '../buttons/View_more';
import { getSessionStorage } from '../../helpers/Session_storage_helper';
import Alert from '@mui/material/Alert';
import IconButton from '@mui/material/IconButton';
import Collapse from '@mui/material/Collapse';
import EquipmentEmptyState from '../../../style/assets/img/empty_state/Add_equipment_first.svg';

const getStatusClassName = (status) => {
  switch (status) {
    case 'available':
      return 'status-available';
    case 'idle':
      return 'status-idle';
    case 'booked':
      return 'status-booked';
    default:
      return '';
  }
};

export const StatusBox = ({ t, item }) => {
  const statusClassName = getStatusClassName(item.status);
  return (
    <div className={`status-box mb-2 ${statusClassName}`}>
      <span className="status-text">
        {t(item.status?.charAt(0).toUpperCase() + item.status.slice(1))}
      </span>
    </div>
  );
};

const EquipmentManagementTable = ({ t, searchState, detectLanguage }) => {
  const navigate = useNavigate();
  const [open, setOpen] = React.useState(true);
  const [onAction, setOnAction] = useState(null);

  const [show, setShow] = useState(false);
  const {
    DeleteEquipment,
    UpdateBookingStatusAfterReturnEquipment,
    UpdateEquipmentStatus
  } = useEquipment();

  const [equipments, setEquipments] = useState(null);
  const [nbHits, setNbHits] = useState(0);
  const [hitsPerPage, setHitsPerPage] = useState(6);
  const [selectedEquipment, setSelectedEquipment] = useState(null);
  const [action, setAction] = useState('');
  const [status, setStatus] = useState('');
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const category = getSessionStorage('category') || null;
  const subcategory = getSessionStorage('sub_category') || null;
  const equipmentStatus = getSessionStorage('status');

  const buildFilters = () => {
    const filters = [`equipper_id:${getCookies('userId')}`, 'is_active:true'];
    if (category) filters.push(`category:"${category}"`);
    if (subcategory) filters.push(`sub_category:"${subcategory}"`);
    if (equipmentStatus) filters.push(`status:"${equipmentStatus}"`);
    return filters.join(' AND ');
  };

  const getHits = useCallback(async () => {
    const filters = buildFilters();
    const { hits, nbHits } = await index.search(searchState.query, {
      filters: filters,
      hitsPerPage: hitsPerPage
    });
    setNbHits(nbHits);
    setEquipments(hits);
  }, [category, subcategory, equipmentStatus, searchState.query, hitsPerPage]);

  useEffect(() => {
    getHits();
  }, [getHits]);

  const onCloseSuccess = async () => {
    setShowSuccessModal(false);
    setShowConfirmationModal(false);
    window.location.reload();
  };

  const onCloseFailure = async () => {
    setShow(false);
    setShowConfirmationModal(false);
  };

  const deleteEquipmentById = useCallback(
    async (id) => {
      setOnAction(true);
      const res = await DeleteEquipment(id);
      if (res.status === 200) {
        await index.deleteObject(id);
        setSelectedEquipment(null);

        setTimeout(() => {
          setOnAction(false);
          setShowSuccessModal(true);
        }, 8000);
      } else {
        setShow(true);
      }
    },
    [DeleteEquipment]
  );

  const updateBookingStatus = useCallback(
    async (id) => {
      setOnAction(true);
      const res = await UpdateBookingStatusAfterReturnEquipment(id);
      if (res.status === 200) {
        await index.partialUpdateObject({ objectID: id, status: 'available' });
        setTimeout(() => {
          setOnAction(false);
          setShowSuccessModal(true);
        }, 8000);
      } else {
        setShow(true);
      }
    },
    [UpdateBookingStatusAfterReturnEquipment]
  );

  const updateEquipmentInAlgolia = useCallback(async (id, status) => {
    try {
      await index.partialUpdateObject({ objectID: id, status });
    } catch (error) {
      console.error('Error updating Algolia object:', error);
    }
  }, []);

  const updateEquipmentStatus = useCallback(
    async (id, status) => {
      setOnAction(true);
      const res = await UpdateEquipmentStatus(id, status);
      if (res.status === 200) {
        await updateEquipmentInAlgolia(id, status);
        setTimeout(() => {
          setOnAction(false);
          setShowSuccessModal(true);
        }, 8000);
      } else {
        setShow(true);
      }
    },
    [UpdateEquipmentStatus, updateEquipmentInAlgolia]
  );

  const message = {
    delete: `${t('Are_you_sure_you_want_to_do_detete_equipment')} ?`,
    update: t('Are_you_sure_you_want_to_update_status'),
    updateStatus: t('Update_equipment_status_message')
  };

  const handleAction = useCallback(
    (status) => {
      switch (action) {
        case 'delete':
          return deleteEquipmentById(selectedEquipment.objectID);
        case 'update':
          return updateBookingStatus(selectedEquipment.objectID);
        case 'updateStatus':
          return updateEquipmentStatus(selectedEquipment.objectID, status);
        default:
          break;
      }
    },
    [
      action,
      deleteEquipmentById,
      selectedEquipment,
      updateBookingStatus,
      updateEquipmentStatus
    ]
  );

  const handleShowConfirmation = (action, status, equipment) => {
    setShowConfirmationModal(true);
    setSelectedEquipment(equipment);
    setAction(action);
    setStatus(status);
  };

  if (!equipments) {
    return <ToloIsLoading />;
  }

  return (
    <>
      <Collapse in={open}>
        <Alert
          severity="info"
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              onClick={() => {
                setOpen(false);
              }}
            >
              <svg
                width="15"
                height="15"
                viewBox="0 0 25 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M19.25 5.25L5.75 18.75"
                  stroke="#333333"
                  strokeWidth="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M19.25 18.75L5.75 5.25"
                  stroke="#333333"
                  strokeWidth="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </IconButton>
          }
          sx={{ mb: 2 }}
        >
          {t('Flash_message_update_equipment_status')}
        </Alert>
      </Collapse>
      {!isEmpty(equipments) ? (
        <>
          <div className="row company-spotlight--similarProduct">
            {equipments?.map((item, index) => (
              <div className="col-xl-6 margin-bottom" key={index}>
                <div className="result-box with-border h-lg-100">
                  <Box
                    display="flex"
                    justifyContent="space-between"
                    alignItems="center"
                  >
                    <StatusBox item={item} t={t} />
                    <Box
                      display="flex"
                      justifyContent="space-between"
                      gap="25px"
                      marginRight="10px"
                    >
                      <Box
                        sx={{ cursor: 'pointer' }}
                        onClick={() =>
                          handleShowConfirmation('delete', '', item)
                        }
                      >
                        <span style={{ color: '#838e9e' }}>
                          <FontAwesomeIcon icon={faTrashAlt} />
                        </span>
                      </Box>
                      <Box
                        sx={{ cursor: 'pointer' }}
                        onClick={() =>
                          navigate(
                            `/equipperManagementPortal/equipmentManagement/updateEquipment/${item.objectID}`
                          )
                        }
                      >
                        <span style={{ color: '#838e9e' }}>
                          <FontAwesomeIcon icon={faPencilAlt} />
                        </span>
                      </Box>
                    </Box>
                  </Box>
                  <div className="d-flex align-items-center justify-content-between margin-lr-0">
                    <div className="result-box__imageContent col-6 col-lg-5 h-100 d-lg-flex flex-column justify-content-between">
                      <div className="result-box__image">
                        <CustomImage
                          imageUrl={
                            item.equipper_equipment_picture &&
                            !item.equipper_equipment_picture.includes(
                              'equipment_library/Empty_state_equipment.png'
                            )
                              ? item.equipper_equipment_picture
                              : item.image_link
                          }
                          alt={t('Cant_load_image')}
                          className="w-75"
                        />
                        <button
                          className="c-black"
                          onClick={() =>
                            handleShowConfirmation('delete', '', item)
                          }
                        />
                      </div>
                    </div>
                    <div className="result-box__rightPart col-6 col-lg-7 p-0">
                      <div className="col-12">
                        <div className="result-box__top">
                          <div className="result-box__top-left">
                            <CustomTooltip
                              placement="bottom"
                              text={
                                item.preferred_equipment_name
                                  ? item.preferred_equipment_name
                                  : item.name
                              }
                            >
                              <h2 className="t-subheading-2 c-fake-black">
                                {item.preferred_equipment_name
                                  ? item.preferred_equipment_name
                                  : item.name}
                              </h2>
                            </CustomTooltip>
                            <div className="row">
                              <div className="result-left-bottom col-lg-12 col-lg-12">
                                <p className="t-body-small c-blue-grey">
                                  <strong className="t-body-small bold d-block">
                                    {t('Description')} :
                                  </strong>
                                  {isEmptyValue(
                                    detectLanguage === 'fr'
                                      ? item.description_fr
                                      : item.description
                                  )}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="col-lg-12 d-lg-block d-none">
                        <div className="top-content">
                          <div className="catalogue-prices flex w-100 mt-2 mr-4 mb-4">
                            {priceData(item, t).map(
                              (item, key) =>
                                item.name !== 'delivery_drop_coast' && (
                                  <div
                                    className="catalogue-prices--content c-near-grey col-4"
                                    key={key}
                                  >
                                    <span className="d-block t-caption-small c-neutrals-gray">
                                      {item.placeholder}
                                    </span>
                                    <p className="c-white t-subheading-2 bold c-fake-black">
                                      {FormatPrice(item.value)}
                                    </p>
                                    <span className="d-block t-caption-small">
                                      {item.currency?.toUpperCase()}
                                    </span>
                                  </div>
                                )
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="row d-lg-none d-flex mt-3 mb-3">
                    <div className="col-12">
                      <div className="top-content">
                        <div className="catalogue-prices flex w-100 mt-2 mr-4 mb-4">
                          {priceData(item, t).map(
                            (item, key) =>
                              item.name !== 'delivery_drop_coast' && (
                                <div
                                  className="catalogue-prices--content c-near-grey col-4"
                                  key={key}
                                >
                                  <span className="d-block t-caption-small c-neutrals-gray">
                                    {item.placeholder}
                                  </span>
                                  <p className="c-white t-subheading-2 bold c-fake-black">
                                    {FormatPrice(item.value)}
                                  </p>
                                  <span className="d-block t-caption-small">
                                    {item.currency?.toUpperCase()}
                                  </span>
                                </div>
                              )
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="result-box__top d-flex lg-absolute">
                    <div className="button-content w-100 mr-2">
                      {item?.status === 'booked' && (
                        <button
                          className="round-button yellow bold w-100 d-inline-flex align-items-center justify-content-center"
                          onClick={() =>
                            handleShowConfirmation('update', 'available', item)
                          }
                        >
                          {t('Make_it_available')}
                        </button>
                      )}
                      {item?.status === 'available' && (
                        <button
                          className="round-button yellow bold w-100 d-inline-flex align-items-center justify-content-center"
                          onClick={() =>
                            handleShowConfirmation('updateStatus', 'idle', item)
                          }
                        >
                          {t('Make_it_idle')}
                        </button>
                      )}
                      {item?.status === 'idle' && (
                        <button
                          className="round-button yellow bold w-100 d-inline-flex align-items-center justify-content-center"
                          onClick={() =>
                            handleShowConfirmation(
                              'updateStatus',
                              'available',
                              item
                            )
                          }
                        >
                          {t('Make_it_available')}
                        </button>
                      )}
                    </div>
                    <div className="button-content w-100">
                      <button
                        className="round-button black w-100 transparent extrabold no-minw-mob"
                        onClick={() =>
                          navigate(
                            `/equipperManagementPortal/equipmentManagement/equipmentDetails/${item.objectID}`
                          )
                        }
                      >
                        {t('View_details')}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <RenderIf condition={nbHits !== equipments.length}>
            <ViewMore
              onClick={() =>
                setHitsPerPage((prevHitsPerPage) => prevHitsPerPage + 6)
              }
            />
          </RenderIf>
          <RenderIf condition={showConfirmationModal}>
            <ConfirmationModal
              show={showConfirmationModal}
              onClose={() => setShowConfirmationModal(false)}
              action={() => handleAction(status)}
              isLoading={onAction}
              buttonText={
                action === 'delete' ? t('Delete') : t('Update_button')
              }
              message={message[action]}
              t={t}
            />
          </RenderIf>
          <RenderIf condition={showSuccessModal}>
            <SuccessPopUp
              onClose={onCloseSuccess}
              show={showSuccessModal}
              message={
                action.includes('update')
                  ? t('Flash_message_update_equipment_status')
                  : ''
              }
            />
          </RenderIf>
          <RenderIf condition={show}>
            <Popup
              t={t}
              onClose={() => onCloseFailure()}
              show={show}
              message={t('You_cant_delete_booked_equipment')}
            />
          </RenderIf>
        </>
      ) : (
        <NoResults t={t} image={EquipmentEmptyState} />
      )}
    </>
  );
};

export default EquipmentManagementTable;
