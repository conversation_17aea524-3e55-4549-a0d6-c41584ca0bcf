import React from 'react';
import { priceData } from '../../helpers/Data_helper';
import FormatPrice from '../../helpers/Price_helper';
import CustomImage from '../images/Custom_image';

export default function EquipmentCard({
  t,
  data,
  isLoading,
  showConfirmation,
  showDetails,
  detectLanguage,
  switchPlace
}) {
  const prices = priceData(data, t);
  return (
    <div className="equipments-content">
      <div className="row">
        <div className="col-lg-2 padding-l-0">
          <CustomImage
            imageUrl={
              data.equipper_equipment_picture &&
              !data.equipper_equipment_picture.includes(
                'equipment_library/Empty_state_equipment.png'
              )
                ? data.equipper_equipment_picture
                : data.image_link
            }
            alt="equipment asset"
          />
        </div>

        <div className={`col-lg-5 status ${data?.status}`}>
          <h2 className="t-header-30 extraBold">
            {data?.preferred_equipment_name ||
              (detectLanguage === 'fr' ? data?.name_fr : data?.name)}
          </h2>
        </div>
        <div className="col-lg-5">
          <div className="catalogue-prices row w-100 text-right mt-2 mr-4">
            {prices?.map((data, key) => (
              <div
                className="catalogue-prices--content t-header-medium c-near-grey bold col-4"
                key={key}
              >
                <p>{data.placeholder}</p>
                <p className="c-white t-subheading-2 bold c-fake-black">
                  {FormatPrice(data.value)}
                </p>
                <span className="d-block t-caption-small">
                  {data.currency?.toUpperCase()}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div
          className={
            switchPlace
              ? 'actions justify-content-end'
              : 'actions d-flex justify-content-end'
          }
        >
          <button
            className="round-button black border-black bold"
            onClick={showDetails}
          >
            {t('View_details')}
          </button>
          {data?.status === 'booked' && (
            <button
              className="round-button border-yellow bold"
              onClick={() => showConfirmation('update', 'available', data)}
            >
              {t('Make_it_available')}
            </button>
          )}
          {data?.status === 'available' && (
            <button
              className="round-button border-yellow bold"
              onClick={() => showConfirmation('updateStatus', 'idle', data)}
            >
              {t('Make_it_idle')}
            </button>
          )}
          {data?.status === 'idle' && (
            <button
              className="round-button border-yellow bold ml-2"
              onClick={() =>
                showConfirmation('updateStatus', 'available', data)
              }
            >
              {t('Make_it_available')}
            </button>
          )}
          <button
            className="round-button yellow bold ml-10"
            onClick={() => showConfirmation('delete', 'deleted', data)}
            disabled={isLoading}
          >
            {t('Delete')}
          </button>
        </div>
      </div>
    </div>
  );
}
