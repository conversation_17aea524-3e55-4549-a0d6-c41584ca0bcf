import React from 'react';
import EquipmentCard from './Equipment_card';

export default function EquipmentCardPerItem({
  equipment,
  t,
  detectLanguage,
  handleShowConfirmation,
  handleShowDetails,
  switchPlace
}) {
  return (
    <EquipmentCard
      data={equipment}
      showConfirmation={(action, status) =>
        handleShowConfirmation(action, status, equipment)
      }
      showDetails={() => handleShowDetails(equipment)}
      detectLanguage={detectLanguage}
      t={t}
      switchPlace={switchPlace}
    />
  );
}
