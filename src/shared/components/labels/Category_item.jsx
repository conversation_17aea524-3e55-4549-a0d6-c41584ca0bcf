import React from 'react';

export default function CategoryItem({
  selected,
  item,
  onClick,
  category,
  t,
  attribute
}) {
  return (
    <li
      className={selected ? `${item.label.replaceAll(' ', '_')}  menu-active c-fake-black t-body-large`  : `${item.label.replaceAll(' ', '_')} c-fake-black t-body-large`}
      name={item.label}
      onClick={onClick}
    >
      <span>
        {category !== '' && attribute === 'sub_category'
          ? t(item.label?.replaceAll(' ', '_'))
          : item.label}
      </span>
    </li>
  );
}
