import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import Select from 'react-select';

const MultiSelect = ({
  options,
  handleChange,
  value,
  name,
  isGeoLocation,
  defaultValue,
  className,
  disabled,
  placeholder,
  allowSelectAll,
  isMulti,
  t
}) => {
  const [optionsState, setOptionsState] = useState(options);
  const customStyles = {
    control: (base) => ({
      ...base,
      border: className.includes('is-invalid')
        ? '1px solid #dc3545'
        : '1px solid #E8ECF2',
      borderRadius: '5px',
      minHeight: '48px',
      margin: '0'
    }),
    placeholder: (base) => ({
      ...base,
      color: '#838E9E'
    }),
    valueContainer: (base) => ({
      ...base,
      padding: '0px 10px',
      display: 'flex',
      flexWrap: 'no-wrap !important',
      overflow: 'hidden !important'
    }),
    valueContainer2: (base) => ({
      ...base,
      padding: '0px 10px',
      display: 'flex',
      flexWrap: 'no-wrap !important',
      overflow: 'hidden !important',
      overflowX: 'scroll'
    }),
    multiValue: (base) => ({
      ...base,
      backgroundColor: '#E8ECF2 !important',
      border: '1px solid #E8ECF2 !important',
      borderRadius: '4px !important',
      boxShadow: 'none !important',
      minWidth: 'max-content !important'
    }),

    disabled: (base) => ({
      ...base,
      backgroundColor: '#e9ecef',
      opacity: '1'
    }),

    option: (base, state) => ({
      ...base,
      backgroundColor: '#ffffff',
      zIndex: '9999999999 !important',
      background: state.isSelected
        ? 'var(--accent-50, rgba(236, 168, 105, 0.20))'
        : '#ffffff',
      color: '#000000',
      '&:hover': {
        backgroundColor: '#ffffff',
        color: '#000000'
      },
      '&:active': {
        backgroundColor: '#ffffff',
        color: '#000000'
      },
      '&:focus': {
        backgroundColor: '#ffffff',
        color: '#000000'
      }
    }),

    menu: (base) => ({
      ...base,
      zIndex: '9999999999 !important'
    }),

    menuList: (base) => ({
      ...base,
      maxHeight: '200px',
      overflowY: 'auto',
      overflowX: 'hidden',
      '&::-webkit-scrollbar': {
        width: '4px',
        backgroundColor: '#ffffff'
      },
      '&::-webkit-scrollbar-thumb': {
        backgroundColor: '#E8ECF2',
        borderRadius: '5px'
      }
    }),

    multiValueRemove: (base, state) =>
      state.data.isFixed ? { ...base, display: 'none' } : base
  };

  const onChange = (selected) => {
    if (
      allowSelectAll &&
      selected?.length > 0 &&
      selected[selected.length - 1].value === allOption.value
    ) {
      return handleChange(options);
    }
    return handleChange(selected);
  };

  const allOption = {
    label: t('Select_all'),
    value: 'all'
  };

  useEffect(() => {
    const getOptions = () => {
      if (allowSelectAll && options?.length > 0) return [allOption, ...options];

      if (!allowSelectAll)
        return isGeoLocation
          ? options
          : options?.map((el) => {
              return { ...el, label: t(el.label) };
            });
    };

    setOptionsState(getOptions());
  }, [options]);

  return (
    <Select
      name={name}
      options={optionsState}
      closeMenuOnSelect={!isMulti}
      closeMenuOnScroll={!isMulti}
      styles={customStyles}
      isMulti={isMulti}
      hideSelectedOptions={false}
      onChange={(value) => onChange(value)}
      value={value}
      defaultValue={defaultValue}
      theme={(theme) => ({
        ...theme,
        border: 0,
        colors: {
          ...theme.colors,
          primary25: 'white',
          primary: '#d1d1d1'
        }
      })}
      isDisabled={disabled}
      placeholder={placeholder}
      isClearable
      allowSelectAll={allowSelectAll}
      noOptionsMessage={() => t('No_options')}
    />
  );
};

export default MultiSelect;

MultiSelect.propTypes = {
  options: PropTypes.array,
  handleChange: PropTypes.func,
  value: PropTypes.array,
  defaultValue: PropTypes.array,
  disabled: PropTypes.bool,
  placeholder: PropTypes.string,
  allowSelectAll: PropTypes.bool,
  isMulti: PropTypes.bool
};

MultiSelect.defaultProps = {
  options: [],
  handleChange: () => {},
  disabled: false,
  placeholder: '',
  isGeoLocation: false,
  allowSelectAll: false,
  isMulti: false,
  className: ''
};
