import React, {
  useRef,
  useState,
  useEffect,
  useCallback,
  useMemo
} from 'react';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import ListItemText from '@mui/material/ListItemText';
import Select from '@mui/material/Select';
import Checkbox from '@mui/material/Checkbox';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import OutlinedInput from '@mui/material/OutlinedInput';
import Input from '@mui/material/Input';
import InputAdornment from '@mui/material/InputAdornment';
import IconButton from '@mui/material/IconButton';
import PropTypes from 'prop-types';
import makeStyles from '@mui/styles/makeStyles';
import { isEmpty } from 'lodash';
import { FixedSizeList as List } from 'react-window';

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 8 + ITEM_PADDING_TOP,
      width: 320
    }
  }
};

const useStyles = makeStyles({
  transparentBorder: {
    border: '1px solid #e8ecf2 !important ',
    maxHeight: '60px',
    padding: '0 10px',
    color: '#000000'
  },
  customInputRoot: {
    '&:after': {
      borderBottom: '2px solid #eca869 '
    }
  },
  customInput: {
    fontFamily: '"airbnb-cereal-medium", sans-serif',
    fontWeight: 'normal',
    fontSize: '16px',
    lineHeight: '24px',
    color: '#838E9E',
    fontStyle: 'normal',
    borderRadius: '5px',
    height: '48px',
    padding: '12px 10px',
    width: 'auto'
  },
  customChipLabel: {
    padding: '0px 5px ',
    fontFamily: '"airbnb-cereal-medium", sans-serif',
    fontWeight: 'normal ',
    fontSize: '12px ',
    color: '#333333'
  },
  customInputRootAfter: {
    '&:after': {
      borderBottom: '2px solid #eca869 '
    }
  },
  customMenuItemSelected: {
    '&.Mui-selected': {
      background: '#f3f6f9 ',
      '&:hover': {
        background: '#f3f6f9 '
      }
    }
  },
  customCheckboxChecked: {
    '&.Mui-checked': {
      color: ' #eca869'
    }
  },
  customCheckboxRoot: {
    borderRadius: '5px ',
    color: '#e1e2eb ',
    padding: '0 ',
    marginRight: '8px '
  },
  customFormControl: {
    margin: '0 '
  },
  customSelectInput: {
    padding: '0 !important',
    display: 'flex',
    flexWrap: 'nowrap',
    fontFamily: '"airbnb-cereal-medium", sans-serif !important '
  },
  customChipRoot: {
    fontFamily: '"airbnb-cereal-medium", sans-serif',
    backgroundColor: '#EEF2F6',
    marginRight: '8px ',
    fontSize: '14px ',
    fontStyle: 'normal ',
    fontWeight: '400 ',
    lineHeight: '20px ',
    color: '#061C3D'
  },
  customSelectText: {
    p: {
      fontFamily: '$primary-font !important',
      fontWeight: 'normal !important',
      fontSize: '16px !important',
      lineHeight: '24px !important',
      color: '$neutrals-gray !important',
      fontStyle: 'normal !important',
      borderRadius: '5px !important',
      height: '48px !important',
      width: 'auto !important',
      padding: '12px 18px !important'
    }
  }
});

const MultipleSelectCheckmarks = ({
  options: rawOptions,
  t,
  disabled,
  placeholder,
  name,
  isClearable,
  key,
  onChange,
  value
}) => {
  const [selectedValues, setSelectedValues] = useState(value || []);
  const [visibleOptions, setVisibleOptions] = useState([]);
  const [searchText, setSearchText] = useState('');

  const selectRef = useRef(null);
  const inputRef = useRef(null);
  const [startIndex, setStartIndex] = useState(0);
  const classes = useStyles();

  const options = useMemo(() => rawOptions, [rawOptions]);

  const handleChange = useCallback(
    (event) => {
      const { value } = event?.target;
      let newSelectedValues = [];

      if (value.includes('select-all')) {
        const currentlyVisibleValues =
          visibleOptions?.map((option) => option.value) || [];
        const areAllVisibleSelected = currentlyVisibleValues.every((v) =>
          selectedValues.includes(v)
        );

        if (areAllVisibleSelected) {
          // Deselect only the currently visible options
          newSelectedValues = selectedValues.filter(
            (v) => !currentlyVisibleValues.includes(v)
          );
        } else {
          // Add all currently visible options to the selection
          const set = new Set(selectedValues);
          currentlyVisibleValues.forEach((v) => set.add(v));
          newSelectedValues = Array.from(set);
        }
      } else {
        newSelectedValues = selectedValues.includes(value[0])
          ? selectedValues.filter((selectedValue) => selectedValue !== value[0])
          : [...selectedValues, value[0]];
      }

      onChange(newSelectedValues);
      setSelectedValues(newSelectedValues);
    },
    [visibleOptions, selectedValues, onChange]
  );

  const handleSearchChange = useCallback((event) => {
    setSearchText(event.target.value);
    setStartIndex(0);
  }, []);

  const handleDelete = useCallback(
    (valueToDelete, event) => {
      event.stopPropagation();
      const newSelectedValues = selectedValues.filter(
        (value) => value !== valueToDelete
      );
      setSelectedValues(newSelectedValues);
      onChange(newSelectedValues);
    },
    [onChange, selectedValues]
  );

  const handleDeleteMore = useCallback(
    (valuesToDelete, event) => {
      event.stopPropagation();
      const newSelectedValues = selectedValues.filter(
        (value) => !valuesToDelete.includes(value)
      );
      setSelectedValues(newSelectedValues);
      onChange(newSelectedValues);
    },
    [onChange, selectedValues]
  );

  const renderSelectedChips = useCallback(
    (selected) => (
      <>
        {selected.length === 0 ? (
          <p>{placeholder}</p>
        ) : (
          <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
            {selected?.slice(0, 2)?.map((value) => (
              <Chip
                key={value}
                label={options?.find((option) => option.value === value)?.label}
                onDelete={(event) => handleDelete(value, event)}
                classes={{ label: classes.customChipLabel }}
                deleteIcon={
                  isClearable && (
                    <IconButton
                      size="small"
                      aria-label="delete"
                      disabled={disabled}
                      onMouseDown={(event) => event.stopPropagation()}
                    >
                      <svg
                        width="10"
                        height="10"
                        viewBox="0 0 25 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M19.25 5.25L5.75 18.75"
                          stroke="#333333"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M19.25 18.75L5.75 5.25"
                          stroke="#333333"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </IconButton>
                  )
                }
              />
            ))}
            {selected.length > 2 && (
              <Chip
                label={`+${selected.length - 2}`}
                onDelete={(event) => handleDeleteMore(selected.slice(2), event)}
                deleteIcon={
                  isClearable && (
                    <IconButton
                      size="small"
                      aria-label="delete"
                      onMouseDown={(event) => event.stopPropagation()}
                    >
                      <svg
                        width="10"
                        height="10"
                        viewBox="0 0 25 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M19.25 5.25L5.75 18.75"
                          stroke="#333333"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M19.25 18.75L5.75 5.25"
                          stroke="#333333"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </IconButton>
                  )
                }
              />
            )}
          </Box>
        )}
      </>
    ),
    [handleDelete, handleDeleteMore, placeholder, options, isClearable]
  );

  useEffect(() => {
    setSelectedValues(value || []);
  }, [value]);

  useEffect(() => {
    const filteredOptions = options?.filter((option) =>
      option.label?.toLowerCase().includes(searchText.toLowerCase())
    );
    setVisibleOptions(filteredOptions);
  }, [options, startIndex, searchText]);

  const handleKeyDown = useCallback((event) => {
    event.stopPropagation();
  }, []);

  const highlightText = useCallback(
    (text) => {
      if (!searchText) return text;
      const query = searchText.trim();
      if (!query) return text;
      const lower = text?.toLowerCase?.() || '';
      const i = lower.indexOf(query.toLowerCase());
      if (i === -1) return text;
      const before = text.slice(0, i);
      const match = text.slice(i, i + query.length);
      const after = text.slice(i + query.length);
      return (
        <span>
          {before}
          <span
            style={{
              backgroundColor: '#FFF4CC',
              borderRadius: 3,
              padding: '0 2px'
            }}
          >
            {match}
          </span>
          {after}
        </span>
      );
    },
    [searchText]
  );

  const renderOption = ({ index, style }) => {
    const option = visibleOptions[index];
    return (
      <MenuItem
        key={`${option.value}-${index}`}
        value={option.value}
        id={`${option.value}-${index}-id`}
        style={style}
        onClick={() => handleChange({ target: { value: [option.value] } })}
      >
        <Checkbox
          checked={!!selectedValues.includes(option.value)}
          name={`${option.value}-${index}`}
          id={`${option.value}-${index}-id-check`}
          classes={{
            root: classes.customCheckboxRoot,
            checked: classes.customCheckboxChecked
          }}
        />
        <ListItemText primary={highlightText(option.label)} />
      </MenuItem>
    );
  };

  return (
    <FormControl
      sx={{ width: '100%' }}
      classes={{ root: classes.customFormControl }}
    >
      <Select
        labelId={`demo-multiple-checkbox-label-${name}`}
        id={`demo-multiple-checkbox-${name}`}
        multiple
        key={key}
        disabled={disabled}
        ref={selectRef}
        className={classes.customSelectInput}
        label={placeholder}
        name={name}
        displayEmpty
        input={
          <OutlinedInput
            classes={{ notchedOutline: classes.transparentBorder }}
            id={`select-multiple-chip-${name}`}
            name={name}
            disabled={disabled}
            className={classes.customInput}
            ref={inputRef}
            onKeyDown={handleKeyDown}
          />
        }
        value={selectedValues}
        inputProps={{ 'aria-label': 'Without label' }}
        renderValue={renderSelectedChips}
        MenuProps={MenuProps}
      >
        <MenuItem
          classes={{ root: classes.customMenuItemSelected }}
          sx={{ position: 'sticky', top: 0, zIndex: 2, bgcolor: '#fff' }}
        >
          <Input
            placeholder={t('Search')}
            value={searchText}
            name="searchText"
            id="searchText-id"
            className={classes.customInputRoot}
            onChange={handleSearchChange}
            sx={{ width: '100%' }}
            onKeyDown={handleKeyDown}
            startAdornment={
              <InputAdornment position="start">
                <Box sx={{ pr: 1, pl: 0.5 }}>
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle
                      cx="11"
                      cy="11"
                      r="7"
                      stroke="#838E9E"
                      strokeWidth="2"
                    />
                    <path
                      d="M20 20L16.65 16.65"
                      stroke="#838E9E"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                  </svg>
                </Box>
              </InputAdornment>
            }
          />
        </MenuItem>
        {!isEmpty(visibleOptions) ? (
          <MenuItem
            key="select-all"
            id="select-all-id"
            value="select-all"
            onClick={() => handleChange({ target: { value: ['select-all'] } })}
            classes={{ root: classes.customMenuItemSelected }}
            sx={{
              position: 'sticky',
              top: 48,
              zIndex: 1,
              bgcolor: '#fff',
              borderBottom: '1px solid #f0f0f0'
            }}
          >
            <Checkbox
              checked={
                visibleOptions?.length > 0 &&
                visibleOptions.every((o) => selectedValues.includes(o.value))
              }
              indeterminate={
                visibleOptions?.length > 0 &&
                visibleOptions.some((o) => selectedValues.includes(o.value)) &&
                !visibleOptions.every((o) => selectedValues.includes(o.value))
              }
              id="select-all-check"
              name="select-all"
              classes={{
                root: classes.customCheckboxRoot,
                checked: classes.customCheckboxChecked
              }}
            />
            <ListItemText
              primary={`${t('Select_all')} (${
                visibleOptions.filter((o) => selectedValues.includes(o.value))
                  .length
              }/${visibleOptions.length})`}
            />
          </MenuItem>
        ) : (
          <ListItemText
            sx={{ textAlign: 'center', color: '#d9d9d9' }}
            primary={t('No_options')}
          />
        )}
        {!isEmpty(visibleOptions) && (
          <List
            height={ITEM_HEIGHT * 6}
            itemCount={visibleOptions.length}
            itemSize={ITEM_HEIGHT}
            width="100%"
          >
            {renderOption}
          </List>
        )}
      </Select>
    </FormControl>
  );
};

MultipleSelectCheckmarks.propTypes = {
  options: PropTypes.array.isRequired,
  t: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  name: PropTypes.string,
  key: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  value: PropTypes.array
};

MultipleSelectCheckmarks.defaultProps = {
  options: [],
  isClearable: true,
  t: (text) => text,
  onChange: () => {}
};

export default MultipleSelectCheckmarks;
