import {
  faChevronDown,
  faChevronUp
} from '@fortawesome/fontawesome-free-solid';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const Collapse = ({ children, showDetails, handleShowDetails, label, t }) => {
  return (
    <>
      {!showDetails && (
        <p className="mb-0">
          <button
            className="t-subheading-3 c-fake-black bold  c-primary-color d-flex align-items-center justify-content-between transparent"
            onClick={handleShowDetails}
            type="button"
          >
            {t(label)}
            <span className="m-2">
              <FontAwesomeIcon
                icon={showDetails ? faChevronUp : faChevronDown}
              />
            </span>
          </button>
        </p>
      )}

      <div>{showDetails && children}</div>

      {showDetails && (
        <p className="mb-0">
          <button
            className="t-subheading-3 c-fake-black bold  c-primary-color d-flex align-items-center justify-content-between transparent"
            onClick={handleShowDetails}
            type="button"
          >
            {t(label)}
            <span className="m-2">
              <FontAwesomeIcon
                icon={showDetails ? faChevronUp : faChevronDown}
              />
            </span>
          </button>
        </p>
      )}
    </>
  );
};

export default Collapse;
