import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useRequest } from '../../context/Requests_context';
import { clearSessionStorage } from '../../helpers/Session_storage_helper';
import RequestConfirmationModal from './Request_confirmation_modal';
import SuccessPopUp from './Success_pop_up';
import Popup from './Popup';
import { useCreditCheckFormContext } from '../../context/Credit_check_form_context';
import { useProjects } from '../../context/Project_context';
import { usePromotions } from '../../context/Promotion_context';
import { BOOK_EQUIPMENT } from '../../helpers/Url_prefixes';

export default function BookingModal({
  isOpen,
  data,
  hoursPicker,
  handleClose,
  detectLanguage,
  t
}) {
  const navigate = useNavigate();
  const { BookEquipment } = useRequest();
  const { GetPromotionByEquipperAndLodgerID } = usePromotions();
  const { GetMyCreditCheckForm } = useCreditCheckFormContext();
  const { GetProjects } = useProjects();
  const [showSuccessPopUp, setShowSuccessPopUp] = useState(false);
  const [promotion, setPromotion] = useState(0);
  const [errorPrefix, setErrorPrefix] = useState('');
  const [showPopUp, setShowPopup] = useState(false);
  const [response, setResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [creditCheckForms, setCreditCheckForms] = useState([]);
  const [projects, setProjects] = useState([]);

  function handleShowSuccessPopUp() {
    setShowSuccessPopUp(!showSuccessPopUp);
  }

  function onCloseSuccessPopUp() {
    setShowSuccessPopUp(false);
    clearSessionStorage();
    navigate('/renterManagementPortal');
  }

  function handleShowPopUp() {
    setShowPopup(!showPopUp);
  }
  async function bookingEquipment(event) {
    setIsLoading(true);
    const requestData = {
      ...event,
      ...event.insurance,
      expiry_date_of_insurance: new Date(
        event.insurance.expiry_date_of_insurance
      ),
      billing_address: {
        ...event.billing_address,
        country: event.billing_address?.country.label,
        state: event.billing_address?.state.label,
        city: event.billing_address?.city.label
      },
      delivery_address: {
        ...event.delivery_address,
        country: event.delivery_address?.country.label,
        state: event.delivery_address?.state.label,
        city: event.delivery_address?.city.label
      },
      company_name: event.company,
      payment_method: event.payment_method?.value,
      project_id: event?.project?.id || '',
      delivery_type: event.isDelivery ? event.delivery_type : '',
      equipment_id: data?.objectID,
      equipment_internal_id: data?.internal_id,
      credit_check_form: event.credit_check_form?.value,
      equipment_name_fr: data?.name_fr,
      equipper_id: data?.equipper_id,
      need_one: !event.need_one,
      comment: event.comment || '',
      currency: data?.currency || 'usd',
      delivery_details: {
        delivery_preference: event.isDelivery ? 'delivery' : 'pick-up',
        drop: event.pickup_time.drop.toISOString().substring(11, 16),
        pickup: event.pickup_time.pickup.toISOString().substring(11, 16),
        pickup_time_validator: event.pickup_time.pickup_time_validator
      },
      datePicker_validator: event.datePicker_validator
    };

    const response = await BookEquipment(requestData);
    if (response.status === 200 || response.status === 201) {
      if (event.isCreditAccount) {
        handleShowSuccessPopUp();
      } else {
        window.location.href = response.data.checkout_url;
      }
    } else {
      setErrorPrefix(t('Booked_equipment_alert'));
      setResponse(response);
      handleShowPopUp();
    }
  }

  useEffect(async () => {
    const ccfRes = await GetMyCreditCheckForm();
    if (ccfRes.status === 200 && ccfRes.data) {
      setCreditCheckForms(
        ccfRes.data?.map((data) => ({
          value: data,
          label: data?.name
        }))
      );
    }
  }, []);
  useEffect(async () => {
    const res = await GetPromotionByEquipperAndLodgerID(data.equipper_id);
    if (res.status === 200 && res.data) {
      setPromotion(res.data);
    }
  }, []);
  useEffect(() => {
    (async () => {
      const response = await GetProjects();
      if (response.status === 200 && response.data) {
        setProjects(
          response.data.map((project) => {
            return {
              value: project,
              label: project.name
            };
          })
        );
      }
    })();
  }, []);

  return (
    <>
      <RequestConfirmationModal
        isLoading={isLoading}
        detectLanguage={detectLanguage}
        onSubmitFn={bookingEquipment}
        hoursPicker={hoursPicker}
        title={t('Reservation_details')}
        promotion={promotion}
        textButton={t('Book_now')}
        handleClose={handleClose}
        creditCheckForms={creditCheckForms}
        projects={projects}
        data={data}
        show={isOpen}
        isBooking
        t={t}
      />

      <SuccessPopUp
        message={t('Booking_success_message')}
        onClose={onCloseSuccessPopUp}
        show={showSuccessPopUp}
      />

      <Popup
        t={t}
        response={response}
        prefix={BOOK_EQUIPMENT}
        message={errorPrefix}
        show={showPopUp}
        onClose={() => {
          setShowPopup(false);
          handleClose();
        }}
      />
    </>
  );
}
