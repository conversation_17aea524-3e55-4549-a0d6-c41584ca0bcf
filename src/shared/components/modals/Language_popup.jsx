import React from 'react';

export default function LanguagePopup({
  setLang,
  setCountry,
  changeLanguage,
  setShowLanguagePopup,
  setCookies
}) {
  const handleLanguageSelect = (selectedLang, selectedCountry) => {
    setLang(selectedLang);
    setCountry(selectedCountry);
    changeLanguage(selectedLang);
    setCookies('lang', selectedLang, 31536000); // 1 year in seconds
    setCookies('country', selectedCountry, 31536000);
    setShowLanguagePopup(false); // Close the popup
  };

  return (
    <div className="popup-overlay">
      <div className="popup-container">
        <h2 className="popup-heading">
          Select Your Country to Find Local Equipment Rentals and View the Site
          in Your Language.
        </h2>
        <div className="flags">
          <button
            onClick={() => handleLanguageSelect('en', 'US')}
            className="flag"
          >
            <img src="https://flagcdn.com/w320/us.png" alt="United States" />
            <span>US - English</span>
          </button>

          <button
            onClick={() => handleLanguageSelect('ar', 'SA')}
            className="flag"
          >
            <img src="https://flagcdn.com/w320/sa.png" alt="Saudi Arabia" />
            <span>KSA - العربية</span>
            <span className="mobile-only">
              حدد بلدك للعثور على معدات للإيجار محليًا وعرض الموقع بلغتك.
            </span>
          </button>

          <button
            onClick={() => handleLanguageSelect('en', 'CA')}
            className="flag"
          >
            <img src="https://flagcdn.com/w320/ca.png" alt="Canada" />
            <span>CAN - English</span>
          </button>

          <button
            onClick={() => handleLanguageSelect('fr', 'CA')}
            className="flag"
          >
            <img src="https://flagcdn.com/w320/ca.png" alt="Quebec" />
            <span>CAN - Français</span>
          </button>
        </div>
      </div>
    </div>
  );
}
