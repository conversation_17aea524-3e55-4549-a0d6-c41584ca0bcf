import React from 'react';
import Modal from './Modal';

export default function OpenHoursModal({
  handleShowModal,
  t,
  isForShow,
  workHours,
  setWorkHours
}) {
  const handleChange = (e, index) => {
    const { name, value, checked } = e.target;
    const list = [...workHours];
    if (checked === true) {
      list[index].open = '';
      list[index].close = '';
      list[index].day_off = true;
    } else {
      list[index][name] = value;
      list[index].day_off = false;
    }
    setWorkHours(list);
  };
  return (
    <Modal
      onClose={handleShowModal}
      t={t}
      noScrollModal
      classNameDescription={
        't-subheading-1 c-fake-black text-center bold text-center t-header-h5 c-grey-titles'
      }
      description={isForShow ? '' : t('Opening_hours_text')}
      className={isForShow ? 'static-hours-modal' : 'modify-profil-modal '}
    >
      <div className="infos-hours text-start">
        <div className="row justify-content-center">
          <div
            className={`${isForShow ? '' : 'col-md-11 open-hours-equipper'}`}
          >
            {workHours?.map(
              ({ day_of_week, open, close, day_off, index }, key) => (
                <>
                  <div className="row justify-content-center align-items-center border-mobile">
                    <div
                      className={`${isForShow ? 'col-5' : 'col-md-3 mb-3'}`}
                      key={key + day_of_week}
                    >
                      <p className="t-body-large c-fake-black">
                        <strong className="t-body-large c-fake-black">
                          <span className="t-body-large c-blue-grey bold">
                            {t(day_of_week)}
                          </span>
                        </strong>
                      </p>
                    </div>
                    <div className={`${isForShow ? 'col-7' : 'col-md-9'}`}>
                      {isForShow ? (
                        <p className="t-body-large c-neutrals-gray text-end">
                          <strong className="t-body-large">
                            {day_off
                              ? t('Closed')
                              : !day_off && open === ''
                              ? 'N/A'
                              : `${open} - ${close}`}
                          </strong>
                        </p>
                      ) : (
                        <div
                          className={`${
                            isForShow ? 'row' : 'row align-items-center mb-3'
                          }`}
                        >
                          <div
                            className={`${isForShow ? 'col-lg-7' : 'col-sm-4'}`}
                          >
                            <p className="form-group mb-0">
                              <input
                                name="open"
                                type="time"
                                value={open}
                                className="form-control"
                                onChange={(e) => handleChange(e, index)}
                              />
                            </p>
                          </div>
                          <div
                            className={`${isForShow ? 'col-lg-7' : 'col-sm-4'}`}
                          >
                            <p className="form-group mb-0">
                              <input
                                name="close"
                                type="time"
                                value={close}
                                className="form-control"
                                onChange={(e) => handleChange(e, index)}
                              />
                            </p>
                          </div>
                          <div className="col-sm-4">
                            <div className="d-flex label-check">
                              <input
                                name="day_off"
                                type="checkbox"
                                id={`check${index + day_of_week}`}
                                checked={day_off === true}
                                onChange={(e) => handleChange(e, index)}
                              />
                              <label
                                className="padding-rigth-2 d-block"
                                htmlFor={`check${index + day_of_week}`}
                              >
                                {t('Closed')}
                              </label>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )
            )}
          </div>
        </div>
      </div>
      <div className="text-align-center fixed-button-modal">
        <button
          className="round-button yellow bold  mt-3"
          type="button"
          onClick={() => handleShowModal()}
        >
          {isForShow ? t('Close_button') : t('Submit_opening_hours_message')}
        </button>
      </div>
    </Modal>
  );
}
