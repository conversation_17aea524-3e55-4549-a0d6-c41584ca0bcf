import React from 'react';
import RenderIf from '../Render_if';
import CustomImage from '../images/Custom_image';
import useResponsive from '../../helpers/Responsive';

export default function Modal({
  className,
  children,
  onClose,
  subTitle,
  description,
  image,
  t,
  classNameDescription,
  noScrollModal
}) {
  const { isMobile } = useResponsive();

  return (
    <div className="modal">
      <div className={`modal-content ${className}`}>
        <button className="close-button" onClick={onClose}>
          <svg
            width="25"
            height="24"
            viewBox="0 0 25 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19.25 5.25L5.75 18.75"
              stroke="#333333"
              strokeWidth="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M19.25 18.75L5.75 5.25"
              stroke="#333333"
              strokeWidth="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
        <div className="row justify-content-lg-start justify-content-center">
          <div className="col-lg-12 mx-auto">
            <div
              className={
                (!noScrollModal && isMobile) || !noScrollModal
                  ? 'scrollBarModal'
                  : ''
              }
            >
              <div className="top-content">
                <RenderIf condition={image}>
                  <CustomImage imageUrl={image} alt={t('Cant_load_image')} />
                </RenderIf>
                <RenderIf condition={description}>
                  <p className={classNameDescription}>{t(description)}</p>
                </RenderIf>
                <RenderIf condition={subTitle}>
                  <p className="subTitle-modal subTitle">{t(subTitle)}</p>
                </RenderIf>
              </div>
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

Modal.defaultProps = {
  className: '',
  description: '',
  image: '',
  noScrollModal: false
};
