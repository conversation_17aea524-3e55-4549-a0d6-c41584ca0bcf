import { useState } from 'react';
import { Button } from 'react-bootstrap';
import FilterByCategorySubcategory from '../../../components/algolia/Filterby_category_subcategory';
import Modal from './Modal';
import dots from '../../../style/assets/img/Icons/DotsNine.svg';

const Filter = ({
  t,
  isEquipmentManagement,
  isAvailableInventory,
  setObjectIDs
}) => {
  const [show, setShow] = useState(false);
  const handleShow = () => {
    setShow(!show);
  };
  return (
    <>
      <div className="map-button-container">
        <Button
          className={`button-style 
          round-button d-flex align-items-center ${
            isEquipmentManagement ? 'yellow' : 'yellow'
          } bold
          filter-btn isSm t-subheading-3 c-white mr-10 margin-top-10 mb-2`}
          onClick={() => handleShow()}
        >
          <img src={dots} className="mr-2" alt="icon" />
          {t('Categories')}
        </Button>
      </div>
      <div className={show ? '' : 'hidden'}>
        <Modal t={t} onClose={handleShow} className="t-header-h5 c-grey-titles">
          <FilterByCategorySubcategory
            t={t}
            isEquipmentManagement={isEquipmentManagement}
            isAvailableInventory={isAvailableInventory}
            setObjectIDs={setObjectIDs}
          />
          <div className="fixed-button-modal">
            <button
              className="round-button yellow bold c-black width-100"
              onClick={() => handleShow()}
            >
              {t('Apply_filters')}
            </button>
          </div>
        </Modal>
      </div>
    </>
  );
};

export default Filter;
