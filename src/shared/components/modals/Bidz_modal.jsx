import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useBidzContext } from '../../context/Bidz_context';
import RequestConfirmationModal from './Request_confirmation_modal';
import { SEND_BIDZ_REQUEST } from '../../helpers/Url_prefixes';
import SuccessPopUp from './Success_pop_up';
import RenderIf from '../Render_if';
import Popup from './Popup';
import { isEmpty } from 'lodash';
import { useCreditCheckFormContext } from '../../context/Credit_check_form_context';
import { useProjects } from '../../context/Project_context';

export default function BidzModal({
  isOpen,
  handleClose,
  bidzRequest,
  detectLanguage,
  t
}) {
  const navigate = useNavigate();
  const { SubmitBidzRequest } = useBidzContext();
  const { GetMyCreditCheckForm } = useCreditCheckFormContext();
  const { GetProjects } = useProjects();
  const [data, setData] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [response, setResponse] = useState('');
  const [creditCheckForms, setCreditCheckForms] = useState([]);
  const [projects, setProjects] = useState([]);

  const onClose = () => {
    setShowSuccessModal(!showSuccessModal);
    navigate('/renterManagementPortal/bidzManagement');
  };

  useEffect(async () => {
    const ccfRes = await GetMyCreditCheckForm();
    if (ccfRes.status === 200 && ccfRes.data) {
      setCreditCheckForms(
        ccfRes.data?.map((data) => ({
          value: data,
          label: data?.name
        }))
      );
    }
  }, []);

  useEffect(() => {
    (async () => {
      const response = await GetProjects();
      if (response.status === 200 && response.data) {
        setProjects(
          response.data.map((project) => {
            return {
              value: project,
              label: project.name
            };
          })
        );
      }
    })();
  }, []);

  useEffect(() => {
    setData(bidzRequest);
  }, [bidzRequest]);
  async function sendBidzRequest(event) {
    setIsLoading(true);
    const response = await SubmitBidzRequest({
      ...event,
      ...data,
      ...event.insurance,
      delivery_address: {
        ...event.delivery_address,
        country: event.delivery_address.country.label,
        state: event.delivery_address.state.label,
        city: event.delivery_address.city.label
      },
      billing_address: {
        ...event.billing_address,
        country: event.billing_address.country.label,
        state: event.billing_address.state.label,
        city: event.billing_address.city.label
      },
      credit_check_form: event.credit_check_form?.credit_check_form_path,
      payment_method: event.isCreditAccount ? 'credit account' : 'credit card',
      delivery_preference: event.isDelivery ? 'delivery' : 'pick-up',
      project_id: event.project?.id || '',
      status: 'pending',
      address: '',
      delivery_type: event.isDelivery ? event.delivery_type : '',
      return_date: event.end_date,
      need_one: !event.need_one,
      comment: event.comment || ''
    });
    if (response.status === 200 || response.status === 201) {
      setShowSuccessModal(true);
    } else {
      setResponse({
        ...response,
        errorPrefix: SEND_BIDZ_REQUEST
      });
      setShowErrorModal(true);
    }
    setIsLoading(false);
  }

  return (
    <>
      <RenderIf condition={isOpen && !isEmpty(data)}>
        <RequestConfirmationModal
          detectLanguage={detectLanguage}
          title={t('Quota_details')}
          textButton={t('Ask_for_quota')}
          onSubmitFn={sendBidzRequest}
          isLoading={isLoading}
          handleClose={handleClose}
          data={data}
          show={isOpen}
          creditCheckForms={creditCheckForms}
          projects={projects}
          isBidz
          t={t}
        />
      </RenderIf>

      <SuccessPopUp show={showSuccessModal} onClose={onClose} />

      <RenderIf condition={showErrorModal}>
        <Popup
          response={response}
          show={showErrorModal}
          onClose={() => {
            setShowErrorModal(!showErrorModal);
            handleClose();
          }}
          t={t}
          prefix={SEND_BIDZ_REQUEST}
        />
      </RenderIf>
    </>
  );
}
