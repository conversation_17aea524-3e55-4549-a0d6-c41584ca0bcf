import React from 'react';
import PropTypes from 'prop-types';
import RenderIf from '../Render_if';
import CustomButton from '../buttons/Custom_button';

export default function ConfirmationModal({
  show,
  message,
  onClose,
  disabled,
  buttonText,
  isLoading,
  cancelText,
  action,
  t
}) {
  return (
    <RenderIf condition={show}>
      <div className="modal">
        <div className="modal-content no-title-margeTop delete-modal">
          <button
            className="close-button"
            onClick={onClose}
            disabled={disabled || isLoading}
          >
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19.25 5.25L5.75 18.75"
                stroke="#333333"
                strokeWidth="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M19.25 18.75L5.75 5.25"
                stroke="#333333"
                strokeWidth="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
          <div className="row">
            <div className="col-lg-10 mx-auto">
              <div className="mt-4">
                <p className="t-header-medium c-grey-titles">{message}</p>
                <span className="t-header-medium c-grey-titles " />
              </div>

              <div className="text-center btn-content fixed-button-modal">
                <button
                  className="round-button black bold"
                  onClick={onClose}
                  disabled={isLoading}
                >
                  {cancelText || t('I_m_not_sure')}
                </button>

                <CustomButton
                  className="round-button yellow bold"
                  isLoading={isLoading}
                  disabled={disabled}
                  onClick={action}
                  textButton={buttonText}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </RenderIf>
  );
}

ConfirmationModal.propTypes = {
  show: PropTypes.bool,
  message: PropTypes.string,
  onClose: PropTypes.func,
  disabled: PropTypes.bool,
  buttonText: PropTypes.string,
  isLoading: PropTypes.bool,
  cancelText: PropTypes.string,
  action: PropTypes.func,
  t: PropTypes.func
};

ConfirmationModal.defaultProps = {
  show: false,
  message: '',
  onClose: () => {},
  disabled: false,
  buttonText: '',
  isLoading: false,
  cancelText: '',
  action: () => {},
  t: () => {}
};
