import React, { useState } from 'react';
import * as Yup from 'yup';
import PropTypes from 'prop-types';
import { Form, Formik } from 'formik';
import { handleKeyDown } from '../../helpers/Textarea';
import InputForm from '../inputs/Input_form';
import Modal from './Modal';
import RenderIf from '../Render_if';
import CustomButton from '../buttons/Custom_button';
import { isValid } from '../../helpers/Data_helper';

export default function BidzCancelModal({
  isRequest,
  onClose,
  action,
  t,
  buttonText,
  requestManagement,
  message,
  cancelText,
  item,
  show
}) {
  const [isLoading, setIsLoading] = useState(false);
  const validate = Yup.object().shape({
    comment: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .min(
        10,
        isRequest
          ? t('Your_reason_for_rejection_must_be_at_least_10_characters')
          : t('Your_reason_for_cancelation_must_be_at_least_10_characters')
      )
      .required(t('Comment_is_required'))
  });

  const cancelAction = async (event) => {
    setIsLoading(true);
    if (requestManagement) {
      const comment = {
        cancel_comment: event.comment
      };
      action(item.id, comment);
    } else if (isRequest) {
      const bidzRequest = {
        rejection_comment: event.comment
      };
      action(item.id, bidzRequest);
    } else {
      const bidzOffer = {
        bids_offer_cancel_comment: event.comment
      };
      action(item.id, bidzOffer);
    }
    setIsLoading(false);
  };

  return (
    <RenderIf condition={show}>
      <Modal
        className="text-center delete-modal t-header-h5 c-grey-titles"
        onClose={onClose}
        t={t}
        noScrollModal
      >
        <Formik
          validationSchema={validate}
          onSubmit={cancelAction}
          initialValues={{ comment: '' }}
        >
          {(formik) => (
            <Form>
              <div className="content-delete">
                <p className="t-header-medium c-grey-titles">{message}</p>
                <div className="form-group">
                  <InputForm
                    placeholder={
                      isRequest
                        ? t('Rejection_reason')
                        : t('Cancelation_reason')
                    }
                    as="textarea"
                    name="comment"
                    autoComplete="off"
                    onKeyDown={(e) => {
                      handleKeyDown(e);
                    }}
                    className={`form-control w-100 ${isValid(
                      formik,
                      'comment'
                    )}`}
                    isFormik
                  />
                </div>
              </div>

              <div className="text-center btn-content marg-top-20 fixed-button-modal">
                <CustomButton
                  className="round-button black bold"
                  onClick={onClose}
                  textButton={cancelText || t('I_m_not_sure')}
                />
                <CustomButton
                  type="submit"
                  disabled={isLoading}
                  className="round-button bold yellow"
                  isLoading={isLoading}
                  textButton={buttonText}
                />
              </div>
            </Form>
          )}
        </Formik>
      </Modal>
    </RenderIf>
  );
}

BidzCancelModal.propTypes = {
  isRequest: PropTypes.bool,
  onClose: PropTypes.func,
  action: PropTypes.func,
  t: PropTypes.func,
  buttonText: PropTypes.string,
  requestManagement: PropTypes.bool,
  message: PropTypes.string,
  cancelText: PropTypes.string,
  item: PropTypes.object,
  show: PropTypes.bool
};

BidzCancelModal.defaultProps = {
  isRequest: false,
  buttonText: '',
  requestManagement: false,
  isLoading: false,
  message: '',
  cancelText: '',
  show: false
};
