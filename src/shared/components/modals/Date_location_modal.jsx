import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import SearchInputAutoCompleteEquipment from '../../../features/search_result/Search_input_auto_complete_equipment';
import RenderIf from '../Render_if';
import { isDisabled } from '../../helpers/Date_helper';
import DatePicker from '../date_picker/Date_picker';
import Modal from './Modal';
import CustomButton from '../buttons/Custom_button';
import { setSessionStorage } from '../../helpers/Session_storage_helper';
import {
  navigateToCompanySpotlight,
  navigateToSearchResult
} from '../../helpers/Algolia_helper';

export default function DateLocationModal({
  hit,
  id,
  show,
  isEquipperExploreMore,
  onClose,
  t,
  detectLanguage
}) {
  const navigate = useNavigate();
  const [startDate, setStartDate] = useState(new Date());
  const [isLoading, setIsLoading] = useState(false);

  const date = new Date();
  const [endDate, setEndDate] = useState(
    new Date(date.setDate(date.getDate() + 6))
  );
  const [location, setLocation] = useState({
    value: '',
    isSelected: false
  });
  const currentTime = new Date();

  const handleStartDateChange = (date) => {
    date.setHours(
      currentTime.getHours(),
      currentTime.getMinutes(),
      currentTime.getSeconds(),
      currentTime.getMilliseconds()
    );
    setStartDate(date);
  };

  const handleEndDateChange = (date) => {
    date.setHours(
      currentTime.getHours(),
      currentTime.getMinutes(),
      currentTime.getSeconds(),
      currentTime.getMilliseconds()
    );
    setEndDate(date);
  };

  const requestedName = (hit) => {
    if (detectLanguage === 'fr') {
      return hit?.name_fr || '';
    }
    return hit?.name_en || hit?.name || '';
  };

  return (
    <RenderIf condition={show}>
      <Modal
        onClose={onClose}
        description="Lets_find_your_equipment"
        className="create-project-modal date-location-modal text-center "
        classNameDescription="t-header-h5 c-grey-titles"
        t={t}
        noScrollModal
      >
        <p className="t-body-regular c-blue-grey mt-1"> {t('Text_location')}</p>
        <div className="form-group location">
          <SearchInputAutoCompleteEquipment
            attribute="location"
            isExploreMore
            placeholder={t('Set_your_location')}
            value={location.value}
            indexName={import.meta.env.VITE_ALGOLIA_INDEX_COVERAGE_AREAS_NAME}
            onChange={setLocation}
            t={t}
            className="search-input-auto-complete form-control"
          />
        </div>

        <div className="date-picker-project form-group">
          <label className="mobile-label d-md-none d-block text-start mt-3">
            {t('Start_End_date')}
          </label>
          <DatePicker
            startDateClassName="form-control w-100 "
            endDateClassName="form-control w-100"
            handleStartDateChange={handleStartDateChange}
            handleEndDateChange={handleEndDateChange}
            startDate={startDate}
            endDate={endDate}
          />
        </div>
        <div className="text-center btn-content marg-top-20">
          <CustomButton
            onClick={async () => {
              setSessionStorage('requestedName', requestedName(hit));
              setSessionStorage('descriptionEn', hit?.description_en || '');
              setSessionStorage('descriptionFr', hit?.description_fr || '');
              setIsLoading(true);
              !isEquipperExploreMore
                ? await navigateToSearchResult(
                    hit,
                    location,
                    startDate,
                    endDate,
                    id,
                    navigate
                  )
                : await navigateToCompanySpotlight(
                    hit,
                    location,
                    startDate,
                    endDate,
                    hit.equipper_id,
                    id,
                    navigate
                  );
            }}
            textPopper={
              isDisabled(startDate, endDate)
                ? t('Date_error_msg')
                : !location.isSelected
                ? t('Select_element_from_dropdown')
                : ''
            }
            isLoading={isLoading}
            className="round-button yellow bold mt-3"
            disabled={isDisabled(startDate, endDate) || !location.isSelected}
            textButton={t('See_availability')}
          />
        </div>
      </Modal>
    </RenderIf>
  );
}
