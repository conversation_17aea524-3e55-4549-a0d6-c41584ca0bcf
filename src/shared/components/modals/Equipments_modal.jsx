import React, { useEffect, useRef, useState } from 'react';
import CustomImage from '../images/Custom_image';
import { isEmptyValue } from '../../helpers/String_helps';
import EquipmentDetailsCard from '../cards/Equipment_details_card';
import {
  calculatePrice,
  calculateRentalDuration,
  equipmentsDetails,
  pricesDetails
} from '../../helpers/Data_helper';
import RenderIf from '../Render_if';
import useResponsive from '../../helpers/Responsive';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

export const LabelandValue = ({ label, value }) => (
  <p className="t-subheading-3 c-fake-black ">
    <span className="bold">{label} : </span>
    {isEmptyValue(value)}
  </p>
);

const PriceDetail = ({ item, isBookingDetails, currency }) => {
  const isInfo = item.name === 'info';
  const isTax = item.name === 'tax';
  const isTotalAmount = item.name === 'total_amount';

  return (
    <div
      className={
        isTotalAmount ? 'col-sm-12 total-amount-price-details' : 'col-md-12'
      }
    >
      <div className="d-flex align-items-center justify-content-between t-body-small">
        <span
          className={`t-body-price ${
            isInfo ? 'c-near-grey bold' : 'c-fake-black'
          }`}
        >
          {isInfo && !isBookingDetails && (
            <FontAwesomeIcon icon={'info-circle'} color="grey" size="25" />
          )}
          {!isInfo && !isTax && item.label}
          {isInfo && !isBookingDetails && item.label}
          {isTax && isBookingDetails && item.label}
        </span>
        <span className="t-body-price c-fake-black bold">
          {!isTax && item.value}
          {!isInfo && !isTax && currency && ` ${currency?.toUpperCase()}`}
          {isTax && isBookingDetails && item.value}
          {isTax &&
            isBookingDetails &&
            currency &&
            ` ${currency?.toUpperCase()}`}
        </span>
      </div>
    </div>
  );
};

export default function EquipmentsModal({
  t,
  onClose,
  isBooking,
  isBookingDetails,
  priceDetailsData,
  isBidz,
  children,
  data
}) {
  const ref = useRef(null);
  const { isMobile } = useResponsive();
  const [scrollBarModal, setScrollBarModal] = useState(false);

  useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.contentRect.height > 580) {
          setScrollBarModal(true);
        } else {
          setScrollBarModal(false);
        }
      }
    });

    if (ref.current) {
      resizeObserver.observe(ref.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [ref]);

  const [showDetails, setShowDetails] = useState(false);
  const handleShowDetails = () => setShowDetails(!showDetails);
  const name = data?.name || data?.equipment_name || data?.name_en;

  const pricesData =
    !isBidz &&
    pricesDetails(
      calculatePrice(data, data?.start_date, data?.end_date, data?.need_one),
      t
    );
  const prices = isBookingDetails ? priceDetailsData : pricesData;
  return (
    <div className="modal">
      <div className="modal-content booking-modal">
        <h3 className="t-titre-h1 c-fake-black bold mb-4">
          {t('Reservation_details')}
        </h3>
        <div className={scrollBarModal || isMobile ? 'scrollBarModal' : ''}>
          <button className="close-button" onClick={onClose}>
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19.25 5.25L5.75 18.75"
                stroke="#333333"
                strokeWidth="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M19.25 18.75L5.75 5.25"
                stroke="#333333"
                strokeWidth="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
          <div className="row">
            <div className="col-lg-3">
              <div className="result-box with-border">
                <div className="result-box__image">
                  <CustomImage
                    imageUrl={
                      data.equipper_equipment_picture &&
                      !data.equipper_equipment_picture.includes(
                        'equipment_library/Empty_state_equipment.png'
                      )
                        ? data.equipper_equipment_picture
                        : data.image_link
                        ? data?.image_link
                        : data?.equipment_image_link
                    }
                    alt={data?.name || data?.equipment_name}
                  />
                </div>
                <div className="result-box__top">
                  <div className="result-box__top-left">
                    <h3 className="t-subheading-2 c-fake-black">
                      {data.preferred_equipment_name
                        ? data.preferred_equipment_name
                        : name}
                    </h3>

                    <RenderIf condition={isBooking && data.description}>
                      <LabelandValue
                        label={t('Description')}
                        value={data.description}
                      />
                    </RenderIf>
                    <RenderIf condition={isBidz}>
                      <LabelandValue
                        label={t('Equipment_prupose_use')}
                        value={data?.equipment_utility}
                      />
                    </RenderIf>

                    <EquipmentDetailsCard
                      data={equipmentsDetails(t, data)}
                      t={t}
                      isBidz={isBidz}
                      showDetails={showDetails}
                      handleShowDetails={handleShowDetails}
                    />
                  </div>
                </div>
              </div>
              <RenderIf condition={!isBidz}>
                <div className="result-box with-border">
                  <div className="comission-price-details ">
                    <div className="col-md-12">
                      <div className="d-flex align-items-center justify-content-between t-body-small">
                        <span
                          className="t-body-price 
                              c-fake-black "
                        >
                          {t('Rental_duration')}
                        </span>
                        <span className="t-body-price c-fake-black bold">
                          {calculateRentalDuration(
                            data.start_date,
                            data.end_date,
                            t
                          )}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="result-box with-border">
                  {isBooking &&
                    prices?.map((item, key) => (
                      <div className="comission-price-details" key={key}>
                        <PriceDetail
                          item={item}
                          isBookingDetails={isBookingDetails}
                          currency={data?.currency}
                        />
                      </div>
                    ))}
                </div>
              </RenderIf>
            </div>
            <div
              className="col-lg-9 d-flex flex-column justify-content-between"
              ref={ref}
            >
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
