import React, { useState, useEffect } from 'react';
import { analyticsVerification } from '../utils/analyticsVerification';

const AnalyticsVerificationPanel = () => {
  const [verificationResults, setVerificationResults] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  // Only show in development or when explicitly enabled
  const shouldShow = import.meta.env.MODE === 'development' || 
                    new URLSearchParams(window.location.search).has('show-analytics-debug');

  useEffect(() => {
    if (!shouldShow) return;

    // Auto-run verification after component mounts
    const timer = setTimeout(() => {
      runVerification();
    }, 2000); // Wait 2 seconds for scripts to load

    return () => clearTimeout(timer);
  }, [shouldShow]);

  const runVerification = async () => {
    setIsRunning(true);
    try {
      const results = await analyticsVerification.runFullVerification();
      setVerificationResults(results);
    } catch (error) {
      console.error('Verification failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const exportResults = () => {
    analyticsVerification.exportResults();
  };

  if (!shouldShow) {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      zIndex: 9999,
      backgroundColor: '#fff',
      border: '2px solid #2196F3',
      borderRadius: '8px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      fontFamily: 'monospace',
      fontSize: '12px',
      maxWidth: '400px'
    }}>
      {/* Toggle Button */}
      <div
        onClick={() => setIsVisible(!isVisible)}
        style={{
          padding: '8px 12px',
          backgroundColor: '#2196F3',
          color: 'white',
          cursor: 'pointer',
          fontWeight: 'bold',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        <span>🔍 Analytics Verification</span>
        <span>{isVisible ? '▼' : '▲'}</span>
      </div>

      {/* Panel Content */}
      {isVisible && (
        <div style={{ padding: '12px' }}>
          <div style={{ marginBottom: '12px' }}>
            <button
              onClick={runVerification}
              disabled={isRunning}
              style={{
                padding: '6px 12px',
                backgroundColor: isRunning ? '#ccc' : '#4CAF50',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: isRunning ? 'not-allowed' : 'pointer',
                marginRight: '8px'
              }}
            >
              {isRunning ? '🔄 Running...' : '▶️ Run Verification'}
            </button>
            
            {verificationResults && (
              <button
                onClick={exportResults}
                style={{
                  padding: '6px 12px',
                  backgroundColor: '#FF9800',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                📁 Export Results
              </button>
            )}
          </div>

          {verificationResults && (
            <div>
              <div style={{
                padding: '8px',
                backgroundColor: verificationResults.successRate >= 80 ? '#E8F5E8' : '#FFF3E0',
                borderRadius: '4px',
                marginBottom: '8px'
              }}>
                <strong>Summary:</strong> {verificationResults.passedChecks}/{verificationResults.totalChecks} checks passed 
                ({verificationResults.successRate}%)
              </div>

              <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                {Object.entries(verificationResults.results).map(([check, passed]) => (
                  <div key={check} style={{
                    padding: '4px 8px',
                    margin: '2px 0',
                    backgroundColor: passed ? '#E8F5E8' : '#FFEBEE',
                    borderRadius: '3px',
                    display: 'flex',
                    justifyContent: 'space-between'
                  }}>
                    <span>{check}</span>
                    <span>{passed ? '✅' : '❌'}</span>
                  </div>
                ))}
              </div>

              <div style={{
                marginTop: '8px',
                padding: '6px',
                backgroundColor: '#F5F5F5',
                borderRadius: '3px',
                fontSize: '10px'
              }}>
                <div><strong>Environment:</strong> {verificationResults.environment}</div>
                <div><strong>GA ID:</strong> {verificationResults.googleAnalyticsId || 'Not set'}</div>
              </div>
            </div>
          )}

          <div style={{
            marginTop: '8px',
            fontSize: '10px',
            color: '#666',
            borderTop: '1px solid #eee',
            paddingTop: '6px'
          }}>
            💡 Open browser console for detailed logs<br/>
            🔧 Add ?show-analytics-debug to URL to show in production
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalyticsVerificationPanel;
