import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const isProduction = import.meta.env.VITE_ENV === 'production';
const googleAnalyticsId = import.meta.env.VITE_GOOGLE_ANALYTICS_ID;

const InnerGoogleAnalyticsInitializer = ({ children }) => {
  const location = useLocation();

  useEffect(() => {
    // Track page views on route change
    if (isProduction && window.gtag && googleAnalyticsId) {
      window.gtag('config', googleAnalyticsId, {
        page_path: location.pathname,
        page_title: document.title,
        page_location: window.location.href
      });
    }
  }, [location]);

  return <>{children}</>;
};

export const GoogleAnalyticsProvider = ({ children }) => {
  useEffect(() => {
    // Only load Google Analytics in production and if ID is provided
    if (!isProduction || !googleAnalyticsId) {
      return;
    }

    // Check if Google Analytics is already loaded
    if (window.gtag) {
      return;
    }

    // Create and load the Google Analytics script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${googleAnalyticsId}`;

    script.onload = () => {
      // Initialize gtag function
      window.dataLayer = window.dataLayer || [];
      function gtag(...args) {
        window.dataLayer.push(args);
      }
      window.gtag = gtag;

      // Configure Google Analytics
      gtag('js', new Date());
      gtag('config', googleAnalyticsId, {
        // Disable automatic page view tracking since we handle it manually
        send_page_view: false,
        // Enable enhanced measurement for better tracking
        enhanced_measurement: true,
        // Respect user privacy preferences
        anonymize_ip: true,
        // Set cookie flags for security
        cookie_flags: 'SameSite=None;Secure'
      });
    };

    script.onerror = () => {
      // Silently handle Google Analytics loading errors
      // This prevents console warnings in development or if GA is blocked
    };

    document.head.appendChild(script);

    // Cleanup function to remove script if component unmounts
    return () => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, []);

  return (
    <InnerGoogleAnalyticsInitializer>
      {children}
    </InnerGoogleAnalyticsInitializer>
  );
};

export default GoogleAnalyticsProvider;
