import React, { useState } from 'react';
import { faSlidersH } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button } from 'react-bootstrap';
import { CustomAdvancedFilters } from './Advanced_filter';

export default function AdvancedFilterMobile({
  t,
  detectLanguage,
  setChangedSearchState
}) {
  const [showAdvancedFilter, setShowAdvancedFilter] = useState(false);
  const handleShowAdvancedFilter = () => {
    setShowAdvancedFilter(!showAdvancedFilter);
  };
  const [addClassName, setAddClassName] = useState(false);
  return (
    <>
      <div className="map-button-container mb-3">
        <Button
          className="round-button yellow c-black button-style filter-btn isSm t-base-large extraBold c-primary-color"
          onClick={handleShowAdvancedFilter}
        >
          <FontAwesomeIcon icon={faSlidersH} color="c-black" className="mr-3" />
          {t('Filters')}
        </Button>
      </div>
      <div
        className={
          showAdvancedFilter
            ? addClassName
              ? 'modal add-scroll-filter'
              : 'modal'
            : 'hidden'
        }
      >
        <div className="modal-content no-title-margeTop  ">
          <button className="close-button" onClick={handleShowAdvancedFilter}>
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19.25 5.25L5.75 18.75"
                stroke="#333333"
                strokeWidth="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M19.25 18.75L5.75 5.25"
                stroke="#333333"
                strokeWidth="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
          <div className="row">
            <CustomAdvancedFilters
              setAddClassName={setAddClassName}
              addClassName={addClassName}
              setChangedSearchState={setChangedSearchState}
              detectLanguage={detectLanguage}
              isMobile
              t={t}
            />
          </div>
          <button
            className="round-button yellow bold"
            onClick={handleShowAdvancedFilter}
          >
            {t('Apply_filters')}
          </button>
        </div>
      </div>
    </>
  );
}
