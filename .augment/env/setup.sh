#!/bin/bash
set -e

# Update system packages
sudo apt-get update

# Install Node.js 18.x (required for frontend)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Yarn package manager
curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | sudo apt-key add -
echo "deb https://dl.yarnpkg.com/debian/ stable main" | sudo tee /etc/apt/sources.list.d/yarn.list
sudo apt-get update
sudo apt-get install -y yarn

# Install Go 1.23 (required for backend)
wget https://go.dev/dl/go1.23.0.linux-amd64.tar.gz
sudo rm -rf /usr/local/go
sudo tar -C /usr/local -xzf go1.23.0.linux-amd64.tar.gz
rm go1.23.0.linux-amd64.tar.gz

# Add Go to PATH
echo 'export PATH=$PATH:/usr/local/go/bin' >> $HOME/.profile
echo 'export GOPATH=$HOME/go' >> $HOME/.profile
echo 'export PATH=$PATH:$GOPATH/bin' >> $HOME/.profile
export PATH=$PATH:/usr/local/go/bin
export GOPATH=$HOME/go
export PATH=$PATH:$GOPATH/bin

# Verify installations
node --version
yarn --version
go version

# Install frontend dependencies
yarn install --frozen-lockfile

# Install backend dependencies
cd backend
go mod download
go mod tidy
cd ..

# Add yarn and npm to PATH if not already there
echo 'export PATH=$PATH:/usr/bin' >> $HOME/.profile

# Source the profile to make sure all paths are available
source $HOME/.profile