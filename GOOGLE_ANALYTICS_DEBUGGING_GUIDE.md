# Google Analytics Debugging Guide

## Browser Developer Tools Verification

### Chrome DevTools

#### 1. Network Tab Verification
1. Open Chrome DevTools (F12)
2. Go to **Network** tab
3. Reload the page
4. Filter by "google" or "gtag"

**What to Look For:**
```
✅ GOOD: Request to https://www.googletagmanager.com/gtag/js?id=G-8JNQD6EW3L
✅ GOOD: Status 200 (successful load)
✅ GOOD: Subsequent requests to https://www.google-analytics.com/g/collect

❌ BAD: 404 errors on GA requests
❌ BAD: No GA requests at all
❌ BAD: Blocked requests (ad blockers)
```

#### 2. Console Verification
Open Console tab and run these commands:

```javascript
// 1. Check if Google Analytics is loaded
console.log('GA Script loaded:', !!document.querySelector('script[src*="googletagmanager"]'));

// 2. Check gtag function
console.log('gtag function available:', typeof window.gtag === 'function');

// 3. Check dataLayer
console.log('dataLayer exists:', Array.isArray(window.dataLayer));
console.log('dataLayer contents:', window.dataLayer);

// 4. Check tracking ID configuration
if (window.dataLayer) {
  const configEvents = window.dataLayer.filter(item => 
    Array.isArray(item) && item[0] === 'config'
  );
  console.log('GA Config events:', configEvents);
}

// 5. Manual tracking test
if (window.gtag) {
  console.log('Sending test page view...');
  window.gtag('config', 'G-8JNQD6EW3L', {
    page_path: '/debug-test',
    page_title: 'Debug Test',
    page_location: window.location.href,
    debug_mode: true
  });
  console.log('Test page view sent');
}

// 6. Check for conflicts with PostHog
console.log('PostHog available:', typeof window.posthog !== 'undefined');
console.log('Both analytics loaded:', 
  typeof window.gtag === 'function' && typeof window.posthog !== 'undefined'
);
```

#### 3. Elements Tab Verification
1. Go to **Elements** tab
2. Search (Ctrl+F) for "googletagmanager"
3. **Expected Result**:
```html
<script async="" src="https://www.googletagmanager.com/gtag/js?id=G-8JNQD6EW3L"></script>
```

#### 4. Application Tab Verification
1. Go to **Application** tab
2. Check **Local Storage** and **Cookies**
3. Look for Google Analytics cookies:
   - `_ga` (Google Analytics identifier)
   - `_ga_*` (Property-specific GA4 cookies)

### Firefox Developer Tools

#### Network Monitor
1. Open Developer Tools (F12)
2. Go to **Network** tab
3. Filter by "google"
4. Look for successful requests to GA endpoints

#### Console Commands
Same JavaScript commands as Chrome (see above)

### Safari Web Inspector

#### Network Tab
1. Open Web Inspector (Cmd+Option+I)
2. Go to **Network** tab
3. Filter requests containing "google"

### Real-Time Google Analytics Verification

#### Using GA4 Real-Time Reports
1. Log into Google Analytics
2. Go to **Reports** > **Real-time**
3. Navigate your site in another tab
4. **Expected**: See active users and page views in real-time

#### Debug View (Advanced)
1. Install Google Analytics Debugger extension
2. Enable debug mode
3. Check console for detailed GA events

## Common Debugging Scenarios

### Scenario 1: Script Not Loading

**Symptoms:**
- No GA script in Network tab
- `window.gtag` is undefined
- No GA cookies set

**Debug Steps:**
```javascript
// Check environment
console.log('Environment:', import.meta.env.VITE_ENV);
console.log('GA ID:', import.meta.env.VITE_GOOGLE_ANALYTICS_ID);

// Check if conditions are met for loading
const isProduction = import.meta.env.VITE_ENV === 'production';
const hasGAId = !!import.meta.env.VITE_GOOGLE_ANALYTICS_ID;
console.log('Should load GA:', isProduction && hasGAId);
```

**Solutions:**
- Verify environment variables are set correctly
- Check if running in production mode
- Verify no ad blockers are interfering

### Scenario 2: Script Loads But gtag Unavailable

**Symptoms:**
- GA script visible in Network tab (200 status)
- `window.gtag` still undefined
- No dataLayer initialization

**Debug Steps:**
```javascript
// Check script loading status
const gaScript = document.querySelector('script[src*="googletagmanager"]');
console.log('GA Script element:', gaScript);
console.log('Script loaded:', gaScript?.readyState);

// Check for JavaScript errors
console.log('Recent errors:', window.onerror);

// Wait and recheck
setTimeout(() => {
  console.log('gtag after delay:', typeof window.gtag);
  console.log('dataLayer after delay:', window.dataLayer);
}, 2000);
```

**Solutions:**
- Wait for script to fully load
- Check for JavaScript errors preventing execution
- Verify network connectivity

### Scenario 3: Page Views Not Tracking

**Symptoms:**
- GA loaded successfully
- gtag function available
- No page view events in GA Real-Time

**Debug Steps:**
```javascript
// Monitor dataLayer for events
const originalPush = window.dataLayer.push;
window.dataLayer.push = function(...args) {
  console.log('DataLayer push:', args);
  return originalPush.apply(this, args);
};

// Test manual page view
window.gtag('config', 'G-8JNQD6EW3L', {
  page_path: window.location.pathname,
  page_title: document.title,
  page_location: window.location.href,
  debug_mode: true
});

// Check if events are being sent
console.log('DataLayer contents:', window.dataLayer);
```

**Solutions:**
- Verify tracking ID is correct (G-8JNQD6EW3L)
- Check browser privacy settings
- Ensure no ad blockers are blocking requests

### Scenario 4: PostHog Conflicts

**Symptoms:**
- One analytics system not working
- Console errors related to analytics
- Unexpected behavior

**Debug Steps:**
```javascript
// Check loading order
console.log('Analytics loading order:');
console.log('1. PostHog loaded:', !!window.posthog);
console.log('2. GA loaded:', !!window.gtag);

// Check for errors
window.addEventListener('error', (e) => {
  if (e.message.includes('posthog') || e.message.includes('gtag')) {
    console.error('Analytics error:', e);
  }
});

// Test both systems
if (window.posthog) {
  window.posthog.capture('debug_test', { source: 'debug' });
  console.log('PostHog test event sent');
}

if (window.gtag) {
  window.gtag('event', 'debug_test', {
    event_category: 'debug',
    event_label: 'manual_test'
  });
  console.log('GA test event sent');
}
```

## Advanced Debugging Tools

### Google Analytics Debugger Extension
1. Install from Chrome Web Store
2. Enable the extension
3. Reload your page
4. Check console for detailed GA debug info

### GA4 Debug Mode
```javascript
// Enable debug mode for detailed logging
window.gtag('config', 'G-8JNQD6EW3L', {
  debug_mode: true
});
```

### Custom Debug Function
Add this to your console for comprehensive debugging:

```javascript
function debugGoogleAnalytics() {
  const results = {
    timestamp: new Date().toISOString(),
    environment: import.meta.env.VITE_ENV,
    gaId: import.meta.env.VITE_GOOGLE_ANALYTICS_ID,
    checks: {}
  };
  
  // Check script loading
  const gaScript = document.querySelector('script[src*="googletagmanager"]');
  results.checks.scriptLoaded = !!gaScript;
  results.checks.scriptSrc = gaScript?.src;
  
  // Check gtag function
  results.checks.gtagAvailable = typeof window.gtag === 'function';
  
  // Check dataLayer
  results.checks.dataLayerExists = Array.isArray(window.dataLayer);
  results.checks.dataLayerLength = window.dataLayer?.length || 0;
  
  // Check PostHog
  results.checks.posthogAvailable = typeof window.posthog !== 'undefined';
  
  // Check cookies
  results.checks.gaCookies = document.cookie
    .split(';')
    .filter(cookie => cookie.trim().startsWith('_ga'))
    .map(cookie => cookie.trim());
  
  // Network requests check
  results.checks.networkRequests = performance
    .getEntriesByType('resource')
    .filter(entry => entry.name.includes('google'))
    .map(entry => ({
      url: entry.name,
      status: entry.responseStatus,
      duration: entry.duration
    }));
  
  console.table(results.checks);
  console.log('Full debug results:', results);
  
  return results;
}

// Run the debug function
debugGoogleAnalytics();
```

## Verification Checklist

### ✅ Production Environment Checklist
- [ ] GA script loads (Network tab shows 200 status)
- [ ] `window.gtag` function is available
- [ ] `window.dataLayer` is an array with content
- [ ] GA cookies are set (`_ga`, `_ga_*`)
- [ ] Page view events visible in dataLayer
- [ ] Real-time GA reports show activity
- [ ] PostHog continues to work
- [ ] No console errors related to analytics

### ✅ Development Environment Checklist
- [ ] No GA script requests in Network tab
- [ ] `window.gtag` is undefined
- [ ] No GA cookies set
- [ ] PostHog works normally
- [ ] Verification panel shows development warnings

## Emergency Debugging Commands

If you need to quickly check everything:

```javascript
// One-liner comprehensive check
console.log({
  env: import.meta.env.VITE_ENV,
  gaId: import.meta.env.VITE_GOOGLE_ANALYTICS_ID,
  gaScript: !!document.querySelector('script[src*="googletagmanager"]'),
  gtag: typeof window.gtag,
  dataLayer: window.dataLayer?.length,
  posthog: typeof window.posthog,
  gaCookies: document.cookie.match(/_ga[^;]*/g)
});
```

This debugging guide should help you identify and resolve any issues with the Google Analytics integration quickly and effectively.
