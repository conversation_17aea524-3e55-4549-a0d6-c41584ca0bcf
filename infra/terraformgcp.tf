
variable "project_id" {
  default = var.project_id
}

variable "project_location" {
  default = "us-central1"
}

resource "google_artifact_registry_repository" "gcf_artifacts" {
  description = "This repository is created and used by Cloud Functions for storing function docker images."
  format      = "DOCKER"
  labels = {
    goog-managed-by = "cloudfunctions"
  }
  location      = var.project_location
  project       = var.project_id
  repository_id = "gcf-artifacts"
}
# terraform import google_artifact_registry_repository.gcf_artifacts projects/project_id/locations/us-central1/repositories/gcf-artifacts

resource "google_project" "tooler_prod" {
  auto_create_network = true
  billing_account     = "0195BC-491403-3FEB15"
  labels = {
    firebase = "enabled"
  }
  name       = var.project_id
  org_id     = "************"
  project_id = var.project_id
}
# terraform import google_project.tooler_prod projects/project_id

resource "google_service_account" "************_compute" {
  account_id   = "************-compute"
  display_name = "Default compute service account"
  project      = var.project_id
}
# terraform import google_service_account.************_compute projects/project_id/serviceAccounts/************-compute@project_id.iam.gserviceaccount.com

resource "google_pubsub_topic" "equipments_upload" {
  name    = "equipments-upload"
  project = var.project_id
}
# terraform import google_pubsub_topic.equipments_upload projects/project_id/topics/equipments-upload

resource "google_logging_log_sink" "a_default" {
  destination            = "logging.googleapis.com/projects/project_id/locations/global/buckets/_Default"
  filter                 = "NOT LOG_ID(\"cloudaudit.googleapis.com/activity\") AND NOT LOG_ID(\"externalaudit.googleapis.com/activity\") AND NOT LOG_ID(\"cloudaudit.googleapis.com/system_event\") AND NOT LOG_ID(\"externalaudit.googleapis.com/system_event\") AND NOT LOG_ID(\"cloudaudit.googleapis.com/access_transparency\") AND NOT LOG_ID(\"externalaudit.googleapis.com/access_transparency\")"
  name                   = "_Default"
  project                = "************"
  unique_writer_identity = true
}
# terraform import google_logging_log_sink.a_default ************###_Default

resource "google_pubsub_subscription" "producer" {
  ack_deadline_seconds = 600
  expiration_policy {
    ttl = "2678400s"
  }
  message_retention_duration = "604800s"
  name                       = "producer"
  project                    = var.project_id
  push_config {
    push_endpoint = "https://tooler-equipment-uploader-zmsslxo7ta-uc.a.run.app"
  }
  retry_policy {
    maximum_backoff = "600s"
    minimum_backoff = "10s"
  }
  topic = "projects/project_id/topics/equipments-upload"
}

# terraform import google_pubsub_subscription.producer projects/project_id/subscriptions/producer

resource "google_service_account" "cloud_run_github_action" {
  account_id   = "cloud-run-github-action"
  display_name = "cloud-run-github-action"
  project      = var.project_id
}
# terraform import google_service_account.cloud_run_github_action projects/project_id/serviceAccounts/cloud-run-github-action@project_id.iam.gserviceaccount.com

resource "google_service_account" "tooler_prod" {
  account_id   = var.project_id
  display_name = "App Engine default service account"
  project      = var.project_id
}
# terraform import google_service_account.tooler_prod projects/project_id/serviceAccounts/project_id@project_id.iam.gserviceaccount.com

resource "google_service_account" "ext_firestore_algolia_sea_uwxf" {
  account_id   = "ext-firestore-algolia-sea-uwxf"
  display_name = "Firebase Extensions firestore-algolia-search-esgn service account"
  project      = var.project_id
}
# terraform import google_service_account.ext_firestore_algolia_sea_uwxf projects/project_id/serviceAccounts/ext-firestore-algolia-sea-uwxf@project_id.iam.gserviceaccount.com

resource "google_logging_log_sink" "a_required" {
  destination            = "logging.googleapis.com/projects/project_id/locations/global/buckets/_Required"
  filter                 = "LOG_ID(\"cloudaudit.googleapis.com/activity\") OR LOG_ID(\"externalaudit.googleapis.com/activity\") OR LOG_ID(\"cloudaudit.googleapis.com/system_event\") OR LOG_ID(\"externalaudit.googleapis.com/system_event\") OR LOG_ID(\"cloudaudit.googleapis.com/access_transparency\") OR LOG_ID(\"externalaudit.googleapis.com/access_transparency\")"
  name                   = "_Required"
  project                = "************"
  unique_writer_identity = true
}
# terraform import google_logging_log_sink.a_required ************###_Required

resource "google_service_account" "ext_firestore_algolia_sea_fh4r" {
  account_id   = "ext-firestore-algolia-sea-fh4r"
  display_name = "Firebase Extensions firestore-algolia-search-7r3v service account"
  project      = var.project_id
}
# terraform import google_service_account.ext_firestore_algolia_sea_fh4r projects/project_id/serviceAccounts/ext-firestore-algolia-sea-fh4r@project_id.iam.gserviceaccount.com

resource "google_service_account" "ext_firestore_algolia_search" {
  account_id   = "ext-firestore-algolia-search"
  display_name = "Firebase Extensions firestore-algolia-search service account"
  project      = var.project_id
}
# terraform import google_service_account.ext_firestore_algolia_search projects/project_id/serviceAccounts/ext-firestore-algolia-search@project_id.iam.gserviceaccount.com

resource "google_service_account" "firebase_adminsdk_tza73" {
  account_id   = "firebase-adminsdk-tza73"
  description  = "Firebase Admin SDK Service Agent"
  display_name = "firebase-adminsdk"
  project      = var.project_id
}
# terraform import google_service_account.firebase_adminsdk_tza73 projects/project_id/serviceAccounts/firebase-adminsdk-tza73@project_id.iam.gserviceaccount.com

resource "google_secret_manager_secret_version" "projects_************_secrets_firestore_algolia_search_7r3v_algolia_api_key_versions_1" {
  enabled     = true
  secret      = "projects/************/secrets/firestore-algolia-search-7r3v-ALGOLIA_API_KEY"
  secret_data = "44ed40a389a80264718887f8f8ea1fdd"
}
# terraform import google_secret_manager_secret_version.projects_************_secrets_firestore_algolia_search_7r3v_algolia_api_key_versions_1 projects/************/secrets/firestore-algolia-search-7r3v-ALGOLIA_API_KEY/versions/1

resource "google_project_service" "artifactregistry_googleapis_com" {
  project = "************"
  service = "artifactregistry.googleapis.com"
}
# terraform import google_project_service.artifactregistry_googleapis_com ************/artifactregistry.googleapis.com

resource "google_secret_manager_secret_version" "projects_************_secrets_firestore_algolia_search_algolia_api_key_versions_1" {
  enabled     = true
  secret      = "projects/************/secrets/firestore-algolia-search-ALGOLIA_API_KEY"
  secret_data = "44ed40a389a80264718887f8f8ea1fdd"
}
# terraform import google_secret_manager_secret_version.projects_************_secrets_firestore_algolia_search_algolia_api_key_versions_1 projects/************/secrets/firestore-algolia-search-ALGOLIA_API_KEY/versions/1

resource "google_project_service" "cloudtrace_googleapis_com" {
  project = "************"
  service = "cloudtrace.googleapis.com"
}
# terraform import google_project_service.cloudtrace_googleapis_com ************/cloudtrace.googleapis.com

resource "google_project_service" "distance_matrix_backend_googleapis_com" {
  project = "************"
  service = "distance-matrix-backend.googleapis.com"
}
# terraform import google_project_service.distance_matrix_backend_googleapis_com ************/distance-matrix-backend.googleapis.com

resource "google_project_service" "firebase_googleapis_com" {
  project = "************"
  service = "firebase.googleapis.com"
}
# terraform import google_project_service.firebase_googleapis_com ************/firebase.googleapis.com

resource "google_project_service" "firebaseextensions_googleapis_com" {
  project = "************"
  service = "firebaseextensions.googleapis.com"
}
# terraform import google_project_service.firebaseextensions_googleapis_com ************/firebaseextensions.googleapis.com

resource "google_secret_manager_secret_version" "projects_************_secrets_firestore_algolia_search_esgn_algolia_api_key_versions_1" {
  enabled     = true
  secret      = "projects/************/secrets/firestore-algolia-search-esgn-ALGOLIA_API_KEY"
  secret_data = "44ed40a389a80264718887f8f8ea1fdd"
}
# terraform import google_secret_manager_secret_version.projects_************_secrets_firestore_algolia_search_esgn_algolia_api_key_versions_1 projects/************/secrets/firestore-algolia-search-esgn-ALGOLIA_API_KEY/versions/1

resource "google_project_service" "datastore_googleapis_com" {
  project = "************"
  service = "datastore.googleapis.com"
}
# terraform import google_project_service.datastore_googleapis_com ************/datastore.googleapis.com

resource "google_secret_manager_secret" "firestore_algolia_search_esgn_algolia_api_key" {
  labels = {
    firebase-extensions-managed = "true"
  }
  project = "************"
  replication {
    automatic = true
  }
  secret_id = "firestore-algolia-search-esgn-ALGOLIA_API_KEY"
}
# terraform import google_secret_manager_secret.firestore_algolia_search_esgn_algolia_api_key projects/************/secrets/firestore-algolia-search-esgn-ALGOLIA_API_KEY

resource "google_project_service" "bigquerystorage_googleapis_com" {
  project = "************"
  service = "bigquerystorage.googleapis.com"
}
# terraform import google_project_service.bigquerystorage_googleapis_com ************/bigquerystorage.googleapis.com

resource "google_project_service" "containerregistry_googleapis_com" {
  project = "************"
  service = "containerregistry.googleapis.com"
}
# terraform import google_project_service.containerregistry_googleapis_com ************/containerregistry.googleapis.com

resource "google_secret_manager_secret" "firestore_algolia_search_algolia_api_key" {
  labels = {
    firebase-extensions-managed = "true"
  }
  project = "************"
  replication {
    automatic = true
  }
  secret_id = "firestore-algolia-search-ALGOLIA_API_KEY"
}
# terraform import google_secret_manager_secret.firestore_algolia_search_algolia_api_key projects/************/secrets/firestore-algolia-search-ALGOLIA_API_KEY

resource "google_project_service" "geolocation_googleapis_com" {
  project = "************"
  service = "geolocation.googleapis.com"
}
# terraform import google_project_service.geolocation_googleapis_com ************/geolocation.googleapis.com

resource "google_project_service" "firebaseremoteconfig_googleapis_com" {
  project = "************"
  service = "firebaseremoteconfig.googleapis.com"
}
# terraform import google_project_service.firebaseremoteconfig_googleapis_com ************/firebaseremoteconfig.googleapis.com

resource "google_project_service" "cloudapis_googleapis_com" {
  project = "************"
  service = "cloudapis.googleapis.com"
}
# terraform import google_project_service.cloudapis_googleapis_com ************/cloudapis.googleapis.com

resource "google_project_service" "bigquerymigration_googleapis_com" {
  project = "************"
  service = "bigquerymigration.googleapis.com"
}
# terraform import google_project_service.bigquerymigration_googleapis_com ************/bigquerymigration.googleapis.com

resource "google_project_service" "serviceusage_googleapis_com" {
  project = "************"
  service = "serviceusage.googleapis.com"
}
# terraform import google_project_service.serviceusage_googleapis_com ************/serviceusage.googleapis.com

resource "google_project_service" "firebaseinstallations_googleapis_com" {
  project = "************"
  service = "firebaseinstallations.googleapis.com"
}
# terraform import google_project_service.firebaseinstallations_googleapis_com ************/firebaseinstallations.googleapis.com

resource "google_project_service" "appengine_googleapis_com" {
  project = "************"
  service = "appengine.googleapis.com"
}
# terraform import google_project_service.appengine_googleapis_com ************/appengine.googleapis.com

resource "google_project_service" "run_googleapis_com" {
  project = "************"
  service = "run.googleapis.com"
}
# terraform import google_project_service.run_googleapis_com ************/run.googleapis.com

resource "google_project_service" "maps_ios_backend_googleapis_com" {
  project = "************"
  service = "maps-ios-backend.googleapis.com"
}
# terraform import google_project_service.maps_ios_backend_googleapis_com ************/maps-ios-backend.googleapis.com

resource "google_project_service" "secretmanager_googleapis_com" {
  project = "************"
  service = "secretmanager.googleapis.com"
}
# terraform import google_project_service.secretmanager_googleapis_com ************/secretmanager.googleapis.com

resource "google_project_service" "fcm_googleapis_com" {
  project = "************"
  service = "fcm.googleapis.com"
}
# terraform import google_project_service.fcm_googleapis_com ************/fcm.googleapis.com

resource "google_project_service" "pubsub_googleapis_com" {
  project = "************"
  service = "pubsub.googleapis.com"
}
# terraform import google_project_service.pubsub_googleapis_com ************/pubsub.googleapis.com

resource "google_project_service" "firestore_googleapis_com" {
  project = "************"
  service = "firestore.googleapis.com"
}
# terraform import google_project_service.firestore_googleapis_com ************/firestore.googleapis.com

resource "google_project_service" "monitoring_googleapis_com" {
  project = "************"
  service = "monitoring.googleapis.com"
}
# terraform import google_project_service.monitoring_googleapis_com ************/monitoring.googleapis.com

resource "google_project_service" "clouddebugger_googleapis_com" {
  project = "************"
  service = "clouddebugger.googleapis.com"
}
# terraform import google_project_service.clouddebugger_googleapis_com ************/clouddebugger.googleapis.com

resource "google_project_service" "places_backend_googleapis_com" {
  project = "************"
  service = "places-backend.googleapis.com"
}
# terraform import google_project_service.places_backend_googleapis_com ************/places-backend.googleapis.com

resource "google_project_service" "static_maps_backend_googleapis_com" {
  project = "************"
  service = "static-maps-backend.googleapis.com"
}
# terraform import google_project_service.static_maps_backend_googleapis_com ************/static-maps-backend.googleapis.com

resource "google_project_service" "geocoding_backend_googleapis_com" {
  project = "************"
  service = "geocoding-backend.googleapis.com"
}
# terraform import google_project_service.geocoding_backend_googleapis_com ************/geocoding-backend.googleapis.com

resource "google_project_service" "cloudresourcemanager_googleapis_com" {
  project = "************"
  service = "cloudresourcemanager.googleapis.com"
}
# terraform import google_project_service.cloudresourcemanager_googleapis_com ************/cloudresourcemanager.googleapis.com

resource "google_project_service" "sql_component_googleapis_com" {
  project = "************"
  service = "sql-component.googleapis.com"
}
# terraform import google_project_service.sql_component_googleapis_com ************/sql-component.googleapis.com

resource "google_project_service" "firebasehosting_googleapis_com" {
  project = "************"
  service = "firebasehosting.googleapis.com"
}
# terraform import google_project_service.firebasehosting_googleapis_com ************/firebasehosting.googleapis.com

resource "google_project_service" "cloudfunctions_googleapis_com" {
  project = "************"
  service = "cloudfunctions.googleapis.com"
}
# terraform import google_project_service.cloudfunctions_googleapis_com ************/cloudfunctions.googleapis.com

resource "google_project_service" "maps_embed_backend_googleapis_com" {
  project = "************"
  service = "maps-embed-backend.googleapis.com"
}
# terraform import google_project_service.maps_embed_backend_googleapis_com ************/maps-embed-backend.googleapis.com

resource "google_project_service" "runtimeconfig_googleapis_com" {
  project = "************"
  service = "runtimeconfig.googleapis.com"
}
# terraform import google_project_service.runtimeconfig_googleapis_com ************/runtimeconfig.googleapis.com

resource "google_project_service" "cloudbuild_googleapis_com" {
  project = "************"
  service = "cloudbuild.googleapis.com"
}
# terraform import google_project_service.cloudbuild_googleapis_com ************/cloudbuild.googleapis.com

resource "google_project_service" "servicemanagement_googleapis_com" {
  project = "************"
  service = "servicemanagement.googleapis.com"
}
# terraform import google_project_service.servicemanagement_googleapis_com ************/servicemanagement.googleapis.com

resource "google_project_service" "logging_googleapis_com" {
  project = "************"
  service = "logging.googleapis.com"
}
# terraform import google_project_service.logging_googleapis_com ************/logging.googleapis.com

resource "google_project_service" "firebaserules_googleapis_com" {
  project = "************"
  service = "firebaserules.googleapis.com"
}
# terraform import google_project_service.firebaserules_googleapis_com ************/firebaserules.googleapis.com

resource "google_project_service" "firebasestorage_googleapis_com" {
  project = "************"
  service = "firebasestorage.googleapis.com"
}
# terraform import google_project_service.firebasestorage_googleapis_com ************/firebasestorage.googleapis.com

resource "google_project_service" "securetoken_googleapis_com" {
  project = "************"
  service = "securetoken.googleapis.com"
}
# terraform import google_project_service.securetoken_googleapis_com ************/securetoken.googleapis.com

resource "google_project_service" "timezone_backend_googleapis_com" {
  project = "************"
  service = "timezone-backend.googleapis.com"
}
# terraform import google_project_service.timezone_backend_googleapis_com ************/timezone-backend.googleapis.com

resource "google_project_service" "deploymentmanager_googleapis_com" {
  project = "************"
  service = "deploymentmanager.googleapis.com"
}
# terraform import google_project_service.deploymentmanager_googleapis_com ************/deploymentmanager.googleapis.com

resource "google_project_service" "storage_component_googleapis_com" {
  project = "************"
  service = "storage-component.googleapis.com"
}
# terraform import google_project_service.storage_component_googleapis_com ************/storage-component.googleapis.com

resource "google_project_service" "street_view_image_backend_googleapis_com" {
  project = "************"
  service = "street-view-image-backend.googleapis.com"
}
# terraform import google_project_service.street_view_image_backend_googleapis_com ************/street-view-image-backend.googleapis.com

resource "google_project_service" "iamcredentials_googleapis_com" {
  project = "************"
  service = "iamcredentials.googleapis.com"
}
# terraform import google_project_service.iamcredentials_googleapis_com ************/iamcredentials.googleapis.com

resource "google_project_service" "elevation_backend_googleapis_com" {
  project = "************"
  service = "elevation-backend.googleapis.com"
}
# terraform import google_project_service.elevation_backend_googleapis_com ************/elevation-backend.googleapis.com

resource "google_project_service" "roads_googleapis_com" {
  project = "************"
  service = "roads.googleapis.com"
}
# terraform import google_project_service.roads_googleapis_com ************/roads.googleapis.com

resource "google_project_service" "maps_android_backend_googleapis_com" {
  project = "************"
  service = "maps-android-backend.googleapis.com"
}
# terraform import google_project_service.maps_android_backend_googleapis_com ************/maps-android-backend.googleapis.com

resource "google_project_service" "directions_backend_googleapis_com" {
  project = "************"
  service = "directions-backend.googleapis.com"
}
# terraform import google_project_service.directions_backend_googleapis_com ************/directions-backend.googleapis.com

resource "google_project_service" "iam_googleapis_com" {
  project = "************"
  service = "iam.googleapis.com"
}
# terraform import google_project_service.iam_googleapis_com ************/iam.googleapis.com

resource "google_project_service" "firebasedynamiclinks_googleapis_com" {
  project = "************"
  service = "firebasedynamiclinks.googleapis.com"
}
# terraform import google_project_service.firebasedynamiclinks_googleapis_com ************/firebasedynamiclinks.googleapis.com

resource "google_project_service" "testing_googleapis_com" {
  project = "************"
  service = "testing.googleapis.com"
}
# terraform import google_project_service.testing_googleapis_com ************/testing.googleapis.com

resource "google_project_service" "storage_api_googleapis_com" {
  project = "************"
  service = "storage-api.googleapis.com"
}
# terraform import google_project_service.storage_api_googleapis_com ************/storage-api.googleapis.com

resource "google_project_service" "identitytoolkit_googleapis_com" {
  project = "************"
  service = "identitytoolkit.googleapis.com"
}
# terraform import google_project_service.identitytoolkit_googleapis_com ************/identitytoolkit.googleapis.com

resource "google_storage_bucket" "eu_artifacts_tooler_prod_appspot_com" {
  force_destroy            = false
  location                 = "EU"
  name                     = "eu.artifacts.project_id.appspot.com"
  project                  = var.project_id
  public_access_prevention = "inherited"
  storage_class            = "STANDARD"
}
# terraform import google_storage_bucket.eu_artifacts_tooler_prod_appspot_com eu.artifacts.project_id.appspot.com

resource "google_secret_manager_secret" "firestore_algolia_search_7r3v_algolia_api_key" {
  labels = {
    firebase-extensions-managed = "true"
  }
  project = "************"
  replication {
    automatic = true
  }
  secret_id = "firestore-algolia-search-7r3v-ALGOLIA_API_KEY"
}
# terraform import google_secret_manager_secret.firestore_algolia_search_7r3v_algolia_api_key projects/************/secrets/firestore-algolia-search-7r3v-ALGOLIA_API_KEY

resource "google_storage_bucket" "staging_tooler_prod_appspot_com" {
  force_destroy = false
  lifecycle_rule {
    action {
      type = "Delete"
    }
    condition {
      age        = 15
      with_state = "ANY"
    }
  }
  location                 = "US"
  name                     = "staging.project_id.appspot.com"
  project                  = var.project_id
  public_access_prevention = "inherited"
  storage_class            = "STANDARD"
}
# terraform import google_storage_bucket.staging_tooler_prod_appspot_com staging.project_id.appspot.com

resource "google_project_service" "storage_googleapis_com" {
  project = "************"
  service = "storage.googleapis.com"
}
# terraform import google_project_service.storage_googleapis_com ************/storage.googleapis.com

resource "google_project_service" "maps_backend_googleapis_com" {
  project = "************"
  service = "maps-backend.googleapis.com"
}
# terraform import google_project_service.maps_backend_googleapis_com ************/maps-backend.googleapis.com

resource "google_project_service" "bigquery_googleapis_com" {
  project = "************"
  service = "bigquery.googleapis.com"
}
# terraform import google_project_service.bigquery_googleapis_com ************/bigquery.googleapis.com

resource "google_storage_bucket" "gcf_sources_************_us_central1" {
  cors {
    method = ["GET"]
    origin = ["https://*.cloud.google.com", "https://*.corp.google.com", "https://*.corp.google.com:*"]
  }
  force_destroy               = false
  location                    = "US-CENTRAL1"
  name                        = "gcf-sources-************-us-central1"
  project                     = var.project_id
  public_access_prevention    = "inherited"
  storage_class               = "STANDARD"
  uniform_bucket_level_access = true
}
# terraform import google_storage_bucket.gcf_sources_************_us_central1 gcf-sources-************-us-central1

resource "google_storage_bucket" "tooler_prod_appspot_com" {
  force_destroy            = false
  location                 = "US"
  name                     = "project_id.appspot.com"
  project                  = var.project_id
  public_access_prevention = "inherited"
  storage_class            = "STANDARD"
}
# terraform import google_storage_bucket.tooler_prod_appspot_com project_id.appspot.com

resource "google_storage_bucket" "us_artifacts_tooler_prod_appspot_com" {
  force_destroy            = false
  location                 = "US"
  name                     = "us.artifacts.project_id.appspot.com"
  project                  = var.project_id
  public_access_prevention = "inherited"
  storage_class            = "STANDARD"
}
# terraform import google_storage_bucket.us_artifacts_tooler_prod_appspot_com us.artifacts.project_id.appspot.com

resource "google_project_service" "streetviewpublish_googleapis_com" {
  project = "************"
  service = "streetviewpublish.googleapis.com"
}
# terraform import google_project_service.streetviewpublish_googleapis_com ************/streetviewpublish.googleapis.com

