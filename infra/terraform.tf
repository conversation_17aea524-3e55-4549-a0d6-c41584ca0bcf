variable "project_id" {
  default = "pp-21fdfkf"
}

variable "project_location" {
  default = "us-central1"
}

variable "firebase_storage_bucket" {
  default = "var.project_id.appspot.com"
}

variable "pubsub_topic_name" {
  default = "equipments-upload"
}

variable "cloud_run_api_name" {
  default = "api"
}

variable "cloud_run_equipments_upload_name" {
  default = "equipments-upload"
}

variable "pubsub_subscription_name" {
  default = "equipments-upload-subscription"
}

resource "google_project" "project" {
  project_id = var.project_id
  name       = var.project_id
}

resource "google_storage_notification" "notification" {
  bucket         = var.firebase_storage_bucket
  topic          = var.pubsub_topic_name
  payload_format = "JSON_API_V1"
}

resource "google_pubsub_topic" "topic" {
  name = var.pubsub_topic_name
}

resource "google_cloud_run_service" "cloud_run_api" {
  name     = var.cloud_run_api_name
  location = var.project_location
  project  = google_project.project.project_id
  image    = "gcr.io/${var.project_id}/${var.cloud_run_api_name}"
  platform = "managed"

  env_vars = {
    FIREBASE_API_KEY        = "${var.firebase_api_key}"
    FIREBASE_STORAGE_BUCKET = var.firebase_storage_bucket
    MODE                    = "production"
    SLACK_WEBHOOK_URL       = "${var.slack_webhook_url}"
  }
}

resource "google_cloud_run_service" "cloud_run_equipments_upload" {
  name     = var.cloud_run_equipments_upload_name
  location = var.project_location
  project  = google_project.project.project_id
  image    = "gcr.io/${var.project_id}/${var.cloud_run_equipments_upload_name}"
  platform = "managed"

  env_vars = {
    FIREBASE_API_KEY        = "${var.firebase_api_key}"
    FIREBASE_STORAGE_BUCKET = var.firebase_storage_bucket
    MODE                    = "production"
    SLACK_WEBHOOK_URL       = "${var.slack_webhook_url}"
  }
}

resource "google_pubsub_subscription" "subscription" {
  name                       = var.pubsub_subscription_name
  topic                      = google_pubsub_topic.topic.name
  ack_deadline_seconds       = 60
  message_retention_duration = "600s"
}

# resource "google_cloud_run_domain_mapping" "cloud_run_domain_mapping" {
#   name     = var.cloud_run_domain_mapping_name
#   location = var.project_location
#   project  = google_project.project.project_id
#   service  = google_cloud_run_service.cloud_run_api.name
#   domain   = var.cloud_run_domain
# }



# resource "google_cloud_run_domain_mapping" "cloud_run_domain_mapping_equipments_upload" {
#   name     = var.cloud_run_domain_mapping_equipments_upload_name
#   location = var.project_location
#   project  = google_project.project.project_id
#   service  = google_cloud_run_service.cloud_run_equipments_upload.name
#   domain   = var.cloud_run_domain_equipments_upload
# }


