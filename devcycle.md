# 🚀 Production Deployment Process

This document outlines the deployment process for our team to ensure that only high-quality code reaches production. We operate with three environments: `dev`, `preprod`, and `prod`.

## 🧩 1. Development Phase
- Create a task in our [GitHub project board](https://github.com/orgs/VIMA-INC/projects/3/views/1).
- Validate all requirements and elaborate your technical implementation plan.
- Use the ticket number to name your branch:
  - For new features, create a branch in the `/feature` folder.
  - For bug fixes, create a branch in the `/fixes` folder.
- For new features:
  - Branch off of `master`, creating a branch for **Backend (BE)** and a branch for **Frontend (FE)**.
  - If the feature requires both BE and FE changes, sync the branches as needed.
- Each commit should:
  - Be atomic and clearly scoped.
  - Pass local linters and unit tests.

## 🔁 2. Pull Request (PR) Phase
- Create a PR into `master`.
- Description should include:
  - Link to the issue ticket 
  - All the product requirements and design decisions.
  - Quick technical description of the implementation.
  - Any manual testing instructions.
  - Relevant links or references.
- **Code Review** is mandatory by at least one other developer:
  - Ensure code quality, readability, performance, and security.
  - Validate test coverage and feature scope.
- **Checklist Before Merge**:
  - ✅ Code reviewed and approved.
  - ✅ Unit tests updated or added.
  - ✅ Functional requirements confirmed.
  - ✅ All CI checks passed (lint, tests, etc.).

## 🧪 3. Manual QA & Preprod Validation
- Create a new tag from master for FE `vX.Y.Z-web` and BE `vX.Y.Z-backend`.
- Merged code is deployed automatically to **preprod** using the created tag.
- Perform manual QA on preprod:
  - Test the new feature.
  - Run regression checks on key flows.
  - Monitor logs for issues.
- If issues are found:
  - Create a new branch from `master`.
  - Fix the issues.
  - Create a new PR into `master` for the fix.

## 🧷 4. Functional/Integration Testing
- For new features or bug fixes:
  - Write/update functional or integration tests.
  - Ensure these are part of the CI pipeline for `preprod`.

## 🤝 5. Production Deployment (Peer-Based)
- Once preprod testing passes:
  - Communicate the deployment time to the team and confirm if anyone is available to assist.
  - Manullay deploy the 2 tags to production in GitHub Actions.
  - Deploy to **production** ideally with **one other developer present**.
  ### Production Deployment (Frontend)

  - git tag -a v0.**number**._number_-web -m "msg-logging-whats-been-pushed"
  - git push origin v0.**number**._number_-web

  ### Production Deployment (Backend)

  - git tag -a v0.**number**._number_-backend -m "msg-logging-whats-been-pushed"
  - git push origin v0.**number**._number_-backend
  
  To keep the Demo Environment up to date with production, we will deploy the same tags to the demo environment.
  ### Demo Deployment (Frontend)

  - git tag -a v0.**number**._number_-demoWeb -m "msg-logging-whats-been-pushed"
  - git push origin v0.**number**._number_-demoWeb

  ### Demo Deployment (Backend)

  - git tag -a v0.**number**._number_-demoBackend -m "msg-logging-whats-been-pushed"
  - git push origin v0.**number**._number_-demoBackend
  

## 🚦 6. Post-Deployment Smoke Testing
- After deploy, perform:
  - Smoke tests on core features (login, global search, booking flow, equipper management, etc.).
  - Monitor logs and app metrics for 15–30 mins.

## 🔙 7. Rollback Strategy
- If critical (requires BE and FE work)issues arise:
  - Revert to the **previous stable tag** and redeploy.
  - If BE rollback is needed, only revert to the previous BE tag on GCP Cloud Run.
  - If FE || FE && BE, rollback is needed, roll back to the previous tag by deploying the previous FE tag.
  - Log the issue and notify the team.
  - Investigate the root cause.
  - communicate the fixes needed to solve the issue.
  - Apply a patch fix via the same review and deploy process.

## 📢 8. Communication & coordination
Use the team Whatsapp channel  and emails for all deployment, release, and incident notifications.
- Post weekly standup updates in our dedicated whatsapp channel (progress, blockers, next steps).
- For PRs and code reviews, tag the relevant developer(s) and use GitHub comments for async feedback.
- Schedule short video calls for critical(requires BE and FE work) releases, major incidents, or planning sessions.
- Use GitHub Issues and the project board to track tasks, bugs, and progress.
- Always update the team after deploying to production, including any issues or rollbacks.

---

## ✅ Release Checklist
| Step                                   | Responsible        
|-----------------------------------------|--------------------|
| PR created & reviewed                   | Dev 1 & Dev 2      | 
| Manual preprod QA                       | Any Dev            | 
| Functional/integration tests updated    | PR Author          | 
| All CI checks passed                    | PR Author          | 
| Merge to `master`                       | PR Author          | 
| Release tag created with changelog      | PR Author          | 
| Peer-based production deploy            | PR Author + Dev 2  | 
| Post-deploy smoke test                  | PR Author + Dev 2  | 
| Monitor app & metrics                   | All Devs           | 
| Communicate deployment & results        | PR Author          | 
| Rollback if needed                      | PR Author + Dev 2  | 
| Update team/project board               | PR Author          | 


---

_Keep this document version-controlled and update it as the team or product evolves._
