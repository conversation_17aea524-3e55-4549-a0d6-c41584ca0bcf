{"indexes": [{"collectionGroup": "bids_offer", "queryScope": "COLLECTION", "fields": [{"fieldPath": "admin_ids", "arrayConfig": "CONTAINS"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "bids_offer", "queryScope": "COLLECTION", "fields": [{"fieldPath": "equipment_id", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "bids_offer", "queryScope": "COLLECTION", "fields": [{"fieldPath": "equipper_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "bids_offer", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lodger_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "bids_request", "queryScope": "COLLECTION", "fields": [{"fieldPath": "admin_ids", "arrayConfig": "CONTAINS"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "bids_request", "queryScope": "COLLECTION", "fields": [{"fieldPath": "equipment_id", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "bids_request", "queryScope": "COLLECTION", "fields": [{"fieldPath": "equipper_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "bids_request", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lodger_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "bids_request", "queryScope": "COLLECTION", "fields": [{"fieldPath": "owner_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "bids_request", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "book_equipments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "admin_ids", "arrayConfig": "CONTAINS"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "book_equipments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "admin_ids", "arrayConfig": "CONTAINS"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "book_equipments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "credit_check_form_id", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "book_equipments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "equipment_id", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "book_equipments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "equipment_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "book_equipments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "equipper_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "book_equipments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lodger_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "book_equipments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "owner_id", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "book_equipments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "owner_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "equipments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "equipper_id", "order": "ASCENDING"}, {"fieldPath": "is_active", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "equipments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "equipper_id", "order": "ASCENDING"}, {"fieldPath": "is_active", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "equipments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "equipper_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}], "fieldOverrides": []}