# Equipment Details Access Restriction

## Overview

This implementation adds access restriction to the equipment details page (`/equipmentDetails/:id`) to ensure that only users with specific language and country settings can access the page.

## Requirements

- **Language**: Must be set to `"en"` (English)
- **Country**: Must be set to `"US"` (United States)

## Implementation Details

### 1. Access Check Function

The `checkAccessPermission()` function verifies that both required cookies are set correctly:

```javascript
const checkAccessPermission = () => {
  const currentLang = getCookies('lang');
  const currentCountry = getCookies('country');
  
  return currentLang === 'en' && currentCountry === 'US';
};
```

### 2. Restriction Logic

If the access check fails, the component returns a `NotFoundPage` with an appropriate message:

```javascript
if (!checkAccessPermission()) {
  return (
    <ScrollToTop>
      <NotFoundPage
        t={t}
        text="Access_not_available"
        description="Feature_not_accessible_in_region"
        img={NoFound}
      />
    </ScrollToTop>
  );
}
```

### 3. Translation Keys

Added new translation keys for consistent messaging across languages:

**English (`src/shared/i18n/en/translation.js`):**
- `Access_not_available`: "Access Not Available"
- `Feature_not_accessible_in_region`: "This feature is not accessible in your country/region."

**French (`src/shared/i18n/fr/translation.js`):**
- `Access_not_available`: "Accès non disponible"
- `Feature_not_accessible_in_region`: "Cette fonctionnalité n'est pas accessible dans votre pays/région."

**Arabic (`src/shared/i18n/ar/translation.js`):**
- `Access_not_available`: "الوصول غير متاح"
- `Feature_not_accessible_in_region`: "هذه الميزة غير متاحة في بلدك/منطقتك."

## Files Modified

1. **`src/components/equipment_details/Equipment_details.jsx`**
   - Added access restriction logic
   - Added imports for `NotFoundPage` and `NoFound` image

2. **Translation Files:**
   - `src/shared/i18n/en/translation.js`
   - `src/shared/i18n/fr/translation.js`
   - `src/shared/i18n/ar/translation.js`

## Testing

### Manual Testing Steps

1. **Test Valid Access (Should Allow):**
   - Set cookies: `lang=en`, `country=US`
   - Navigate to `/equipmentDetails/[any-id]`
   - Expected: Equipment details page loads normally

2. **Test Invalid Access (Should Restrict):**
   - Set cookies: `lang=fr`, `country=CA` (or any other combination)
   - Navigate to `/equipmentDetails/[any-id]`
   - Expected: "Access Not Available" message displayed

3. **Test No Cookies (Should Restrict):**
   - Clear all cookies
   - Navigate to `/equipmentDetails/[any-id]`
   - Expected: "Access Not Available" message displayed

### Test Scenarios

| Language | Country | Access | Expected Result |
|----------|---------|--------|----------------|
| `en`     | `US`    | ✅ Allow | Equipment details page loads |
| `fr`     | `CA`    | ❌ Restrict | Access restriction message |
| `ar`     | `SA`    | ❌ Restrict | Access restriction message |
| `en`     | `CA`    | ❌ Restrict | Access restriction message |
| `null`   | `null`  | ❌ Restrict | Access restriction message |

## User Experience

- **Allowed Users**: Experience no change - equipment details page works as before
- **Restricted Users**: See a clean, translated message explaining that the feature is not available in their region
- **Message Language**: The restriction message is displayed in the user's current language setting
- **Navigation**: Users can still navigate back to the home page via the provided link

## Security Notes

- This is a client-side restriction for UX purposes
- Server-side validation should also be implemented for security
- The restriction is based on cookie values which can be modified by users
- Consider implementing additional server-side checks if this is a security requirement

## Future Enhancements

1. **Server-side Validation**: Add API-level checks for additional security
2. **IP-based Detection**: Implement automatic country detection based on IP address
3. **Graceful Degradation**: Provide alternative content or suggestions for restricted users
4. **Analytics**: Track access attempts from restricted regions for business insights
