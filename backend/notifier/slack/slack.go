package slack

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

// Client represents the Slack client.
type Client struct {
	http       *http.Client
	webhookURL string
}

// NewClient creates a new Slack client.
func NewClient(url string) *Client {
	return &Client{
		http: &http.Client{
			Timeout: 10 * time.Second,
		},
		webhookURL: url,
	}
}

type slackRequestBody struct {
	Text string `json:"text"`
}

// Send send slack message.
func (c *Client) Send(ctx context.Context, message string) error {
	slackBody, err := json.Marshal(slackRequestBody{Text: message})
	if err != nil {
		return err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, c.webhookURL, bytes.NewBuffer(slackBody))
	if err != nil {
		return err
	}

	req.Header.Add("Content-Type", "application/json")

	resp, err := c.http.Do(req)
	if err != nil {
		return err
	}

	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return fmt.Errorf("error occurred when sending slack notification for message : %s", message)
	}

	return nil
}
