package firestoredb

import (
	"context"
	"errors"
	"fmt"
	"log"

	"google.golang.org/api/iterator"

	"github.com/vima-inc/derental/models"
)

const projectCollectionName = "projects"

// GetProjectByID returns a project by id.
func (f *firestoredb) GetProjectByID(ctx context.Context, id string) (models.Project, error) {
	snapshot, err := f.client.Collection(projectCollectionName).Doc(id).Get(ctx)
	if err != nil {
		return models.Project{}, fmt.Errorf("unable to retrieve project with id: %s error: %w", id, err)
	}

	var project models.Project

	err = snapshot.DataTo(&project)
	if err != nil {
		return models.Project{}, fmt.<PERSON><PERSON><PERSON>("unable to parse project with id: %s error: %w", id, err)
	}

	return project, nil
}

func (f *firestoredb) GetProjectByCreditApplicationID(ctx context.Context, ccaID string) ([]models.Project, error) {
	var projects []models.Project

	fmt.Println(ccaID)

	rows := f.client.Collection(projectCollectionName).
		Where("credit_check_form_id", "==", ccaID).
		Documents(ctx)

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve project with ccaID: %s error: %w", ccaID, err)
		}

		var project models.Project

		err = doc.DataTo(&project)
		if err != nil {
			return nil, fmt.Errorf("unable to parse project with ccaID: %s error: %w", ccaID, err)
		}

		projects = append(projects, project)
	}

	return projects, nil
}

// GetProjectByLodgerID returns a project by lodger id.
func (f *firestoredb) GetProjectByLodgerID(ctx context.Context, lodgerID string) ([]models.Project, error) {
	var projects []models.Project

	rows := f.client.Collection(projectCollectionName).
		Where("owner_id", "==", lodgerID).
		Documents(ctx)

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve project with lodger_id: %s error: %w", lodgerID, err)
		}

		var project models.Project

		err = doc.DataTo(&project)
		if err != nil {
			return nil, fmt.Errorf("unable to parse project with lodger_id: %s error: %w", lodgerID, err)
		}

		projects = append(projects, project)
	}

	return projects, nil
}

// GetProjectByMemberID returns a project by member id.
func (f *firestoredb) GetProjectByMemberID(ctx context.Context, memberID string) ([]models.Project, error) {
	var projects []models.Project

	rows := f.client.Collection(projectCollectionName).
		Where("members", "array-contains", memberID).
		Documents(ctx)

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve project with member_id: %s error: %w", memberID, err)
		}

		var project models.Project

		err = doc.DataTo(&project)
		if err != nil {
			return nil, fmt.Errorf("unable to parse project with member_id: %s error: %w", memberID, err)
		}

		projects = append(projects, project)
	}

	return projects, nil
}

// AddProject adds a new project.
func (f *firestoredb) AddProject(ctx context.Context, project models.Project) (string, error) {
	newDoc := f.client.Collection(projectCollectionName).NewDoc()
	project.ID = newDoc.ID

	_, err := newDoc.Set(ctx, project)
	if err != nil {
		return project.ID, fmt.Errorf("unable to set project with id: %s error: %w", project.ID, err)
	}

	return project.ID, nil
}

// UpdateProject updates an existing project.
func (f *firestoredb) UpdateProject(ctx context.Context, project models.Project) error {
	_, err := f.client.Collection(projectCollectionName).Doc(project.ID).Set(ctx, project)
	if err != nil {
		return fmt.Errorf("unable to set project with id: %s error: %w", project.ID, err)
	}

	return nil
}

// DeleteProject deletes an existing project by id.
func (f *firestoredb) DeleteProject(ctx context.Context, id string) error {
	_, err := f.client.Collection(projectCollectionName).Doc(id).Delete(ctx)
	if err != nil {
		return fmt.Errorf("unable to delete project with id: %s error: %w", id, err)
	}

	return nil
}

// PopulateProject populates project gets project id and returns a PopulatedProject.
func (f *firestoredb) PopulateProject(ctx context.Context, projectID string) (models.PopulatedProject, error) {
	project, err := f.GetProjectByID(ctx, projectID)
	if err != nil {
		return models.PopulatedProject{}, fmt.Errorf("unable to retrieve project with id: %s error: %w", projectID, err)
	}

	populatedProject := models.PopulatedProject{
		ID:             project.ID,
		Name:           project.Name,
		StartDate:      project.StartDate,
		EndDate:        project.EndDate,
		Members:        []models.Member{},
		Equipments:     []models.BookEquipment{},
		BidzEquipments: []models.BidzOffer{},
	}

	owner, err := f.GetLodgerByID(ctx, project.OwnerID)
	if err != nil {
		return models.PopulatedProject{}, fmt.Errorf("unable to retrieve owner with id: %s error: %w", project.OwnerID, err)
	}

	populatedProject.Owner = owner

	Creator, err := f.GetLodgerByID(ctx, project.CreatorID)
	if err != nil {
		return models.PopulatedProject{}, fmt.Errorf("unable to retrieve owner with id: %s error: %w", Creator.ID, err)
	}

	populatedProject.Creator = Creator

	for _, memberID := range project.Members {
		member, err := f.GetMemberByID(ctx, memberID, owner.MemberOf)
		if err != nil {
			log.Printf("Warning: unable to retrieve member with id: %s error: %v", memberID, err)
		}

		populatedProject.Members = append(populatedProject.Members, member)
	}

	for _, equipmentID := range project.Equipments {
		equipment, err := f.GetBookEquipmentByID(ctx, equipmentID)
		if err != nil {
			log.Printf("Warning: unable to retrieve equipment with id: %s error: %v", equipmentID, err)
		}

		populatedProject.Equipments = append(populatedProject.Equipments, equipment)
	}

	for _, bidzEquipmentID := range project.BidzEquipments {
		equipment, err := f.GetBidsOfferID(ctx, bidzEquipmentID)
		if err != nil {
			log.Printf("Warning: unable to retrieve bidzEquipment with id: %s error: %v", bidzEquipmentID, err)
		}

		populatedProject.BidzEquipments = append(populatedProject.BidzEquipments, equipment)
	}

	populatedProject.BillingAddress = project.BillingAddress

	populatedProject.DeliveryAddress = project.DeliveryAddress

	populatedProject.EndDate = project.EndDate

	populatedProject.StartDate = project.StartDate

	populatedProject.CreditCheckForm = project.CreditCheckForm

	return populatedProject, nil
}

// GetProjectsByEquipmentID returns a project by equipment id.
func (f *firestoredb) GetProjectsByEquipmentID(ctx context.Context, equipmentID string) ([]models.Project, error) {
	var projects []models.Project

	rows := f.client.Collection(projectCollectionName).
		Where("equipments", "array-contains", equipmentID).
		Documents(ctx)

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve project with equipment_id: %s error: %w", equipmentID, err)
		}

		var project models.Project

		err = doc.DataTo(&project)
		if err != nil {
			return nil, fmt.Errorf("unable to parse project with equipment_id: %s error: %w", equipmentID, err)
		}

		projects = append(projects, project)
	}

	return projects, nil
}
