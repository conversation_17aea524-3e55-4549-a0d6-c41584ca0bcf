package firestoredb

import (
	"context"
	"fmt"

	"github.com/vima-inc/derental/models"
)

const passwordResetCollectionName = "password_resets"

// GetPasswordReset returns a password reset by email.
func (f *firestoredb) GetPasswordReset(ctx context.Context, email string) (models.PasswordReset, error) {
	snapshot, err := f.client.Collection(passwordResetCollectionName).Doc(email).Get(ctx)
	if err != nil {
		return models.PasswordReset{}, fmt.Errorf("unable to retrieve passwordReset with id: %s error: %w", email, err)
	}

	var passwordReset models.PasswordReset

	err = snapshot.DataTo(&passwordReset)
	if err != nil {
		return models.PasswordReset{}, fmt.Errorf("unable to parse passwordReset with id: %s error: %w", email, err)
	}

	return passwordReset, nil
}

// AddPasswordReset adds a new password reset.
func (f *firestoredb) AddPasswordReset(ctx context.Context, passwordReset models.PasswordReset) error {
	_, err := f.client.Collection(passwordResetCollectionName).Doc(passwordReset.Email).Set(ctx, passwordReset)
	if err != nil {
		return fmt.Errorf("unable to create password reset record. error: %w", err)
	}

	return nil
}

// PasswordReset deletes a password reset.
func (f *firestoredb) DeletePasswordReset(ctx context.Context, id string) error {
	_, err := f.client.Collection(passwordResetCollectionName).Doc(id).Delete(ctx)
	if err != nil {
		return fmt.Errorf("unable to delete password with id: %s error: %w", id, err)
	}

	return nil
}
