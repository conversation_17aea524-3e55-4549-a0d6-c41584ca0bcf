package firestoredb

import (
	"context"
	"errors"
	"fmt"

	"google.golang.org/api/iterator"

	"github.com/vima-inc/derental/models"
)

const lodgerCollectionName = "lodgers"

// GetLodgerByID returns a lodger by id.
func (f *firestoredb) GetLodgerByID(ctx context.Context, id string) (models.Lodger, error) {
	snapshot, err := f.client.Collection(lodgerCollectionName).Doc(id).Get(ctx)
	if err != nil {
		return models.Lodger{}, fmt.<PERSON>rro<PERSON>("unable to retrieve lodger with id: %s error: %w", id, err)
	}

	var lodger models.Lodger

	err = snapshot.DataTo(&lodger)
	if err != nil {
		return models.Lodger{}, fmt.Erro<PERSON>("unable to parse lodger with id: %s error: %w", id, err)
	}

	return lodger, nil
}

// AddLodger adds a new lodger.
func (f *firestoredb) AddLodger(ctx context.Context, lodger models.Lodger) error {
	_, err := f.client.Collection(lodgerCollectionName).Doc(lodger.ID).Set(ctx, lodger)
	if err != nil {
		return fmt.Errorf("unable to set lodger with id: %s error: %w", lodger.ID, err)
	}

	return nil
}

// UpdateLodger updates an existing lodger.
func (f *firestoredb) UpdateLodger(ctx context.Context, lodger models.Lodger) error {
	_, err := f.client.Collection(lodgerCollectionName).Doc(lodger.ID).Set(ctx, lodger)
	if err != nil {
		return fmt.Errorf("unable to set lodger with id: %s error: %w", lodger.ID, err)
	}

	return nil
}

// DeleteLodger deletes an existing lodger by id.
func (f *firestoredb) DeleteLodger(ctx context.Context, id string) error {
	_, err := f.client.Collection(lodgerCollectionName).Doc(id).Delete(ctx)
	if err != nil {
		return fmt.Errorf("unable to delete lodger with id: %s error: %w", id, err)
	}

	return nil
}

// GetAllLodgersByCountry fetch all the lodgers of the same country.
func (f *firestoredb) GetAllLodgersByCountry(ctx context.Context, country string) ([]models.Lodger, error) {
	var lodgers []models.Lodger

	iter := f.client.Collection(lodgerCollectionName).Where("address.country", "==", country).Documents(ctx)

	for {
		doc, err := iter.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to iterate lodgers error: %w", err)
		}

		var lodger models.Lodger

		err = doc.DataTo(&lodger)
		if err != nil {
			return nil, fmt.Errorf("unable to parse lodger error: %w", err)
		}

		lodgers = append(lodgers, lodger)
	}

	return lodgers, nil
}

// GetAllLodgers fetch all the lodgers.
func (f *firestoredb) GetAllLodgers(ctx context.Context) ([]models.Lodger, error) {
	var lodgers []models.Lodger

	iter := f.client.Collection(lodgerCollectionName).Documents(ctx)

	for {
		doc, err := iter.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to iterate lodgers error: %w", err)
		}

		var lodger models.Lodger

		err = doc.DataTo(&lodger)
		if err != nil {
			return nil, fmt.Errorf("unable to parse lodger error: %w", err)
		}

		lodgers = append(lodgers, lodger)
	}

	return lodgers, nil
}

// GetLodgerByEmail fetch all the lodgers by email.
func (f *firestoredb) GetLodgerByEmail(ctx context.Context, email string) (models.Lodger, error) {
	iter := f.client.Collection(lodgerCollectionName).Where("email", "==", email).Documents(ctx)
	doc, err := iter.Next()

	if err != nil {
		if errors.Is(err, iterator.Done) {
			return models.Lodger{}, fmt.Errorf("unable to find lodger by email: %s", email)
		}
		return models.Lodger{}, fmt.Errorf("unable to iterate lodgers error: %w", err)
	}

	var lodger models.Lodger

	err = doc.DataTo(&lodger)
	if err != nil {
		return models.Lodger{}, fmt.Errorf("unable to parse lodger error: %w", err)
	}

	return lodger, nil
}

// GetLodgerByMemberID returns a lodger by member id.
func (f *firestoredb) GetLodgerByMemberID(ctx context.Context, memberID string) (models.Lodger, error) {
	var lodgers []models.Lodger

	iter := f.client.Collection(lodgerCollectionName).Where("member_id", "==", memberID).Documents(ctx)

	for {
		doc, err := iter.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return models.Lodger{}, fmt.Errorf("unable to iterate lodgers error: %w", err)
		}

		var lodger models.Lodger

		err = doc.DataTo(&lodger)
		if err != nil {
			return models.Lodger{}, fmt.Errorf("unable to parse lodger error: %w", err)
		}

		lodgers = append(lodgers, lodger)
	}

	return lodgers[0], nil
}
