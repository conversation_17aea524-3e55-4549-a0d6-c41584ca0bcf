package firestoredb

import (
	"context"
	"fmt"

	"cloud.google.com/go/firestore"
	firebase "firebase.google.com/go"

	"github.com/vima-inc/derental/db"
)

type firestoredb struct {
	client *firestore.Client
}

// New creates a new instance of Firestore client.
func New(ctx context.Context, app *firebase.App) (db.Database, error) {
	client, err := app.Firestore(ctx)
	if err != nil {
		return nil, fmt.Errorf("unable to create firestore client %w", err)
	}

	return &firestoredb{client}, nil
}

// GetFirestoreClient returns the underlying Firestore client for advanced operations
func (f *firestoredb) GetFirestoreClient() interface{} {
	return f.client
}
