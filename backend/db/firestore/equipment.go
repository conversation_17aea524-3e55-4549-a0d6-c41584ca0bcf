package firestoredb

import (
	"context"
	"errors"
	"fmt"
	"math"

	"cloud.google.com/go/firestore"
	"google.golang.org/api/iterator"

	"github.com/vima-inc/derental/models"
)

const (
	equipmentCollectionName = "equipments"
	maxBatchSize            = 500
)

// GetEquipmentByID returns a equipment by id.
func (f *firestoredb) GetEquipmentByID(ctx context.Context, id string) (models.Equipment, error) {
	snapshot, err := f.client.Collection(equipmentCollectionName).Where("id", "==", id).Where("is_active", "==", true).Documents(ctx).Next()
	if err != nil {
		return models.Equipment{}, fmt.Errorf("unable to retrieve equipment with id: %s error: %w", id, err)
	}

	var equipment models.Equipment

	err = snapshot.DataTo(&equipment)
	if err != nil {
		return models.Equipment{}, fmt.Errorf("unable to parse equipment with id: %s error: %w", id, err)
	}

	equipment.ID = snapshot.Ref.ID

	return equipment, nil
}

// GetAllEquipmentsByEquipperID returns equipments by equipper id.
func (f *firestoredb) GetEquipmentByName(ctx context.Context, name string) ([]models.Equipment, error) {
	query := f.client.Collection(equipmentCollectionName).
		Where("name", "==", name).
		Where("is_active", "==", true).
		Documents(ctx)

	var equipments []models.Equipment

	for {
		doc, err := query.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve equipment with name: %s error: %w", name, err)
		}

		var equipment models.Equipment

		err = doc.DataTo(&equipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse equipment with name: %s error: %w", name, err)
		}

		equipments = append(equipments, equipment)
	}

	return equipments, nil
}

// GetAllEquipmentsByEquipperID returns equipments by equipper id.
func (f *firestoredb) GetAllEquipmentsByEquipperID(ctx context.Context, equipperID string) ([]models.Equipment, error) {
	query := f.client.Collection(equipmentCollectionName).
		Where("equipper_id", "==", equipperID).
		Where("is_active", "==", true).
		Documents(ctx)

	var equipments []models.Equipment

	for {
		doc, err := query.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve equipment with equipper_id: %s error: %w", equipperID, err)
		}

		var equipment models.Equipment

		err = doc.DataTo(&equipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse equipment with equipper_id: %s error: %w", equipperID, err)
		}

		equipments = append(equipments, equipment)
	}

	return equipments, nil
}

// GetEquipmentsByEquipperID returns equipments by equipper id.
func (f *firestoredb) GetEquipmentsByEquipperID(ctx context.Context, equipperID string, limit int, lastID string) ([]models.Equipment, error) {
	query := f.client.Collection(equipmentCollectionName).Where("equipper_id", "==", equipperID).Where("is_active", "==", true).OrderBy("id", firestore.Asc)

	if lastID != "" {
		query = query.StartAfter(lastID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	rows := query.Documents(ctx)

	var equipments []models.Equipment

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve equipment with equipper_id: %s error: %w", equipperID, err)
		}

		var equipment models.Equipment

		err = doc.DataTo(&equipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse equipment with equipper_id: %s error: %w", equipperID, err)
		}

		equipments = append(equipments, equipment)
	}

	return equipments, nil
}

// GetBookedEquipmentsByEquipperID returns booked equipments by equipper id.
func (f *firestoredb) GetBookedEquipmentsByEquipperID(ctx context.Context, equipperID string, limit int, lastID string) ([]models.Equipment, error) {
	query := f.client.Collection(equipmentCollectionName).
		Where("equipper_id", "==", equipperID).
		Where("status", "==", models.EquipmentBooked).
		Where("is_active", "==", true).
		OrderBy("id", firestore.Asc)

	if lastID != "" {
		query = query.StartAfter(lastID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	rows := query.Documents(ctx)

	var equipments []models.Equipment

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve equipment with equipper_id: %s error: %w", equipperID, err)
		}

		var equipment models.Equipment

		err = doc.DataTo(&equipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse equipment with equipper_id: %s error: %w", equipperID, err)
		}

		equipments = append(equipments, equipment)
	}

	return equipments, nil
}

// GetAllBookedEquipmentsByEquipperID returns booked equipments by equipper id.
func (f *firestoredb) GetAllBookedEquipmentsByEquipperID(ctx context.Context, equipperID string) ([]models.Equipment, error) {
	query := f.client.Collection(equipmentCollectionName).
		Where("equipper_id", "==", equipperID).
		Where("status", "==", models.EquipmentBooked).
		Where("is_active", "==", true).
		OrderBy("id", firestore.Asc)

	rows := query.Documents(ctx)

	var equipments []models.Equipment

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve equipment with equipper_id: %s error: %w", equipperID, err)
		}

		var equipment models.Equipment

		err = doc.DataTo(&equipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse equipment with equipper_id: %s error: %w", equipperID, err)
		}

		equipments = append(equipments, equipment)
	}

	return equipments, nil
}

// GetAvailableEquipmentsByEquipperIDAndEquipmentName returns available equipments by equipper id & equipment name.
func (f *firestoredb) GetAvailableEquipmentsByEquipperIDAndEquipmentName(ctx context.Context, equipperID string, equipmentName string) ([]models.Equipment, error) {
	query := f.client.Collection(equipmentCollectionName).
		Where("equipper_id", "==", equipperID).
		Where("name", "==", equipmentName).
		Where("is_active", "==", true)

	rows := query.Documents(ctx)

	var equipments []models.Equipment

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve equipment with equipper_id: %s error: %w", equipperID, err)
		}

		var equipment models.Equipment

		err = doc.DataTo(&equipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse equipment with equipper_id: %s error: %w", equipperID, err)
		}

		equipments = append(equipments, equipment)
	}

	return equipments, nil
}

// GetAvailableEquipmentsByEquipperIDAndAliasEN returns available equipments by equipper id & equipment name.
func (f *firestoredb) GetAvailableEquipmentsByEquipperIDAndAliasEN(ctx context.Context, equipperID string, equipmentName string) ([]models.Equipment, error) {
	query := f.client.Collection(equipmentCollectionName).
		Where("equipper_id", "==", equipperID).
		Where("status", "==", models.EquipmentAvailable).
		Where("en", "array-contains", equipmentName).
		Where("is_active", "==", true)

	rows := query.Documents(ctx)

	var equipments []models.Equipment

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve equipment with equipper_id: %s error: %w", equipperID, err)
		}

		var equipment models.Equipment

		err = doc.DataTo(&equipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse equipment with equipper_id: %s error: %w", equipperID, err)
		}

		equipments = append(equipments, equipment)
	}

	return equipments, nil
}

// GetAvailableEquipmentsByEquipperIDAndAliasFR returns available equipments by equipper id & equipment name.
func (f *firestoredb) GetAvailableEquipmentsByEquipperIDAndAliasFR(ctx context.Context, equipperID string, equipmentName string) ([]models.Equipment, error) {
	query := f.client.Collection(equipmentCollectionName).
		Where("equipper_id", "==", equipperID).
		Where("status", "==", models.EquipmentAvailable).
		Where("fr", "array-contains", equipmentName).
		Where("is_active", "==", true)

	rows := query.Documents(ctx)

	var equipments []models.Equipment

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve equipment with equipper_id: %s error: %w", equipperID, err)
		}

		var equipment models.Equipment

		err = doc.DataTo(&equipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse equipment with equipper_id: %s error: %w", equipperID, err)
		}

		equipments = append(equipments, equipment)
	}

	return equipments, nil
}

// AddEquipment adds a new equipment.
func (f *firestoredb) AddEquipment(ctx context.Context, equipment models.Equipment) (models.Equipment, error) {
	newDoc := f.client.Collection(equipmentCollectionName).NewDoc()
	equipment.ID = newDoc.ID

	_, err := newDoc.Set(ctx, equipment)
	if err != nil {
		return models.Equipment{}, fmt.Errorf("unable to set equipment with id: %s error: %w", equipment.ID, err)
	}

	return equipment, nil
}

// BatchAddEquipment adds equipments in batch.
func (f *firestoredb) BatchAddEquipment(ctx context.Context, equipments []models.Equipment) error {
	collection := f.client.Collection(equipmentCollectionName)

	totalEquipments := len(equipments)
	nbBatch := int(math.Ceil(float64(totalEquipments) / float64(maxBatchSize)))

	for i := 0; i < nbBatch; i++ {
		from := i * maxBatchSize
		to := (i + 1) * maxBatchSize

		if to > totalEquipments {
			to = totalEquipments
		}

		batch := f.client.BulkWriter(ctx)

		for _, equipment := range equipments[from:to] {
			var doc *firestore.DocumentRef

			if equipment.ID == "" {
				query := collection.Where("internal_id", "==", equipment.InternalID).Limit(1)
				docs, err := query.Documents(ctx).GetAll()
				if err != nil {
					batch.End()
					return fmt.Errorf("unable to query equipment with internalID: %s error: %w", equipment.InternalID, err)
				}

				if len(docs) > 0 {
					doc = docs[0].Ref
					equipment.ID = doc.ID
				} else {
					doc = collection.NewDoc()
					equipment.ID = doc.ID
				}
			} else {
				doc = collection.Doc(equipment.ID)
			}

			_, err := batch.Set(doc, equipment)
			if err != nil {
				batch.End()
				return fmt.Errorf("unable to set equipment with id: %s error: %w", equipment.ID, err)
			}
		}

		batch.End()
	}

	return nil
}

// BatchUpdateEquipment updates equipments in batch.
func (f *firestoredb) BatchUpdateEquipment(ctx context.Context, equipments []models.Equipment) error {
	collection := f.client.Collection(equipmentCollectionName)

	totalEquipments := len(equipments)
	nbBatch := int(math.Ceil(float64(totalEquipments) / float64(maxBatchSize)))

	for i := 0; i < nbBatch; i++ {
		from := i * maxBatchSize
		to := (i + 1) * maxBatchSize

		if to > totalEquipments {
			to = totalEquipments
		}

		batch := f.client.BulkWriter(ctx)

		for _, equipment := range equipments[from:to] {
			if equipment.ID == "" {
				equipment.ID = collection.NewDoc().ID
			}

			doc := collection.Doc(equipment.ID)

			_, err := batch.Set(doc, equipment)
			if err != nil {
				return fmt.Errorf("unable to set equipment with id: %s error: %w", equipment.ID, err)
			}
		}

		batch.End()
	}

	return nil
}

// UpdateEquipment updates an existing equipment.
func (f *firestoredb) UpdateEquipment(ctx context.Context, equipment models.Equipment) error {
	_, err := f.client.Collection(equipmentCollectionName).Doc(equipment.ID).Set(ctx, equipment)
	if err != nil {
		return fmt.Errorf("unable to set equipment with id: %s error: %w", equipment.ID, err)
	}

	return nil
}

// DeleteEquipment deletes an existing equipment by id.
func (f *firestoredb) DeleteEquipment(ctx context.Context, id string) error {
	_, err := f.client.Collection(equipmentCollectionName).Doc(id).Delete(ctx)
	if err != nil {
		return fmt.Errorf("unable to delete equipment with id: %s error: %w", id, err)
	}

	return nil
}

// DeleteEquipmentsByEquipperID deletes equipments by equipper id.
func (f *firestoredb) DeleteEquipmentsByEquipperID(ctx context.Context, equipperID string) error {
	query := f.client.Collection(equipmentCollectionName).Where("equipper_id", "==", equipperID)

	rows := query.Documents(ctx)

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return fmt.Errorf("unable to retrieve equipment with equipper_id: %s error: %w", equipperID, err)
		}

		_, err = doc.Ref.Delete(ctx)
		if err != nil {
			return fmt.Errorf("unable to delete equipment with id: %s error: %w", doc.Ref.ID, err)
		}
	}

	return nil
}

// ChangeEquipmentStatus changes the status of an equipment.
func (f *firestoredb) ChangeEquipmentStatus(ctx context.Context, id string, status models.EquipmentStatus) error {
	_, err := f.client.Collection(equipmentCollectionName).Doc(id).Update(ctx, []firestore.Update{
		{
			Path:  "status",
			Value: status,
		},
	})
	if err != nil {
		return fmt.Errorf("unable to update equipment with id: %s error: %w", id, err)
	}

	return nil
}

// CountAllEquipments returns the total count of all equipments
func (f *firestoredb) CountAllEquipments(ctx context.Context) (int, error) {
	// Count active equipments by iterating through documents
	query := f.client.Collection(equipmentCollectionName).Where("is_active", "==", true)

	iter := query.Documents(ctx)
	defer iter.Stop()

	count := 0
	for {
		_, err := iter.Next()
		if errors.Is(err, iterator.Done) {
			break
		}
		if err != nil {
			return 0, fmt.Errorf("unable to iterate equipments for counting: %w", err)
		}
		count++
	}

	return count, nil
}

// GetEquipmentByID returns a equipment by id.
func (f *firestoredb) GetEquipmentByInternalID(ctx context.Context, id string) (models.Equipment, error) {
	snapshot, err := f.client.Collection(equipmentCollectionName).Where("internal_id", "==", id).Where("is_active", "==", true).Documents(ctx).Next()
	if err != nil {
		return models.Equipment{}, fmt.Errorf("unable to retrieve equipment with internal_id: %s error: %w", id, err)
	}

	var equipment models.Equipment

	err = snapshot.DataTo(&equipment)
	if err != nil {
		return models.Equipment{}, fmt.Errorf("unable to parse equipment with internal_id: %s error: %w", id, err)
	}

	equipment.ID = snapshot.Ref.ID

	return equipment, nil
}
