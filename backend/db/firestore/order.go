package firestoredb

import (
	"context"
	"fmt"

	"github.com/vima-inc/derental/models"
)

const orderCollectionName = "orders"

// AddOrder adds an order to the database.
func (f *firestoredb) AddOrder(ctx context.Context, order models.Order) error {
	_, err := f.client.Collection(orderCollectionName).Doc(order.ID).Set(ctx, order)
	if err != nil {
		return fmt.Errorf("unable to set order with id: %s error: %w", order.ID, err)
	}

	return nil
}

// GetOrderByID returns an order by id.
func (f *firestoredb) GetOrderByID(ctx context.Context, id string) (models.Order, error) {
	snapshot, err := f.client.Collection(orderCollectionName).Doc(id).Get(ctx)
	if err != nil {
		return models.Order{}, fmt.Errorf("unable to retrieve order with id: %s error: %w", id, err)
	}

	var order models.Order

	err = snapshot.DataTo(&order)
	if err != nil {
		return models.Order{}, fmt.Errorf("unable to parse order with id: %s error: %w", id, err)
	}

	return order, nil
}

// GetOrderByBookingID returns an order by booking id.
func (f *firestoredb) GetOrderByBookingID(ctx context.Context, id string) (models.Order, error) {
	snapshot, err := f.client.Collection(orderCollectionName).Where("booking_id", "==", id).Documents(ctx).GetAll()
	if err != nil {
		return models.Order{}, fmt.Errorf("unable to retrieve order with booking id: %s error: %w", id, err)
	}

	if len(snapshot) == 0 {
		return models.Order{}, fmt.Errorf("unable to retrieve order with booking id: %s error: %w", id, err)
	}

	if len(snapshot) > 1 {
		return models.Order{}, fmt.Errorf("multiple orders found with booking id: %s", id)
	}

	var order models.Order
	err = snapshot[0].DataTo(&order)
	if err != nil {
		return models.Order{}, fmt.Errorf("unable to parse order with booking id: %s error: %w", id, err)
	}

	return order, nil
}

// GetOrderByPaymentIntentID returns an order by payment intent id.
func (f *firestoredb) GetOrderByPaymentIntentID(ctx context.Context, paymentIntentID string) (models.Order, error) {
	snapshot, err := f.client.Collection(orderCollectionName).Where("payment.payment_intent_id", "==", paymentIntentID).Documents(ctx).GetAll()
	if err != nil {
		return models.Order{}, fmt.Errorf("unable to retrieve order with payment intent id: %s error: %w", paymentIntentID, err)
	}

	if len(snapshot) == 0 {
		return models.Order{}, fmt.Errorf("unable to retrieve order with payment intent id: %s error: %w", paymentIntentID, err)
	}

	if len(snapshot) > 1 {
		return models.Order{}, fmt.Errorf("multiple orders found with payment intent id: %s", paymentIntentID)
	}

	var order models.Order
	err = snapshot[0].DataTo(&order)
	if err != nil {
		return models.Order{}, fmt.Errorf("unable to parse order with payment intent id: %s error: %w", paymentIntentID, err)
	}

	return order, nil
}

// UpdateOrder updates an order in the database.
func (f *firestoredb) UpdateOrder(ctx context.Context, order models.Order) error {
	_, err := f.client.Collection(orderCollectionName).Doc(order.ID).Set(ctx, order)
	if err != nil {
		return fmt.Errorf("unable to set order with id: %s error: %w", order.ID, err)
	}

	return nil
}
