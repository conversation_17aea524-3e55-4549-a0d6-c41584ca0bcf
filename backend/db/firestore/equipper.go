package firestoredb

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"google.golang.org/api/iterator"

	"github.com/vima-inc/derental/models"
)

const equipperCollectionName = "equippers"

// GetEquipperByID return an equipper by id.
func (f *firestoredb) GetEquipperByID(ctx context.Context, id string) (models.Equipper, error) {
	snapshot, err := f.client.Collection(equipperCollectionName).Doc(id).Get(ctx)
	if err != nil {
		return models.Equipper{}, fmt.Errorf("unable to retrieve equipper with id: %s error: %w", id, err)
	}

	var equipper models.Equipper

	err = snapshot.DataTo(&equipper)
	if err != nil {
		return models.Equipper{}, fmt.Errorf("unable to parse equipper with id: %s error: %w", id, err)
	}

	return equipper, nil
}

// GetEquipperByEmail returns an equipper by email.
func (f *firestoredb) GetEquipperByEmail(ctx context.Context, email string) (models.Equipper, error) {
	snapshot := f.client.Collection(equipperCollectionName).Where("email", "==", email).Documents(ctx)
	defer snapshot.Stop()

	var equipper models.Equipper

	for {
		doc, err := snapshot.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		err = doc.DataTo(&equipper)
		if err != nil {
			return models.Equipper{}, fmt.Errorf("unable to parse equipper with email: %s error: %w", email, err)
		}
	}

	return equipper, nil
}

// GetAllEquipper returns all equippers.
func (f *firestoredb) GetAllEquipper(ctx context.Context) ([]models.Equipper, error) {
	snapshot := f.client.Collection(equipperCollectionName).Documents(ctx)
	defer snapshot.Stop()

	var equippers []models.Equipper

	for {
		doc, err := snapshot.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve all equipper error: %w", err)
		}

		var equipper models.Equipper

		err = doc.DataTo(&equipper)
		if err != nil {
			return []models.Equipper{}, fmt.Errorf("unable to get all equipper  error: %w", err)
		}

		equippers = append(equippers, equipper)
	}

	return equippers, nil
}

// GetAllEquipper returns all equippers.
func (f *firestoredb) GetAllEquippersByCategory(ctx context.Context, category []string) ([]models.Equipper, error) {
	snapshot := f.client.Collection(equipperCollectionName).Where("categories", "array-contains-any", category).Documents(ctx)
	defer snapshot.Stop()

	var equippers []models.Equipper

	for {
		doc, err := snapshot.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve all equipper error: %w", err)
		}

		var equipper models.Equipper

		err = doc.DataTo(&equipper)
		if err != nil {
			return []models.Equipper{}, fmt.Errorf("unable to get all equipper  error: %w", err)
		}

		equippers = append(equippers, equipper)
	}

	return equippers, nil
}

// GetAllEquippersByCategoryCoverageArea returns all equippers.
func (f *firestoredb) GetAllEquippersByCategoryCoverageArea(ctx context.Context, category []string, coverageArea []string) ([]models.Equipper, error) {
	snapshot := f.client.Collection(equipperCollectionName).Where("categories", "array-contains-any", category).Documents(ctx)
	defer snapshot.Stop()

	var equippers []models.Equipper

	for {
		doc, err := snapshot.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve all equipper error: %w", err)
		}

		var equipper models.Equipper

		err = doc.DataTo(&equipper)
		if err != nil {
			return []models.Equipper{}, fmt.Errorf("unable to get all equipper  error: %w", err)
		}

		equippers = append(equippers, equipper)
	}

	equippersCoverage := []models.Equipper{}

	for _, equipper := range equippers {
		for _, coverage := range equipper.CoverageArea {
			if coverage == coverageArea[0] {
				equippersCoverage = append(equippersCoverage, equipper)
			}
		}
	}

	return equippersCoverage, nil
}

// AddEquipper adds a new equipper.
func (f *firestoredb) AddEquipper(ctx context.Context, equipper models.Equipper) error {
	_, err := f.client.Collection(equipperCollectionName).Doc(equipper.ID).Set(ctx, equipper)
	if err != nil {
		return fmt.Errorf("unable to set equipper with id: %s error: %w", equipper.ID, err)
	}

	return nil
}

// UpdateEquipper updates an existing equipper.
func (f *firestoredb) UpdateEquipper(ctx context.Context, equipper models.Equipper) error {
	_, err := f.client.Collection(equipperCollectionName).Doc(equipper.ID).Set(ctx, equipper)
	if err != nil {
		return fmt.Errorf("unable to set equipper with id: %s error: %w", equipper.ID, err)
	}

	return nil
}

// DeleteEquipper deletes an existing equipper by id.
func (f *firestoredb) DeleteEquipper(ctx context.Context, id string) error {
	_, err := f.client.Collection(equipperCollectionName).Doc(id).Delete(ctx)
	if err != nil {
		return fmt.Errorf("unable to delete equipper with id: %s error: %w", id, err)
	}

	return nil
}

// GetEquipperByMemberID returns an equipper by member id.
func (f *firestoredb) GetEquipperByMemberID(ctx context.Context, memberID string) (models.Equipper, error) {
	var equipper []models.Equipper

	iter := f.client.Collection(equipperCollectionName).Where("member_id", "==", memberID).Documents(ctx)

	for {
		doc, err := iter.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return models.Equipper{}, fmt.Errorf("unable to get equipper by member id: %s error: %w", memberID, err)
		}

		var lodger models.Equipper

		err = doc.DataTo(&lodger)
		if err != nil {
			return models.Equipper{}, fmt.Errorf("unable to get equipper by member id: %s error: %w", memberID, err)
		}

		equipper = append(equipper, lodger)
	}

	return equipper[0], nil
}

// CheckEquipperByUserName checks if an equipper exists by userName.
func (f *firestoredb) CheckEquipperByUserName(ctx context.Context, userName string) (bool, error) {
	iter := f.client.Collection(equipperCollectionName).Where("user_name", "==", userName).Documents(ctx)

	for {
		doc, err := iter.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return false, fmt.Errorf("unable to check equipper by username: %s error: %w", userName, err)
		}

		if doc != nil {
			return true, nil
		}
	}

	return false, nil
}

// GetEquipperByUserName returns an equipper by userName.
func (f *firestoredb) GetEquipperByUserName(ctx context.Context, userName string) (models.Equipper, error) {
	var equipper []models.Equipper

	iter := f.client.Collection(equipperCollectionName).Where("user_name", "==", userName).Documents(ctx)

	for {
		doc, err := iter.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return models.Equipper{}, fmt.Errorf("unable to get equipper by username: %s error: %w", userName, err)
		}

		var lodger models.Equipper

		err = doc.DataTo(&lodger)
		if err != nil {
			return models.Equipper{}, fmt.Errorf("unable to get equipper by username: %s error: %w", userName, err)
		}

		equipper = append(equipper, lodger)
	}

	return equipper[0], nil
}

// GetEquippersByCountry returns all equippers by country.
func (f *firestoredb) GetEquippersByCountry(ctx context.Context, countryCode string) ([]models.Equipper, error) {
	snapshot := f.client.Collection(equipperCollectionName).Where("address.country_code", "==", strings.ToUpper(countryCode)).Documents(ctx)
	defer snapshot.Stop()

	var equippers []models.Equipper

	for {
		doc, err := snapshot.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve equippers with country: %s error: %w", countryCode, err)
		}

		var equipper models.Equipper

		err = doc.DataTo(&equipper)
		if err != nil {
			return []models.Equipper{}, fmt.Errorf("unable to get equippers with country: %s error: %w", countryCode, err)
		}

		equippers = append(equippers, equipper)
	}

	return equippers, nil
}
