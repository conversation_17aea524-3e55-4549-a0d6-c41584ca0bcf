package firestoredb

import (
	"context"
	"errors"
	"fmt"
	"math"

	"cloud.google.com/go/firestore"
	"google.golang.org/api/iterator"

	"github.com/vima-inc/derental/models"
)

const (
	bidsRequestCollectionName = "bids_request"
	bidsOfferCollectionName   = "bids_offer"
)

// AddBidsRequest add a new Bids Request.
func (f *firestoredb) AddBidsRequest(ctx context.Context, lodgerID string, bidsRequest models.BidsRequest) error {
	newDoc := f.client.Collection(bidsRequestCollectionName).NewDoc()
	bidsRequest.ID = newDoc.ID

	_, err := newDoc.Set(ctx, bidsRequest)
	if err != nil {
		return fmt.Errorf("unable to add member with uid: %s error: %w", lodgerID, err)
	}

	return nil
}

// UpdateRequest updates an existing Bids offer.
func (f *firestoredb) UpdateRequest(ctx context.Context, bidsRequest models.BidsRequest) error {
	_, err := f.client.Collection(bidsRequestCollectionName).Doc(bidsRequest.ID).Set(ctx, bidsRequest)
	if err != nil {
		return fmt.Errorf("unable to set bids request with id: %s error: %w", bidsRequest.ID, err)
	}

	return nil
}

// GetAllLodgerBidsRequest get list of all bids request for a lodger.
func (f *firestoredb) GetAllLodgerBidsRequest(ctx context.Context, limit int, lastID string, status models.BidsRequestStatus, lodgerID string) ([]models.BidsRequest, error) {
	query := f.client.Collection(bidsRequestCollectionName).Where("status", "==", status).OrderBy("id", firestore.Asc)

	if lastID != "" {
		query = query.StartAfter(lastID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if lodgerID != "" {
		query = query.Where("lodger_id", "==", lodgerID)
	}

	rows := query.Documents(ctx)

	var bidsRequests []models.BidsRequest

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to get bids request error: %w", err)
		}

		var bidsRequest models.BidsRequest

		err = doc.DataTo(&bidsRequest)
		if err != nil {
			return nil, fmt.Errorf("unable to parse bids request error: %w", err)
		}

		bidsRequest.ID = doc.Ref.ID

		bidsRequests = append(bidsRequests, bidsRequest)
	}

	return bidsRequests, nil
}

func (f *firestoredb) GetAllBidsRequest(ctx context.Context, limit int, lastID string, status models.BidsRequestStatus, equipperID string) ([]models.BidsRequest, error) {
	query := f.client.Collection(bidsRequestCollectionName).Where("status", "==", status).OrderBy("id", firestore.Asc)

	if lastID != "" {
		query = query.StartAfter(lastID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if equipperID != "" {
		query = query.Where("equipper_id", "==", equipperID)
	}

	rows := query.Documents(ctx)

	var bidsRequests []models.BidsRequest

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to get bids request error: %w", err)
		}

		var bidsRequest models.BidsRequest

		err = doc.DataTo(&bidsRequest)
		if err != nil {
			return nil, fmt.Errorf("unable to parse bids request error: %w", err)
		}

		bidsRequest.ID = doc.Ref.ID

		bidsRequests = append(bidsRequests, bidsRequest)
	}

	return bidsRequests, nil
}

// GetBidsRequestID returns bids request id.
func (f *firestoredb) GetBidsRequestID(ctx context.Context, id string) (models.BidsRequest, error) {
	snapshot, err := f.client.Collection(bidsRequestCollectionName).Doc(id).Get(ctx)
	if err != nil {
		return models.BidsRequest{}, fmt.Errorf("unable to retrieve bids request with id: %s error: %w", id, err)
	}

	var bidsRequest models.BidsRequest

	err = snapshot.DataTo(&bidsRequest)
	if err != nil {
		return models.BidsRequest{}, fmt.Errorf("unable to parse bids request with id: %s error: %w", id, err)
	}

	bidsRequest.ID = snapshot.Ref.ID

	return bidsRequest, nil
}

// AddOfferRequest add a new bids offer.
func (f *firestoredb) AddOfferRequest(ctx context.Context, equipperID string, bidsOffer models.BidzOffer) error {
	newDoc := f.client.Collection(bidsOfferCollectionName).NewDoc()
	bidsOffer.ID = newDoc.ID

	_, err := newDoc.Set(ctx, bidsOffer)
	if err != nil {
		return fmt.Errorf("unable to add member with uid: %s error: %w", equipperID, err)
	}

	return nil
}

// GetAllBidsOffer get list of all bids offer.
func (f *firestoredb) GetAllBidsOffer(ctx context.Context, limit int, lastID string, status models.BidsOfferStatus, where map[string]interface{}) ([]models.BidzOffer, error) {
	query := f.client.Collection(bidsOfferCollectionName).Where("status", "in", []models.BidsOfferStatus{status}).OrderBy("id", firestore.Asc)
	if lastID != "" {
		query = query.StartAfter(lastID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if len(where) > 0 {
		for key, val := range where {
			query = query.Where(key, "==", val)
		}
	}

	rows := query.Documents(ctx)

	var bidsOffers []models.BidzOffer

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve bids offer error: %w", err)
		}

		var bidsOffer models.BidzOffer

		err = doc.DataTo(&bidsOffer)
		if err != nil {
			return nil, fmt.Errorf("unable to parse bids offer error: %w", err)
		}

		bidsOffers = append(bidsOffers, bidsOffer)
	}

	return bidsOffers, nil
}

// GetAllBidsOffer get list of all bids offer.
func (f *firestoredb) GetAllBidsLodgerOffer(ctx context.Context, limit int, lastID string, status models.BidsOfferStatus, lodgerID string) ([]models.BidzOffer, error) {
	query := f.client.Collection(bidsOfferCollectionName).Where("status", "==", status).OrderBy("id", firestore.Asc)

	if lastID != "" {
		query = query.StartAfter(lastID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if lodgerID != "" {
		query = query.Where("lodger_id", "==", lodgerID)
	}

	rows := query.Documents(ctx)

	var bidsOffers []models.BidzOffer

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve bids offer error: %w", err)
		}

		var bidsOffer models.BidzOffer

		err = doc.DataTo(&bidsOffer)
		if err != nil {
			return nil, fmt.Errorf("unable to parse bids offer error: %w", err)
		}

		bidsOffers = append(bidsOffers, bidsOffer)
	}

	return bidsOffers, nil
}

// GetBidsOfferID returns bids offer by id.
func (f *firestoredb) GetBidsOfferID(ctx context.Context, id string) (models.BidzOffer, error) {
	snapshot, err := f.client.Collection(bidsOfferCollectionName).Doc(id).Get(ctx)
	if err != nil {
		return models.BidzOffer{}, fmt.Errorf("unable to retrieve bids offer with id: %s error: %w", id, err)
	}

	var bidsOffer models.BidzOffer

	err = snapshot.DataTo(&bidsOffer)
	if err != nil {
		return models.BidzOffer{}, fmt.Errorf("unable to parse bids offer with id: %s error: %w", id, err)
	}

	bidsOffer.ID = snapshot.Ref.ID

	return bidsOffer, nil
}

// GetBidsOfferByEquipmentID returns bids offer by equipment id.
func (f *firestoredb) GetBidsOfferByEquipmentID(ctx context.Context, equipmentID string) ([]models.BidzOffer, error) {
	query := f.client.Collection(bidsOfferCollectionName).Where("equipment_id", "==", equipmentID).OrderBy("id", firestore.Asc)

	rows := query.Documents(ctx)

	var bidsOffers []models.BidzOffer

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve bids offer error: %w", err)
		}

		var bidsOffer models.BidzOffer

		err = doc.DataTo(&bidsOffer)
		if err != nil {
			return nil, fmt.Errorf("unable to parse bids offer error: %w", err)
		}

		bidsOffers = append(bidsOffers, bidsOffer)
	}

	return bidsOffers, nil
}

// BatchUpdateProjects updates projects in batch.
func (f *firestoredb) BatchUpdateProjects(ctx context.Context, projects []models.Project) error {
	collection := f.client.Collection(projectCollectionName)

	totalProjects := len(projects)
	nbBatch := int(math.Ceil(float64(totalProjects) / float64(maxBatchSize)))

	for i := 0; i < nbBatch; i++ {
		from := i * maxBatchSize
		to := (i + 1) * maxBatchSize

		if to > totalProjects {
			to = totalProjects
		}

		batch := f.client.BulkWriter(ctx)

		for _, project := range projects[from:to] {
			if project.ID == "" {
				return fmt.Errorf("project id is empty")
			}

			doc := collection.Doc(project.ID)

			_, err := batch.Set(doc, project)
			if err != nil {
				return fmt.Errorf("unable to set project with id: %s error: %w", project.ID, err)
			}
		}

		batch.End()
	}

	return nil
}

// GetBidsOfferByEquipmentID returns bids offer by equipment id.
func (f *firestoredb) GetBidsRequestByEquipmentID(ctx context.Context, equipmentID string) ([]models.BidsRequest, error) {
	query := f.client.Collection(bidsRequestCollectionName).Where("equipment_id", "==", equipmentID).OrderBy("id", firestore.Asc)

	rows := query.Documents(ctx)

	var bidsRequests []models.BidsRequest

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve bids offer error: %w", err)
		}

		var bidsRequest models.BidsRequest

		err = doc.DataTo(&bidsRequest)
		if err != nil {
			return nil, fmt.Errorf("unable to parse bids offer error: %w", err)
		}

		bidsRequests = append(bidsRequests, bidsRequest)
	}

	return bidsRequests, nil
}

// UpdateOffer updates an existing bids offer.
func (f *firestoredb) UpdateOffer(ctx context.Context, bidsOffer models.BidzOffer) error {
	_, err := f.client.Collection(bidsOfferCollectionName).Doc(bidsOffer.ID).Set(ctx, bidsOffer)
	if err != nil {
		return fmt.Errorf("unable to set bids offer with id: %s error: %w", bidsOffer.ID, err)
	}

	return nil
}

// GetAllOwnerBidsRequest get list of all bids request for a specific owner.
func (f *firestoredb) GetAllOwnerBidsRequest(ctx context.Context, ownerID string, limit int, lastID string, status models.BidsRequestStatus) ([]models.BidsRequest, error) {
	query := f.client.Collection(bidsRequestCollectionName).Where("owner_id", "==", ownerID).Where("status", "in", []models.BidsRequestStatus{status}).OrderBy("id", firestore.Asc)
	if lastID != "" {
		query = query.StartAfter(lastID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	rows := query.Documents(ctx)

	var bidsRequests []models.BidsRequest

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve bids request error: %w", err)
		}

		var bidsRequest models.BidsRequest

		err = doc.DataTo(&bidsRequest)
		if err != nil {
			return nil, fmt.Errorf("unable to parse bids request error: %w", err)
		}

		bidsRequests = append(bidsRequests, bidsRequest)
	}

	return bidsRequests, nil
}

// GetAllAdminsBidsRequest get list of all bids request for a specific admin.
func (f *firestoredb) GetAllAdminsBidsRequest(ctx context.Context, adminID string, limit int, lastID string, status models.BidsRequestStatus) ([]models.BidsRequest, error) {
	query := f.client.Collection(bidsRequestCollectionName).Where("admin_ids", "array-contains", adminID).Where("status", "in", []models.BidsRequestStatus{status}).OrderBy("id", firestore.Asc)
	if lastID != "" {
		query = query.StartAfter(lastID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	rows := query.Documents(ctx)

	var bidsRequests []models.BidsRequest

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve bids request error: %w", err)
		}

		var bidsRequest models.BidsRequest

		err = doc.DataTo(&bidsRequest)
		if err != nil {
			return nil, fmt.Errorf("unable to parse bids request error: %w", err)
		}

		bidsRequests = append(bidsRequests, bidsRequest)
	}

	return bidsRequests, nil
}

// GetAllAdminsBidsOffer get list of all bids offer for a specific admin.
func (f *firestoredb) GetAllAdminsBidsOffer(ctx context.Context, adminID string, limit int, lastID string, status models.BidsOfferStatus) ([]models.BidzOffer, error) {
	query := f.client.Collection(bidsOfferCollectionName).Where("admin_ids", "array-contains", adminID).Where("status", "in", []models.BidsOfferStatus{status}).OrderBy("id", firestore.Asc)
	if lastID != "" {
		query = query.StartAfter(lastID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	rows := query.Documents(ctx)

	var bidsOffers []models.BidzOffer

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve bids offer error: %w", err)
		}

		var bidsOffer models.BidzOffer

		err = doc.DataTo(&bidsOffer)
		if err != nil {
			return nil, fmt.Errorf("unable to parse bids offer error: %w", err)
		}

		bidsOffers = append(bidsOffers, bidsOffer)
	}

	return bidsOffers, nil
}

// GetAllOwnerBidsOffer get list of all bids offer for a specific owner.
func (f *firestoredb) GetAllOwnerBidsOffer(ctx context.Context, ownerID string, limit int, lastID string, status models.BidsOfferStatus) ([]models.BidzOffer, error) {
	query := f.client.Collection(bidsOfferCollectionName).Where("owner_id", "==", ownerID).Where("status", "in", []models.BidsOfferStatus{status}).OrderBy("id", firestore.Asc)
	if lastID != "" {
		query = query.StartAfter(lastID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	rows := query.Documents(ctx)

	var bidsOffers []models.BidzOffer

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve bids offer error: %w", err)
		}

		var bidsOffer models.BidzOffer

		err = doc.DataTo(&bidsOffer)
		if err != nil {
			return nil, fmt.Errorf("unable to parse bids offer error: %w", err)
		}

		bidsOffers = append(bidsOffers, bidsOffer)
	}

	return bidsOffers, nil
}
