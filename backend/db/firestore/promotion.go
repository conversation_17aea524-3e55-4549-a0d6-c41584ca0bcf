package firestoredb

import (
	"context"
	"errors"
	"fmt"

	"google.golang.org/api/iterator"

	"github.com/vima-inc/derental/models"
)

const promotionCollectionName = "promotions"

// CreatePromotionCode creates a new promotion code.
func (f *firestoredb) CreatePromotionCode(ctx context.Context, promo models.Promotion) error {
	_, err := f.client.Collection(promotionCollectionName).Doc(promo.Code).Set(ctx, promo)
	if err != nil {
		return err
	}

	return nil
}

// DeletePromotionCode deletes a promotion code.
func (f *firestoredb) DeletePromotionCode(ctx context.Context, equipperID string, code string) error {
	rows := f.client.Collection(promotionCollectionName).Where("equipper_id", "==", equipperID).Where("id", "==", code).Documents(ctx)
	for {
		doc, err := rows.Next()

		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return fmt.Errorf("unable to get promotion for equipper %s and lodger %s: %w", equipperID, code, err)
		}

		_, err = doc.Ref.Delete(ctx)
		if err != nil {
			return fmt.Errorf("unable to delete promotion for equipper %s and lodger %s: %w", equipperID, code, err)
		}
	}

	return nil
}

// GetAllPromotionCodeByEquipperID gets all promotion codes for an equipper.
func (f *firestoredb) GetAllPromotionCodeByEquipperID(ctx context.Context, equipperID string) ([]models.Promotion, error) {
	var promotions []models.Promotion

	rows := f.client.Collection(promotionCollectionName).
		Where("equipper_id", "==", equipperID).
		Documents(ctx)

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to get promotion for equipper %s: %w", equipperID, err)
		}

		var promotion models.Promotion

		err = doc.DataTo(&promotion)
		if err != nil {
			return nil, err
		}

		promotions = append(promotions, promotion)
	}

	return promotions, nil
}

// GetPromotionCodeByEquipperIDAndLodgerID gets a promotion code for an equipper and lodger.
func (f *firestoredb) GetPromotionCodeByEquipperIDAndLodgerID(ctx context.Context, equipperID string, lodgerID string) (models.Promotion, error) {
	var promotion models.Promotion

	rows := f.client.Collection(promotionCollectionName).
		Where("equipper_id", "==", equipperID).
		Where("lodger_id", "==", lodgerID).
		Documents(ctx)

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return models.Promotion{}, fmt.Errorf("unable to get promotion for equipper %s and lodger %s: %w", equipperID, lodgerID, err)
		}

		err = doc.DataTo(&promotion)
		if err != nil {
			return models.Promotion{}, err
		}
	}

	return promotion, nil
}
