// Code generated by mockery v2.52.3. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"

	models "github.com/vima-inc/derental/models"
)

// Database is an autogenerated mock type for the Database type
type Database struct {
	mock.Mock
}

// AddBidsRequest provides a mock function with given fields: ctx, lodgerID, bidsRequest
func (_m *Database) AddBidsRequest(ctx context.Context, lodgerID string, bidsRequest models.BidsRequest) error {
	ret := _m.Called(ctx, lodgerID, bidsRequest)

	if len(ret) == 0 {
		panic("no return value specified for AddBidsRequest")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, models.BidsRequest) error); ok {
		r0 = rf(ctx, lodgerID, bidsRequest)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// AddBookEquipment provides a mock function with given fields: ctx, bookEquipment
func (_m *Database) AddBookEquipment(ctx context.Context, bookEquipment models.BookEquipment) (string, error) {
	ret := _m.Called(ctx, bookEquipment)

	if len(ret) == 0 {
		panic("no return value specified for AddBookEquipment")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, models.BookEquipment) (string, error)); ok {
		return rf(ctx, bookEquipment)
	}
	if rf, ok := ret.Get(0).(func(context.Context, models.BookEquipment) string); ok {
		r0 = rf(ctx, bookEquipment)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, models.BookEquipment) error); ok {
		r1 = rf(ctx, bookEquipment)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// AddCreditCheckForm provides a mock function with given fields: ctx, creditCheckForm
func (_m *Database) AddCreditCheckForm(ctx context.Context, creditCheckForm models.CreditCheckForm) (models.CreditCheckForm, error) {
	ret := _m.Called(ctx, creditCheckForm)

	if len(ret) == 0 {
		panic("no return value specified for AddCreditCheckForm")
	}

	var r0 models.CreditCheckForm
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, models.CreditCheckForm) (models.CreditCheckForm, error)); ok {
		return rf(ctx, creditCheckForm)
	}
	if rf, ok := ret.Get(0).(func(context.Context, models.CreditCheckForm) models.CreditCheckForm); ok {
		r0 = rf(ctx, creditCheckForm)
	} else {
		r0 = ret.Get(0).(models.CreditCheckForm)
	}

	if rf, ok := ret.Get(1).(func(context.Context, models.CreditCheckForm) error); ok {
		r1 = rf(ctx, creditCheckForm)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// AddEquipment provides a mock function with given fields: ctx, equipment
func (_m *Database) AddEquipment(ctx context.Context, equipment models.Equipment) (models.Equipment, error) {
	ret := _m.Called(ctx, equipment)

	if len(ret) == 0 {
		panic("no return value specified for AddEquipment")
	}

	var r0 models.Equipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Equipment) (models.Equipment, error)); ok {
		return rf(ctx, equipment)
	}
	if rf, ok := ret.Get(0).(func(context.Context, models.Equipment) models.Equipment); ok {
		r0 = rf(ctx, equipment)
	} else {
		r0 = ret.Get(0).(models.Equipment)
	}

	if rf, ok := ret.Get(1).(func(context.Context, models.Equipment) error); ok {
		r1 = rf(ctx, equipment)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// AddEquipper provides a mock function with given fields: ctx, equipper
func (_m *Database) AddEquipper(ctx context.Context, equipper models.Equipper) error {
	ret := _m.Called(ctx, equipper)

	if len(ret) == 0 {
		panic("no return value specified for AddEquipper")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Equipper) error); ok {
		r0 = rf(ctx, equipper)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// AddLead provides a mock function with given fields: ctx, lead
func (_m *Database) AddLead(ctx context.Context, lead models.Lead) error {
	ret := _m.Called(ctx, lead)

	if len(ret) == 0 {
		panic("no return value specified for AddLead")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Lead) error); ok {
		r0 = rf(ctx, lead)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// AddLodger provides a mock function with given fields: ctx, lodger
func (_m *Database) AddLodger(ctx context.Context, lodger models.Lodger) error {
	ret := _m.Called(ctx, lodger)

	if len(ret) == 0 {
		panic("no return value specified for AddLodger")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Lodger) error); ok {
		r0 = rf(ctx, lodger)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// AddMember provides a mock function with given fields: ctx, lodgerID, member
func (_m *Database) AddMember(ctx context.Context, lodgerID string, member models.Member) (models.Member, error) {
	ret := _m.Called(ctx, lodgerID, member)

	if len(ret) == 0 {
		panic("no return value specified for AddMember")
	}

	var r0 models.Member
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, models.Member) (models.Member, error)); ok {
		return rf(ctx, lodgerID, member)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, models.Member) models.Member); ok {
		r0 = rf(ctx, lodgerID, member)
	} else {
		r0 = ret.Get(0).(models.Member)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, models.Member) error); ok {
		r1 = rf(ctx, lodgerID, member)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// AddOfferRequest provides a mock function with given fields: ctx, equipperID, bidsOffer
func (_m *Database) AddOfferRequest(ctx context.Context, equipperID string, bidsOffer models.BidzOffer) error {
	ret := _m.Called(ctx, equipperID, bidsOffer)

	if len(ret) == 0 {
		panic("no return value specified for AddOfferRequest")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, models.BidzOffer) error); ok {
		r0 = rf(ctx, equipperID, bidsOffer)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// AddOrder provides a mock function with given fields: ctx, order
func (_m *Database) AddOrder(ctx context.Context, order models.Order) error {
	ret := _m.Called(ctx, order)

	if len(ret) == 0 {
		panic("no return value specified for AddOrder")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Order) error); ok {
		r0 = rf(ctx, order)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// AddPasswordReset provides a mock function with given fields: ctx, passwordReset
func (_m *Database) AddPasswordReset(ctx context.Context, passwordReset models.PasswordReset) error {
	ret := _m.Called(ctx, passwordReset)

	if len(ret) == 0 {
		panic("no return value specified for AddPasswordReset")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.PasswordReset) error); ok {
		r0 = rf(ctx, passwordReset)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// AddProject provides a mock function with given fields: ctx, project
func (_m *Database) AddProject(ctx context.Context, project models.Project) (string, error) {
	ret := _m.Called(ctx, project)

	if len(ret) == 0 {
		panic("no return value specified for AddProject")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Project) (string, error)); ok {
		return rf(ctx, project)
	}
	if rf, ok := ret.Get(0).(func(context.Context, models.Project) string); ok {
		r0 = rf(ctx, project)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, models.Project) error); ok {
		r1 = rf(ctx, project)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// AddToolerBidzEquipment provides a mock function with given fields: ctx, toolerBidzEquipmentInventory
func (_m *Database) AddToolerBidzEquipment(ctx context.Context, toolerBidzEquipmentInventory models.ToolerBidzEquipment) error {
	ret := _m.Called(ctx, toolerBidzEquipmentInventory)

	if len(ret) == 0 {
		panic("no return value specified for AddToolerBidzEquipment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.ToolerBidzEquipment) error); ok {
		r0 = rf(ctx, toolerBidzEquipmentInventory)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BatchAddEquipment provides a mock function with given fields: ctx, equipments
func (_m *Database) BatchAddEquipment(ctx context.Context, equipments []models.Equipment) error {
	ret := _m.Called(ctx, equipments)

	if len(ret) == 0 {
		panic("no return value specified for BatchAddEquipment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []models.Equipment) error); ok {
		r0 = rf(ctx, equipments)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BatchUpdateBookingRequests provides a mock function with given fields: ctx, bookingRequests
func (_m *Database) BatchUpdateBookingRequests(ctx context.Context, bookingRequests []models.BookEquipment) error {
	ret := _m.Called(ctx, bookingRequests)

	if len(ret) == 0 {
		panic("no return value specified for BatchUpdateBookingRequests")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []models.BookEquipment) error); ok {
		r0 = rf(ctx, bookingRequests)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BatchUpdateEquipment provides a mock function with given fields: ctx, equipments
func (_m *Database) BatchUpdateEquipment(ctx context.Context, equipments []models.Equipment) error {
	ret := _m.Called(ctx, equipments)

	if len(ret) == 0 {
		panic("no return value specified for BatchUpdateEquipment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []models.Equipment) error); ok {
		r0 = rf(ctx, equipments)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BatchUpdateProjects provides a mock function with given fields: ctx, projects
func (_m *Database) BatchUpdateProjects(ctx context.Context, projects []models.Project) error {
	ret := _m.Called(ctx, projects)

	if len(ret) == 0 {
		panic("no return value specified for BatchUpdateProjects")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []models.Project) error); ok {
		r0 = rf(ctx, projects)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ChangeEquipmentStatus provides a mock function with given fields: ctx, equipmentID, status
func (_m *Database) ChangeEquipmentStatus(ctx context.Context, equipmentID string, status models.EquipmentStatus) error {
	ret := _m.Called(ctx, equipmentID, status)

	if len(ret) == 0 {
		panic("no return value specified for ChangeEquipmentStatus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, models.EquipmentStatus) error); ok {
		r0 = rf(ctx, equipmentID, status)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CheckEquipperByUserName provides a mock function with given fields: ctx, userName
func (_m *Database) CheckEquipperByUserName(ctx context.Context, userName string) (bool, error) {
	ret := _m.Called(ctx, userName)

	if len(ret) == 0 {
		panic("no return value specified for CheckEquipperByUserName")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return rf(ctx, userName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = rf(ctx, userName)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, userName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreatePromotionCode provides a mock function with given fields: ctx, promo
func (_m *Database) CreatePromotionCode(ctx context.Context, promo models.Promotion) error {
	ret := _m.Called(ctx, promo)

	if len(ret) == 0 {
		panic("no return value specified for CreatePromotionCode")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Promotion) error); ok {
		r0 = rf(ctx, promo)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteBookEquipment provides a mock function with given fields: ctx, id
func (_m *Database) DeleteBookEquipment(ctx context.Context, id string) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteBookEquipment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteCreditCheckForm provides a mock function with given fields: ctx, id
func (_m *Database) DeleteCreditCheckForm(ctx context.Context, id string) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteCreditCheckForm")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteEquipment provides a mock function with given fields: ctx, id
func (_m *Database) DeleteEquipment(ctx context.Context, id string) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteEquipment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteEquipmentsByEquipperID provides a mock function with given fields: ctx, equipperID
func (_m *Database) DeleteEquipmentsByEquipperID(ctx context.Context, equipperID string) error {
	ret := _m.Called(ctx, equipperID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteEquipmentsByEquipperID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, equipperID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteEquipper provides a mock function with given fields: ctx, id
func (_m *Database) DeleteEquipper(ctx context.Context, id string) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteEquipper")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteLodger provides a mock function with given fields: ctx, id
func (_m *Database) DeleteLodger(ctx context.Context, id string) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteLodger")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteMember provides a mock function with given fields: ctx, lodgerID, memberID
func (_m *Database) DeleteMember(ctx context.Context, lodgerID string, memberID ...string) error {
	_va := make([]interface{}, len(memberID))
	for _i := range memberID {
		_va[_i] = memberID[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, lodgerID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DeleteMember")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, ...string) error); ok {
		r0 = rf(ctx, lodgerID, memberID...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeletePasswordReset provides a mock function with given fields: ctx, id
func (_m *Database) DeletePasswordReset(ctx context.Context, id string) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeletePasswordReset")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteProject provides a mock function with given fields: ctx, id
func (_m *Database) DeleteProject(ctx context.Context, id string) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteProject")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeletePromotionCode provides a mock function with given fields: ctx, equipperID, code
func (_m *Database) DeletePromotionCode(ctx context.Context, equipperID string, code string) error {
	ret := _m.Called(ctx, equipperID, code)

	if len(ret) == 0 {
		panic("no return value specified for DeletePromotionCode")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, equipperID, code)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteToolerBidzEquipment provides a mock function with given fields: ctx, id
func (_m *Database) DeleteToolerBidzEquipment(ctx context.Context, id string) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteToolerBidzEquipment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DropToolerBidzEquipments provides a mock function with given fields: ctx
func (_m *Database) DropToolerBidzEquipments(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for DropToolerBidzEquipments")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetAcceptedBookEquipmentByEquipmentID provides a mock function with given fields: ctx, equipmentID
func (_m *Database) GetAcceptedBookEquipmentByEquipmentID(ctx context.Context, equipmentID string) (models.BookEquipment, error) {
	ret := _m.Called(ctx, equipmentID)

	if len(ret) == 0 {
		panic("no return value specified for GetAcceptedBookEquipmentByEquipmentID")
	}

	var r0 models.BookEquipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.BookEquipment, error)); ok {
		return rf(ctx, equipmentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.BookEquipment); ok {
		r0 = rf(ctx, equipmentID)
	} else {
		r0 = ret.Get(0).(models.BookEquipment)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, equipmentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAdminsByOwnerID provides a mock function with given fields: ctx, ownerID
func (_m *Database) GetAdminsByOwnerID(ctx context.Context, ownerID string) ([]models.Member, error) {
	ret := _m.Called(ctx, ownerID)

	if len(ret) == 0 {
		panic("no return value specified for GetAdminsByOwnerID")
	}

	var r0 []models.Member
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.Member, error)); ok {
		return rf(ctx, ownerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.Member); ok {
		r0 = rf(ctx, ownerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Member)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, ownerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllAdminsBidsOffer provides a mock function with given fields: ctx, adminID, limit, lastID, status
func (_m *Database) GetAllAdminsBidsOffer(ctx context.Context, adminID string, limit int, lastID string, status models.BidsOfferStatus) ([]models.BidzOffer, error) {
	ret := _m.Called(ctx, adminID, limit, lastID, status)

	if len(ret) == 0 {
		panic("no return value specified for GetAllAdminsBidsOffer")
	}

	var r0 []models.BidzOffer
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int, string, models.BidsOfferStatus) ([]models.BidzOffer, error)); ok {
		return rf(ctx, adminID, limit, lastID, status)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int, string, models.BidsOfferStatus) []models.BidzOffer); ok {
		r0 = rf(ctx, adminID, limit, lastID, status)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BidzOffer)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int, string, models.BidsOfferStatus) error); ok {
		r1 = rf(ctx, adminID, limit, lastID, status)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllAdminsBidsRequest provides a mock function with given fields: ctx, adminID, limit, lastID, status
func (_m *Database) GetAllAdminsBidsRequest(ctx context.Context, adminID string, limit int, lastID string, status models.BidsRequestStatus) ([]models.BidsRequest, error) {
	ret := _m.Called(ctx, adminID, limit, lastID, status)

	if len(ret) == 0 {
		panic("no return value specified for GetAllAdminsBidsRequest")
	}

	var r0 []models.BidsRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int, string, models.BidsRequestStatus) ([]models.BidsRequest, error)); ok {
		return rf(ctx, adminID, limit, lastID, status)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int, string, models.BidsRequestStatus) []models.BidsRequest); ok {
		r0 = rf(ctx, adminID, limit, lastID, status)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BidsRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int, string, models.BidsRequestStatus) error); ok {
		r1 = rf(ctx, adminID, limit, lastID, status)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllBidsLodgerOffer provides a mock function with given fields: ctx, limit, lastID, status, lodgerID
func (_m *Database) GetAllBidsLodgerOffer(ctx context.Context, limit int, lastID string, status models.BidsOfferStatus, lodgerID string) ([]models.BidzOffer, error) {
	ret := _m.Called(ctx, limit, lastID, status, lodgerID)

	if len(ret) == 0 {
		panic("no return value specified for GetAllBidsLodgerOffer")
	}

	var r0 []models.BidzOffer
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int, string, models.BidsOfferStatus, string) ([]models.BidzOffer, error)); ok {
		return rf(ctx, limit, lastID, status, lodgerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, string, models.BidsOfferStatus, string) []models.BidzOffer); ok {
		r0 = rf(ctx, limit, lastID, status, lodgerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BidzOffer)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, string, models.BidsOfferStatus, string) error); ok {
		r1 = rf(ctx, limit, lastID, status, lodgerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllBidsOffer provides a mock function with given fields: ctx, limit, lastID, status, where
func (_m *Database) GetAllBidsOffer(ctx context.Context, limit int, lastID string, status models.BidsOfferStatus, where map[string]interface{}) ([]models.BidzOffer, error) {
	ret := _m.Called(ctx, limit, lastID, status, where)

	if len(ret) == 0 {
		panic("no return value specified for GetAllBidsOffer")
	}

	var r0 []models.BidzOffer
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int, string, models.BidsOfferStatus, map[string]interface{}) ([]models.BidzOffer, error)); ok {
		return rf(ctx, limit, lastID, status, where)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, string, models.BidsOfferStatus, map[string]interface{}) []models.BidzOffer); ok {
		r0 = rf(ctx, limit, lastID, status, where)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BidzOffer)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, string, models.BidsOfferStatus, map[string]interface{}) error); ok {
		r1 = rf(ctx, limit, lastID, status, where)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllBidsRequest provides a mock function with given fields: ctx, limit, lastID, status, equipperID
func (_m *Database) GetAllBidsRequest(ctx context.Context, limit int, lastID string, status models.BidsRequestStatus, equipperID string) ([]models.BidsRequest, error) {
	ret := _m.Called(ctx, limit, lastID, status, equipperID)

	if len(ret) == 0 {
		panic("no return value specified for GetAllBidsRequest")
	}

	var r0 []models.BidsRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int, string, models.BidsRequestStatus, string) ([]models.BidsRequest, error)); ok {
		return rf(ctx, limit, lastID, status, equipperID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, string, models.BidsRequestStatus, string) []models.BidsRequest); ok {
		r0 = rf(ctx, limit, lastID, status, equipperID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BidsRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, string, models.BidsRequestStatus, string) error); ok {
		r1 = rf(ctx, limit, lastID, status, equipperID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllBookedEquipmentsByEquipperID provides a mock function with given fields: ctx, equipperID
func (_m *Database) GetAllBookedEquipmentsByEquipperID(ctx context.Context, equipperID string) ([]models.Equipment, error) {
	ret := _m.Called(ctx, equipperID)

	if len(ret) == 0 {
		panic("no return value specified for GetAllBookedEquipmentsByEquipperID")
	}

	var r0 []models.Equipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.Equipment, error)); ok {
		return rf(ctx, equipperID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.Equipment); ok {
		r0 = rf(ctx, equipperID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Equipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, equipperID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllEquipmentsByEquipperID provides a mock function with given fields: ctx, equipperID
func (_m *Database) GetAllEquipmentsByEquipperID(ctx context.Context, equipperID string) ([]models.Equipment, error) {
	ret := _m.Called(ctx, equipperID)

	if len(ret) == 0 {
		panic("no return value specified for GetAllEquipmentsByEquipperID")
	}

	var r0 []models.Equipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.Equipment, error)); ok {
		return rf(ctx, equipperID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.Equipment); ok {
		r0 = rf(ctx, equipperID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Equipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, equipperID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllEquipper provides a mock function with given fields: ctx
func (_m *Database) GetAllEquipper(ctx context.Context) ([]models.Equipper, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllEquipper")
	}

	var r0 []models.Equipper
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]models.Equipper, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []models.Equipper); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Equipper)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllEquippersByCategory provides a mock function with given fields: ctx, category
func (_m *Database) GetAllEquippersByCategory(ctx context.Context, category []string) ([]models.Equipper, error) {
	ret := _m.Called(ctx, category)

	if len(ret) == 0 {
		panic("no return value specified for GetAllEquippersByCategory")
	}

	var r0 []models.Equipper
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) ([]models.Equipper, error)); ok {
		return rf(ctx, category)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) []models.Equipper); ok {
		r0 = rf(ctx, category)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Equipper)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, category)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllEquippersByCategoryCoverageArea provides a mock function with given fields: ctx, category, CoverageArea
func (_m *Database) GetAllEquippersByCategoryCoverageArea(ctx context.Context, category []string, CoverageArea []string) ([]models.Equipper, error) {
	ret := _m.Called(ctx, category, CoverageArea)

	if len(ret) == 0 {
		panic("no return value specified for GetAllEquippersByCategoryCoverageArea")
	}

	var r0 []models.Equipper
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string, []string) ([]models.Equipper, error)); ok {
		return rf(ctx, category, CoverageArea)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string, []string) []models.Equipper); ok {
		r0 = rf(ctx, category, CoverageArea)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Equipper)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string, []string) error); ok {
		r1 = rf(ctx, category, CoverageArea)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllLodgerBidsRequest provides a mock function with given fields: ctx, limit, lastID, status, lodgerID
func (_m *Database) GetAllLodgerBidsRequest(ctx context.Context, limit int, lastID string, status models.BidsRequestStatus, lodgerID string) ([]models.BidsRequest, error) {
	ret := _m.Called(ctx, limit, lastID, status, lodgerID)

	if len(ret) == 0 {
		panic("no return value specified for GetAllLodgerBidsRequest")
	}

	var r0 []models.BidsRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int, string, models.BidsRequestStatus, string) ([]models.BidsRequest, error)); ok {
		return rf(ctx, limit, lastID, status, lodgerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, string, models.BidsRequestStatus, string) []models.BidsRequest); ok {
		r0 = rf(ctx, limit, lastID, status, lodgerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BidsRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, string, models.BidsRequestStatus, string) error); ok {
		r1 = rf(ctx, limit, lastID, status, lodgerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllLodgers provides a mock function with given fields: ctx
func (_m *Database) GetAllLodgers(ctx context.Context) ([]models.Lodger, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllLodgers")
	}

	var r0 []models.Lodger
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]models.Lodger, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []models.Lodger); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Lodger)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllOwnerBidsOffer provides a mock function with given fields: ctx, ownerID, limit, lastID, status
func (_m *Database) GetAllOwnerBidsOffer(ctx context.Context, ownerID string, limit int, lastID string, status models.BidsOfferStatus) ([]models.BidzOffer, error) {
	ret := _m.Called(ctx, ownerID, limit, lastID, status)

	if len(ret) == 0 {
		panic("no return value specified for GetAllOwnerBidsOffer")
	}

	var r0 []models.BidzOffer
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int, string, models.BidsOfferStatus) ([]models.BidzOffer, error)); ok {
		return rf(ctx, ownerID, limit, lastID, status)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int, string, models.BidsOfferStatus) []models.BidzOffer); ok {
		r0 = rf(ctx, ownerID, limit, lastID, status)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BidzOffer)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int, string, models.BidsOfferStatus) error); ok {
		r1 = rf(ctx, ownerID, limit, lastID, status)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllOwnerBidsRequest provides a mock function with given fields: ctx, ownerID, limit, lastID, status
func (_m *Database) GetAllOwnerBidsRequest(ctx context.Context, ownerID string, limit int, lastID string, status models.BidsRequestStatus) ([]models.BidsRequest, error) {
	ret := _m.Called(ctx, ownerID, limit, lastID, status)

	if len(ret) == 0 {
		panic("no return value specified for GetAllOwnerBidsRequest")
	}

	var r0 []models.BidsRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int, string, models.BidsRequestStatus) ([]models.BidsRequest, error)); ok {
		return rf(ctx, ownerID, limit, lastID, status)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int, string, models.BidsRequestStatus) []models.BidsRequest); ok {
		r0 = rf(ctx, ownerID, limit, lastID, status)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BidsRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int, string, models.BidsRequestStatus) error); ok {
		r1 = rf(ctx, ownerID, limit, lastID, status)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllPromotionCodeByEquipperID provides a mock function with given fields: ctx, equipperID
func (_m *Database) GetAllPromotionCodeByEquipperID(ctx context.Context, equipperID string) ([]models.Promotion, error) {
	ret := _m.Called(ctx, equipperID)

	if len(ret) == 0 {
		panic("no return value specified for GetAllPromotionCodeByEquipperID")
	}

	var r0 []models.Promotion
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.Promotion, error)); ok {
		return rf(ctx, equipperID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.Promotion); ok {
		r0 = rf(ctx, equipperID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Promotion)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, equipperID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllToolerBidzEquipment provides a mock function with given fields: ctx
func (_m *Database) GetAllToolerBidzEquipment(ctx context.Context) ([]models.ToolerBidzEquipment, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllToolerBidzEquipment")
	}

	var r0 []models.ToolerBidzEquipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]models.ToolerBidzEquipment, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []models.ToolerBidzEquipment); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.ToolerBidzEquipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAvailableEquipmentsByEquipperIDAndAliasEN provides a mock function with given fields: ctx, equipperID, equipmentName
func (_m *Database) GetAvailableEquipmentsByEquipperIDAndAliasEN(ctx context.Context, equipperID string, equipmentName string) ([]models.Equipment, error) {
	ret := _m.Called(ctx, equipperID, equipmentName)

	if len(ret) == 0 {
		panic("no return value specified for GetAvailableEquipmentsByEquipperIDAndAliasEN")
	}

	var r0 []models.Equipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) ([]models.Equipment, error)); ok {
		return rf(ctx, equipperID, equipmentName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) []models.Equipment); ok {
		r0 = rf(ctx, equipperID, equipmentName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Equipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, equipperID, equipmentName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAvailableEquipmentsByEquipperIDAndAliasFR provides a mock function with given fields: ctx, equipperID, equipmentName
func (_m *Database) GetAvailableEquipmentsByEquipperIDAndAliasFR(ctx context.Context, equipperID string, equipmentName string) ([]models.Equipment, error) {
	ret := _m.Called(ctx, equipperID, equipmentName)

	if len(ret) == 0 {
		panic("no return value specified for GetAvailableEquipmentsByEquipperIDAndAliasFR")
	}

	var r0 []models.Equipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) ([]models.Equipment, error)); ok {
		return rf(ctx, equipperID, equipmentName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) []models.Equipment); ok {
		r0 = rf(ctx, equipperID, equipmentName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Equipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, equipperID, equipmentName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAvailableEquipmentsByEquipperIDAndEquipmentName provides a mock function with given fields: ctx, equipperID, equipmentName
func (_m *Database) GetAvailableEquipmentsByEquipperIDAndEquipmentName(ctx context.Context, equipperID string, equipmentName string) ([]models.Equipment, error) {
	ret := _m.Called(ctx, equipperID, equipmentName)

	if len(ret) == 0 {
		panic("no return value specified for GetAvailableEquipmentsByEquipperIDAndEquipmentName")
	}

	var r0 []models.Equipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) ([]models.Equipment, error)); ok {
		return rf(ctx, equipperID, equipmentName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) []models.Equipment); ok {
		r0 = rf(ctx, equipperID, equipmentName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Equipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, equipperID, equipmentName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBidsOfferByEquipmentID provides a mock function with given fields: ctx, equipmentID
func (_m *Database) GetBidsOfferByEquipmentID(ctx context.Context, equipmentID string) ([]models.BidzOffer, error) {
	ret := _m.Called(ctx, equipmentID)

	if len(ret) == 0 {
		panic("no return value specified for GetBidsOfferByEquipmentID")
	}

	var r0 []models.BidzOffer
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.BidzOffer, error)); ok {
		return rf(ctx, equipmentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.BidzOffer); ok {
		r0 = rf(ctx, equipmentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BidzOffer)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, equipmentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBidsOfferID provides a mock function with given fields: ctx, id
func (_m *Database) GetBidsOfferID(ctx context.Context, id string) (models.BidzOffer, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetBidsOfferID")
	}

	var r0 models.BidzOffer
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.BidzOffer, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.BidzOffer); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(models.BidzOffer)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBidsRequestByEquipmentID provides a mock function with given fields: ctx, equipmentID
func (_m *Database) GetBidsRequestByEquipmentID(ctx context.Context, equipmentID string) ([]models.BidsRequest, error) {
	ret := _m.Called(ctx, equipmentID)

	if len(ret) == 0 {
		panic("no return value specified for GetBidsRequestByEquipmentID")
	}

	var r0 []models.BidsRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.BidsRequest, error)); ok {
		return rf(ctx, equipmentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.BidsRequest); ok {
		r0 = rf(ctx, equipmentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BidsRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, equipmentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBidsRequestID provides a mock function with given fields: ctx, id
func (_m *Database) GetBidsRequestID(ctx context.Context, id string) (models.BidsRequest, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetBidsRequestID")
	}

	var r0 models.BidsRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.BidsRequest, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.BidsRequest); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(models.BidsRequest)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBookEquipmentByAdminIDs provides a mock function with given fields: ctx, adminID, status
func (_m *Database) GetBookEquipmentByAdminIDs(ctx context.Context, adminID string, status models.BookingStatus) ([]models.BookEquipment, error) {
	ret := _m.Called(ctx, adminID, status)

	if len(ret) == 0 {
		panic("no return value specified for GetBookEquipmentByAdminIDs")
	}

	var r0 []models.BookEquipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, models.BookingStatus) ([]models.BookEquipment, error)); ok {
		return rf(ctx, adminID, status)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, models.BookingStatus) []models.BookEquipment); ok {
		r0 = rf(ctx, adminID, status)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BookEquipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, models.BookingStatus) error); ok {
		r1 = rf(ctx, adminID, status)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBookEquipmentByEquipmentID provides a mock function with given fields: ctx, equipmentID
func (_m *Database) GetBookEquipmentByEquipmentID(ctx context.Context, equipmentID string) ([]models.BookEquipment, error) {
	ret := _m.Called(ctx, equipmentID)

	if len(ret) == 0 {
		panic("no return value specified for GetBookEquipmentByEquipmentID")
	}

	var r0 []models.BookEquipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.BookEquipment, error)); ok {
		return rf(ctx, equipmentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.BookEquipment); ok {
		r0 = rf(ctx, equipmentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BookEquipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, equipmentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBookEquipmentByEquipperID provides a mock function with given fields: ctx, equipperID
func (_m *Database) GetBookEquipmentByEquipperID(ctx context.Context, equipperID string) ([]models.BookEquipment, error) {
	ret := _m.Called(ctx, equipperID)

	if len(ret) == 0 {
		panic("no return value specified for GetBookEquipmentByEquipperID")
	}

	var r0 []models.BookEquipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.BookEquipment, error)); ok {
		return rf(ctx, equipperID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.BookEquipment); ok {
		r0 = rf(ctx, equipperID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BookEquipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, equipperID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBookEquipmentByID provides a mock function with given fields: ctx, id
func (_m *Database) GetBookEquipmentByID(ctx context.Context, id string) (models.BookEquipment, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetBookEquipmentByID")
	}

	var r0 models.BookEquipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.BookEquipment, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.BookEquipment); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(models.BookEquipment)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBookEquipmentByLodgerID provides a mock function with given fields: ctx, lodgerID
func (_m *Database) GetBookEquipmentByLodgerID(ctx context.Context, lodgerID string) ([]models.BookEquipment, error) {
	ret := _m.Called(ctx, lodgerID)

	if len(ret) == 0 {
		panic("no return value specified for GetBookEquipmentByLodgerID")
	}

	var r0 []models.BookEquipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.BookEquipment, error)); ok {
		return rf(ctx, lodgerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.BookEquipment); ok {
		r0 = rf(ctx, lodgerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BookEquipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, lodgerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBookEquipmentByOwnerID provides a mock function with given fields: ctx, ownerID, status
func (_m *Database) GetBookEquipmentByOwnerID(ctx context.Context, ownerID string, status models.BookingStatus) ([]models.BookEquipment, error) {
	ret := _m.Called(ctx, ownerID, status)

	if len(ret) == 0 {
		panic("no return value specified for GetBookEquipmentByOwnerID")
	}

	var r0 []models.BookEquipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, models.BookingStatus) ([]models.BookEquipment, error)); ok {
		return rf(ctx, ownerID, status)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, models.BookingStatus) []models.BookEquipment); ok {
		r0 = rf(ctx, ownerID, status)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BookEquipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, models.BookingStatus) error); ok {
		r1 = rf(ctx, ownerID, status)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBookEquipmentByStatusAndEndDate provides a mock function with given fields: ctx
func (_m *Database) GetBookEquipmentByStatusAndEndDate(ctx context.Context) ([]models.BookEquipment, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetBookEquipmentByStatusAndEndDate")
	}

	var r0 []models.BookEquipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]models.BookEquipment, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []models.BookEquipment); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BookEquipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBookedByStatusAndEquipperID provides a mock function with given fields: ctx, equipperID, status, limit, lastID
func (_m *Database) GetBookedByStatusAndEquipperID(ctx context.Context, equipperID string, status models.BookingStatus, limit int, lastID string) ([]models.BookEquipment, error) {
	ret := _m.Called(ctx, equipperID, status, limit, lastID)

	if len(ret) == 0 {
		panic("no return value specified for GetBookedByStatusAndEquipperID")
	}

	var r0 []models.BookEquipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, models.BookingStatus, int, string) ([]models.BookEquipment, error)); ok {
		return rf(ctx, equipperID, status, limit, lastID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, models.BookingStatus, int, string) []models.BookEquipment); ok {
		r0 = rf(ctx, equipperID, status, limit, lastID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BookEquipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, models.BookingStatus, int, string) error); ok {
		r1 = rf(ctx, equipperID, status, limit, lastID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBookedEquipmentByStatusAndLodgerID provides a mock function with given fields: ctx, equipperID, status, limit, lastID
func (_m *Database) GetBookedEquipmentByStatusAndLodgerID(ctx context.Context, equipperID string, status models.BookingStatus, limit int, lastID string) ([]models.BookEquipment, error) {
	ret := _m.Called(ctx, equipperID, status, limit, lastID)

	if len(ret) == 0 {
		panic("no return value specified for GetBookedEquipmentByStatusAndLodgerID")
	}

	var r0 []models.BookEquipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, models.BookingStatus, int, string) ([]models.BookEquipment, error)); ok {
		return rf(ctx, equipperID, status, limit, lastID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, models.BookingStatus, int, string) []models.BookEquipment); ok {
		r0 = rf(ctx, equipperID, status, limit, lastID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BookEquipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, models.BookingStatus, int, string) error); ok {
		r1 = rf(ctx, equipperID, status, limit, lastID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBookedEquipmentsByEquipperID provides a mock function with given fields: ctx, equipperID, limit, lastID
func (_m *Database) GetBookedEquipmentsByEquipperID(ctx context.Context, equipperID string, limit int, lastID string) ([]models.Equipment, error) {
	ret := _m.Called(ctx, equipperID, limit, lastID)

	if len(ret) == 0 {
		panic("no return value specified for GetBookedEquipmentsByEquipperID")
	}

	var r0 []models.Equipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int, string) ([]models.Equipment, error)); ok {
		return rf(ctx, equipperID, limit, lastID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int, string) []models.Equipment); ok {
		r0 = rf(ctx, equipperID, limit, lastID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Equipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int, string) error); ok {
		r1 = rf(ctx, equipperID, limit, lastID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBookingRequestsByCCFID provides a mock function with given fields: ctx, ccfID
func (_m *Database) GetBookingRequestsByCCFID(ctx context.Context, ccfID string) ([]models.BookEquipment, error) {
	ret := _m.Called(ctx, ccfID)

	if len(ret) == 0 {
		panic("no return value specified for GetBookingRequestsByCCFID")
	}

	var r0 []models.BookEquipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.BookEquipment, error)); ok {
		return rf(ctx, ccfID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.BookEquipment); ok {
		r0 = rf(ctx, ccfID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.BookEquipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, ccfID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCreditCheckFormByID provides a mock function with given fields: ctx, id
func (_m *Database) GetCreditCheckFormByID(ctx context.Context, id string) (models.CreditCheckForm, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetCreditCheckFormByID")
	}

	var r0 models.CreditCheckForm
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.CreditCheckForm, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.CreditCheckForm); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(models.CreditCheckForm)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCreditCheckFormByLodgerID provides a mock function with given fields: ctx, lodgerID
func (_m *Database) GetCreditCheckFormByLodgerID(ctx context.Context, lodgerID string) ([]models.CreditCheckForm, error) {
	ret := _m.Called(ctx, lodgerID)

	if len(ret) == 0 {
		panic("no return value specified for GetCreditCheckFormByLodgerID")
	}

	var r0 []models.CreditCheckForm
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.CreditCheckForm, error)); ok {
		return rf(ctx, lodgerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.CreditCheckForm); ok {
		r0 = rf(ctx, lodgerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.CreditCheckForm)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, lodgerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetEquipmentByID provides a mock function with given fields: ctx, id
func (_m *Database) GetEquipmentByID(ctx context.Context, id string) (models.Equipment, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetEquipmentByID")
	}

	var r0 models.Equipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.Equipment, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.Equipment); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(models.Equipment)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetEquipmentByInternalID provides a mock function with given fields: ctx, id
func (_m *Database) GetEquipmentByInternalID(ctx context.Context, id string) (models.Equipment, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetEquipmentByInternalID")
	}

	var r0 models.Equipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.Equipment, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.Equipment); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(models.Equipment)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetEquipmentByName provides a mock function with given fields: ctx, name
func (_m *Database) GetEquipmentByName(ctx context.Context, name string) ([]models.Equipment, error) {
	ret := _m.Called(ctx, name)

	if len(ret) == 0 {
		panic("no return value specified for GetEquipmentByName")
	}

	var r0 []models.Equipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.Equipment, error)); ok {
		return rf(ctx, name)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.Equipment); ok {
		r0 = rf(ctx, name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Equipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetEquipmentInventoryByName provides a mock function with given fields: ctx, name
func (_m *Database) GetEquipmentInventoryByName(ctx context.Context, name string) (models.ToolerBidzEquipment, error) {
	ret := _m.Called(ctx, name)

	if len(ret) == 0 {
		panic("no return value specified for GetEquipmentInventoryByName")
	}

	var r0 models.ToolerBidzEquipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.ToolerBidzEquipment, error)); ok {
		return rf(ctx, name)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.ToolerBidzEquipment); ok {
		r0 = rf(ctx, name)
	} else {
		r0 = ret.Get(0).(models.ToolerBidzEquipment)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetEquipmentsByEquipperID provides a mock function with given fields: ctx, equipperID, limit, lastID
func (_m *Database) GetEquipmentsByEquipperID(ctx context.Context, equipperID string, limit int, lastID string) ([]models.Equipment, error) {
	ret := _m.Called(ctx, equipperID, limit, lastID)

	if len(ret) == 0 {
		panic("no return value specified for GetEquipmentsByEquipperID")
	}

	var r0 []models.Equipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int, string) ([]models.Equipment, error)); ok {
		return rf(ctx, equipperID, limit, lastID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int, string) []models.Equipment); ok {
		r0 = rf(ctx, equipperID, limit, lastID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Equipment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int, string) error); ok {
		r1 = rf(ctx, equipperID, limit, lastID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetEquipperByEmail provides a mock function with given fields: ctx, email
func (_m *Database) GetEquipperByEmail(ctx context.Context, email string) (models.Equipper, error) {
	ret := _m.Called(ctx, email)

	if len(ret) == 0 {
		panic("no return value specified for GetEquipperByEmail")
	}

	var r0 models.Equipper
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.Equipper, error)); ok {
		return rf(ctx, email)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.Equipper); ok {
		r0 = rf(ctx, email)
	} else {
		r0 = ret.Get(0).(models.Equipper)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetEquipperByID provides a mock function with given fields: ctx, id
func (_m *Database) GetEquipperByID(ctx context.Context, id string) (models.Equipper, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetEquipperByID")
	}

	var r0 models.Equipper
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.Equipper, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.Equipper); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(models.Equipper)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetEquipperByMemberID provides a mock function with given fields: ctx, memberID
func (_m *Database) GetEquipperByMemberID(ctx context.Context, memberID string) (models.Equipper, error) {
	ret := _m.Called(ctx, memberID)

	if len(ret) == 0 {
		panic("no return value specified for GetEquipperByMemberID")
	}

	var r0 models.Equipper
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.Equipper, error)); ok {
		return rf(ctx, memberID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.Equipper); ok {
		r0 = rf(ctx, memberID)
	} else {
		r0 = ret.Get(0).(models.Equipper)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, memberID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetEquipperByUserName provides a mock function with given fields: ctx, userName
func (_m *Database) GetEquipperByUserName(ctx context.Context, userName string) (models.Equipper, error) {
	ret := _m.Called(ctx, userName)

	if len(ret) == 0 {
		panic("no return value specified for GetEquipperByUserName")
	}

	var r0 models.Equipper
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.Equipper, error)); ok {
		return rf(ctx, userName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.Equipper); ok {
		r0 = rf(ctx, userName)
	} else {
		r0 = ret.Get(0).(models.Equipper)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, userName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetEquippersByCountry provides a mock function with given fields: ctx, countryCode
func (_m *Database) GetEquippersByCountry(ctx context.Context, countryCode string) ([]models.Equipper, error) {
	ret := _m.Called(ctx, countryCode)

	if len(ret) == 0 {
		panic("no return value specified for GetEquippersByCountry")
	}

	var r0 []models.Equipper
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.Equipper, error)); ok {
		return rf(ctx, countryCode)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.Equipper); ok {
		r0 = rf(ctx, countryCode)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Equipper)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, countryCode)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllLodgersByCountry fetch all the lodgers of the same country. provides a mock function with given fields: ctx, email
func (_m *Database) GetAllLodgersByCountry(ctx context.Context, country string) ([]models.Lodger, error) {
	ret := _m.Called(ctx, country)

	if len(ret) == 0 {
		panic("no return value specified for GetAllLodgersByCountry")
	}

	var r0 []models.Lodger
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.Lodger, error)); ok {
		return rf(ctx, country)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.Lodger); ok {
		r0 = rf(ctx, country)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Lodger)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, country)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLodgerByEmail provides a mock function with given fields: ctx, email
func (_m *Database) GetLodgerByEmail(ctx context.Context, email string) (models.Lodger, error) {
	ret := _m.Called(ctx, email)

	if len(ret) == 0 {
		panic("no return value specified for GetLodgerByEmail")
	}

	var r0 models.Lodger
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.Lodger, error)); ok {
		return rf(ctx, email)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.Lodger); ok {
		r0 = rf(ctx, email)
	} else {
		r0 = ret.Get(0).(models.Lodger)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLodgerByID provides a mock function with given fields: ctx, id
func (_m *Database) GetLodgerByID(ctx context.Context, id string) (models.Lodger, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetLodgerByID")
	}

	var r0 models.Lodger
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.Lodger, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.Lodger); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(models.Lodger)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLodgerByMemberID provides a mock function with given fields: ctx, memberID
func (_m *Database) GetLodgerByMemberID(ctx context.Context, memberID string) (models.Lodger, error) {
	ret := _m.Called(ctx, memberID)

	if len(ret) == 0 {
		panic("no return value specified for GetLodgerByMemberID")
	}

	var r0 models.Lodger
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.Lodger, error)); ok {
		return rf(ctx, memberID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.Lodger); ok {
		r0 = rf(ctx, memberID)
	} else {
		r0 = ret.Get(0).(models.Lodger)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, memberID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetMemberByID provides a mock function with given fields: ctx, memberID, lodgerID
func (_m *Database) GetMemberByID(ctx context.Context, memberID string, lodgerID string) (models.Member, error) {
	ret := _m.Called(ctx, memberID, lodgerID)

	if len(ret) == 0 {
		panic("no return value specified for GetMemberByID")
	}

	var r0 models.Member
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (models.Member, error)); ok {
		return rf(ctx, memberID, lodgerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) models.Member); ok {
		r0 = rf(ctx, memberID, lodgerID)
	} else {
		r0 = ret.Get(0).(models.Member)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, memberID, lodgerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetMembers provides a mock function with given fields: ctx, lodgerID
func (_m *Database) GetMembers(ctx context.Context, lodgerID string) (models.Members, error) {
	ret := _m.Called(ctx, lodgerID)

	if len(ret) == 0 {
		panic("no return value specified for GetMembers")
	}

	var r0 models.Members
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.Members, error)); ok {
		return rf(ctx, lodgerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.Members); ok {
		r0 = rf(ctx, lodgerID)
	} else {
		r0 = ret.Get(0).(models.Members)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, lodgerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetOrderByBookingID provides a mock function with given fields: ctx, id
func (_m *Database) GetOrderByBookingID(ctx context.Context, id string) (models.Order, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderByBookingID")
	}

	var r0 models.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.Order, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.Order); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(models.Order)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetOrderByID provides a mock function with given fields: ctx, id
func (_m *Database) GetOrderByID(ctx context.Context, id string) (models.Order, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderByID")
	}

	var r0 models.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.Order, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.Order); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(models.Order)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetOrderByPaymentIntentID provides a mock function with given fields: ctx, paymentIntentID
func (_m *Database) GetOrderByPaymentIntentID(ctx context.Context, paymentIntentID string) (models.Order, error) {
	ret := _m.Called(ctx, paymentIntentID)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderByPaymentIntentID")
	}

	var r0 models.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.Order, error)); ok {
		return rf(ctx, paymentIntentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.Order); ok {
		r0 = rf(ctx, paymentIntentID)
	} else {
		r0 = ret.Get(0).(models.Order)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, paymentIntentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPasswordReset provides a mock function with given fields: ctx, email
func (_m *Database) GetPasswordReset(ctx context.Context, email string) (models.PasswordReset, error) {
	ret := _m.Called(ctx, email)

	if len(ret) == 0 {
		panic("no return value specified for GetPasswordReset")
	}

	var r0 models.PasswordReset
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.PasswordReset, error)); ok {
		return rf(ctx, email)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.PasswordReset); ok {
		r0 = rf(ctx, email)
	} else {
		r0 = ret.Get(0).(models.PasswordReset)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProjectByCreditApplicationID provides a mock function with given fields: ctx, ccaID
func (_m *Database) GetProjectByCreditApplicationID(ctx context.Context, ccaID string) ([]models.Project, error) {
	ret := _m.Called(ctx, ccaID)

	if len(ret) == 0 {
		panic("no return value specified for GetProjectByCreditApplicationID")
	}

	var r0 []models.Project
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.Project, error)); ok {
		return rf(ctx, ccaID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.Project); ok {
		r0 = rf(ctx, ccaID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Project)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, ccaID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProjectByID provides a mock function with given fields: ctx, id
func (_m *Database) GetProjectByID(ctx context.Context, id string) (models.Project, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetProjectByID")
	}

	var r0 models.Project
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.Project, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.Project); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(models.Project)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProjectByLodgerID provides a mock function with given fields: ctx, lodgerID
func (_m *Database) GetProjectByLodgerID(ctx context.Context, lodgerID string) ([]models.Project, error) {
	ret := _m.Called(ctx, lodgerID)

	if len(ret) == 0 {
		panic("no return value specified for GetProjectByLodgerID")
	}

	var r0 []models.Project
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.Project, error)); ok {
		return rf(ctx, lodgerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.Project); ok {
		r0 = rf(ctx, lodgerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Project)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, lodgerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProjectByMemberID provides a mock function with given fields: ctx, memberID
func (_m *Database) GetProjectByMemberID(ctx context.Context, memberID string) ([]models.Project, error) {
	ret := _m.Called(ctx, memberID)

	if len(ret) == 0 {
		panic("no return value specified for GetProjectByMemberID")
	}

	var r0 []models.Project
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.Project, error)); ok {
		return rf(ctx, memberID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.Project); ok {
		r0 = rf(ctx, memberID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Project)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, memberID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProjectsByCCFID provides a mock function with given fields: ctx, ccfID
func (_m *Database) GetProjectsByCCFID(ctx context.Context, ccfID string) ([]models.Project, error) {
	ret := _m.Called(ctx, ccfID)

	if len(ret) == 0 {
		panic("no return value specified for GetProjectsByCCFID")
	}

	var r0 []models.Project
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.Project, error)); ok {
		return rf(ctx, ccfID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.Project); ok {
		r0 = rf(ctx, ccfID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Project)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, ccfID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProjectsByEquipmentID provides a mock function with given fields: ctx, equipmentID
func (_m *Database) GetProjectsByEquipmentID(ctx context.Context, equipmentID string) ([]models.Project, error) {
	ret := _m.Called(ctx, equipmentID)

	if len(ret) == 0 {
		panic("no return value specified for GetProjectsByEquipmentID")
	}

	var r0 []models.Project
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.Project, error)); ok {
		return rf(ctx, equipmentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.Project); ok {
		r0 = rf(ctx, equipmentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Project)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, equipmentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPromotionCodeByEquipperIDAndLodgerID provides a mock function with given fields: ctx, equipperID, lodgerID
func (_m *Database) GetPromotionCodeByEquipperIDAndLodgerID(ctx context.Context, equipperID string, lodgerID string) (models.Promotion, error) {
	ret := _m.Called(ctx, equipperID, lodgerID)

	if len(ret) == 0 {
		panic("no return value specified for GetPromotionCodeByEquipperIDAndLodgerID")
	}

	var r0 models.Promotion
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (models.Promotion, error)); ok {
		return rf(ctx, equipperID, lodgerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) models.Promotion); ok {
		r0 = rf(ctx, equipperID, lodgerID)
	} else {
		r0 = ret.Get(0).(models.Promotion)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, equipperID, lodgerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSelectionList provides a mock function with given fields: ctx, member
func (_m *Database) GetSelectionList(ctx context.Context, member models.Member) ([]models.Member, error) {
	ret := _m.Called(ctx, member)

	if len(ret) == 0 {
		panic("no return value specified for GetSelectionList")
	}

	var r0 []models.Member
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Member) ([]models.Member, error)); ok {
		return rf(ctx, member)
	}
	if rf, ok := ret.Get(0).(func(context.Context, models.Member) []models.Member); ok {
		r0 = rf(ctx, member)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Member)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, models.Member) error); ok {
		r1 = rf(ctx, member)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetToolerBidzEquipmentByID provides a mock function with given fields: ctx, id
func (_m *Database) GetToolerBidzEquipmentByID(ctx context.Context, id string) (models.ToolerBidzEquipment, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetToolerBidzEquipmentByID")
	}

	var r0 models.ToolerBidzEquipment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.ToolerBidzEquipment, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.ToolerBidzEquipment); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(models.ToolerBidzEquipment)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// InvitationExists provides a mock function with given fields: ctx, email, memberOf
func (_m *Database) InvitationExists(ctx context.Context, email string, memberOf string) (bool, error) {
	ret := _m.Called(ctx, email, memberOf)

	if len(ret) == 0 {
		panic("no return value specified for InvitationExists")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (bool, error)); ok {
		return rf(ctx, email, memberOf)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) bool); ok {
		r0 = rf(ctx, email, memberOf)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, email, memberOf)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PopulateProject provides a mock function with given fields: ctx, projectID
func (_m *Database) PopulateProject(ctx context.Context, projectID string) (models.PopulatedProject, error) {
	ret := _m.Called(ctx, projectID)

	if len(ret) == 0 {
		panic("no return value specified for PopulateProject")
	}

	var r0 models.PopulatedProject
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.PopulatedProject, error)); ok {
		return rf(ctx, projectID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.PopulatedProject); ok {
		r0 = rf(ctx, projectID)
	} else {
		r0 = ret.Get(0).(models.PopulatedProject)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, projectID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateBookEquipment provides a mock function with given fields: ctx, bookingRequest
func (_m *Database) UpdateBookEquipment(ctx context.Context, bookingRequest models.BookEquipment) error {
	ret := _m.Called(ctx, bookingRequest)

	if len(ret) == 0 {
		panic("no return value specified for UpdateBookEquipment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.BookEquipment) error); ok {
		r0 = rf(ctx, bookingRequest)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateCreditCheckForm provides a mock function with given fields: ctx, creditCheckForm
func (_m *Database) UpdateCreditCheckForm(ctx context.Context, creditCheckForm models.CreditCheckForm) error {
	ret := _m.Called(ctx, creditCheckForm)

	if len(ret) == 0 {
		panic("no return value specified for UpdateCreditCheckForm")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.CreditCheckForm) error); ok {
		r0 = rf(ctx, creditCheckForm)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateEquipment provides a mock function with given fields: ctx, equipment
func (_m *Database) UpdateEquipment(ctx context.Context, equipment models.Equipment) error {
	ret := _m.Called(ctx, equipment)

	if len(ret) == 0 {
		panic("no return value specified for UpdateEquipment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Equipment) error); ok {
		r0 = rf(ctx, equipment)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateEquipper provides a mock function with given fields: ctx, equipper
func (_m *Database) UpdateEquipper(ctx context.Context, equipper models.Equipper) error {
	ret := _m.Called(ctx, equipper)

	if len(ret) == 0 {
		panic("no return value specified for UpdateEquipper")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Equipper) error); ok {
		r0 = rf(ctx, equipper)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateLodger provides a mock function with given fields: ctx, lodger
func (_m *Database) UpdateLodger(ctx context.Context, lodger models.Lodger) error {
	ret := _m.Called(ctx, lodger)

	if len(ret) == 0 {
		panic("no return value specified for UpdateLodger")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Lodger) error); ok {
		r0 = rf(ctx, lodger)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateMember provides a mock function with given fields: ctx, lodgerID, member
func (_m *Database) UpdateMember(ctx context.Context, lodgerID string, member models.Member) error {
	ret := _m.Called(ctx, lodgerID, member)

	if len(ret) == 0 {
		panic("no return value specified for UpdateMember")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, models.Member) error); ok {
		r0 = rf(ctx, lodgerID, member)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateOffer provides a mock function with given fields: ctx, bidsOffer
func (_m *Database) UpdateOffer(ctx context.Context, bidsOffer models.BidzOffer) error {
	ret := _m.Called(ctx, bidsOffer)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOffer")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.BidzOffer) error); ok {
		r0 = rf(ctx, bidsOffer)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateOrder provides a mock function with given fields: ctx, order
func (_m *Database) UpdateOrder(ctx context.Context, order models.Order) error {
	ret := _m.Called(ctx, order)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOrder")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Order) error); ok {
		r0 = rf(ctx, order)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateProject provides a mock function with given fields: ctx, project
func (_m *Database) UpdateProject(ctx context.Context, project models.Project) error {
	ret := _m.Called(ctx, project)

	if len(ret) == 0 {
		panic("no return value specified for UpdateProject")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Project) error); ok {
		r0 = rf(ctx, project)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateRequest provides a mock function with given fields: ctx, bidsRequest
func (_m *Database) UpdateRequest(ctx context.Context, bidsRequest models.BidsRequest) error {
	ret := _m.Called(ctx, bidsRequest)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRequest")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.BidsRequest) error); ok {
		r0 = rf(ctx, bidsRequest)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateToolerBidzEquipment provides a mock function with given fields: ctx, toolerBidzEquipmentInventory
func (_m *Database) UpdateToolerBidzEquipment(ctx context.Context, toolerBidzEquipmentInventory models.ToolerBidzEquipment) error {
	ret := _m.Called(ctx, toolerBidzEquipmentInventory)

	if len(ret) == 0 {
		panic("no return value specified for UpdateToolerBidzEquipment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.ToolerBidzEquipment) error); ok {
		r0 = rf(ctx, toolerBidzEquipmentInventory)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewDatabase creates a new instance of Database. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDatabase(t interface {
	mock.TestingT
	Cleanup(func())
}) *Database {
	mock := &Database{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
