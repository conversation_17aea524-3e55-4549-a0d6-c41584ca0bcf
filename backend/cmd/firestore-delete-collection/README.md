# Firestore Delete Collection Tool

This tool allows you to delete equipment documents from Firestore, either for a specific equipper or all equipment documents in the collection.

## Prerequisites

- Go 1.18 or higher
- Access to the Derental Firebase project
- Proper authentication credentials set up (service account key or gcloud authentication)

## Environment Configuration

The tool uses environment variables to determine which Google Cloud project to connect to. These variables are loaded from the `.env` file in the project directory.

### Important Environment Variables

- `GOOGLE_CLOUD_PROJECT`: Specifies the Google Cloud project ID (e.g., `dev-derental`, `demos-derental`, `prod-derental`)
- `MODE`: Execution mode (`dev` or `production`)
- `FIREBASE_STORAGE_BUCKET`: Firebase storage bucket name

The default `.env` file is configured for the development environment:

```
GOOGLE_CLOUD_PROJECT=dev-derental
MODE=dev
FIREBASE_STORAGE_BUCKET=dev-derental.appspot.com
```

To use a different environment, you can either:
1. Modify the `.env` file directly
2. Uncomment the appropriate environment section in the `.env` file
3. Set environment variables directly when running the command

## Usage

### Run the tool directly

You can run the script directly without building it:

#### Delete equipment for a specific equipper

```bash
cd backend/cmd/firestore-delete-collection
go run main.go -equipper=<equipper_id>
```

Replace `<equipper_id>` with the ID of the equipper whose equipment you want to delete.

#### Delete all equipment

```bash
cd backend/cmd/firestore-delete-collection
go run main.go
```

This will prompt for confirmation before deleting all equipment documents.

### Build and run (optional)

If you prefer to build the tool first:

```bash
cd backend/cmd/firestore-delete-collection
go build
./firestore-delete-collection [options]
```

## Environment Selection

You can override the environment by setting the `GOOGLE_CLOUD_PROJECT` environment variable:

```bash
# For development (default from .env)
in .env file GOOGLE_CLOUD_PROJECT=dev-derental 

# For production
in .env file GOOGLE_CLOUD_PROJECT=prod-derental 

# For demo environment
in .env file GOOGLE_CLOUD_PROJECT=demos-derental 

# For pre-production
in .env file GOOGLE_CLOUD_PROJECT=preprod-derental 
```

## Safety Features

- When running against the production environment (`prod-derental`), an additional warning and confirmation prompt will appear
- When deleting all equipment (without specifying an equipper ID), you must type 'DELETE ALL' to confirm
- The script logs all deleted document IDs and provides a count of total deleted documents

## Examples

### Delete equipment for a specific equipper in development

```bash
cd backend/cmd/firestore-delete-collection
go run main.go -equipper=equipper123
```


## Caution

⚠️ **WARNING**: This tool permanently deletes data from Firestore. Use with extreme caution, especially in production environments.

- Always double-check the environment you're running against by verifying the `GOOGLE_CLOUD_PROJECT` value
- Consider making a backup before deleting large amounts of data
- For production, it's recommended to have another team member verify the operation before proceeding