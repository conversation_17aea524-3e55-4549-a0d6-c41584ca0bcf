package main

import (
	"bufio"
	"context"
	"errors"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"

	"cloud.google.com/go/firestore"
	firebase "firebase.google.com/go"
	"google.golang.org/api/iterator"

	"github.com/vima-inc/derental/config"
)

func main() {
	// Define command line flags
	equipperID := flag.String("equipper", "", "Equipper ID to delete equipments for (optional)")
	flag.Parse()

	c, err := config.New()
	if err != nil {
		log.Fatalf("unable to init config %+v", err)
	}

	conf := &firebase.Config{ProjectID: c.ProjectID}

	ctx := context.Background()

	app, err := firebase.NewApp(ctx, conf)
	if err != nil {
		log.Fatalf("unable to create firebase app: %v", err)
	}

	client, err := app.Firestore(ctx)
	if err != nil {
		log.Fatalf("unable to create firestore client: %v", err)
	}
	log.Printf("Cloud Project: %s", c.ProjectID)

	// Add warning for production environment
	if c.ProjectID == "prod-derental" {
		fmt.Println("⚠️ WARNING: You are running this script on PRODUCTION environment!")
		fmt.Println("⚠️ This will permanently delete data from the production database.")
		fmt.Print("Are you sure you want to continue? (y/N): ")

		var response string
		if _, err := fmt.Scanln(&response); err != nil {
			log.Fatalf("failed to read input: %v", err)
		}

		if strings.ToLower(response) != "y" {
			fmt.Println("Operation cancelled.")
			return
		}

		fmt.Println("Proceeding with deletion on PRODUCTION...")
	}

	// Create query based on whether equipper ID is provided
	var refs *firestore.DocumentRefIterator
	if *equipperID != "" {
		// Get equipments for specific equipper
		query := client.Collection("equipments").Where("equipper_id", "==", *equipperID)
		docSnaps, err := query.Documents(ctx).GetAll()
		if err != nil {
			log.Fatalf("unable to query equipments: %v", err)
		}

		if len(docSnaps) == 0 {
			log.Printf("No equipments found for equipper ID: %s", *equipperID)
			return
		}

		log.Printf("Found %d equipments for equipper ID: %s", len(docSnaps), *equipperID)

		// Delete each document
		total := 0
		for _, doc := range docSnaps {
			_, err = client.Collection("equipments").Doc(doc.Ref.ID).Delete(ctx)
			if err != nil {
				log.Printf("unable to delete equipment: %v", err)
				continue
			}

			fmt.Printf("deleted equipment: %s\n", doc.Ref.ID)
			total++
		}

		fmt.Printf("total deleted: %d\n", total)
		return
	}

	// If no equipper ID provided, prompt for confirmation before deleting all equipments
	fmt.Printf("\n⚠️  WARNING: You are about to delete ALL equipments in project %s ⚠️\n", c.ProjectID)
	fmt.Printf("This action cannot be undone. Please type 'DELETE ALL' to confirm: ")

	reader := bufio.NewReader(os.Stdin)
	input, _ := reader.ReadString('\n')
	input = strings.TrimSpace(input)

	if input != "DELETE ALL" {
		fmt.Println("Operation cancelled.")
		return
	}

	fmt.Println("Proceeding with deletion of ALL equipments...")

	// If confirmed, delete all equipments
	refs = client.Collection("equipments").DocumentRefs(ctx)

	total := 0
	for {
		ref, err := refs.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			log.Printf("unable to get next ref: %v", err)
			continue
		}

		go func(docRef *firestore.DocumentRef) {
			_, err = docRef.Delete(ctx)
			if err != nil {
				log.Printf("unable to delete equipment: %v", err)
				return
			}

			fmt.Printf("deleted equipment: %s\n", docRef.ID)
		}(ref)

		total++
	}

	fmt.Printf("total deleted: %d\n", total)
}
