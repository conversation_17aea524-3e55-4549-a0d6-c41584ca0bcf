package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"cloud.google.com/go/firestore"
	"github.com/joho/godotenv"
	"github.com/vima-inc/derental/config"
	"github.com/vima-inc/derental/external/openrouter"
	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

func main() {
	// Load .env file from the same directory as this executable
	execPath, err := os.Executable()
	if err != nil {
		log.Printf("Warning: Could not determine executable path: %v", err)
	} else {
		envPath := filepath.Join(filepath.Dir(execPath), ".env")
		if _, err := os.Stat(envPath); err == nil {
			if err := godotenv.Load(envPath); err != nil {
				log.Printf("Warning: Could not load .env file from %s: %v", envPath, err)
			} else {
				log.Printf("Loaded .env file from: %s", envPath)
			}
		}
	}

	// Also try to load .env from current directory
	if _, err := os.Stat(".env"); err == nil {
		if err := godotenv.Load(".env"); err != nil {
			log.Printf("Warning: Could not load .env file from current directory: %v", err)
		} else {
			log.Printf("Loaded .env file from current directory")
		}
	}

	// Command line flags
	var (
		all             = flag.Bool("all", false, "Classify all unclassified equipment")
		equipmentIDs    = flag.String("equipment-ids", "", "Comma-separated list of equipment IDs to classify")
		batchSize       = flag.Int("batch-size", 0, "Batch size for classification (0 = use config default)")
		model           = flag.String("model", "", "OpenRouter model to use (empty = use config default)")
		temperature     = flag.Float64("temperature", -1, "Temperature for AI model (-1 = use config default)")
		dryRun          = flag.Bool("dry-run", false, "Estimate cost without actually classifying")
		useCache        = flag.Bool("use-cache", true, "Use caching for classification results")
		maxRetries      = flag.Int("max-retries", 3, "Maximum number of retries for failed requests")
		retryDelay      = flag.Int("retry-delay", 2, "Delay between retries in seconds")
		forceReclassify = flag.Bool("force-reclassify", false, "Force reclassification of already classified equipment")
		stats           = flag.Bool("stats", false, "Show equipment and taxonomy statistics")
		help            = flag.Bool("help", false, "Show help message")
	)
	flag.Parse()

	if *help {
		showHelp()
		return
	}

	// Load configuration
	cfg, err := config.New()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Validate OpenRouter API key
	if cfg.OpenRouterAPIKey == "" {
		log.Fatal("OPENROUTER_API_KEY environment variable is required")
	}

	// Initialize Firestore client
	ctx := context.Background()
	firestoreClient, err := firestore.NewClient(ctx, cfg.ProjectID)
	if err != nil {
		log.Fatalf("Failed to create Firestore client: %v", err)
	}
	defer firestoreClient.Close()

	// Initialize services
	openRouterClient := openrouter.NewClient(cfg.OpenRouterAPIKey)
	araTaxonomyService := service.NewARATaxonomyService(firestoreClient)
	equipmentDataService := service.NewEquipmentDataService(firestoreClient)

	// Build classification config
	classificationConfig := buildClassificationConfig(cfg, *batchSize, *model, *temperature, *dryRun, *useCache, *forceReclassify, *maxRetries, *retryDelay)

	// Show configuration being used
	log.Printf("Configuration loaded:")
	log.Printf("  OpenRouter API Key: %s", maskAPIKey(cfg.OpenRouterAPIKey))
	log.Printf("  Batch Size: %d", classificationConfig.BatchSize)
	log.Printf("  Model: %s", classificationConfig.Model)
	log.Printf("  Temperature: %.1f", classificationConfig.Temperature)
	log.Printf("  Dry Run: %t", classificationConfig.DryRun)
	log.Printf("  Use Cache: %t", classificationConfig.UseCache)

	araClassificationService := service.NewARAClassificationService(
		openRouterClient,
		araTaxonomyService,
		equipmentDataService,
		classificationConfig,
	)

	// Handle different commands
	switch {
	case *stats:
		showStats(ctx, araTaxonomyService, equipmentDataService)
	case *all:
		classifyAllEquipment(ctx, araClassificationService)
	case *equipmentIDs != "":
		ids := parseEquipmentIDs(*equipmentIDs)
		classifySpecificEquipment(ctx, araClassificationService, ids)
	default:
		fmt.Println("No action specified. Use --help for usage information.")
		os.Exit(1)
	}
}

func buildClassificationConfig(cfg *config.Config, batchSize int, model string, temperature float64, dryRun, useCache, forceReclassify bool, maxRetries, retryDelay int) models.ARAClassificationConfig {
	config := models.DefaultARAClassificationConfig()

	// Override with config values
	if cfg.ARAClassificationBatchSize > 0 {
		config.BatchSize = cfg.ARAClassificationBatchSize
	}
	if cfg.ARAClassificationModel != "" {
		config.Model = cfg.ARAClassificationModel
	}
	if cfg.ARAClassificationTemperature > 0 {
		config.Temperature = cfg.ARAClassificationTemperature
	}

	// Override with command line flags
	if batchSize > 0 {
		config.BatchSize = batchSize
	}
	if model != "" {
		config.Model = model
	}
	if temperature >= 0 {
		config.Temperature = temperature
	}

	config.DryRun = dryRun
	config.UseCache = useCache
	config.ForceReclassify = forceReclassify
	config.MaxRetries = maxRetries
	config.RetryDelaySeconds = retryDelay

	return config
}

func parseEquipmentIDs(idsStr string) []string {
	ids := strings.Split(idsStr, ",")
	var cleanIDs []string
	for _, id := range ids {
		cleanID := strings.TrimSpace(id)
		if cleanID != "" {
			cleanIDs = append(cleanIDs, cleanID)
		}
	}
	return cleanIDs
}

func showStats(ctx context.Context, araTaxonomyService *service.ARATaxonomyService, equipmentDataService *service.EquipmentDataService) {
	fmt.Println("=== ARA Classification Statistics ===")

	// Get taxonomy stats
	taxonomyStats, err := araTaxonomyService.GetTaxonomyStats(ctx)
	if err != nil {
		log.Printf("Failed to get taxonomy stats: %v", err)
	} else {
		fmt.Printf("ARA Taxonomy:\n")
		fmt.Printf("  Level 1 Categories: %d\n", taxonomyStats["level1_categories"])
		fmt.Printf("  Level 2 Types: %d\n", taxonomyStats["level2_types"])
		fmt.Printf("  Level 3 Specifications: %d\n", taxonomyStats["level3_specifications"])
	}

	// Get equipment stats
	equipmentStats, err := equipmentDataService.GetEquipmentStats(ctx)
	if err != nil {
		log.Printf("Failed to get equipment stats: %v", err)
	} else {
		fmt.Printf("\nEquipment:\n")
		fmt.Printf("  Total Equipment: %d\n", equipmentStats["total_equipment"])
		fmt.Printf("  Classified Equipment: %d\n", equipmentStats["classified_equipment"])
		fmt.Printf("  Unclassified Equipment: %d\n", equipmentStats["unclassified_equipment"])

		if equipmentStats["total_equipment"] > 0 {
			percentage := float64(equipmentStats["classified_equipment"]) / float64(equipmentStats["total_equipment"]) * 100
			fmt.Printf("  Classification Progress: %.1f%%\n", percentage)
		}
	}
}

func classifyAllEquipment(ctx context.Context, araClassificationService *service.ARAClassificationService) {
	fmt.Println("=== Classifying All Unclassified Equipment ===")

	stats, err := araClassificationService.ClassifyAllEquipment(ctx)
	if err != nil {
		log.Fatalf("Classification failed: %v", err)
	}

	printClassificationStats(stats)
}

func classifySpecificEquipment(ctx context.Context, araClassificationService *service.ARAClassificationService, equipmentIDs []string) {
	fmt.Printf("=== Classifying %d Specific Equipment Items ===\n", len(equipmentIDs))

	stats, err := araClassificationService.ClassifyEquipmentByIDs(ctx, equipmentIDs)
	if err != nil {
		log.Fatalf("Classification failed: %v", err)
	}

	printClassificationStats(stats)
}

func printClassificationStats(stats *models.ARAClassificationStats) {
	fmt.Printf("\n=== Classification Results ===\n")
	fmt.Printf("Total Equipment: %d\n", stats.TotalEquipment)
	fmt.Printf("Processed Equipment: %d\n", stats.ProcessedEquipment)
	fmt.Printf("Successfully Classified: %d\n", stats.SuccessfullyClassified)
	fmt.Printf("Failed Classifications: %d\n", stats.FailedClassifications)
	fmt.Printf("Total Batches: %d\n", stats.TotalBatches)
	fmt.Printf("Processed Batches: %d\n", stats.ProcessedBatches)
	fmt.Printf("Processing Time: %v\n", stats.ProcessingTime)

	if stats.TotalTokensUsed > 0 {
		fmt.Printf("Total Tokens Used: %d\n", stats.TotalTokensUsed)
	}

	if stats.EstimatedCost > 0 {
		fmt.Printf("Estimated Cost: $%.4f\n", stats.EstimatedCost)
	}

	if stats.ProcessedEquipment > 0 {
		successRate := float64(stats.SuccessfullyClassified) / float64(stats.ProcessedEquipment) * 100
		fmt.Printf("Success Rate: %.1f%%\n", successRate)
	}

	if stats.ProcessingTime > 0 && stats.ProcessedEquipment > 0 {
		avgTimePerEquipment := stats.ProcessingTime / time.Duration(stats.ProcessedEquipment)
		fmt.Printf("Average Time per Equipment: %v\n", avgTimePerEquipment)
	}
}

func showHelp() {
	fmt.Println("ARA Equipment Classifier")
	fmt.Println("========================")
	fmt.Println()
	fmt.Println("This tool classifies equipment into ARA (American Rental Association) categories using AI.")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Println("  ara-classifier [options]")
	fmt.Println()
	fmt.Println("Options:")
	fmt.Println("  --all                     Classify all unclassified equipment")
	fmt.Println("  --equipment-ids=IDs       Classify specific equipment (comma-separated IDs)")
	fmt.Println("  --batch-size=N            Number of equipment items per batch (default: 15)")
	fmt.Println("  --model=MODEL             OpenRouter model to use (default: anthropic/claude-3-haiku)")
	fmt.Println("  --temperature=T           AI model temperature 0.0-1.0 (default: 0.1)")
	fmt.Println("  --dry-run                 Estimate cost without classifying")
	fmt.Println("  --use-cache               Use caching for results (default: true)")
	fmt.Println("  --no-cache                Disable caching")
	fmt.Println("  --max-retries=N           Maximum retries for failed requests (default: 3)")
	fmt.Println("  --retry-delay=N           Delay between retries in seconds (default: 2)")
	fmt.Println("  --force-reclassify        Force reclassification of already classified equipment")
	fmt.Println("  --stats                   Show equipment and taxonomy statistics")
	fmt.Println("  --help                    Show this help message")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  # Show current statistics")
	fmt.Println("  ara-classifier --stats")
	fmt.Println()
	fmt.Println("  # Estimate cost for classifying all equipment")
	fmt.Println("  ara-classifier --all --dry-run")
	fmt.Println()
	fmt.Println("  # Classify all unclassified equipment with batch size 20")
	fmt.Println("  ara-classifier --all --batch-size=20")
	fmt.Println()
	fmt.Println("  # Classify specific equipment items")
	fmt.Println("  ara-classifier --equipment-ids=\"id1,id2,id3\"")
	fmt.Println()
	fmt.Println("  # Use a different model with higher temperature")
	fmt.Println("  ara-classifier --all --model=\"anthropic/claude-3-sonnet\" --temperature=0.3")
	fmt.Println()
	fmt.Println("  # Force reclassification of all equipment (including already classified)")
	fmt.Println("  ara-classifier --all --force-reclassify")
	fmt.Println()
	fmt.Println("Environment Variables:")
	fmt.Println("  OPENROUTER_API_KEY                 Required: OpenRouter API key")
	fmt.Println("  ARA_CLASSIFICATION_BATCH_SIZE      Default batch size")
	fmt.Println("  ARA_CLASSIFICATION_MODEL           Default AI model")
	fmt.Println("  ARA_CLASSIFICATION_TEMPERATURE     Default temperature")
	fmt.Println()
	fmt.Println("Available Models:")
	fmt.Println("  anthropic/claude-3-haiku          Default: Reliable and cost-effective")
	fmt.Println("  openai/gpt-4o-mini                Fast and cost-effective")
	fmt.Println("  anthropic/claude-3-sonnet         Balanced performance")
	fmt.Println("  openai/gpt-4o                     OpenAI's most capable model with vision")
	fmt.Println("  openai/gpt-4                      OpenAI's previous flagship model")
}

// maskAPIKey masks an API key for safe logging
func maskAPIKey(key string) string {
	if len(key) <= 8 {
		return "***"
	}
	return key[:4] + "..." + key[len(key)-4:]
}
