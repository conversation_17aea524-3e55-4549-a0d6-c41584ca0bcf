# ARA Equipment Classifier

This tool classifies equipment from the `tooler_bidz_equipment_inventory` collection into ARA (American Rental Association) categories using AI models via OpenRouter.

## Features

- **Batch Processing**: Processes multiple equipment items per API call to reduce costs (configurable batch size)
- **Cost Optimization**: Supports dry-run mode to estimate costs before processing
- **Caching**: Caches classification results to avoid re-processing identical equipment
- **Retry Logic**: Automatic retry with exponential backoff for failed requests
- **Multiple AI Models**: Supports various OpenRouter models (Claude, GPT, etc.)
- **Detailed Descriptions**: Uses comprehensive equipment descriptions from `Single_equipment_details_data_generation.js`
- **Progress Tracking**: Real-time statistics and progress reporting

## Prerequisites

1. **OpenRouter API Key**: Required for AI classification
2. **ARA Taxonomy**: Must be imported using the `ara-taxonomy-importer` tool
3. **Equipment Data**: Equipment must exist in `tooler_bidz_equipment_inventory` collection

## Installation

```bash
# Build the tool
cd backend
make ara-classifier

# Or run directly
go run ./cmd/ara-classifier [options]
```

## Configuration

### Environment Variables

```bash
# Required
OPENROUTER_API_KEY=your_openrouter_api_key

# Optional (with defaults)
ARA_CLASSIFICATION_BATCH_SIZE=15
ARA_CLASSIFICATION_MODEL=anthropic/claude-3-haiku
ARA_CLASSIFICATION_TEMPERATURE=0.1
```
🌡️ **Temperature Explained**
- Temperature Range: 0.0 to 1.0

- 0.0: Most deterministic/consistent (same input = same output)
- 0.1: Very low randomness (our setting)
- 0.5: Balanced creativity
- 1.0: Maximum creativity/randomness

**When to Adjust Temperature**
- Keep 0.1: For production classification (recommended)
- Increase to 0.3: If model seems too rigid and misses obvious classifications
- Never use > 0.5: For classification tasks (too unpredictable)


### Available Models

- `anthropic/claude-3-haiku` - Fast and cost-effective (recommended)
- `anthropic/claude-3-sonnet` - Balanced performance and accuracy
- `openai/gpt-3.5-turbo` - OpenAI's efficient model
- `openai/gpt-4` - OpenAI's most capable model

## Usage

### Show Statistics
```bash
./ara-classifier --stats
```

### Estimate Costs (Dry Run)
```bash
# Estimate cost for all unclassified equipment
./ara-classifier --all --dry-run

# Estimate cost for specific equipment
./ara-classifier --equipment-ids="id1,id2,id3" --dry-run
```

### Classify Equipment
```bash
# Classify all unclassified equipment
./ara-classifier --all

# Classify specific equipment by IDs
./ara-classifier --equipment-ids="equipment_id_1,equipment_id_2"

# Use custom batch size
./ara-classifier --all --batch-size=20

# Use different model
./ara-classifier --all --model="anthropic/claude-3-sonnet"

# Disable caching
./ara-classifier --all --no-cache
```

### Advanced Options
```bash
# Custom configuration
./ara-classifier --all \
  --batch-size=25 \
  --model="anthropic/claude-3-sonnet" \
  --temperature=0.2 \
  --max-retries=5 \
  --retry-delay=3
```

## Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--all` | Classify all unclassified equipment | - |
| `--equipment-ids` | Comma-separated list of equipment IDs | - |
| `--batch-size` | Number of equipment items per batch | 15 |
| `--model` | OpenRouter model to use | anthropic/claude-3-haiku |
| `--temperature` | AI model temperature (0.0-1.0) | 0.1 |
| `--dry-run` | Estimate cost without classifying | false |
| `--use-cache` | Use caching for results | true |
| `--no-cache` | Disable caching | false |
| `--max-retries` | Maximum retries for failed requests | 3 |
| `--retry-delay` | Delay between retries (seconds) | 2 |
| `--stats` | Show equipment and taxonomy statistics | false |
| `--help` | Show help message | false |

## Cost Optimization

### Batch Processing
The tool processes multiple equipment items in a single API call:
- **Default batch size**: 15 equipment per call
- **Cost reduction**: ~10-15x compared to individual calls
- **Configurable**: Adjust batch size based on your needs

### Model Selection
Choose models based on your accuracy vs cost requirements:
- **claude-3-haiku**: Fastest and cheapest (~$0.25/M tokens)
- **claude-3-sonnet**: Balanced option (~$3.00/M tokens)
- **gpt-4**: Most accurate but expensive (~$30.00/M tokens)

### Caching
- Automatically caches results for identical equipment names
- Reduces redundant API calls for duplicate equipment
- Can be disabled with `--no-cache` if needed

## Output

The tool provides detailed statistics after processing:

```
=== Classification Results ===
Total Equipment: 1000
Processed Equipment: 1000
Successfully Classified: 985
Failed Classifications: 15
Total Batches: 67
Processed Batches: 67
Processing Time: 5m30s
Estimated Cost: $3.45
Success Rate: 98.5%
Average Time per Equipment: 330ms
```

## Database Schema

The tool adds the following fields to equipment documents:

```json
{
  "ara_level1_id": "string",
  "ara_level2_id": "string", 
  "ara_level1_name": "string",
  "ara_level2_name": "string",
  "ara_classified_at": "timestamp"
}
```

## Error Handling

- **Validation**: Ensures ARA category IDs exist in taxonomy
- **Retry Logic**: Automatically retries failed API calls
- **Partial Success**: Continues processing even if some items fail
- **Detailed Logging**: Provides clear error messages and progress updates

## Troubleshooting

### Common Issues

1. **Missing OpenRouter API Key**
   ```
   Error: OPENROUTER_API_KEY environment variable is required
   ```
   Solution: Set the `OPENROUTER_API_KEY` environment variable

2. **Equipment Details File Not Found**
   ```
   Warning: Failed to load equipment details: failed to read equipment details file
   ```
   Solution: Ensure `src/shared/helpers/Single_equipment_details_data_generation.js` exists

3. **ARA Taxonomy Not Found**
   ```
   Error: failed to get ARA taxonomy
   ```
   Solution: Run the `ara-taxonomy-importer` tool first

4. **Rate Limiting**
   ```
   Error: API request failed with status 429
   ```
   Solution: Increase `--retry-delay` or reduce `--batch-size`

### Performance Tips

1. **Start with dry-run**: Always estimate costs first
2. **Use appropriate batch size**: 15-25 items work well for most models
3. **Enable caching**: Especially useful for datasets with duplicate equipment
4. **Choose the right model**: Start with claude-3-haiku for cost-effectiveness

## Integration

The ARA classifier integrates with:
- **ARA Taxonomy Importer**: Requires taxonomy data to be imported first
- **Equipment Management**: Updates existing equipment with ARA classifications
- **API Endpoints**: Can be extended to provide REST API access
- **Monitoring**: Provides detailed statistics for tracking progress

## Development

To extend or modify the classifier:

1. **Models**: Add new structures in `backend/models/ara_classification.go`
2. **Services**: Extend functionality in `backend/service/ara_*.go`
3. **OpenRouter Client**: Modify API integration in `backend/external/openrouter/client.go`
4. **CLI**: Update command-line interface in `backend/cmd/ara-classifier/main.go`
