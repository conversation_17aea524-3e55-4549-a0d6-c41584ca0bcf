package main

import (
	"cloud.google.com/go/firestore"
	"context"
	_ "fmt"
	"log"
	"time"

	firebase "firebase.google.com/go/v4"
	_ "google.golang.org/api/iterator"

	"github.com/vima-inc/derental/config"
)

func main() {
	c, err := config.New()
	if err != nil {
		log.Fatalf("unable to init config %+v", err)
	}

	conf := &firebase.Config{ProjectID: c.ProjectID}
	ctx := context.Background()

	app, err := firebase.NewApp(ctx, conf)
	if err != nil {
		log.Fatalf("unable to create firebase app: %v", err)
	}

	client, err := app.Firestore(ctx)
	if err != nil {
		log.Fatalf("unable to create firestore client: %v", err)
	}
	defer client.Close()

	const pageSize = 100
	var lastDoc *firestore.DocumentSnapshot
	totalUpdated := 0

	for {
		query := client.Collection("bids_offer").
			OrderBy(firestore.DocumentID, firestore.Asc).
			Limit(pageSize)

		if lastDoc != nil {
			query = query.StartAfter(lastDoc.Ref.ID)
		}

		docs, err := query.Documents(ctx).GetAll()
		if err != nil {
			log.Printf("Query failed: %v", err)
			break
		}
		if len(docs) == 0 {
			break
		}

		for _, doc := range docs {
			data := doc.Data()
			val, ok := data["type_of_propulsion"]
			if !ok {
				continue
			}

			strVal, isString := val.(string)
			if !isString {
				continue
			}

			_, err := doc.Ref.Update(ctx, []firestore.Update{
				{Path: "type_of_propulsion", Value: []string{strVal}},
			})
			if err != nil {
				log.Printf("❌ Failed to update doc %s: %v", doc.Ref.ID, err)
			} else {
				log.Printf("✅ Updated doc %s: [%s]", doc.Ref.ID, strVal)
				totalUpdated++
			}
		}

		lastDoc = docs[len(docs)-1]
		time.Sleep(300 * time.Millisecond) // delay to reduce Firestore pressure
	}

	log.Printf("🎉 Finished. Total documents updated: %d\n", totalUpdated)
}
