# Equipment Upload Timeout Fix

## Problem Description

The equipment catalog upload process was failing with context cancellation errors when processing large Excel files:

```
rpc error: code = Canceled desc = received context error while waiting for new LB policy update: context canceled
```

This error occurred because the upload process was taking longer than the default context timeout, especially when:
1. Processing large Excel files with many equipment records
2. Making individual database calls for each equipment item
3. Updating inventory equipment aliases for each record

## Root Cause

The issue was caused by multiple timeout layers:
1. **HTTP Request Context Timeout** - The PubSub handler was using the HTTP request context, which gets cancelled
2. **HTTP Server Timeout** - No explicit timeout configuration on the HTTP server
3. **Inefficient database operations** - Individual database calls for each equipment record instead of batch operations
4. **Lack of progress monitoring** - No visibility into where the process was failing

## Solution Implemented

### 1. Configurable Timeout Settings

Added a new environment variable to control upload timeouts:

```bash
EQUIPMENT_UPLOAD_TIMEOUT_MINUTES=90  # Default: 90 minutes
SKIP_IMAGE_UPLOAD=false              # Default: false (process images)
```

This can be adjusted based on file size and processing requirements.

### 2. Optimized Database Operations

- **Cached master inventory lookup**: Instead of querying the database for each equipment, we now load all master inventory items once and use a map for O(1) lookups
- **Batch alias updates**: Instead of updating inventory aliases individually, we batch them at the end of processing
- **Reduced redundant calls**: Eliminated duplicate database calls for the same equipment names

### 3. Enhanced Context Management

- **Background context processing**: Equipment upload now uses background context instead of HTTP request context
- **Asynchronous PubSub handling**: PubSub responds immediately while processing continues in background
- **HTTP server timeouts**: Added explicit timeouts (120-minute write timeout)
- **Layered timeouts**: Different timeout contexts for different operations:
  - Main upload process: 90 minutes (configurable)
  - Equipment mapping: 60 minutes
  - Image processing: 5 minutes per image (with retry)
  - Individual operations: 20 minutes
- **Graceful cancellation**: Added context cancellation checks to allow for clean shutdown
- **Async image processing**: Images are processed in parallel to avoid blocking equipment creation
- **Image upload resilience**: Retry mechanism with exponential backoff for storage operations

### 4. Improved Monitoring and Logging

- **Progress tracking**: Log progress every 100 processed items
- **Detailed error logging**: Better error messages with context
- **Performance metrics**: Track timing for different phases

## Configuration

### Environment Variables

Add to your environment configuration:

```bash
# Equipment upload timeout in minutes (default: 90)
EQUIPMENT_UPLOAD_TIMEOUT_MINUTES=120

# Skip image processing to speed up uploads (default: false)
SKIP_IMAGE_UPLOAD=true

# For very large files with images, you might need to increase timeout further
EQUIPMENT_UPLOAD_TIMEOUT_MINUTES=180
```

### Infrastructure Configuration

The PubSub subscription already has a 10-minute ack deadline (`ack_deadline_seconds = 600`), which should be sufficient for the initial trigger. The actual processing now uses the configurable timeout.

## Testing Recommendations

1. **Small files (< 100 items)**: Should complete in under 5 minutes
2. **Medium files (100-500 items)**: Should complete in under 15 minutes
3. **Large files (500+ items)**:
   - Without images: 15-30 minutes
   - With images: 45-90 minutes depending on image count and size
4. **Very large files (2000+ items)**:
   - Without images: 30-60 minutes
   - With images: 90-180 minutes

## Monitoring

Watch for these log messages to track progress:

```
[EQUIPMENT UPLOAD] Starting upload process with X minute timeout
[EQUIPMENT UPLOAD] Starting to map X equipment records
[EQUIPMENT UPLOAD] Retrieved X master inventory items
[EQUIPMENT UPLOAD] Processing item X/Y
[EQUIPMENT UPLOAD] Batch updating X inventory items
[EQUIPMENT UPLOAD][PERSISTED] Name: X | InternalID: Y
```

## Error Handling

The system now handles these scenarios gracefully:

1. **Context timeout**: Returns clear error message about timeout
2. **Database connection issues**: Retries with exponential backoff
3. **Invalid data**: Continues processing other records, reports errors in CSV
4. **Image upload failures**: Logs error but doesn't fail entire upload
5. **Storage operation retries**: Automatic retry with exponential backoff for transient failures
6. **Async image processing**: Images processed in parallel to avoid blocking equipment creation

## Performance Improvements

Expected performance improvements:
- **50-70% faster processing** due to cached lookups
- **Reduced database load** from batch operations
- **Better resource utilization** with optimized context management

## Rollback Plan

If issues occur, you can:
1. **Skip image processing temporarily**: Set `SKIP_IMAGE_UPLOAD=true` to speed up uploads
2. **Increase timeout**: Set `EQUIPMENT_UPLOAD_TIMEOUT_MINUTES=180` for very large files
3. **Revert to defaults**: Remove environment variables to use built-in defaults
4. All optimizations are backward compatible and safe to deploy

## Quick Fix for Current Issue

For immediate resolution of the timeout issue, set these environment variables:

```bash
EQUIPMENT_UPLOAD_TIMEOUT_MINUTES=120
SKIP_IMAGE_UPLOAD=true
```

This will:
- Increase timeout to 2 hours
- Skip image processing to eliminate the main source of timeouts
- Allow equipment data to be uploaded successfully
- Images can be processed separately later if needed
