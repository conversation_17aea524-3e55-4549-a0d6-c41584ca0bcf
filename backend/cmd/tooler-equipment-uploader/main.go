package main

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"github.com/vima-inc/derental/mailer"
	"github.com/vima-inc/derental/mailer/ses"
	"github.com/vima-inc/derental/storage"

	firebase "firebase.google.com/go"
	"google.golang.org/api/option"

	"github.com/vima-inc/derental/api"
	"github.com/vima-inc/derental/config"
	firestoredb "github.com/vima-inc/derental/db/firestore"
	"github.com/vima-inc/derental/notifier/slack"
	"github.com/vima-inc/derental/service"
	googlestorage "github.com/vima-inc/derental/storage/google-storage"
)

const (
	SendgridSendEquipperCSVReportID   = "d-66d978f3bc714c84a35e9e8492156121"
	SendgridSendEquipperCSVReportIDFR = "d-9215612166d978f3bc714c84a35e9e84"
)

func main() {
	c, err := config.New()
	if err != nil {
		log.Fatalf("unable to init config %+v", err)
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	app := initFirebase(ctx, c)

	firestore, err := firestoredb.New(ctx, app)
	if err != nil {
		log.Panicf("unable to initialize firestore: %v", err)
	}

	storage, err := googlestorage.New(ctx, c.FirebaseStorageBucket)
	if err != nil {
		log.Panicf("unable to initialize google storage: %v", err)
	}

	mailer := initMail(c, storage)
	sk := slack.NewClient(c.SlackWebhookURL)
	svc := service.New(
		service.WithDB(firestore),
		service.WithStorage(storage),
		service.WithNotifier(sk),
		service.WithFrontendURL(c.FrontendURL),
		service.WithProjectID(c.ProjectID),
		service.WithMailer(mailer),
		service.WithTranslator(c.GoogleTranslationApiKey),
		service.WithEquipmentUploadTimeout(c.EquipmentUploadTimeoutMinutes),
		service.WithSkipImageUpload(c.SkipImageUpload),
	)

	srv := api.New(c.Port, svc, api.WithPubSub())

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM, os.Interrupt)

	go func() {
		err = srv.StartHTTP()
		if err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Printf("an error was occurred %v", err)
			<-quit
		}
	}()

	<-quit
	log.Print("Shutdown Server ...")

	if err := srv.Shutdown(ctx); err != nil {
		log.Printf("Server Shutdown: %v", err)
	}

	log.Printf("Server exiting")
}
func initMail(c *config.Config, storage storage.Storage) mailer.Mailer {
	bucket := fmt.Sprintf("%s_private_informations", c.ProjectID)
	ses, err := ses.New(c.AWSAccessKey, c.AWSSecretKey, c.AWSRegion, c.SendgridSender, storage, bucket)
	if err != nil {
		log.Panicf("unable to initialize ses: %v", err)
	}

	ses.SetTemplateIDs(map[string]string{

		mailer.SendgridSendEquipperCSVReportID:   SendgridSendEquipperCSVReportID,
		mailer.SendgridSendEquipperCSVReportIDFR: SendgridSendEquipperCSVReportIDFR,
	})

	return ses
}
func initFirebase(ctx context.Context, c *config.Config) *firebase.App {
	isDevelopment := c.Mode != "production"

	options := []option.ClientOption{}
	if isDevelopment {
		options = append(options, option.WithCredentialsFile("./key.json"))
	}

	config := &firebase.Config{
		StorageBucket: c.FirebaseStorageBucket,
	}

	app, err := firebase.NewApp(ctx, config, options...)
	if err != nil {
		log.Fatalf("unable to initialize firebase app: %v", err)
	}

	return app
}
