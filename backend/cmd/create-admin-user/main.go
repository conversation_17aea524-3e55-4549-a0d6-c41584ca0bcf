package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"

	firebase "firebase.google.com/go"
	"firebase.google.com/go/auth"
	"google.golang.org/api/option"

	"github.com/vima-inc/derental/config"
)

func main() {
	var (
		email      = flag.String("email", "", "Admin email address (must end with @derentalequipment.com)")
		password   = flag.String("password", "", "Admin password (minimum 6 characters)")
		configPath = flag.String("config", "", "Path to config file (optional)")
	)
	flag.Parse()

	if *email == "" || *password == "" {
		fmt.Println("Usage: go run main.go -email=<EMAIL> -password=yourpassword")
		fmt.Println("Options:")
		fmt.Println("  -email     Admin email address (required)")
		fmt.Println("  -password  Admin password (required)")
		fmt.Println("  -config    Path to config file (optional)")
		os.Exit(1)
	}

	// Validate email domain
	if !strings.HasSuffix(*email, "@derentalequipment.com") {
		log.Fatal("Error: Admin email must end with @derentalequipment.com")
	}

	// Validate password length
	if len(*password) < 6 {
		log.Fatal("Error: Password must be at least 6 characters long")
	}

	ctx := context.Background()

	// Initialize Firebase
	app, err := initFirebase(ctx, *configPath)
	if err != nil {
		log.Fatalf("Error initializing Firebase: %v", err)
	}

	// Get Auth client
	authClient, err := app.Auth(ctx)
	if err != nil {
		log.Fatalf("Error getting Auth client: %v", err)
	}

	// Create admin user
	err = createAdminUser(ctx, authClient, *email, *password)
	if err != nil {
		log.Fatalf("Error creating admin user: %v", err)
	}

	fmt.Printf("✅ Successfully created admin user: %s\n", *email)
	fmt.Println("You can now sign in to the admin portal at /admin/login")
}

func initFirebase(ctx context.Context, configPath string) (*firebase.App, error) {
	c, err := config.New()
	if err != nil {
		return nil, fmt.Errorf("unable to load config: %w", err)
	}

	// Determine if we're in development mode
	isDevelopment := c.Mode != "production"

	options := []option.ClientOption{}
	if isDevelopment {
		// Check if key.json exists
		if _, err := os.Stat("./key.json"); err == nil {
			options = append(options, option.WithCredentialsFile("./key.json"))
		} else if _, err := os.Stat("../../key.json"); err == nil {
			// Try relative path from cmd directory
			options = append(options, option.WithCredentialsFile("../../key.json"))
		} else {
			return nil, fmt.Errorf("key.json not found. Please ensure Firebase service account key is available")
		}
	}

	config := &firebase.Config{
		StorageBucket: c.FirebaseStorageBucket,
	}

	app, err := firebase.NewApp(ctx, config, options...)
	if err != nil {
		return nil, fmt.Errorf("unable to initialize firebase app: %w", err)
	}

	return app, nil
}

func createAdminUser(ctx context.Context, authClient *auth.Client, email, password string) error {
	// Check if user already exists
	existingUser, err := authClient.GetUserByEmail(ctx, email)
	if err == nil {
		fmt.Printf("⚠️  User %s already exists with UID: %s\n", email, existingUser.UID)

		// Ask if user wants to update password
		fmt.Print("Do you want to update the password? (y/N): ")
		var response string
		if _, err := fmt.Scanln(&response); err != nil {
			// If there's an error reading input, default to "no"
			response = "n"
		}

		if strings.ToLower(response) == "y" || strings.ToLower(response) == "yes" {
			params := (&auth.UserToUpdate{}).Password(password)
			_, err = authClient.UpdateUser(ctx, existingUser.UID, params)
			if err != nil {
				return fmt.Errorf("failed to update user password: %w", err)
			}
			fmt.Println("✅ Password updated successfully")
		}
		return nil
	}

	// Create new user
	params := (&auth.UserToCreate{}).
		Email(email).
		Password(password).
		EmailVerified(true)

	userRecord, err := authClient.CreateUser(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}

	fmt.Printf("✅ Created new user with UID: %s\n", userRecord.UID)
	return nil
}
