FUNCTION_NAME = Sync
RUNTIME = go120
PROJECT_ID = prod-derental
COLLECTIONS = bids_offer bids_request book_equipments equipments equippers


deploy:
	for collection in $(COLLECTIONS); do \
		gcloud functions deploy $(FUNCTION_NAME)-$$collection \
		--runtime $(RUNTIME) \
		--trigger-event "providers/cloud.firestore/eventTypes/document.write" \
		--trigger-resource "projects/$(PROJECT_ID)/databases/(default)/documents/$$collection/{id}" \
		--set-env-vars GOOGLE_CLOUD_PROJECT=$(PROJECT_ID) \
		--entry-point $(FUNCTION_NAME) \
		--project $(PROJECT_ID); \
	done
