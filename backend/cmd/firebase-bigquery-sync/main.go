package sync

import (
	"context"
	"fmt"
	"log"
	"reflect"
	"strings"
	"time"

	"cloud.google.com/go/bigquery"
	"cloud.google.com/go/firestore"
	firebase "firebase.google.com/go/v4"
	_ "github.com/GoogleCloudPlatform/functions-framework-go/funcframework"

	"github.com/vima-inc/derental/config"
	"github.com/vima-inc/derental/models"
)

const (
	dataSet = "firebase_bigquery_sync"
)

// FirestoreEvent is the payload of a Firestore event.
type FirestoreEvent struct {
	Value FirestoreValue `json:"value"`
}

// FirestoreValue holds Firestore fields.
type FirestoreValue struct {
	CreateTime time.Time `json:"createTime"`
	Name       string    `json:"name"`
	UpdateTime time.Time `json:"updateTime"`
}

var (
	client         *firestore.Client
	bigqueryClient *bigquery.Client
)

func init() {
	c, err := config.New()
	if err != nil {
		log.Fatalf("unable to init config %+v", err)
	}

	conf := &firebase.Config{ProjectID: c.ProjectID}

	ctx := context.Background()

	app, err := firebase.NewApp(ctx, conf)
	if err != nil {
		log.Fatalf("unable to create firebase app: %v", err)
	}

	client, err = app.Firestore(ctx)
	if err != nil {
		log.Fatalf("unable to create firestore client: %v", err)
	}

	bigqueryClient, err = bigquery.NewClient(ctx, c.ProjectID)
	if err != nil {
		log.Fatalf("unable to create bigquery client: %v", err)
	}
}

// Sync is the entry point for the Cloud Function.
func Sync(ctx context.Context, e FirestoreEvent) error {
	fullPath := strings.Split(e.Value.Name, "/documents/")[1]
	pathParts := strings.Split(fullPath, "/")
	collection := pathParts[0]
	doc := strings.Join(pathParts[1:], "/")

	var err error

	switch collection {
	case "book_equipments":
		var model models.BookEquipment
		err = insert(ctx, collection, model, doc)
	case "equipments":
		var model models.Equipment
		err = insert(ctx, collection, model, doc)
	case "equippers":
		var model models.Equipper
		err = insert(ctx, collection, model, doc)
	case "bids_request":
		var model models.BidsRequest
		err = insert(ctx, collection, model, doc)
	case "bids_offer":
		var model models.BidzOffer
		err = insert(ctx, collection, model, doc)
	default:
		return fmt.Errorf("unknown collection %s", collection)
	}

	if err != nil {
		return fmt.Errorf("unable to insert document %s: %w", doc, err)
	}

	return nil
}

func insert[T any](ctx context.Context, collection string, model T, doc string) error {
	data, err := client.Collection(collection).Doc(doc).Get(ctx)
	if err != nil {
		return fmt.Errorf("unable to get document %s: %w", doc, err)
	}

	err = data.DataTo(&model)
	if err != nil {
		return fmt.Errorf("unable to convert document %s to BookEquipment: %w", doc, err)
	}

	err = createTable(ctx, collection, model)
	if err != nil {
		return fmt.Errorf("unable to create table %s: %w", collection, err)
	}

	value := bigquery.StructSaver{
		InsertID: getID(model),
		Struct:   model,
	}

	err = bigqueryClient.Dataset(dataSet).Table(collection).Inserter().Put(ctx, &value)
	if err != nil {
		return fmt.Errorf("unable to insert document %s: %w", collection, err)
	}

	return nil
}

func createTable(ctx context.Context, collection string, model any) error {
	_, err := bigqueryClient.Dataset(dataSet).Metadata(ctx)
	if err != nil {
		err = bigqueryClient.Dataset(dataSet).Create(ctx, &bigquery.DatasetMetadata{
			Name: dataSet,
		})
		if err != nil {
			return fmt.Errorf("unable to create dataset %s : %w", dataSet, err)
		}
	}

	_, err = bigqueryClient.Dataset(dataSet).Table(collection).Metadata(ctx)
	if err != nil {
		schema, err := bigquery.InferSchema(model)
		if err != nil {
			return fmt.Errorf("unable to infer schema for model %v: %w", model, err)
		}

		err = bigqueryClient.Dataset(dataSet).Table(collection).Create(ctx, &bigquery.TableMetadata{
			Name:   collection,
			Schema: schema,
		})
		if err != nil {
			return fmt.Errorf("unable to create dataset %s : %w", dataSet, err)
		}
	}

	return nil
}

func getID(model any) string {
	v := reflect.ValueOf(model).FieldByName("ID")
	if v.IsValid() {
		return v.String()
	}

	return ""
}
