package main

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	firebase "firebase.google.com/go"
	"google.golang.org/api/option"

	"github.com/vima-inc/derental/api"
	"github.com/vima-inc/derental/config"
	firestoredb "github.com/vima-inc/derental/db/firestore"
	"github.com/vima-inc/derental/mailer"
	"github.com/vima-inc/derental/mailer/ses"
	"github.com/vima-inc/derental/notifier/slack"
	"github.com/vima-inc/derental/service"
	"github.com/vima-inc/derental/storage"
	googlestorage "github.com/vima-inc/derental/storage/google-storage"
)

const (
	SendgridSendAutomaticUpdateTemplateID   = "d-49d445c1662248c78fe90a297f5986fb92156121"
	SendgridSendAutomaticUpdateTemplateIDFR = "d-9215612107434831fb1641f99da89f5af26661d6"
)

func main() {
	c, err := config.New()
	if err != nil {
		log.Fatalf("unable to init config %+v", err)
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	app := initFirebase(ctx, c)

	firestore, err := firestoredb.New(ctx, app)
	if err != nil {
		log.Panicf("unable to initialize firestore: %v", err)
	}

	storage, err := googlestorage.New(ctx, c.FirebaseStorageBucket)
	if err != nil {
		log.Panicf("unable to initialize google storage: %v", err)
	}

	mailer := initMail(c, storage)
	sk := slack.NewClient(c.SlackWebhookURL)
	svc := service.New(
		service.WithDB(firestore),
		service.WithStorage(storage),
		service.WithNotifier(sk),
		service.WithMailer(mailer),
		service.WithFrontendURL(c.FrontendURL),
		service.WithProjectID(c.ProjectID),
	)

	srv := api.New(c.Port, svc, api.WithCron())

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM, os.Interrupt)

	go func() {
		err = srv.StartHTTP()
		if err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Printf("an error was occurred %v", err)
			<-quit
		}
	}()

	<-quit
	log.Print("Shutdown Server ...")

	if err := srv.Shutdown(ctx); err != nil {
		log.Printf("Server Shutdown: %v", err)
	}

	log.Printf("Server exiting")
}

func initFirebase(ctx context.Context, c *config.Config) *firebase.App {
	isDevelopment := c.Mode != "production"

	options := []option.ClientOption{}
	if isDevelopment {
		options = append(options, option.WithCredentialsFile("./key.json"))
	}

	config := &firebase.Config{
		StorageBucket: c.FirebaseStorageBucket,
	}

	app, err := firebase.NewApp(ctx, config, options...)
	if err != nil {
		log.Fatalf("unable to initialize firebase app: %v", err)
	}

	return app
}

func initMail(c *config.Config, storage storage.Storage) mailer.Mailer {
	bucket := fmt.Sprintf("%s_private_informations", c.ProjectID)
	ses, err := ses.New(c.AWSAccessKey, c.AWSSecretKey, c.AWSRegion, c.SendgridSender, storage, bucket)
	if err != nil {
		log.Panicf("unable to initialize ses: %v", err)
	}

	ses.SetTemplateIDs(map[string]string{
		mailer.SendgridSendAutomaticUpdateTemplateID:   SendgridSendAutomaticUpdateTemplateID,
		mailer.SendgridSendAutomaticUpdateTemplateIDFR: SendgridSendAutomaticUpdateTemplateIDFR,
	})

	return ses
}
