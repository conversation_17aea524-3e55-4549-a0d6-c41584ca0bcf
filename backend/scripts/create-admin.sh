#!/bin/bash

# Script to create admin users for Derental admin portal
# Usage: ./create-admin.sh

echo "🔧 Derental Admin User Creation Tool"
echo "===================================="

# Check if we're in the backend directory
if [ ! -f "go.mod" ]; then
    echo "❌ Error: Please run this script from the backend directory"
    exit 1
fi

# Check if key.json exists
if [ ! -f "key.json" ]; then
    echo "❌ Error: key.json (Firebase service account key) not found"
    echo "Please ensure your Firebase service account key is saved as 'key.json' in the backend directory"
    exit 1
fi

# Get email input
echo ""
read -p "Enter admin email (must end with @derentalequipment.com): " email

# Validate email domain
if [[ ! "$email" =~ @derentalequipment\.com$ ]]; then
    echo "❌ Error: Email must end with @derentalequipment.com"
    exit 1
fi

# Get password input (hidden)
echo ""
read -s -p "Enter password (minimum 6 characters): " password
echo ""

# Validate password length
if [ ${#password} -lt 6 ]; then
    echo "❌ Error: Password must be at least 6 characters long"
    exit 1
fi

echo ""
echo "Creating admin user..."

# Run the Go program
cd cmd/create-admin-user
go run main.go -email="$email" -password="$password"

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Admin user created successfully!"
    echo "You can now sign in at: /admin/login"
    echo "Email: $email"
else
    echo ""
    echo "❌ Failed to create admin user"
    exit 1
fi
