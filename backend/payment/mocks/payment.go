// Code generated by mockery v2.46.3. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	models "github.com/vima-inc/derental/models"
)

// Payment is an autogenerated mock type for the Payment type
type Payment struct {
	mock.Mock
}

// Authorize provides a mock function with given fields: order, successURL, cancelURL, autoCapture
func (_m *Payment) Authorize(order models.Order, successURL string, cancelURL string, autoCapture bool) (string, error) {
	ret := _m.Called(order, successURL, cancelURL, autoCapture)

	if len(ret) == 0 {
		panic("no return value specified for Authorize")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(models.Order, string, string, bool) (string, error)); ok {
		return rf(order, successURL, cancelURL, autoCapture)
	}
	if rf, ok := ret.Get(0).(func(models.Order, string, string, bool) string); ok {
		r0 = rf(order, successURL, cancelURL, autoCapture)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(models.Order, string, string, bool) error); ok {
		r1 = rf(order, successURL, cancelURL, autoCapture)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Cancel provides a mock function with given fields: paymentIntentID
func (_m *Payment) Cancel(paymentIntentID string) error {
	ret := _m.Called(paymentIntentID)

	if len(ret) == 0 {
		panic("no return value specified for Cancel")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(paymentIntentID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Capture provides a mock function with given fields: paymentIntentID
func (_m *Payment) Capture(paymentIntentID string) error {
	ret := _m.Called(paymentIntentID)

	if len(ret) == 0 {
		panic("no return value specified for Capture")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(paymentIntentID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ChargeComplete provides a mock function with given fields: payload, signature
func (_m *Payment) ChargeComplete(payload []byte, signature string) (models.ChargeResponse, error) {
	ret := _m.Called(payload, signature)

	if len(ret) == 0 {
		panic("no return value specified for ChargeComplete")
	}

	var r0 models.ChargeResponse
	var r1 error
	if rf, ok := ret.Get(0).(func([]byte, string) (models.ChargeResponse, error)); ok {
		return rf(payload, signature)
	}
	if rf, ok := ret.Get(0).(func([]byte, string) models.ChargeResponse); ok {
		r0 = rf(payload, signature)
	} else {
		r0 = ret.Get(0).(models.ChargeResponse)
	}

	if rf, ok := ret.Get(1).(func([]byte, string) error); ok {
		r1 = rf(payload, signature)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CheckoutComplete provides a mock function with given fields: payload, signature
func (_m *Payment) CheckoutComplete(payload []byte, signature string) (models.CheckoutResponse, error) {
	ret := _m.Called(payload, signature)

	if len(ret) == 0 {
		panic("no return value specified for CheckoutComplete")
	}

	var r0 models.CheckoutResponse
	var r1 error
	if rf, ok := ret.Get(0).(func([]byte, string) (models.CheckoutResponse, error)); ok {
		return rf(payload, signature)
	}
	if rf, ok := ret.Get(0).(func([]byte, string) models.CheckoutResponse); ok {
		r0 = rf(payload, signature)
	} else {
		r0 = ret.Get(0).(models.CheckoutResponse)
	}

	if rf, ok := ret.Get(1).(func([]byte, string) error); ok {
		r1 = rf(payload, signature)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateCoupon provides a mock function with given fields: code, percentOff, currency, duration
func (_m *Payment) CreateCoupon(code string, percentOff float64, currency string, duration string) (string, error) {
	ret := _m.Called(code, percentOff, currency, duration)

	if len(ret) == 0 {
		panic("no return value specified for CreateCoupon")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(string, float64, string, string) (string, error)); ok {
		return rf(code, percentOff, currency, duration)
	}
	if rf, ok := ret.Get(0).(func(string, float64, string, string) string); ok {
		r0 = rf(code, percentOff, currency, duration)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(string, float64, string, string) error); ok {
		r1 = rf(code, percentOff, currency, duration)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateCustomer provides a mock function with given fields: name, email, address
func (_m *Payment) CreateCustomer(name string, email string, address models.Address) (string, error) {
	ret := _m.Called(name, email, address)

	if len(ret) == 0 {
		panic("no return value specified for CreateCustomer")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, models.Address) (string, error)); ok {
		return rf(name, email, address)
	}
	if rf, ok := ret.Get(0).(func(string, string, models.Address) string); ok {
		r0 = rf(name, email, address)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(string, string, models.Address) error); ok {
		r1 = rf(name, email, address)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteCoupon provides a mock function with given fields: id
func (_m *Payment) DeleteCoupon(id string) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteCoupon")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetCoupon provides a mock function with given fields: code
func (_m *Payment) GetCoupon(code string) (*models.Coupon, error) {
	ret := _m.Called(code)

	if len(ret) == 0 {
		panic("no return value specified for GetCoupon")
	}

	var r0 *models.Coupon
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*models.Coupon, error)); ok {
		return rf(code)
	}
	if rf, ok := ret.Get(0).(func(string) *models.Coupon); ok {
		r0 = rf(code)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Coupon)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(code)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SignWebhookPayload provides a mock function with given fields: payload, signature
func (_m *Payment) SignWebhookPayload(payload []byte, signature string) ([]byte, error) {
	ret := _m.Called(payload, signature)

	if len(ret) == 0 {
		panic("no return value specified for SignWebhookPayload")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func([]byte, string) ([]byte, error)); ok {
		return rf(payload, signature)
	}
	if rf, ok := ret.Get(0).(func([]byte, string) []byte); ok {
		r0 = rf(payload, signature)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func([]byte, string) error); ok {
		r1 = rf(payload, signature)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewPayment creates a new instance of Payment. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewPayment(t interface {
	mock.TestingT
	Cleanup(func())
}) *Payment {
	mock := &Payment{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
