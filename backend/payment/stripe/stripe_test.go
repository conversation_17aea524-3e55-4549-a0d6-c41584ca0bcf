//go:build integration
// +build integration

package stripeclient

import (
	"os"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/vima-inc/derental/models"
)

func TestAuthorize(t *testing.T) {
	key := os.Getenv("STRIPE_API_KEY")
	webhook := os.Getenv("STRIPE_WEBHOOK_SECRET")

	taxRates := map[models.Currency]map[models.Address]string{
		models.USD: {
			models.Address{State: "Arizona", CountryCode: "US"}:    "txr_1PT6GXCsESVKI3VoMSQJMPA8",
			models.Address{State: "Nevada", CountryCode: "US"}:     "txr_1PT6FwCsESVKI3VoXXGEFOmP",
			models.Address{State: "California", CountryCode: "US"}: "txr_1PT6BICsESVKI3VofRwrmKkL",
			models.Address{State: "Texas", CountryCode: "US"}:      "txr_1PWhoBCsESVKI3Vo97FxP6q1",
		},
		models.SAR: {
			models.Address{CountryCode: "SA"}: "txr_1QWfVcCsESVKI3VogWc6H8bX",
		},
	}

	c := New(key, webhook, "https://example.com/renterManagementPortal", taxRates)

	customer, err := c.CreateCustomer("test1", "<EMAIL>", models.Address{
		State:       "California",
		ZipCode:     "92108",
		CountryCode: "US",
	})
	require.NoError(t, err)
	require.NotEmpty(t, customer)

	coupon, err := c.CreateCoupon("COUPON1", 10, "USD", "forever")
	require.NoError(t, err)
	require.NotEmpty(t, coupon)

	// Cleanup resources after test
	t.Cleanup(func() {
		if err := c.DeleteCoupon(coupon); err != nil {
			t.Logf("Failed to delete coupon: %v", err)
		}
	})

	order := models.Order{
		CustomerID: customer,
		Items: []models.Item{
			{
				Name:      "Product 1",
				Price:     1000,
				Quantity:  1,
				IsTaxable: true,
				Coupon:    coupon,
			},
			{
				Name:      "Limited Damage Waiver",
				Price:     1000,
				Quantity:  1,
				IsTaxable: true,
			},
		},
		Currency: models.SAR,
		UserID:   "user123",
		Address: models.Address{
			State:       "Texas",
			ZipCode:     "92108",
			CountryCode: "SA",
		},
	}

	successURL := "https://example.com/success"
	cancelURL := "https://example.com/cancel"
	autoCapture := true

	url, err := c.Authorize(order, successURL, cancelURL, autoCapture)
	require.NoError(t, err)
	require.NotEmpty(t, url)
	t.Logf("Authorize URL: %s", url)

	require.Contains(t, url, "https://checkout.stripe.com/")
}
