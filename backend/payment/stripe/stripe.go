package stripeclient

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/stripe/stripe-go/v79"
	"github.com/stripe/stripe-go/v79/checkout/session"
	"github.com/stripe/stripe-go/v79/coupon"
	"github.com/stripe/stripe-go/v79/customer"
	"github.com/stripe/stripe-go/v79/paymentintent"
	"github.com/stripe/stripe-go/v79/promotioncode"
	"github.com/stripe/stripe-go/v79/webhook"

	"github.com/vima-inc/derental/models"
)

type client struct {
	webhookSigningSecret string
	baseURL              string
	taxRates             map[models.Currency]map[models.Address]string
}

// New creates a new Stripe client
func New(key string, webhookSigningSecret string, baseURL string, taxRates map[models.Currency]map[models.Address]string) *client {
	stripe.Key = key
	return &client{
		baseURL:              baseURL,
		webhookSigningSecret: webhookSigningSecret,
		taxRates:             taxRates,
	}
}

// CreateCustomer creates a new customer in Stripe
func (c *client) CreateCustomer(name, email string, address models.Address) (string, error) {
	params := &stripe.CustomerParams{
		Name:  stripe.String(name),
		Email: stripe.String(email),
		Shipping: &stripe.CustomerShippingParams{
			Name: stripe.String(address.Name),
			Address: &stripe.AddressParams{
				State:      stripe.String(address.State),
				PostalCode: stripe.String(address.ZipCode),
				Country:    stripe.String(address.CountryCode),
			},
		},
	}

	customer, err := customer.New(params)
	if err != nil {
		return "", fmt.Errorf("unable to create customer: %w", err)
	}

	return customer.ID, nil
}

// Authorize creates an authorization for the given order
func (c *client) Authorize(order models.Order, successURL string, cancelURL string, autoCapture bool) (string, error) {
	lineItems := make([]*stripe.CheckoutSessionLineItemParams, 0, len(order.Items))

	var taxBehavior stripe.TaxCalculationLineItemTaxBehavior
	for _, item := range order.Items {
		taxBehavior = stripe.TaxCalculationLineItemTaxBehaviorInclusive
		if item.IsTaxable {
			taxBehavior = stripe.TaxCalculationLineItemTaxBehaviorExclusive
		}

		var coupon *models.Coupon
		var err error

		if item.Coupon != "" {
			coupon, err = c.GetCoupon(item.Coupon)
			if err != nil {
				return "", fmt.Errorf("unable to retrieve coupon: %w", err)
			}
		}

		amount := item.Price
		if coupon != nil {
			amount = amount * int64(100-coupon.PercentOff) / 100
		}

		lineItem := &stripe.CheckoutSessionLineItemParams{
			PriceData: &stripe.CheckoutSessionLineItemPriceDataParams{
				ProductData: &stripe.CheckoutSessionLineItemPriceDataProductDataParams{
					Name: stripe.String(item.Name),
				},
				Currency:    stripe.String(string(order.Currency)),
				UnitAmount:  stripe.Int64(amount),
				TaxBehavior: stripe.String(string(taxBehavior)),
			},
			Quantity: stripe.Int64(item.Quantity),
		}

		if (order.Currency == models.USD || order.Currency == models.SAR) && item.IsTaxable {
			taxRates := c.taxRates[order.Currency]
			for address, taxRate := range taxRates {
				if address.CountryCode == "US" {
					if address.State == order.Address.State {
						lineItem.TaxRates = append(lineItem.TaxRates, stripe.String(taxRate))
					}
				} else {
					lineItem.TaxRates = append(lineItem.TaxRates, stripe.String(taxRate))
				}
			}
		}

		lineItems = append(lineItems, lineItem)
	}

	paymentMode := stripe.PaymentIntentCaptureMethodAutomatic
	if !autoCapture {
		paymentMode = stripe.PaymentIntentCaptureMethodManual
	}

	isAutomaticTax := order.Currency != models.USD && order.Currency != models.SAR
	params := &stripe.CheckoutSessionParams{
		Customer:           stripe.String(order.CustomerID),
		SuccessURL:         stripe.String(fmt.Sprintf("%s/renterManagementPortal?stripe=true", c.baseURL)),
		CancelURL:          stripe.String(c.baseURL),
		PaymentMethodTypes: stripe.StringSlice([]string{"card"}),
		LineItems:          lineItems,
		Mode:               stripe.String(string(stripe.CheckoutSessionModePayment)),
		PaymentIntentData: &stripe.CheckoutSessionPaymentIntentDataParams{
			Metadata: map[string]string{
				"order_id": order.ID,
			},
			CaptureMethod: stripe.String(string(paymentMode)),
		},
		AutomaticTax: &stripe.CheckoutSessionAutomaticTaxParams{Enabled: &isAutomaticTax},
	}

	session, err := session.New(params)
	if err != nil {
		return "", fmt.Errorf("unable to create checkout session: %w", err)
	}

	return session.URL, nil
}

// Capture captures the payment for the given order.
func (c *client) Capture(paymentIntentID string) error {
	_, err := paymentintent.Capture(paymentIntentID, nil)
	if err != nil {
		return fmt.Errorf("unable to capture payment: %w", err)
	}

	return nil
}

// Cancel cancels the authorization for the given order.
func (c *client) Cancel(paymentIntentID string) error {
	_, err := paymentintent.Cancel(paymentIntentID, nil)
	if err != nil {
		return fmt.Errorf("unable to cancel payment: %w", err)
	}

	return nil
}

// SignWebhookPayload verifies the signature of a webhook payload and returns the raw payload.
func (c *client) SignWebhookPayload(payload []byte, signature string) ([]byte, error) {
	event, err := webhook.ConstructEvent(payload, signature, c.webhookSigningSecret)
	if err != nil {
		return nil, fmt.Errorf("unable to construct event: %w", err)
	}

	return event.Data.Raw, nil
}

// ChargeComplete handles a charge complete webhook.
func (c *client) ChargeComplete(payload []byte, signature string) (models.ChargeResponse, error) {
	event, err := webhook.ConstructEvent(payload, signature, c.webhookSigningSecret)
	if err != nil {
		return models.ChargeResponse{}, fmt.Errorf("unable to construct event: %w", err)
	}

	if event.Type != "charge.succeeded" {
		return models.ChargeResponse{}, nil
	}

	var charge stripe.Charge
	err = json.Unmarshal(event.Data.Raw, &charge)
	if err != nil {
		return models.ChargeResponse{}, fmt.Errorf("unable to unmarshal event data: %w", err)
	}

	orderID := charge.Metadata["order_id"]
	if orderID == "" {
		return models.ChargeResponse{}, fmt.Errorf("order ID not found")
	}

	return models.ChargeResponse{
		OrderID:         orderID,
		PaymentIntentID: charge.PaymentIntent.ID,
	}, nil
}

// ChargeComplete handles a charge complete webhook.
func (c *client) CheckoutComplete(payload []byte, signature string) (models.CheckoutResponse, error) {
	event, err := webhook.ConstructEvent(payload, signature, c.webhookSigningSecret)
	if err != nil {
		return models.CheckoutResponse{}, fmt.Errorf("unable to construct event: %w", err)
	}

	if event.Type != "checkout.session.completed" {
		return models.CheckoutResponse{}, nil
	}

	var charge stripe.CheckoutSession

	err = json.Unmarshal(event.Data.Raw, &charge)
	if err != nil {
		return models.CheckoutResponse{}, fmt.Errorf("unable to unmarshal event data: %w", err)
	}

	return models.CheckoutResponse{
		PaymentIntentID: charge.PaymentIntent.ID,
		Amount:          charge.AmountSubtotal,
		PaidAmount:      charge.AmountTotal,
		DiscountAmount:  charge.TotalDetails.AmountDiscount,
		TaxAmount:       charge.TotalDetails.AmountTax,
	}, nil
}

// CreateCoupon creates a new coupon in Stripe
func (c *client) CreateCoupon(code string, percentOff float64, currency string, duration string) (string, error) {
	params := &stripe.CouponParams{
		Name:       stripe.String(code),
		PercentOff: stripe.Float64(percentOff),
		Currency:   stripe.String(strings.ToLower(currency)),
		Duration:   stripe.String(duration),
	}

	coupon, err := coupon.New(params)
	if err != nil {
		return "", fmt.Errorf("unable to create coupon: %w", err)
	}

	return coupon.ID, nil
}

// GetCoupon retrieves a coupon from Stripe by its ID
func (c *client) GetCoupon(code string) (*models.Coupon, error) {
	coupon, err := coupon.Get(code, nil)
	if err != nil {
		return nil, fmt.Errorf("unable to retrieve coupon: %w", err)
	}

	return &models.Coupon{
		ID:         coupon.ID,
		Name:       coupon.Name,
		AmountOff:  coupon.AmountOff,
		PercentOff: coupon.PercentOff,
		Currency:   string(coupon.Currency),
	}, nil
}

// CreatePromotionCode creates a new promotion code in Stripe
func (c *client) CreatePromotionCode(coupon string, customer string) (string, error) {
	params := &stripe.PromotionCodeParams{
		Coupon: stripe.String(coupon),
	}

	promotion, err := promotioncode.New(params)
	if err != nil {
		return "", fmt.Errorf("unable to create coupon: %w", err)
	}

	return promotion.ID, nil
}

// DeletePromotionCode deletes a promotion code in Stripe
func (c *client) DeleteCoupon(id string) error {
	_, err := coupon.Del(id, nil)
	if err != nil {
		return fmt.Errorf("unable to delete coupon: %w", err)
	}

	return nil
}
