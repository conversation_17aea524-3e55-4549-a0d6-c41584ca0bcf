Project_ID = dev-Derental
Tooler_API = derental
Tooler_Equipment_Uploader = tooler-equipment-uploader
Region = us-central1
GCR = us-central1-docker.pkg.dev
Platform = linux/amd64
STRIPE_PUBSUB_TOPIC_NAME=stripe-webhook
STRIPE_PUBSUB_SUBSCRIPTION_NAME=stripe-webhook-subscription
STRIPE_PUSH_ENDPOINT_URI=https://derental-api-dxfkgjmkqa-uc.a.run.app/public/order/notify


PUBSUB_TOPIC_NAME=equipments-upload
PUBSUB_SUBSCRIPTION_NAME=equipments-upload-subscription

build:
	docker buildx build --platform $(Platform) -t $(GCR)/$(Project_ID)/tooler -f ./cmd/tooler-api/Dockerfile .

push:
	docker push $(GCR)/$(Project_ID)/tooler

deploy: build push
	gcloud run deploy tooler --image=$(GCR)/$(Project_ID)/tooler --platform managed --region $(Region) \
		--set-env-vars FIREBASE_API_KEY=${FIREBASE_API_KEY} \
		--set-env-vars FIREBASE_STORAGE_BUCKET=${FIREBASE_STORAGE_BUCKET} \
		--set-env-vars MODE=production \
		--set-env-vars SENDGRID_API_KEY=${SENDGRID_API_KEY} \
		--set-env-vars SENDGRID_SENDER=${SENDGRID_SENDER} \
		--set-env-vars SENDGRID_FORGOT_PASSWORD_TEMPLATE_ID=${SENDGRID_FORGOT_PASSWORD_TEMPLATE_ID} \
		--set-env-vars SLACK_WEBHOOK_URL=${SLACK_WEBHOOK_URL}

seeddb:
	go run seed/main.go

env:
	export $(grep -v '^#' .env | xargs)

# ARA Classifier CLI tool
ara-classifier:
	go build -o bin/ara-classifier ./cmd/ara-classifier

ara-classifier-stats:
	go run ./cmd/ara-classifier --stats

ara-classifier-dry-run:
	go run ./cmd/ara-classifier --all --dry-run

ara-classifier-run:
	go run ./cmd/ara-classifier --all

deploy-equipment-uploader:
	docker buildx build --platform $(Platform) -t $(GCR)/$(Project_ID)/$(Tooler_Equipment_Uploader) -f ./cmd/tooler-equipment-uploader/Dockerfile .
	docker push $(GCR)/$(Project_ID)/$(Tooler_Equipment_Uploader):latest
	gcloud run deploy $(Tooler_Equipment_Uploader) --image=$(GCR)/$(Project_ID)/$(Tooler_Equipment_Uploader) --platform managed --region $(Region) \
		--set-env-vars MODE=production \
		--set-env-vars FIREBASE_STORAGE_BUCKET=${FIREBASE_STORAGE_BUCKET} \
		--set-env-vars SLACK_WEBHOOK_URL=${SLACK_WEBHOOK_URL}

.PHONY: stripe-pubsub

stripe-pubsub:
	@gcloud pubsub topics list | grep ${STRIPE_PUBSUB_TOPIC_NAME} || gcloud pubsub topics create ${STRIPE_PUBSUB_TOPIC_NAME}
	@gcloud pubsub topics list | grep ${STRIPE_PUBSUB_TOPIC_NAME}-dead-letter || gcloud pubsub topics create ${STRIPE_PUBSUB_TOPIC_NAME}-dead-letter
	@gcloud pubsub subscriptions list | grep ${STRIPE_PUBSUB_SUBSCRIPTION_NAME} || gcloud pubsub subscriptions create ${STRIPE_PUBSUB_SUBSCRIPTION_NAME} \
		--topic=${STRIPE_PUBSUB_TOPIC_NAME} \
		--push-endpoint=${STRIPE_PUSH_ENDPOINT_URI} \
		--push-no-wrapper \
		--ack-deadline=600 \
		--message-retention-duration=7d \
		--expiration-period=never \
		--enable-message-ordering \
		--min-retry-delay=10s \
		--max-retry-delay=600s \
		--dead-letter-topic=${STRIPE_PUBSUB_TOPIC_NAME}-dead-letter \
		--max-delivery-attempts=5



ENV_CONFIGS = \
	dev:dev-derental.appspot.com:https://tooler-equipment-uploader-331701047664.us-central1.run.app \
	preprod:preprod-derental.appspot.com:https://tooler-equipment-uploader-1088850275970.us-central1.run.app \
	demos:demos-derental.appspot.com:https://tooler-equipment-uploader-456577859241.us-central1.run.app \
	#prod:prod-derental.appspot.com:https://tooler-equipment-uploader-1064773213419.us-central1.run.app \

ENV_LIST = $(foreach conf,$(subst $(newline), ,$(ENV_CONFIGS)),$(word 1,$(subst :, ,$(conf))))


list-envs:
	@echo "Environnements disponibles:"
	@$(foreach env,$(ENV_LIST),echo "  - $(env)";)

list-storage-pubsub:
	@$(foreach env,$(ENV_LIST),\
		$(eval ENV_CONFIG := $(filter $(env):%,$(ENV_CONFIGS)))\
		$(eval FIREBASE_STORAGE_BUCKET := $(word 2,$(subst :, ,$(ENV_CONFIG))))\
		echo "### Liste des notifications pour $(env)..." && \
		gsutil notification list gs://${FIREBASE_STORAGE_BUCKET} && \
		echo "### Liste terminée pour $(env)";)

delete-storage-pubsub:
	@echo "Suppression des notifications pour l'environnement: $(subst env-,,$@)"
	@$(foreach env,$(ENV_LIST),\
		$(eval ENV_CONFIG := $(filter $(env):%,$(ENV_CONFIGS)))\
		$(eval FIREBASE_STORAGE_BUCKET := $(word 2,$(subst :, ,$(ENV_CONFIG))))\
		gsutil notification delete gs://${FIREBASE_STORAGE_BUCKET} && \
		echo "Suppression terminée pour $(env)";)

storage-pubsub:
	@echo "Configuration des notifications pour tous les environnements:"
	@$(foreach env,$(ENV_LIST),\
		$(eval ENV_CONFIG := $(filter $(env):%,$(ENV_CONFIGS)))\
		$(eval FIREBASE_STORAGE_BUCKET := $(word 2,$(subst :, ,$(ENV_CONFIG))))\
		$(eval URL_PARTS := $(subst :, ,$(ENV_CONFIG)))\
		$(eval PUSH_ENDPOINT_URI := https:$(word 4,$(URL_PARTS)))\
		echo "PUSH_ENDPOINT_URI: $(PUSH_ENDPOINT_URI)" && \
		echo "### Configuration pour $(env)-Derental..." && \
		gcloud config set project $(env)-derental && \
		echo "### Suppression des notifications existantes..." && \
		gsutil notification delete gs://${FIREBASE_STORAGE_BUCKET} && \
		echo "### Création du topic..." && \
		gcloud pubsub topics list | grep ${PUBSUB_TOPIC_NAME} || gcloud pubsub topics create ${PUBSUB_TOPIC_NAME} && \
		echo "### Création du topic DLQ..." && \
		gcloud pubsub topics list | grep ${PUBSUB_TOPIC_NAME}-dead-letter || gcloud pubsub topics create ${PUBSUB_TOPIC_NAME}-dead-letter && \
		echo "### Création de la notification..." && \
		gsutil notification create -t ${PUBSUB_TOPIC_NAME} -f json gs://${FIREBASE_STORAGE_BUCKET} && \
		echo "### Suppression de la subscription existante..." && \
		gcloud pubsub subscriptions list | grep ${PUBSUB_SUBSCRIPTION_NAME} && gcloud pubsub subscriptions delete ${PUBSUB_SUBSCRIPTION_NAME} || true && \
		echo "### Suppression de la subscription DLQ..." && \
		gcloud pubsub subscriptions list | grep ${PUBSUB_SUBSCRIPTION_NAME}-dlq && gcloud pubsub subscriptions delete ${PUBSUB_SUBSCRIPTION_NAME}-dlq || true && \
		echo "### Création de la subscription DLQ..." && \
		gcloud pubsub subscriptions create ${PUBSUB_SUBSCRIPTION_NAME}-dlq \
			--topic=${PUBSUB_TOPIC_NAME}-dead-letter \
			--push-endpoint=${PUSH_ENDPOINT_URI} \
			--push-no-wrapper \
			--ack-deadline=600 \
			--message-retention-duration=7d \
			--expiration-period=never \
			--enable-message-ordering \
			--min-retry-delay=10s \
			--max-retry-delay=600s && \
		echo "### Création de la subscription..." && \
		gcloud pubsub subscriptions create ${PUBSUB_SUBSCRIPTION_NAME} \
			--topic=${PUBSUB_TOPIC_NAME} \
			--push-endpoint=${PUSH_ENDPOINT_URI} \
			--ack-deadline=600 \
			--message-retention-duration=7d \
			--expiration-period=never \
			--enable-message-ordering \
			--min-retry-delay=10s \
			--max-retry-delay=600s \
			--dead-letter-topic=${PUBSUB_TOPIC_NAME}-dead-letter \
			--max-delivery-attempts=5 && \
		echo "Configuration terminée pour $(env)";)
