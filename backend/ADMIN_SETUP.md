# Admin User Setup Guide

This guide explains how to create admin users for the Derental admin portal.

## Prerequisites

1. **Firebase Service Account Key**: Download your Firebase service account key and save it as `key.json` in the backend directory.
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Select your project → Project Settings → Service Accounts
   - Click "Generate new private key"
   - Save the downloaded JSON file as `key.json` in the backend directory

2. **Go Environment**: Ensure Go is installed and the project dependencies are available.

## Method 1: Using the Shell Script (Recommended)

The easiest way to create an admin user:

```bash
# From the backend directory
./scripts/create-admin.sh
```

The script will:
- Prompt you for an email address (must end with @derentalequipment.com)
- Prompt you for a password (minimum 6 characters)
- Create the user in Firebase Authentication
- Handle existing users by offering to update passwords

## Method 2: Using the Go Command Directly

```bash
# From the backend directory
cd cmd/create-admin-user
go run main.go -email=<EMAIL> -password=yourpassword
```

### Options:
- `-email`: Admin email address (required, must end with @derentalequipment.com)
- `-password`: Admin password (required, minimum 6 characters)
- `-config`: Path to config file (optional)

## Method 3: Using Firebase Console

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Navigate to **Authentication** → **Users**
4. Click **"Add user"**
5. Enter email: `<EMAIL>`
6. Set a secure password
7. Click **"Add user"**

## Verification

After creating the admin user, you can verify it works by:

1. Starting your application (frontend and backend)
2. Navigating to `/admin/login`
3. Signing in with the created credentials
4. You should be redirected to the admin dashboard

## Security Notes

- Only emails ending with `@derentalequipment.com` can access the admin area
- Use strong passwords for admin accounts
- Consider implementing additional security measures for production:
  - Multi-factor authentication
  - IP restrictions
  - Session timeouts
  - Audit logging

## Troubleshooting

### "key.json not found"
- Ensure your Firebase service account key is saved as `key.json` in the backend directory
- Download it from Firebase Console → Project Settings → Service Accounts

### "Authentication failed"
- Verify the user was created in Firebase Authentication (not just Firestore)
- Check that the email domain is exactly `@derentalequipment.com`
- Ensure the password meets Firebase requirements (minimum 6 characters)

### "Access restricted to @derentalequipment.com domain"
- The email must end exactly with `@derentalequipment.com`
- No other domains are allowed for admin access

## Example

```bash
$ ./scripts/create-admin.sh

🔧 Derental Admin User Creation Tool
====================================

Enter admin email (must end with @derentalequipment.com): <EMAIL>

Enter password (minimum 6 characters): [hidden]

Creating admin user...
✅ Created new user with UID: abc123def456
✅ Successfully created admin user: <EMAIL>

🎉 Admin user created successfully!
You can now sign in at: /admin/login
Email: <EMAIL>
```
