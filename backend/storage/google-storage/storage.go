package googlestorage

import (
	"context"
	"fmt"
	"io"
	"time"

	"cloud.google.com/go/storage"

	isotrage "github.com/vima-inc/derental/storage"
)

type googleStorage struct {
	client        *storage.Client
	defaultBucket string
}

// New creates a new google storage client.
func New(ctx context.Context, bucket string) (isotrage.Storage, error) {
	client, err := storage.NewClient(ctx)
	if err != nil {
		return nil, fmt.Errorf("unable to create google storage client: %w", err)
	}

	return &googleStorage{
		client:        client,
		defaultBucket: bucket,
	}, err
}

// DefaultBucket returns the default bucket.
func (s *googleStorage) DefaultBucket() string {
	return s.defaultBucket
}

// Read reads a file from google storage.
func (s *googleStorage) Read(ctx context.Context, bucket string, path string) (io.ReadCloser, error) {
	buck := s.client.Bucket(bucket)

	reader, err := buck.Object(path).NewReader(ctx)
	if err != nil {
		return nil, fmt.Errorf("unable to read file from google storage: %w", err)
	}

	return reader, nil
}

// ReadWithMetadata reads a file from google storage with metadata.
func (s *googleStorage) ReadWithMetadata(ctx context.Context, bucket string, path string) (io.ReadCloser, isotrage.Metadata, error) {
	buck := s.client.Bucket(bucket)

	reader, err := buck.Object(path).NewReader(ctx)
	if err != nil {
		return nil, nil, fmt.Errorf("unable to read file from google storage: %w", err)
	}

	// Get the metadata for the object
	attrs, err := buck.Object(path).Attrs(ctx)
	if err != nil {
		return nil, nil, fmt.Errorf("unable to get object attributes: %w", err)
	}

	metadata := isotrage.Metadata{}

	for k, v := range attrs.Metadata {
		metadata[k] = v
	}

	return reader, metadata, nil
}

// SignedURL returns a signed url for a file in google storage.
func (s *googleStorage) SignedURL(ctx context.Context, bucket string, path string) (string, error) {
	opts := &storage.SignedURLOptions{
		Scheme:  storage.SigningSchemeV4,
		Method:  "GET",
		Expires: time.Now().Add(24 * time.Hour),
	}

	u, err := s.client.Bucket(bucket).SignedURL(path, opts)
	if err != nil {
		return "", fmt.Errorf("unable to get signed for file bucket:%s path:%s : %w", bucket, path, err)
	}

	return u, nil
}

// Write writes a file to google storage.
func (s *googleStorage) Write(ctx context.Context, bucket string, path string, data io.Reader) (string, error) {
	buck := s.client.Bucket(bucket)

	obj := buck.Object(path)

	w := obj.NewWriter(ctx)
	if _, err := io.Copy(w, data); err != nil {
		return "", fmt.Errorf("unable to copy data to google storage: %w", err)
	}

	if err := w.Close(); err != nil {
		return "", fmt.Errorf("unable to close google storage writer: %w", err)
	}

	objAttrs, err := obj.Attrs(ctx)
	if err != nil {
		return "", fmt.Errorf("unable to read attributes from google storage: %w", err)
	}

	return objAttrs.MediaLink, nil
}

// Delete deletes a file from google storage.
func (s *googleStorage) Delete(ctx context.Context, bucket string, path string) error {
	buck := s.client.Bucket(bucket)

	obj := buck.Object(path)

	err := obj.Delete(ctx)
	if err != nil {
		return fmt.Errorf("unable to delete file from google storage: %w", err)
	}

	return nil
}
