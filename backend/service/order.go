package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"

	"github.com/vima-inc/derental/models"
)

// Service represents the order service
func (s *Service) CreateOrder(ctx context.Context, order models.Order, successURL, cancelURL string) (string, error) {
	if order.ID == "" {
		order.ID = uuid.New().String()
	}

	autoCapture := false

	url, err := s.payment.Authorize(order, successURL, cancelURL, autoCapture)
	if err != nil {
		return "", fmt.Errorf("unable to create checkout session: %w", err)
	}

	order.Status = models.OrderStatusPending
	order.CreatedAt = time.Now()
	order.UpdatedAt = time.Now()

	err = s.db.AddOrder(ctx, order)
	if err != nil {
		return "", fmt.Errorf("unable to create order: %w", err)
	}

	return url, nil
}

// Service represents the order service
func (s *Service) CreateBidzOrder(ctx context.Context, order models.Order, successURL, cancelURL string) (string, error) {
	if order.ID == "" {
		order.ID = uuid.New().String()
	}

	autoCapture := true

	url, err := s.payment.Authorize(order, successURL, cancelURL, autoCapture)
	if err != nil {
		return "", fmt.Errorf("unable to create checkout session: %w", err)
	}

	order.Status = models.OrderStatusCaptured
	order.CreatedAt = time.Now()
	order.UpdatedAt = time.Now()
	order.IsBidz = true

	err = s.db.AddOrder(ctx, order)
	if err != nil {
		return "", fmt.Errorf("unable to create order: %w", err)
	}

	return url, nil
}

// CaptureOrder captures an order.
func (s *Service) CaptureOrder(ctx context.Context, order models.Order) error {
	err := s.payment.Capture(order.Payment.PaymentIntentID)
	if err != nil {
		return fmt.Errorf("unable to capture payment: %w", err)
	}

	order.Status = models.OrderStatusCaptured
	order.UpdatedAt = time.Now()

	err = s.db.UpdateOrder(ctx, order)
	if err != nil {
		return fmt.Errorf("unable to update order: %w", err)
	}

	return nil
}

// CancelOrder cancels an order.
func (s *Service) CancelOrder(ctx context.Context, order models.Order) error {
	err := s.payment.Cancel(order.Payment.PaymentIntentID)
	if err != nil {
		return fmt.Errorf("unable to cancel payment: %w", err)
	}

	order.Status = models.OrderStatusCancelled
	order.UpdatedAt = time.Now()

	err = s.db.UpdateOrder(ctx, order)
	if err != nil {
		return fmt.Errorf("unable to update order: %w", err)
	}

	return nil
}

// HandleWebhook handles a Stripe webhook
func (s *Service) HandleWebhook(body []byte, sigHeader string) error {
	_, err := s.payment.SignWebhookPayload(body, sigHeader)
	if err != nil {
		return fmt.Errorf("unable to sign webhook payload: %w", err)
	}

	webhookEvent := models.WebhookEvent{
		Signature: sigHeader,
		Payload:   body,
	}

	b, err := json.Marshal(webhookEvent)
	if err != nil {
		return fmt.Errorf("unable to marshal webhook event: %w", err)
	}

	err = s.broker.Publish(context.Background(), stripePubSubTopic, b)
	if err != nil {
		return fmt.Errorf("unable to publish to pubsub: %w", err)
	}

	return nil
}

// HandlePubSub handles a Stripe pubsub event
func (s *Service) HandlePubSub(body []byte, sigHeader string) error {
	err := s.handleCharge(body, sigHeader)
	if err != nil {
		return err
	}

	err = s.handleCheckout(body, sigHeader)
	if err != nil {
		return err
	}

	return nil
}

func (s *Service) handleCharge(body []byte, sigHeader string) error {
	ctx := context.Background()

	charge, err := s.payment.ChargeComplete(body, sigHeader)
	if err != nil {
		return fmt.Errorf("unable to handle charge succeeded: %w", err)
	}

	if charge.OrderID == "" {
		return nil
	}

	order, err := s.db.GetOrderByID(ctx, charge.OrderID)
	if err != nil {
		log.Printf("unable to get order: %v", err)

		return fmt.Errorf("unable to get order: %w", err)
	}

	order.Payment.PaymentIntentID = charge.PaymentIntentID
	order.Status = models.OrderStatusAuthorized

	order.UpdatedAt = time.Now()

	err = s.db.UpdateOrder(ctx, order)
	if err != nil {
		log.Printf("unable to update order: %v", err)
		return fmt.Errorf("unable to update order: %w", err)
	}

	return nil
}

// HandleWebhook handles a Stripe webhook
func (s *Service) handleCheckout(body []byte, sigHeader string) error {
	ctx := context.Background()

	charge, err := s.payment.CheckoutComplete(body, sigHeader)
	if err != nil {
		return fmt.Errorf("unable to handle checkout session completed: %w", err)
	}

	if charge.PaymentIntentID == "" {
		return nil
	}

	order, err := s.db.GetOrderByPaymentIntentID(ctx, charge.PaymentIntentID)
	if err != nil {
		log.Printf("unable to get order: %v", err)

		return fmt.Errorf("unable to get order: %w", err)
	}

	var totalDiscount int64
	var couponPercentOff float64
	for _, item := range order.Items {
		if item.Coupon == "" {
			continue
		}

		coupon, err := s.payment.GetCoupon(item.Coupon)
		if err != nil {
			log.Printf("unable to get coupon for item %v: %v", item, err)

			return fmt.Errorf("unable to get coupon for item %v: %w", item, err)
		}

		totalDiscount += int64(item.Price) * int64(item.Quantity) * int64(coupon.PercentOff) / 100
		couponPercentOff = coupon.PercentOff
	}

	order.Payment.Amount = charge.Amount
	order.Payment.AmountCaptured = charge.PaidAmount
	order.Payment.Discount = totalDiscount
	order.Payment.Tax = charge.TaxAmount
	order.UpdatedAt = time.Now()

	err = s.db.UpdateOrder(ctx, order)
	if err != nil {
		log.Printf("unable to update order: %v", err)
		return fmt.Errorf("unable to update order: %w", err)
	}

	booking, err := s.db.GetBookEquipmentByID(ctx, order.BookingID)
	if err != nil {
		log.Printf("unable to get booking: %v", err)

		return fmt.Errorf("unable to get booking: %w", err)
	}

	booking.Status = models.BookingPending
	booking.Amount = float64(order.Payment.Amount) / 100
	booking.PaidAmount = float64(order.Payment.AmountCaptured) / 100
	booking.DiscountAmount = float64(totalDiscount) / 100
	booking.DiscountRate = couponPercentOff / 100
	booking.UpdatedAt = time.Now()
	booking.TaxAmount = float64(charge.TaxAmount) / 100
	booking.TaxRate = (booking.PaidAmount/booking.Amount - 1) * 100
	booking.TotalAmount = booking.PaidAmount

	err = s.db.UpdateBookEquipment(ctx, booking)
	if err != nil {
		log.Printf("unable to update booking: %v", err)

		return fmt.Errorf("unable to update booking: %w", err)
	}

	equipper, err := s.db.GetEquipperByID(ctx, booking.EquipperID)
	if err != nil {
		log.Printf("unable to get equipper: %v", err)

		return fmt.Errorf("unable to get equipper: %w", err)
	}

	lodger, err := s.db.GetLodgerByID(ctx, booking.LodgerID)
	if err != nil {
		log.Printf("unable to get lodger: %v", err)

		return fmt.Errorf("unable to get lodger: %w", err)
	}

	s.sendBookingRequestEmails(ctx, equipper, lodger, booking)

	return nil
}
