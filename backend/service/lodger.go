package service

import (
	"context"
	"fmt"
	"io"
	"path/filepath"

	"github.com/vima-inc/derental/models"
)

// GetLodgerByID returns a lodger by id.
func (s *Service) GetLodgerByID(ctx context.Context, id string) (models.Lodger, error) {
	return s.db.GetLodgerByID(ctx, id)
}

// AddLodger adds a new lodger.
func (s *Service) AddLodger(ctx context.Context, lodger models.Lodger) error {
	return s.db.AddLodger(ctx, lodger)
}

// UpdateLodger updates an existing lodger.
func (s *Service) UpdateLodger(ctx context.Context, lodger models.Lodger) error {
	dbLodger, err := s.db.GetLodgerByID(ctx, lodger.ID)
	if err != nil {
		return fmt.Errorf("error getting lodger: %w", err)
	}

	if dbLodger.Email != lodger.Email {
		err := s.auth.UpdateEmail(ctx, lodger.ID, lodger.Email)
		if err != nil {
			return fmt.Errorf("error updating email: %w", err)
		}
	}

	if dbLodger.MemberOf != "" {
		member, err := s.GetMemberByID(ctx, dbLodger.MemberID, dbLodger.MemberOf)
		if err != nil {
			return fmt.Errorf("can not get member by id %s error %w", member.ID, err)
		}

		member.PhoneNumber = lodger.PhoneNumber
		if lodger.Company != "" {
			member.FirstName = lodger.Company
		}

		member.FirstName = lodger.FirstName
		member.Email = lodger.Email
		member.LastName = lodger.LastName
		member.Projects = lodger.Projects
		lodger.MemberID = dbLodger.MemberID
		lodger.MemberOf = dbLodger.MemberOf
		member.PhotoURL = lodger.PhotoURL

		err = s.UpdateMemberLodger(ctx, dbLodger.MemberOf, dbLodger.MemberID, member)
		if err != nil {
			return fmt.Errorf("can not update member with id %s error %w", member.ID, err)
		}
	}

	return s.db.UpdateLodger(ctx, lodger)
}

// GetAllLodgers fetch all the lodgers.
func (s *Service) GetAllLodgers(ctx context.Context) ([]models.Lodger, error) {
	return s.db.GetAllLodgers(ctx)
}

// GetAllLodgersByCountry fetch all the lodgers of the same country.
func (s *Service) GetAllLodgersByCountry(ctx context.Context, country string) ([]models.Lodger, error) {
	return s.db.GetAllLodgersByCountry(ctx, country)
}

// DeleteLodger deletes an existing lodger by id.
func (s *Service) DeleteLodger(ctx context.Context, id string) error {
	err := s.auth.DeleteUser(ctx, id)
	if err != nil {
		return fmt.Errorf("error deleting user: %w", err)
	}

	return s.db.DeleteLodger(ctx, id)
}

// UploadLodgerPhoto save Lodger photo in storage and update photo url with new path.
func (s *Service) UploadLodgerPhoto(ctx context.Context, equipperID string, fileName string, data io.Reader) error {
	path := fmt.Sprintf("lodger/%s%s", equipperID, filepath.Ext(fileName))

	photoURL, err := s.storage.Write(ctx, s.storage.DefaultBucket(), path, data)
	if err != nil {
		return fmt.Errorf("error uploading photo: %w", err)
	}

	lodger, err := s.db.GetLodgerByID(ctx, equipperID)
	if err != nil {
		return fmt.Errorf("error getting equipper: %w", err)
	}

	lodger.PhotoURL = photoURL

	err = s.db.UpdateLodger(ctx, lodger)
	if err != nil {
		return fmt.Errorf("error updating equipper: %w", err)
	}

	if lodger.MemberOf != "" || lodger.MemberID != "" {
		member, err := s.GetMemberByID(ctx, lodger.MemberID, lodger.MemberOf)
		if err != nil {
			return fmt.Errorf("can not get member by id error %w", err)
		}

		member.PhotoURL = photoURL

		err = s.UpdateMemberLodger(ctx, lodger.ID, lodger.MemberID, member)
		if err != nil {
			return fmt.Errorf("can not update member error %w", err)
		}
	}

	return nil
}
