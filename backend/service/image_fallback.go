package service

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"cloud.google.com/go/firestore"
	"github.com/vima-inc/derental/models"
)

// ImageFallbackService handles finding alternative images for equipment
type ImageFallbackService struct {
	client *firestore.Client
}

// NewImageFallbackService creates a new image fallback service
func NewImageFallbackService(client *firestore.Client) *ImageFallbackService {
	return &ImageFallbackService{
		client: client,
	}
}

// FindAlternativeImages finds working image URLs for equipment with problematic images
func (s *ImageFallbackService) FindAlternativeImages(ctx context.Context, equipment []models.EquipmentForClassification) ([]models.EquipmentForClassification, error) {
	var updatedEquipment []models.EquipmentForClassification

	for _, eq := range equipment {
		// Check if current image URL is accessible
		if eq.ImageURL != "" && s.isImageAccessible(eq.ImageURL) {
			updatedEquipment = append(updatedEquipment, eq)
			continue
		}

		// Find alternative image for this equipment
		alternativeImageURL, err := s.findAlternativeImageURL(ctx, eq.Name)
		if err != nil {
			log.Printf("Failed to find alternative image for %s: %v", eq.Name, err)
			// Keep original equipment but clear the problematic image URL
			eq.ImageURL = ""
			updatedEquipment = append(updatedEquipment, eq)
			continue
		}

		if alternativeImageURL != "" {
			log.Printf("Found alternative image for %s: %s", eq.Name, alternativeImageURL)
			eq.ImageURL = alternativeImageURL
		} else {
			// No alternative found, clear the image URL for text-only classification
			eq.ImageURL = ""
		}

		updatedEquipment = append(updatedEquipment, eq)
	}

	return updatedEquipment, nil
}

// isImageAccessible checks if an image URL is accessible
func (s *ImageFallbackService) isImageAccessible(imageURL string) bool {
	if imageURL == "" {
		return false
	}

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Head(imageURL)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK
}

// findAlternativeImageURL searches for equipment with similar names that have working images
func (s *ImageFallbackService) findAlternativeImageURL(ctx context.Context, equipmentName string) (string, error) {
	// Normalize the equipment name for searching
	normalizedName := s.normalizeEquipmentName(equipmentName)
	keywords := s.extractKeywords(normalizedName)

	if len(keywords) == 0 {
		return "", fmt.Errorf("no keywords extracted from equipment name: %s", equipmentName)
	}

	// Search for equipment with similar names in the catalog (equipments collection)
	log.Printf("Searching catalog for alternative image for: %s", equipmentName)
	iter := s.client.Collection("equipments").Documents(ctx)
	defer iter.Stop()

	var candidates []struct {
		name     string
		imageURL string
		score    int
	}

	for {
		doc, err := iter.Next()
		if err != nil {
			if err.Error() == "no more items in iterator" {
				break
			}
			return "", fmt.Errorf("error iterating catalog documents: %v", err)
		}

		data := doc.Data()

		// Get equipment name from catalog (try different field names)
		var candidateName string
		if nameEN, ok := data["name_en"].(string); ok && nameEN != "" {
			candidateName = nameEN
		} else if name, ok := data["name"].(string); ok && name != "" {
			candidateName = name
		} else if title, ok := data["title"].(string); ok && title != "" {
			candidateName = title
		} else if equipmentName, ok := data["equipment_name"].(string); ok && equipmentName != "" {
			candidateName = equipmentName
		} else {
			continue // Skip if no name found
		}

		// Skip if it's the same equipment
		if strings.EqualFold(candidateName, equipmentName) {
			continue
		}

		// Get image URL from catalog
		imageURL, ok := data["image_link"].(string)
		if !ok || imageURL == "" {
			continue
		}

		// Check if this image is accessible
		if !s.isImageAccessible(imageURL) {
			continue
		}

		// Calculate similarity score
		score := s.calculateSimilarityScore(normalizedName, keywords, candidateName)
		if score > 0 {
			candidates = append(candidates, struct {
				name     string
				imageURL string
				score    int
			}{
				name:     candidateName,
				imageURL: imageURL,
				score:    score,
			})
		}
	}

	// Find the best match
	if len(candidates) == 0 {
		return "", nil
	}

	bestCandidate := candidates[0]
	for _, candidate := range candidates[1:] {
		if candidate.score > bestCandidate.score {
			bestCandidate = candidate
		}
	}

	return bestCandidate.imageURL, nil
}

// normalizeEquipmentName normalizes equipment name for comparison
func (s *ImageFallbackService) normalizeEquipmentName(name string) string {
	// Convert to lowercase and remove extra spaces
	normalized := strings.ToLower(strings.TrimSpace(name))

	// Remove common suffixes/prefixes that might vary
	suffixes := []string{" tool", " equipment", " machine", " device"}
	for _, suffix := range suffixes {
		if strings.HasSuffix(normalized, suffix) {
			normalized = strings.TrimSuffix(normalized, suffix)
			break
		}
	}

	return normalized
}

// extractKeywords extracts important keywords from equipment name
func (s *ImageFallbackService) extractKeywords(name string) []string {
	// Split by common delimiters
	words := strings.FieldsFunc(name, func(c rune) bool {
		return c == ' ' || c == '-' || c == '_' || c == ',' || c == '.'
	})

	// Filter out common stop words
	stopWords := map[string]bool{
		"the": true, "a": true, "an": true, "and": true, "or": true,
		"but": true, "in": true, "on": true, "at": true, "to": true,
		"for": true, "of": true, "with": true, "by": true,
	}

	var keywords []string
	for _, word := range words {
		word = strings.ToLower(strings.TrimSpace(word))
		if len(word) > 2 && !stopWords[word] {
			keywords = append(keywords, word)
		}
	}

	return keywords
}

// calculateSimilarityScore calculates how similar two equipment names are
func (s *ImageFallbackService) calculateSimilarityScore(originalName string, originalKeywords []string, candidateName string) int {
	candidateNormalized := s.normalizeEquipmentName(candidateName)
	candidateKeywords := s.extractKeywords(candidateNormalized)

	score := 0

	// Exact match bonus
	if originalName == candidateNormalized {
		score += 100
	}

	// Keyword matching
	for _, originalKeyword := range originalKeywords {
		for _, candidateKeyword := range candidateKeywords {
			if originalKeyword == candidateKeyword {
				score += 10
			} else if strings.Contains(candidateKeyword, originalKeyword) || strings.Contains(originalKeyword, candidateKeyword) {
				score += 5
			}
		}
	}

	// Substring matching bonus
	if strings.Contains(candidateNormalized, originalName) || strings.Contains(originalName, candidateNormalized) {
		score += 15
	}

	return score
}
