package service

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/dustin/go-humanize"

	"github.com/google/uuid"
	"github.com/vima-inc/derental/mailer"
	"github.com/vima-inc/derental/models"
)

const (
	NotSpecified = "Not specified"
)

// SendBidzRequest sends an email to the lodger with the details of the request.
func (s *Service) SendBidzRequest(ctx context.Context, lodgerID string, input models.BidsRequest) error {
	lodger, err := s.db.GetLodgerByID(ctx, lodgerID)
	if err != nil {
		return fmt.Errorf("unable to get lodger with lodger_id %s error: %w", lodgerID, err)
	}

	input.LodgerName = lodger.Company
	input.LodgerImageLink = lodger.PhotoURL
	input.LodgerEmail = lodger.Email
	input.CreatedAt = time.Now()

	equippers, err := s.db.GetAllEquippersByCategoryCoverageArea(ctx, input.Category, input.CoverageArea)
	if err != nil {
		return fmt.Errorf("can not get all equippers, error: %w", err)
	}

	if len(equippers) == 0 {
		return fmt.Errorf("no equippers found : for category %s and coverage area %s", input.Category, input.CoverageArea)
	}

	adminIDs := []string{}

	if lodger.MemberOf != "" {
		input.OwnerID = lodger.MemberOf

		admins, err := s.db.GetAdminsByOwnerID(ctx, lodger.MemberOf)
		if err != nil {
			return fmt.Errorf("can not get admins by lodger_id %s, error: %w", lodger.MemberOf, err)
		}

		for _, admin := range admins {
			adminIDs = append(adminIDs, admin.ID)
		}

		input.AdminIDs = adminIDs
	}

	input.CreatedByMember = false

	for i := 0; i < len(equippers); i++ {
		input.EquipperID = equippers[i].ID
		input.EquipperEmail = equippers[i].Email
		input.EquipperName = equippers[i].Company
		input.EquipperImageLink = equippers[i].PhotoURL
		input.Currency = equippers[i].Currency

		if equippers[i].HasInventory {
			input.Status = models.RequestSent
		} else {
			input.Status = models.RequestPending
		}

		err := s.db.AddBidsRequest(ctx, lodgerID, input)
		if err != nil {
			return fmt.Errorf("can not create Bids request, error: %w", err)
		}

		s.SendEquippersEmails(ctx, equippers, i, input)
	}

	if lodger.CommunicationPreferences == models.French {
		err = s.mailer.SendFromTemplate(ctx, mailer.SendRenterBidzRequestIDFR, []string{input.LodgerEmail}, mailDataFromBidzRequestFR(input, s.frontendURL))
		if err != nil {
			log.Print(fmt.Errorf("unable to send Bids request to renter error: %w", err))
		}
	} else {
		err = s.mailer.SendFromTemplate(ctx, mailer.SendRenterBidzRequestID, []string{input.LodgerEmail}, mailDataFromBidzRequest(input, s.frontendURL))
		if err != nil {
			log.Print(fmt.Errorf("unable to send Bids request to renter error: %w", err))
		}
	}

	return nil
}

func (s *Service) SendEquippersEmails(ctx context.Context, equippers []models.Equipper, i int, input models.BidsRequest) {
	onClickLink := fmt.Sprintf("%s?sign_in=true", s.frontendURL)

	if equippers[i].HasInventory {
		s.SendEmailToEquippersWithInventory(ctx, equippers, i, input, onClickLink)
	} else {
		s.sendEmailToEquipperWithNoInventory(ctx, equippers, i, input, onClickLink)
	}
}

func (s *Service) sendEmailToEquipperWithNoInventory(ctx context.Context, equippers []models.Equipper, i int, input models.BidsRequest, onClickLink string) {
	if equippers[i].CommunicationPreferences == models.French {
		err := s.mailer.SendFromTemplate(ctx, mailer.SendEquipperBidzRequestIDFR, []string{equippers[i].Email}, mailDataFromBidzRequestFR(input, onClickLink))
		if err != nil {
			log.Print(fmt.Errorf("unable to send Bids request to equipper error: %w", err))
		}
	} else {
		err := s.mailer.SendFromTemplate(ctx, mailer.SendEquipperBidzRequestID, []string{equippers[i].Email}, mailDataFromBidzRequest(input, onClickLink))
		if err != nil {
			log.Print(fmt.Errorf("unable to send Bids request to equipper error: %w", err))
		}
	}
}

func (s *Service) SendEmailToEquippersWithInventory(ctx context.Context, equippers []models.Equipper, i int, input models.BidsRequest, onClickLink string) {
	if equippers[i].CommunicationPreferences == models.French {
		err := s.mailer.SendFromTemplate(ctx, mailer.SendgridRandomEquippersBidzRequestIDFR, []string{equippers[i].Email}, mailDataFromBidzRequestFR(input, onClickLink))
		if err != nil {
			log.Print(fmt.Errorf("unable to send Bids request to equipper error: %w", err))
		}
	} else {
		err := s.mailer.SendFromTemplate(ctx, mailer.SendgridRandomEquippersBidzRequestID, []string{equippers[i].Email}, mailDataFromBidzRequest(input, onClickLink))
		if err != nil {
			log.Print(fmt.Errorf("unable to send Bids request to equipper error: %w", err))
		}
	}
}

// GetAllBidsRequest returns all bidz requests for a given lodger.
func (s *Service) GetAllLodgerBidsRequest(ctx context.Context, limit int, lastID string, status models.BidsRequestStatus, lodgerID string) ([]models.BidsRequest, error) {
	bidzRequests, err := s.db.GetAllLodgerBidsRequest(ctx, limit, lastID, status, lodgerID)
	if err != nil {
		return nil, fmt.Errorf("can not get all Bids requests, error: %w", err)
	}

	lodger, err := s.db.GetLodgerByID(ctx, lodgerID)
	if err != nil {
		return nil, fmt.Errorf("can not get lodger by id %s, error: %w", lodgerID, err)
	}

	if lodger.MemberOf != "" {
		member, err := s.db.GetMemberByID(ctx, lodger.MemberID, lodger.MemberOf)
		if err != nil {
			return nil, fmt.Errorf("can not get member by id %s, error: %w", lodger.MemberOf, err)
		}

		bidzRequests, err = s.GetTeamBidzRequests(ctx, member, bidzRequests, limit, lastID, status)
		if err != nil {
			return nil, err
		}

		return bidzRequests, nil
	}

	return bidzRequests, nil
}

// GetAllBidsRequest returns all bidz requests.
func (s *Service) GetAllBidsRequest(ctx context.Context, limit int, lastID string, status models.BidsRequestStatus, equipperID string) ([]models.BidsRequest, error) {
	return s.db.GetAllBidsRequest(ctx, limit, lastID, status, equipperID)
}

func (s *Service) GetTeamBidzRequests(ctx context.Context, member models.Member, bidzRequests []models.BidsRequest, limit int, lastID string, status models.BidsRequestStatus) ([]models.BidsRequest, error) {
	if member.Type == models.Admin && member.MembershipStatus == models.MemberAccepted {
		adminBidzRequests, err := s.AdminBidzRequests(ctx, member, limit, lastID, status, bidzRequests)
		if err != nil {
			return nil, err
		}

		return adminBidzRequests, nil
	} else if member.MembershipStatus == models.MemberOwner && member.Type == models.Admin {
		ownerBidzRequests, err := s.OwnerBidzRequests(ctx, member, limit, lastID, status, bidzRequests)
		if err != nil {
			return nil, err
		}

		return ownerBidzRequests, nil
	}

	return bidzRequests, nil
}

func (s *Service) OwnerBidzRequests(ctx context.Context, member models.Member, limit int, lastID string, status models.BidsRequestStatus, bidzRequests []models.BidsRequest) ([]models.BidsRequest, error) {
	ownerBidzRequests, err := s.db.GetAllOwnerBidsRequest(ctx, member.ID, limit, lastID, status)
	if err != nil {
		return nil, fmt.Errorf("can not get all Bids requests, error: %w", err)
	}

	if len(ownerBidzRequests) > 0 {
		for _, bidzRequest := range ownerBidzRequests {
			bidzRequest.CreatedByMember = true

			bidzRequests = append(bidzRequests, bidzRequest)
		}

		bidzRequests = s.RemoveDuplicatesBidzRequest(bidzRequests)
	}

	return bidzRequests, nil
}

// Remove duplicates from bidz requests.
func (s *Service) RemoveDuplicatesBidzRequest(bidzRequests []models.BidsRequest) []models.BidsRequest {
	keys := make(map[string]bool)
	list := []models.BidsRequest{}

	for _, entry := range bidzRequests {
		if _, value := keys[entry.ID]; !value {
			keys[entry.ID] = true

			list = append(list, entry)
		}
	}

	return list
}

func (s *Service) AdminBidzRequests(ctx context.Context, member models.Member, limit int, lastID string, status models.BidsRequestStatus, bidzRequests []models.BidsRequest) ([]models.BidsRequest, error) {
	adminBidzRequests, err := s.db.GetAllAdminsBidsRequest(ctx, member.ID, limit, lastID, status)
	if err != nil {
		return nil, fmt.Errorf("can not get all Bids requests, error: %w", err)
	}

	if len(adminBidzRequests) > 0 {
		for _, bidzRequest := range adminBidzRequests {
			if member.LodgerID != bidzRequest.LodgerID {
				bidzRequest.CreatedByMember = true
			}

			bidzRequests = s.RemoveDuplicatesBidzRequest(bidzRequests)
		}
	}

	return bidzRequests, nil
}

// GetBidsRequestID  returns bidz request by id.
func (s *Service) GetBidsRequestID(ctx context.Context, id string) (models.BidsRequest, error) {
	return s.db.GetBidsRequestID(ctx, id)
}

// AddOfferRequest adds an bidz offer.
func (s *Service) AddOfferRequest(ctx context.Context, equipperID string, bidzOffer models.BidzOffer) error {
	lodger, err := s.db.GetLodgerByID(ctx, bidzOffer.LodgerID)
	if err != nil {
		return fmt.Errorf("can not get lodger by id %s, error: %w", bidzOffer.LodgerID, err)
	}

	adminIDs := []string{}

	if lodger.MemberOf != "" {
		bidzOffer.OwnerID = lodger.MemberOf
		admins, err := s.db.GetAdminsByOwnerID(ctx, lodger.MemberOf)
		if err != nil {
			return fmt.Errorf("can not get admins by lodger_id %s, error: %w", lodger.MemberOf, err)
		}
		for _, admin := range admins {
			adminIDs = append(adminIDs, admin.ID)
		}
		bidzOffer.AdminIDs = adminIDs
	}

	duration := bidzOffer.ReturnDate.Sub(bidzOffer.StartDate).Hours()/24 + 1
	days := int(duration) % 7
	week := int(duration) / 7 % 4
	fourWeeks := int(duration) / 28

	bidzOffer.CreatedByMember = false

	bidzOffer.Invoice.SubTotal = float64(days)*bidzOffer.PricePerDay + float64(week)*bidzOffer.PricePerWeek + float64(fourWeeks)*bidzOffer.PricePerMonth
	bidzOffer.Invoice.ServiceFees = bidzOffer.Invoice.SubTotal * 0.03
	bidzOffer.WaiverInsurance = 0.0

	if bidzOffer.NeedOne {
		bidzOffer.WaiverInsurance = bidzOffer.Invoice.SubTotal * 0.0996
	}

	bidzOffer.Invoice.TotalAmount = bidzOffer.WaiverInsurance + bidzOffer.Invoice.SubTotal + bidzOffer.WaiverInsurance

	err = s.db.AddOfferRequest(ctx, equipperID, bidzOffer)
	if err != nil {
		return fmt.Errorf("can not create Bidz offer, an error: %w", err)
	}

	onClickLink := fmt.Sprintf("%s?sign_in=true", s.frontendURL)

	if lodger.CommunicationPreferences == models.French {
		err = s.mailer.SendFromTemplate(ctx, mailer.SendgridSendRenterBidzOfferIDFR, []string{bidzOffer.LodgerEmail}, mailDataFromBidzOfferFR(bidzOffer, onClickLink))
		if err != nil {
			log.Print(fmt.Errorf("unable to send Bidz offer to renter error: %w", err))
		}
	} else {
		err = s.mailer.SendFromTemplate(ctx, mailer.SendgridSendRenterBidzOfferID, []string{bidzOffer.LodgerEmail}, mailDataFromBidzOffer(bidzOffer, onClickLink))
		if err != nil {
			log.Print(fmt.Errorf("unable to send Bidz offer to renter error: %w", err))
		}
	}

	return nil
}

// GetAllBidsOffer returns all bidz offers.
func (s *Service) GetAllBidsOffer(ctx context.Context, limit int, lastID string, status models.BidsOfferStatus, where map[string]interface{}) ([]models.BidzOffer, error) {
	return s.db.GetAllBidsOffer(ctx, limit, lastID, status, where)
}

// GetAllBidsLodgerOffer returns all bidz offers.
func (s *Service) GetAllBidsLodgerOffer(ctx context.Context, limit int, lastID string, status models.BidsOfferStatus, lodgerID string) ([]models.BidzOffer, error) {
	bidzOffers, err := s.db.GetAllBidsLodgerOffer(ctx, limit, lastID, status, lodgerID)
	if err != nil {
		return nil, fmt.Errorf("can not get all Bids offers, error: %w", err)
	}

	lodger, err := s.db.GetLodgerByID(ctx, lodgerID)
	if err != nil {
		return nil, fmt.Errorf("can not get lodger by id %s, error: %w", lodgerID, err)
	}

	if lodger.MemberOf != "" {
		member, err := s.db.GetMemberByID(ctx, lodger.MemberID, lodger.MemberOf)
		if err != nil {
			return nil, fmt.Errorf("can not get member by id %s, error: %w", lodger.MemberOf, err)
		}

		bidzOffers, err = s.GetTeamBidzOffer(ctx, member, bidzOffers, limit, lastID, status)
		if err != nil {
			return nil, err
		}

		return bidzOffers, nil
	}

	return bidzOffers, nil
}

func (s *Service) GetTeamBidzOffer(ctx context.Context, member models.Member, bidzOffers []models.BidzOffer, limit int, lastID string, status models.BidsOfferStatus) ([]models.BidzOffer, error) {
	if member.Type == models.Admin {
		adminBidzOffers, err := s.AdminBidzOffers(ctx, member, limit, lastID, status, bidzOffers)
		if err != nil {
			return nil, err
		}

		bidzOffers = adminBidzOffers
	} else if member.MembershipStatus == models.MemberOwner {
		ownerBidzOffers, err := s.OwnerBidzOffers(ctx, member, limit, lastID, status, bidzOffers)
		if err != nil {
			return nil, err
		}

		bidzOffers = ownerBidzOffers
	}

	return bidzOffers, nil
}

func (s *Service) OwnerBidzOffers(ctx context.Context, member models.Member, limit int, lastID string, status models.BidsOfferStatus, bidzOffers []models.BidzOffer) ([]models.BidzOffer, error) {
	ownerBidzOffers, err := s.db.GetAllOwnerBidsOffer(ctx, member.ID, limit, lastID, status)
	if err != nil {
		return nil, fmt.Errorf("can not get all Bids offers, error: %w", err)
	}

	if len(ownerBidzOffers) > 0 {
		for _, ownerBidzOffer := range ownerBidzOffers {
			ownerBidzOffer.CreatedByMember = true

			bidzOffers = append(bidzOffers, ownerBidzOffer)
		}

		bidzOffers = s.RemoveDuplicates(bidzOffers)
	}

	return bidzOffers, nil
}

// Remove duplicates from bidz offers.
func (s *Service) RemoveDuplicates(bidzOffers []models.BidzOffer) []models.BidzOffer {
	keys := make(map[string]bool)
	list := []models.BidzOffer{}

	for _, entry := range bidzOffers {
		if _, value := keys[entry.ID]; !value {
			keys[entry.ID] = true

			list = append(list, entry)
		}
	}

	return list
}

func (s *Service) AdminBidzOffers(ctx context.Context, member models.Member, limit int, lastID string, status models.BidsOfferStatus, bidzOffers []models.BidzOffer) ([]models.BidzOffer, error) {
	adminBidzOffers, err := s.db.GetAllAdminsBidsOffer(ctx, member.ID, limit, lastID, status)
	if err != nil {
		return nil, fmt.Errorf("can not get all Bids offers, error: %w", err)
	}

	if len(adminBidzOffers) > 0 {
		for _, adminBidzOffer := range adminBidzOffers {
			adminBidzOffer.CreatedByMember = true

			bidzOffers = append(bidzOffers, adminBidzOffer)
		}

		bidzOffers = s.RemoveDuplicates(bidzOffers)
	}

	return bidzOffers, nil
}

// GetBidsOfferID returns bidz offer by id.
func (s *Service) GetBidsOfferID(ctx context.Context, id string) (models.BidzOffer, error) {
	return s.db.GetBidsOfferID(ctx, id)
}

// AcceptOffer accepts an offer.
func (s *Service) AcceptOffer(ctx context.Context, lodgerID string, offerID string) (string, error) {
	offer, err := s.db.GetBidsOfferID(ctx, offerID)
	if err != nil {
		return "", fmt.Errorf("unable to get offer: %w", err)
	}

	if offer.LodgerID != lodgerID {
		return "", fmt.Errorf("unauthorized offer for lodger %s current_lodger %s", offer.LodgerID, lodgerID)
	}

	if offer.Status != models.OfferPending {
		return "", fmt.Errorf("wrong offer status %s", offer.Status)
	}

	offer.Status = models.OfferAccepted

	err = s.db.UpdateOffer(ctx, offer)
	if err != nil {
		return "", fmt.Errorf("unable to accept offer with offer_id : %s error: %w", offerID, err)
	}

	if offer.ProjectID != "" {
		project, err := s.db.GetProjectByID(ctx, offer.ProjectID)
		if err != nil {
			return "", fmt.Errorf("unable to get project")
		}

		project.BidzEquipments = append(project.BidzEquipments, offer.ID)
	}

	err = s.OfferToDecline(ctx, offer.EquipmentID)
	if err != nil {
		return "", fmt.Errorf("unable to get offer to decline: %w", err)
	}

	err = s.RequestToDecline(ctx, offer.EquipmentID, offer.StartDate, offer.ReturnDate)
	if err != nil {
		return "", fmt.Errorf("unable to get request to decline: %w", err)
	}

	equipper, err := s.GetEquipperByID(ctx, offer.EquipperID)
	if err != nil {
		return "", fmt.Errorf("unable to get equipper: %w", err)
	}

	if equipper.CommunicationPreferences == models.French {
		err = s.mailer.SendFromTemplate(ctx, mailer.SendgridSendConfirmationEquipperBidzOfferIDFR, []string{offer.EquipperEmail}, mailDataFromBidzOfferFR(offer, s.frontendURL))
		if err != nil {
			log.Print(fmt.Errorf("unable to send Bidz confirmation offer to equipper error: %w", err))
		}
	} else {
		err = s.mailer.SendFromTemplate(ctx, mailer.SendgridSendConfirmationEquipperBidzOfferID, []string{offer.EquipperEmail}, mailDataFromBidzOffer(offer, s.frontendURL))
		if err != nil {
			log.Print(fmt.Errorf("unable to send Bidz confirmation offer to equipper error: %w", err))
		}
	}

	lodger, err := s.GetLodgerByID(ctx, offer.LodgerID)
	if err != nil {
		return "", fmt.Errorf("unable to get renter: %w", err)
	}

	if lodger.CommunicationPreferences == models.French {
		err = s.mailer.SendFromTemplate(ctx, mailer.SendgridSendConfirmationRenterBidzOfferIDFR, []string{offer.LodgerEmail}, mailDataFromBidzOfferFR(offer, s.frontendURL))
		if err != nil {
			log.Print(fmt.Errorf("unable to send Bidz confirmation offer to renter error: %w", err))
		}
	} else {
		err = s.mailer.SendFromTemplate(ctx, mailer.SendgridSendConfirmationRenterBidzOfferID, []string{offer.LodgerEmail}, mailDataFromBidzOffer(offer, s.frontendURL))
		if err != nil {
			log.Print(fmt.Errorf("unable to send Bidz confirmation offer to renter error: %w", err))
		}
	}

	err = s.sendMailToEquippersWhoLostTheBidz(ctx, offer, equipper)
	if err != nil {
		log.Print(fmt.Errorf("unable to send Bidz confirmation offer to equipper error: %w", err))
	}

	if offer.ProjectID != "" {
		project, err := s.db.GetProjectByID(ctx, offer.ProjectID)
		if err != nil {
			return "", fmt.Errorf("unable to get project by id : %w", err)
		}

		project.BidzEquipments = append(project.BidzEquipments, offerID)

		err = s.db.UpdateProject(ctx, project)
		if err != nil {
			return "", fmt.Errorf("can not update project with id : %s , error : %w", project.ID, err)
		}
	}

	checkoutURL := ""
	switch offer.PaymentMethod {
	case models.PaymentMethodCreditCard:
		items := []models.Item{
			{
				ID:        uuid.New().String(),
				Name:      offer.EquipmentName,
				Quantity:  1,
				Price:     int64(offer.Invoice.TotalAmount * 100),
				IsTaxable: true,
			},
		}

		if offer.NeedOne {
			items = append(items, models.Item{
				ID:        uuid.New().String(),
				Name:      "Limited Damage Waiver",
				Quantity:  1,
				Price:     int64(offer.WaiverInsurance * 100),
				IsTaxable: true,
			})
		}

		checkoutURL, err = s.CreateBidzOrder(ctx, models.Order{
			ID:            uuid.New().String(),
			Items:         items,
			Currency:      models.Currency(offer.Currency),
			UserID:        lodgerID,
			BookingID:     offerID,
			CustomerEmail: offer.LodgerEmail,
		}, "/", "/")
		if err != nil {
			return "", fmt.Errorf("unable to create order: %w", err)
		}
	}

	println(checkoutURL)

	return checkoutURL, nil

}

// send mail to equipper who lost the bidz offer.
func (s *Service) sendMailToEquippersWhoLostTheBidz(ctx context.Context, offer models.BidzOffer, equipper models.Equipper) error {
	equippers, err := s.db.GetAllEquippersByCategory(ctx, offer.Category)
	if err != nil {
		return fmt.Errorf("can not get all equippers, error: %w", err)
	}

	// remove equipper who won the bidz offer.
	for i, e := range equippers {
		if e.ID == equipper.ID {
			equippers = append(equippers[:i], equippers[i+1:]...)

			break
		}
	}

	// send mail to all equippers who doesn't won the bidz offer.
	for _, e := range equippers {
		if e.CommunicationPreferences == models.French {
			err = s.mailer.SendFromTemplate(ctx, mailer.SendgridSendCanceledEquipperBidzOfferIDFR, []string{e.Email}, mailDataFromBidzOfferFR(offer, s.frontendURL))
			if err != nil {
				log.Print(fmt.Errorf("unable to send Bidz offer to equipper error: %w", err))
			}
		} else {
			err = s.mailer.SendFromTemplate(ctx, mailer.SendgridSendCanceledEquipperBidzOfferID, []string{e.Email}, mailDataFromBidzOffer(offer, s.frontendURL))
			if err != nil {
				log.Print(fmt.Errorf("unable to send Bidz offer to equipper error: %w", err))
			}
		}
	}

	return nil
}

// RejectOffer rejects an offer.
func (s *Service) RejectOffer(ctx context.Context, lodgerID string, offerID string) error {
	offer, err := s.db.GetBidsOfferID(ctx, offerID)
	if err != nil {
		return fmt.Errorf("unable to get offer: %w", err)
	}

	if offer.LodgerID != lodgerID {
		return fmt.Errorf("unauthorized offer for lodger %s", offer.LodgerID)
	}

	if offer.Status != models.OfferPending {
		return fmt.Errorf("wrong offer status %s", offer.Status)
	}

	offer.Status = models.OfferRejected

	err = s.db.UpdateOffer(ctx, offer)
	if err != nil {
		return fmt.Errorf("unable to reject offer with offer_id : %s error: %w", offerID, err)
	}

	return nil
}

// AcceptRequest accepts a request.
func (s *Service) AcceptRequest(ctx context.Context, equipperID string, requestID string) error {
	request, err := s.db.GetBidsRequestID(ctx, requestID)
	if err != nil {
		return fmt.Errorf("unable to get request for price: %w", err)
	}

	if request.EquipperID != equipperID {
		return fmt.Errorf("unauthorized offer for  equipper %s", request.EquipperID)
	}

	if request.Status != models.RequestPending {
		return fmt.Errorf("wrong offer status %s", request.Status)
	}

	request.Status = models.RequestAccepted

	err = s.db.UpdateRequest(ctx, request)
	if err != nil {
		return fmt.Errorf("unable to accept request with request_id : %s error: %w", requestID, err)
	}

	return nil
}

// RejectRequest rejects a request.
func (s *Service) RejectRequest(ctx context.Context, equipperID string, requestID string, input models.BidsRequest) error {
	request, err := s.db.GetBidsRequestID(ctx, requestID)
	if err != nil {
		return fmt.Errorf("unable to get request for price: %w", err)
	}

	if request.EquipperID != equipperID {
		return fmt.Errorf("unauthorized offer for  equipper %s", request.EquipperID)
	}

	if request.Status != models.RequestPending {
		return fmt.Errorf("wrong offer status %s", request.Status)
	}

	request.RejectionComment = input.RejectionComment
	request.Status = models.RequestRejected

	err = s.db.UpdateRequest(ctx, request)
	if err != nil {
		return fmt.Errorf("unable to reject request with request_id : %s error: %w", requestID, err)
	}

	return nil
}

// CancelBidzOffer cancel a BidzOffer.
func (s *Service) CancelBidzOffer(ctx context.Context, equipperID string, bidzOfferID string, input models.BidzOffer) error {
	bidzOffer, err := s.db.GetBidsOfferID(ctx, bidzOfferID)
	if err != nil {
		return fmt.Errorf("unable to get offer: %w", err)
	}

	if bidzOffer.EquipperID != equipperID {
		return fmt.Errorf("offer is not yours")
	}

	bidzOffer.Status = models.OfferCanceled
	bidzOffer.BidsOfferCancelComment = input.BidsOfferCancelComment

	err = s.db.UpdateOffer(ctx, bidzOffer)
	if err != nil {
		return fmt.Errorf("unable to cancel offer with offer_id : %s error: %w", bidzOfferID, err)
	}

	projects, err := s.db.GetProjectsByEquipmentID(ctx, bidzOffer.ID)
	if err != nil {
		return fmt.Errorf("unable to get projects: %w", err)
	}

	updatedProjects := make([]models.Project, 0)

	for _, project := range projects {
		for _, equipmentID := range project.Equipments {
			if bidzOffer.ID != equipmentID {
				project.Equipments = append(project.Equipments, equipmentID)
			}
		}

		updatedProjects = append(updatedProjects, project)
	}

	err = s.db.BatchUpdateProjects(ctx, updatedProjects)
	if err != nil {
		return fmt.Errorf("unable to update projects with bidzOfferID: %s error: %w", bidzOfferID, err)
	}

	lodger, err := s.db.GetLodgerByID(ctx, bidzOffer.LodgerID)
	if err != nil {
		return fmt.Errorf("unable to get lodger: %w", err)
	}

	if lodger.CommunicationPreferences == models.French {
		err = s.mailer.SendFromTemplate(ctx, mailer.SendgridSendCanceledRenterBidzOfferIDFR, []string{bidzOffer.LodgerEmail}, mailDataFromBidzOfferFR(bidzOffer, s.frontendURL))
		if err != nil {
			log.Print(fmt.Errorf("unable to send Bidz canceled offer to equipper error: %w", err))
		}
	} else {
		err = s.mailer.SendFromTemplate(ctx, mailer.SendgridSendCanceledRenterBidzOfferID, []string{bidzOffer.LodgerEmail}, mailDataFromBidzOffer(bidzOffer, s.frontendURL))
		if err != nil {
			log.Print(fmt.Errorf("unable to send Bidz canceled offer to equipper error: %w", err))
		}
	}

	return nil
}

// OfferToDecline decline a BidzOffers with equipmentID.
func (s *Service) OfferToDecline(ctx context.Context, equipmentID string) error {
	offerToDecline, err := s.db.GetBidsOfferByEquipmentID(ctx, equipmentID)
	if err != nil {
		return fmt.Errorf("unable to get offer to decline: %w", err)
	}

	if len(offerToDecline) != 0 {
		for _, offer := range offerToDecline {
			if offer.Status == models.OfferAccepted {
				continue
			}

			offer.Status = models.OfferRejected

			err = s.db.UpdateOffer(ctx, offer)
			if err != nil {
				return fmt.Errorf("unable to decline offer with offer_id : %s error: %w", offer.ID, err)
			}
		}
	}

	return nil
}

// RequestToDecline decline BidzRequests with equipmentID , startDate and returnData.
func (s *Service) RequestToDecline(ctx context.Context, equipmentID string, startDate time.Time, returnDate time.Time) error {
	requestToDecline, err := s.db.GetBidsRequestByEquipmentID(ctx, equipmentID)
	if err != nil {
		return fmt.Errorf("unable to get request to decline: %w", err)
	}

	if len(requestToDecline) != 0 {
		for _, request := range requestToDecline {
			if request.Status == models.RequestAccepted {
				continue
			}

			if (request.StartDate.After(startDate) || request.StartDate.Equal(startDate)) && (request.ReturnDate.Before(returnDate) || request.ReturnDate.Equal(returnDate)) {
				request.Status = models.RequestRejected

				err = s.db.UpdateRequest(ctx, request)
				if err != nil {
					return fmt.Errorf("unable to decline request with request_id : %s error: %w", request.ID, err)
				}
			}
		}
	}

	return nil
}

// mailDataFromBidzRequest data for BIDZ request.
func mailDataFromBidzRequest(bidzRequest models.BidsRequest, frontendURL string) map[string]string {
	newBidzRequest := ReplaceBidzRequestEmptyString(bidzRequest)

	return map[string]string{
		"renter_name":               newBidzRequest.LodgerName,
		"equipper_name":             newBidzRequest.EquipperName,
		"equipment_name":            newBidzRequest.EquipmentName,
		"type_of_propulsion":        strings.Join(newBidzRequest.TypeOfPropulsion, ", "),
		"brand":                     newBidzRequest.Brand,
		"weight":                    newBidzRequest.Weight,
		"length_bidz":               newBidzRequest.Length,
		"height":                    newBidzRequest.Height,
		"kw":                        newBidzRequest.Kw,
		"cfm":                       newBidzRequest.Cfm,
		"volt":                      newBidzRequest.Volt,
		"force":                     newBidzRequest.Force,
		"usage_hours":               newBidzRequest.UsageHours,
		"btu":                       newBidzRequest.Btu,
		"capacity":                  newBidzRequest.Capacity,
		"consumption":               newBidzRequest.Consumption,
		"description":               newBidzRequest.Description,
		"equipment_image_link":      newBidzRequest.EquipmentImageLink,
		"credit_check_form":         newBidzRequest.CreditCheckForm,
		"contact":                   newBidzRequest.Contact,
		"payment_method":            string(newBidzRequest.PaymentMethod),
		"delivery_preference":       newBidzRequest.DeliveryPreference,
		"start_date":                newBidzRequest.StartDate.Format("2006-01-02"),
		"return_date":               newBidzRequest.ReturnDate.Format("2006-01-02"),
		"billing_address_address":   newBidzRequest.BillingAddress.Address,
		"billing_address_zip_code":  newBidzRequest.BillingAddress.ZipCode,
		"billing_address_city":      newBidzRequest.BillingAddress.City,
		"billing_address_state":     newBidzRequest.BillingAddress.State,
		"delivery_address_address":  newBidzRequest.DeliveryAddress.Address,
		"delivery_address_zip_code": newBidzRequest.DeliveryAddress.ZipCode,
		"delivery_address_city":     newBidzRequest.DeliveryAddress.City,
		"delivery_address_state":    newBidzRequest.DeliveryAddress.State,
		"po_number":                 newBidzRequest.PoNumber,
		"equippers_emails":          newBidzRequest.EquipperEmail,
		"equipment_utility":         newBidzRequest.EquipmentUtility,
		"width":                     newBidzRequest.Width,
		"on_click":                  frontendURL,
	}
}

// mailDataFromBidzRequest data for BIDZ request french version.
func mailDataFromBidzRequestFR(bidzRequest models.BidsRequest, frontendURL string) map[string]string {
	newBidzRequest := ReplaceBidzRequestEmptyString(bidzRequest)

	return map[string]string{
		"renter_name":               newBidzRequest.LodgerName,
		"equipper_name":             newBidzRequest.EquipperName,
		"equipment_name":            newBidzRequest.EquipmentNameFR,
		"type_of_propulsion":        strings.Join(newBidzRequest.TypeOfPropulsion, ", "),
		"brand":                     newBidzRequest.Brand,
		"weight":                    newBidzRequest.Weight,
		"length_bidz":               newBidzRequest.Length,
		"height":                    newBidzRequest.Height,
		"kw":                        newBidzRequest.Kw,
		"cfm":                       newBidzRequest.Cfm,
		"volt":                      newBidzRequest.Volt,
		"force":                     newBidzRequest.Force,
		"usage_hours":               newBidzRequest.UsageHours,
		"btu":                       newBidzRequest.Btu,
		"capacity":                  newBidzRequest.Capacity,
		"consumption":               newBidzRequest.Consumption,
		"description":               newBidzRequest.Description,
		"equipment_image_link":      newBidzRequest.EquipmentImageLink,
		"credit_check_form":         newBidzRequest.CreditCheckForm,
		"contact":                   newBidzRequest.Contact,
		"payment_method":            string(newBidzRequest.PaymentMethod),
		"delivery_preference":       newBidzRequest.DeliveryPreference,
		"start_date":                newBidzRequest.StartDate.Format("2006-01-02"),
		"return_date":               newBidzRequest.ReturnDate.Format("2006-01-02"),
		"billing_address_address":   newBidzRequest.BillingAddress.Address,
		"billing_address_zip_code":  newBidzRequest.BillingAddress.ZipCode,
		"billing_address_city":      newBidzRequest.BillingAddress.City,
		"billing_address_state":     newBidzRequest.BillingAddress.State,
		"delivery_address_address":  newBidzRequest.DeliveryAddress.Address,
		"delivery_address_zip_code": newBidzRequest.DeliveryAddress.ZipCode,
		"delivery_address_city":     newBidzRequest.DeliveryAddress.City,
		"delivery_address_state":    newBidzRequest.DeliveryAddress.State,
		"po_number":                 newBidzRequest.PoNumber,
		"equippers_emails":          newBidzRequest.EquipperEmail,
		"equipment_utility":         newBidzRequest.EquipmentUtility,
		"width":                     newBidzRequest.Width,
		"on_click":                  frontendURL,
	}
}

// mailDataFromBidzOffer data for BIDZ offer.
func mailDataFromBidzOffer(bidzOffer models.BidzOffer, frontendURL string) map[string]string {
	newBidzOffer := ReplaceBidzOfferEmptyString(bidzOffer)

	return map[string]string{
		"renter_name":               newBidzOffer.LodgerName,
		"equipper_name":             newBidzOffer.EquipperName,
		"equipment_name":            newBidzOffer.EquipmentName,
		"equipment_utility":         newBidzOffer.EquipmentUtility,
		"type_of_propulsion":        strings.Join(newBidzOffer.TypeOfPropulsion, ", "),
		"price_per_day":             humanize.CommafWithDigits(newBidzOffer.PricePerDay, 2),
		"price_per_week":            humanize.CommafWithDigits(newBidzOffer.PricePerWeek, 2),
		"price_per_month":           humanize.CommafWithDigits(newBidzOffer.PricePerMonth, 2),
		"total_amount":              humanize.CommafWithDigits(newBidzOffer.Invoice.TotalAmount, 2),
		"delivery_cost":             humanize.CommafWithDigits(newBidzOffer.DeliveryPickupCost+newBidzOffer.DeliveryDropCost, 2),
		"brand":                     newBidzOffer.Brand,
		"weight":                    newBidzOffer.Weight,
		"length_bidz":               newBidzOffer.Length,
		"height":                    newBidzOffer.Height,
		"kw":                        newBidzOffer.Kw,
		"cfm":                       newBidzOffer.Cfm,
		"volt":                      newBidzOffer.Volt,
		"force":                     newBidzOffer.Force,
		"usage_hours":               newBidzOffer.UsageHours,
		"btu":                       newBidzOffer.Btu,
		"capacity":                  newBidzOffer.Capacity,
		"consumption":               newBidzOffer.Consumption,
		"description":               newBidzOffer.Description,
		"equipment_image_link":      newBidzOffer.EquipmentImageLink,
		"credit_check_form":         newBidzOffer.CreditCheckForm,
		"contact":                   newBidzOffer.Contact,
		"payment_method":            string(newBidzOffer.PaymentMethod),
		"delivery_preference":       newBidzOffer.DeliveryPreference,
		"start_date":                newBidzOffer.StartDate.Format("2006-01-02"),
		"return_date":               newBidzOffer.ReturnDate.Format("2006-01-02"),
		"billing_address_address":   newBidzOffer.BillingAddress.Address,
		"billing_address_zip_code":  newBidzOffer.BillingAddress.ZipCode,
		"billing_address_city":      newBidzOffer.BillingAddress.City,
		"billing_address_state":     newBidzOffer.BillingAddress.State,
		"delivery_address_address":  newBidzOffer.DeliveryAddress.Address,
		"delivery_address_zip_code": newBidzOffer.DeliveryAddress.ZipCode,
		"delivery_address_city":     newBidzOffer.DeliveryAddress.City,
		"delivery_address_state":    newBidzOffer.DeliveryAddress.State,
		"po_number":                 newBidzOffer.PoNumber,
		"equippers_emails":          newBidzOffer.EquipperEmail,
		"width":                     newBidzOffer.Width,
		"on_click":                  frontendURL,
		"bids_offer_cancel_comment": newBidzOffer.BidsOfferCancelComment,
	}
}

// mailDataFromBidzOffer data for BIDZ offer.
func mailDataFromBidzOfferFR(bidzOffer models.BidzOffer, frontendURL string) map[string]string {
	newBidzOffer := ReplaceBidzOfferEmptyString(bidzOffer)

	return map[string]string{
		"renter_name":               newBidzOffer.LodgerName,
		"equipper_name":             newBidzOffer.EquipperName,
		"equipment_name":            newBidzOffer.EquipmentNameFR,
		"equipment_utility":         newBidzOffer.EquipmentUtility,
		"type_of_propulsion":        strings.Join(newBidzOffer.TypeOfPropulsion, ", "),
		"price_per_day":             humanize.CommafWithDigits(newBidzOffer.PricePerDay, 2),
		"price_per_week":            humanize.CommafWithDigits(newBidzOffer.PricePerWeek, 2),
		"price_per_month":           humanize.CommafWithDigits(newBidzOffer.PricePerMonth, 2),
		"total_amount":              humanize.CommafWithDigits(newBidzOffer.Invoice.TotalAmount, 2),
		"brand":                     newBidzOffer.Brand,
		"weight":                    newBidzOffer.Weight,
		"length_bidz":               newBidzOffer.Length,
		"height":                    newBidzOffer.Height,
		"kw":                        newBidzOffer.Kw,
		"cfm":                       newBidzOffer.Cfm,
		"volt":                      newBidzOffer.Volt,
		"force":                     newBidzOffer.Force,
		"usage_hours":               newBidzOffer.UsageHours,
		"btu":                       newBidzOffer.Btu,
		"capacity":                  newBidzOffer.Capacity,
		"consumption":               newBidzOffer.Consumption,
		"description":               newBidzOffer.Description,
		"equipment_image_link":      newBidzOffer.EquipmentImageLink,
		"credit_check_form":         newBidzOffer.CreditCheckForm,
		"contact":                   newBidzOffer.Contact,
		"payment_method":            string(newBidzOffer.PaymentMethod),
		"delivery_preference":       newBidzOffer.DeliveryPreference,
		"start_date":                newBidzOffer.StartDate.Format("2006-01-02"),
		"return_date":               newBidzOffer.ReturnDate.Format("2006-01-02"),
		"billing_address_address":   newBidzOffer.BillingAddress.Address,
		"billing_address_zip_code":  newBidzOffer.BillingAddress.ZipCode,
		"billing_address_city":      newBidzOffer.BillingAddress.City,
		"billing_address_state":     newBidzOffer.BillingAddress.State,
		"delivery_address_address":  newBidzOffer.DeliveryAddress.Address,
		"delivery_address_zip_code": newBidzOffer.DeliveryAddress.ZipCode,
		"delivery_address_city":     newBidzOffer.DeliveryAddress.City,
		"delivery_address_state":    newBidzOffer.DeliveryAddress.State,
		"po_number":                 newBidzOffer.PoNumber,
		"equippers_emails":          newBidzOffer.EquipperEmail,
		"width":                     newBidzOffer.Width,
		"on_click":                  frontendURL,
		"bids_offer_cancel_comment": newBidzOffer.BidsOfferCancelComment,
	}
}

func ReplaceBidzRequestEmptyString(bidzRequest models.BidsRequest) models.BidsRequest {
	typeOfprop := strings.Join(bidzRequest.TypeOfPropulsion, ", ")
	attributes := []*string{
		&bidzRequest.LodgerName,
		&bidzRequest.LodgerPhoneNumber,
		&bidzRequest.EquipperName,
		&bidzRequest.EquipmentName,
		&bidzRequest.EquipmentImageLink,
		&bidzRequest.EquipperImageLink,
		&bidzRequest.Weight,
		&bidzRequest.Height,
		&bidzRequest.Btu,
		&bidzRequest.Kw,
		&bidzRequest.Cfm,
		&bidzRequest.Volt,
		&typeOfprop,
		&bidzRequest.Description,
		&bidzRequest.Brand,
		&bidzRequest.Width,
		&bidzRequest.Length,
		&bidzRequest.Force,
		&bidzRequest.UsageHours,
		&bidzRequest.DeliveryAddress.Address,
		&bidzRequest.DeliveryAddress.City,
		&bidzRequest.DeliveryAddress.Country,
		&bidzRequest.DeliveryAddress.ZipCode,
		&bidzRequest.DeliveryAddress.State,
		&bidzRequest.BillingAddress.Address,
		&bidzRequest.BillingAddress.City,
		&bidzRequest.BillingAddress.Country,
		&bidzRequest.BillingAddress.ZipCode,
		&bidzRequest.BillingAddress.State,
		&bidzRequest.DeliveryPreference,
		&bidzRequest.CreditCheckForm,
		&bidzRequest.PoNumber,
		&bidzRequest.Capacity,
		&bidzRequest.Consumption,
		&bidzRequest.Contact,
		&bidzRequest.EquipmentUtility,
	}

	for _, attribute := range attributes {
		if *attribute == "" {
			*attribute = NotSpecified
		}
	}

	return bidzRequest
}

func ReplaceBidzOfferEmptyString(bidzOffer models.BidzOffer) models.BidzOffer {
	typeOfprop := strings.Join(bidzOffer.TypeOfPropulsion, ", ")
	attributes := []*string{
		&bidzOffer.LodgerName,
		&bidzOffer.LodgerPhoneNumber,
		&bidzOffer.EquipperName,
		&bidzOffer.EquipmentName,
		&bidzOffer.EquipmentImageLink,
		&bidzOffer.EquipperImageLink,
		&bidzOffer.Weight,
		&bidzOffer.Height,
		&bidzOffer.Btu,
		&bidzOffer.Kw,
		&bidzOffer.Cfm,
		&bidzOffer.Volt,
		&typeOfprop,
		&bidzOffer.Description,
		&bidzOffer.Brand,
		&bidzOffer.Width,
		&bidzOffer.Length,
		&bidzOffer.Force,
		&bidzOffer.UsageHours,
		&bidzOffer.DeliveryAddress.Address,
		&bidzOffer.DeliveryAddress.City,
		&bidzOffer.DeliveryAddress.Country,
		&bidzOffer.DeliveryAddress.ZipCode,
		&bidzOffer.DeliveryAddress.State,
		&bidzOffer.BillingAddress.Address,
		&bidzOffer.BillingAddress.City,
		&bidzOffer.BillingAddress.Country,
		&bidzOffer.BillingAddress.ZipCode,
		&bidzOffer.BillingAddress.State,
		&bidzOffer.DeliveryPreference,
		&bidzOffer.CreditCheckForm,
		&bidzOffer.PoNumber,
		&bidzOffer.Capacity,
		&bidzOffer.Consumption,
		&bidzOffer.Contact,
		&bidzOffer.BidsOfferCancelComment,
		&bidzOffer.EquipmentUtility,
	}

	for _, attribute := range attributes {
		if *attribute == "" {
			*attribute = NotSpecified
		}
	}

	return bidzOffer
}
