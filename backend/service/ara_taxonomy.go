package service

import (
	"context"
	"fmt"
	"log"
	"strconv"

	"cloud.google.com/go/firestore"
	"github.com/vima-inc/derental/models"
)

// ARATaxonomyService handles ARA taxonomy operations
type ARATaxonomyService struct {
	firestoreClient *firestore.Client
}

// NewARATaxonomyService creates a new ARA taxonomy service
func NewARATaxonomyService(firestoreClient *firestore.Client) *ARATaxonomyService {
	return &ARATaxonomyService{
		firestoreClient: firestoreClient,
	}
}

// GetAllLevel1Categories retrieves all ARA Level 1 categories
func (s *ARATaxonomyService) GetAllLevel1Categories(ctx context.Context) ([]models.ARALevel1Category, error) {
	iter := s.firestoreClient.Collection("ara_level1_categories").Documents(ctx)
	defer iter.Stop()

	var categories []models.ARALevel1Category
	for {
		doc, err := iter.Next()
		if err != nil {
			if err.Error() == "no more items in iterator" {
				break
			}
			return nil, fmt.Errorf("failed to iterate level1 categories: %w", err)
		}

		var category models.ARALevel1Category
		if err := doc.DataTo(&category); err != nil {
			log.Printf("Failed to unmarshal level1 category %s: %v", doc.Ref.ID, err)
			continue
		}

		if category.ID == 0 {
			// Try to parse the document ID as integer
			if id, err := strconv.Atoi(doc.Ref.ID); err == nil {
				category.ID = id
			}
		}

		categories = append(categories, category)
	}

	return categories, nil
}

// GetAllLevel2Types retrieves all ARA Level 2 types
func (s *ARATaxonomyService) GetAllLevel2Types(ctx context.Context) ([]models.ARALevel2Type, error) {
	iter := s.firestoreClient.Collection("ara_level2_types").Documents(ctx)
	defer iter.Stop()

	var types []models.ARALevel2Type
	for {
		doc, err := iter.Next()
		if err != nil {
			if err.Error() == "no more items in iterator" {
				break
			}
			return nil, fmt.Errorf("failed to iterate level2 types: %w", err)
		}

		var araType models.ARALevel2Type
		if err := doc.DataTo(&araType); err != nil {
			log.Printf("Failed to unmarshal level2 type %s: %v", doc.Ref.ID, err)
			continue
		}

		if araType.ID == 0 {
			// Try to parse the document ID as integer
			if id, err := strconv.Atoi(doc.Ref.ID); err == nil {
				araType.ID = id
			}
		}

		types = append(types, araType)
	}

	return types, nil
}

// GetLevel2TypesByLevel1 retrieves Level 2 types for a specific Level 1 category
func (s *ARATaxonomyService) GetLevel2TypesByLevel1(ctx context.Context, level1ID string) ([]models.ARALevel2Type, error) {
	iter := s.firestoreClient.Collection("ara_level2_types").
		Where("level1_id", "==", level1ID).
		Documents(ctx)
	defer iter.Stop()

	var types []models.ARALevel2Type
	for {
		doc, err := iter.Next()
		if err != nil {
			if err.Error() == "no more items in iterator" {
				break
			}
			return nil, fmt.Errorf("failed to iterate level2 types for level1 %s: %w", level1ID, err)
		}

		var araType models.ARALevel2Type
		if err := doc.DataTo(&araType); err != nil {
			log.Printf("Failed to unmarshal level2 type %s: %v", doc.Ref.ID, err)
			continue
		}

		if araType.ID == 0 {
			// Try to parse the document ID as integer
			if id, err := strconv.Atoi(doc.Ref.ID); err == nil {
				araType.ID = id
			}
		}

		types = append(types, araType)
	}

	return types, nil
}

// GetLevel1CategoryByID retrieves a specific Level 1 category by ID
func (s *ARATaxonomyService) GetLevel1CategoryByID(ctx context.Context, id int) (*models.ARALevel1Category, error) {
	idStr := strconv.Itoa(id)
	doc, err := s.firestoreClient.Collection("ara_level1_categories").Doc(idStr).Get(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get level1 category %d: %w", id, err)
	}

	var category models.ARALevel1Category
	if err := doc.DataTo(&category); err != nil {
		return nil, fmt.Errorf("failed to unmarshal level1 category %d: %w", id, err)
	}

	if category.ID == 0 {
		// Try to parse the document ID as integer
		if parsedID, err := strconv.Atoi(doc.Ref.ID); err == nil {
			category.ID = parsedID
		}
	}

	return &category, nil
}

// GetLevel2TypeByID retrieves a specific Level 2 type by ID
func (s *ARATaxonomyService) GetLevel2TypeByID(ctx context.Context, id int) (*models.ARALevel2Type, error) {
	idStr := strconv.Itoa(id)
	doc, err := s.firestoreClient.Collection("ara_level2_types").Doc(idStr).Get(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get level2 type %d: %w", id, err)
	}

	var araType models.ARALevel2Type
	if err := doc.DataTo(&araType); err != nil {
		return nil, fmt.Errorf("failed to unmarshal level2 type %d: %w", id, err)
	}

	if araType.ID == 0 {
		// Try to parse the document ID as integer
		if parsedID, err := strconv.Atoi(doc.Ref.ID); err == nil {
			araType.ID = parsedID
		}
	}

	return &araType, nil
}

// GetTaxonomyForClassification retrieves taxonomy data optimized for AI classification
func (s *ARATaxonomyService) GetTaxonomyForClassification(ctx context.Context) ([]models.ARALevel2Type, error) {
	// Get all Level 2 types with their Level 1 parent information
	level2Types, err := s.GetAllLevel2Types(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get level2 types: %w", err)
	}

	// Get all Level 1 categories to populate parent names
	level1Categories, err := s.GetAllLevel1Categories(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get level1 categories: %w", err)
	}

	// Create a map for quick Level 1 lookup
	level1Map := make(map[int]models.ARALevel1Category)
	for _, category := range level1Categories {
		level1Map[category.ID] = category
	}

	// Populate Level 1 names in Level 2 types
	for i := range level2Types {
		if level1Category, exists := level1Map[level2Types[i].Level1ID]; exists {
			level2Types[i].Level1Name = level1Category.Name
		}
	}

	return level2Types, nil
}

// ValidateClassificationResult validates that the classification result has valid ARA IDs
func (s *ARATaxonomyService) ValidateClassificationResult(ctx context.Context, result *models.ARAClassificationResult) error {
	// First validate Level 2 type exists (this is the primary classification)
	level2Type, err := s.GetLevel2TypeByID(ctx, result.ARALevel2ID)
	if err != nil {
		return fmt.Errorf("invalid ARA Level2 ID %d: %w", result.ARALevel2ID, err)
	}

	// Set the correct Level1 ID from the Level2 type
	result.ARALevel1ID = level2Type.Level1ID

	// Validate Level 1 category exists (only if Level1ID is not 0)
	if result.ARALevel1ID != 0 {
		_, err = s.GetLevel1CategoryByID(ctx, result.ARALevel1ID)
		if err != nil {
			return fmt.Errorf("invalid ARA Level1 ID %d (derived from Level2): %w", result.ARALevel1ID, err)
		}
	}

	return nil
}

// EnrichClassificationResult enriches a classification result with names from the taxonomy
func (s *ARATaxonomyService) EnrichClassificationResult(ctx context.Context, result *models.ARAClassificationResult) error {
	// Get Level 1 category name
	level1Category, err := s.GetLevel1CategoryByID(ctx, result.ARALevel1ID)
	if err != nil {
		return fmt.Errorf("failed to get level1 category name: %w", err)
	}
	result.ARALevel1Name = level1Category.Name

	// Get Level 2 type name
	level2Type, err := s.GetLevel2TypeByID(ctx, result.ARALevel2ID)
	if err != nil {
		return fmt.Errorf("failed to get level2 type name: %w", err)
	}
	result.ARALevel2Name = level2Type.Name

	return nil
}

// GetTaxonomyStats returns statistics about the ARA taxonomy
func (s *ARATaxonomyService) GetTaxonomyStats(ctx context.Context) (map[string]int, error) {
	stats := make(map[string]int)

	// Count Level 1 categories
	level1Iter := s.firestoreClient.Collection("ara_level1_categories").Documents(ctx)
	level1Count := 0
	for {
		_, err := level1Iter.Next()
		if err != nil {
			if err.Error() == "no more items in iterator" {
				break
			}
			return nil, fmt.Errorf("failed to count level1 categories: %w", err)
		}
		level1Count++
	}
	level1Iter.Stop()
	stats["level1_categories"] = level1Count

	// Count Level 2 types
	level2Iter := s.firestoreClient.Collection("ara_level2_types").Documents(ctx)
	level2Count := 0
	for {
		_, err := level2Iter.Next()
		if err != nil {
			if err.Error() == "no more items in iterator" {
				break
			}
			return nil, fmt.Errorf("failed to count level2 types: %w", err)
		}
		level2Count++
	}
	level2Iter.Stop()
	stats["level2_types"] = level2Count

	// Count Level 3 specifications
	level3Iter := s.firestoreClient.Collection("ara_level3_specifications").Documents(ctx)
	level3Count := 0
	for {
		_, err := level3Iter.Next()
		if err != nil {
			if err.Error() == "no more items in iterator" {
				break
			}
			return nil, fmt.Errorf("failed to count level3 specifications: %w", err)
		}
		level3Count++
	}
	level3Iter.Stop()
	stats["level3_specifications"] = level3Count

	return stats, nil
}
