package service

import (
	"context"
	"fmt"
	"log"

	"github.com/vima-inc/derental/models"
)

// GetProjectByLodgerID returns a project by lodger id.
func (s *Service) GetProjectByLodgerID(ctx context.Context, lodgerID string) ([]models.PopulatedProject, error) {
	projects, err := s.db.GetProjectByLodgerID(ctx, lodgerID)
	if err != nil {
		return nil, fmt.Errorf("unable to get project by lodger id error: %w", err)
	}

	lodger, err := s.GetLodgerByID(ctx, lodgerID)
	if err != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("unable get lodger error: %w", err)
	}

	if lodger.MemberID != "" {
		projectsMemberOf, err := s.db.GetProjectByMemberID(ctx, lodger.MemberID)
		if err != nil {
			return nil, fmt.Errorf("unable get project by member id error: %w", err)
		}
		projects = append(projects, projectsMemberOf...)
	}

	if len(projects) == 0 {
		return nil, nil
	}

	populatedProjects := make([]models.PopulatedProject, 0, len(projects))

	for _, project := range projects {
		populatedProject, err := s.db.PopulateProject(ctx, project.ID)
		if err != nil {
			return nil, fmt.Errorf("unable to populate project error: %w", err)
		}

		populatedProjects = append(populatedProjects, populatedProject)
	}

	return populatedProjects, nil
}

// GetProjectByID returns a project by id.
func (s *Service) GetProjectByID(ctx context.Context, id string) (models.Project, error) {
	return s.db.GetProjectByID(ctx, id)
}

// AddProject adds a new project.
func (s *Service) AddProject(ctx context.Context, project models.Project) error {
	lodger, err := s.db.GetLodgerByID(ctx, project.CreatorID)
	if err != nil {
		return fmt.Errorf("unable get lodger error: %w", err)
	}

	if lodger.MemberOf == "" {
		project.OwnerID = project.CreatorID
	} else {
		project.OwnerID = lodger.MemberOf
		if project.CreatorID != project.OwnerID {
			project.Members = append(project.Members, lodger.MemberID)
		}
	}

	project.CreditCheckFormID = project.CreditCheckForm.ID

	projectID, err := s.db.AddProject(ctx, project)
	if err != nil {
		return fmt.Errorf("unable to add project error: %w", err)
	}

	lodger.Projects = append(lodger.Projects, projectID)

	err = s.UpdateLodger(ctx, lodger)
	if err != nil {
		return fmt.Errorf("unable to update renter error: %w", err)
	}

	if project.Members != nil {
		for _, memberID := range project.Members {
			member, err := s.db.GetMemberByID(ctx, memberID, lodger.MemberOf)
			if err != nil {
				return fmt.Errorf("unable to get member error: %w", err)
			}

			err = s.UpdateMemberLodger(ctx, member.LodgerID, memberID, member)
			if err != nil {
				return fmt.Errorf("unable to update member error: %w", err)
			}
		}
	}

	return nil
}

// UpdateProject updates an existing project.
func (s *Service) UpdateProject(ctx context.Context, lodgerID string, project models.Project) error {
	projectToUpdate, err := s.db.GetProjectByID(ctx, project.ID)
	if err != nil {
		return fmt.Errorf("unable to get project with project_id: %s error: %w", project.ID, err)
	}

	if projectToUpdate.OwnerID != lodgerID {
		log.Printf("lodger with id: %s is not the owner of project with id: %s", lodgerID, project.ID)
	}

	project.OwnerID = projectToUpdate.OwnerID
	project.CreatorID = projectToUpdate.CreatorID

	for _, member := range project.Members {
		position := pos(projectToUpdate.Members, member)

		tmpLodger, err := s.db.GetLodgerByMemberID(ctx, member)
		if err != nil {
			return fmt.Errorf("unable to get lodger error: %w", err)
		}

		if position != -1 {
			err = s.RemoveProjectFromLodger(ctx, tmpLodger, project)
			if err != nil {
				return err
			}
		} else {
			err = s.AddProjectToLodger(ctx, tmpLodger, project)
			if err != nil {
				return err
			}
		}
	}

	return s.db.UpdateProject(ctx, project)
}

func (s *Service) AddProjectToLodger(ctx context.Context, tmpLodger models.Lodger, project models.Project) error {
	if pos(tmpLodger.Projects, project.ID) == -1 {
		tmpLodger.Projects = append(tmpLodger.Projects, project.ID)
	}

	err := s.UpdateLodger(ctx, tmpLodger)
	if err != nil {
		return fmt.Errorf("unable to update lodger error: %w", err)
	}

	return nil
}

func (s *Service) RemoveProjectFromLodger(ctx context.Context, tmpLodger models.Lodger, project models.Project) error {
	if pos(tmpLodger.Projects, project.ID) != -1 {
		tmpLodger.Projects = RemoveIndex(tmpLodger.Projects, pos(tmpLodger.Projects, project.ID))
	}

	err := s.UpdateLodger(ctx, tmpLodger)
	if err != nil {
		return fmt.Errorf("unable to update lodger error: %w", err)
	}

	return nil
}

// DeleteProject deletes an existing project by id.
func (s *Service) DeleteProject(ctx context.Context, id string) error {
	project, err := s.db.GetProjectByID(ctx, id)
	if err != nil {
		return fmt.Errorf("unable to get project with project_id: %s error: %w", id, err)
	}

	if len(project.Members) > 0 {
		err = s.RemoveAssociation(ctx, project)
		if err != nil {
			return err
		}
	}

	err = s.db.DeleteProject(ctx, id)
	if err != nil {
		return fmt.Errorf("unable to delete project with project_id: %s error: %w", id, err)
	}

	return nil
}

func (s *Service) RemoveAssociation(ctx context.Context, project models.Project) error {
	for _, member := range project.Members {
		tmpLodger, err := s.db.GetLodgerByMemberID(ctx, member)
		if err != nil {
			return fmt.Errorf("unable to get lodger error: %w", err)
		}

		if len(tmpLodger.Projects) > 0 {
			err = s.RemoveProjectFromLodger(ctx, tmpLodger, project)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (s *Service) RemoveProjectMember(ctx context.Context, member models.Member, id string, lodgerID string) error {
	if member.ID != lodgerID {
		log.Printf("lodger with id: %s is not the owner of member with id: %s", lodgerID, member.ID)
	}

	if len(member.Projects) > 0 {
		member.Projects = RemoveIndex(member.Projects, pos(member.Projects, id))

		err := s.UpdateMemberLodger(ctx, lodgerID, member.ID, member)
		if err != nil {
			return fmt.Errorf("unable to update member error: %w", err)
		}
	}

	return nil
}

// AffectOneEquipmentToProject affects one equipment to a project.
func (s *Service) AffectOneEquipmentToProject(ctx context.Context, ownerTD string, projectID string, input models.Project) error {
	project, err := s.db.GetProjectByID(ctx, projectID)
	if err != nil {
		return fmt.Errorf("unable to get project with project_id: %s error: %w", projectID, err)
	}

	if project.OwnerID != ownerTD {
		return fmt.Errorf("unable to affect equipment to project with project_id: %s, because the project is not owned by %s", projectID, ownerTD)
	}

	for _, equipmentID := range input.Equipments {
		for _, equipmentInProject := range project.Equipments {
			if equipmentID == equipmentInProject {
				return fmt.Errorf("unable to affect equipment to project with project_id: %s, because the equipment is already in the project", projectID)
			}

			project.Equipments = append(project.Equipments, equipmentID)
		}
	}

	return s.db.UpdateProject(ctx, project)
}

// AffectOneBidzEquipmentToProject affects one equipment to a project.
func (s *Service) AffectOneBidzEquipmentToProject(ctx context.Context, ownerTD string, projectID string, input models.Project) error {
	project, err := s.db.GetProjectByID(ctx, projectID)
	if err != nil {
		return fmt.Errorf("unable to get project with project_id: %s error: %w", projectID, err)
	}

	if project.OwnerID != ownerTD {
		return fmt.Errorf("unable to affect equipment to project with project_id: %s, because the project is not owned by %s", projectID, ownerTD)
	}

	for _, equipmentID := range input.BidzEquipments {
		for _, equipmentInProject := range project.BidzEquipments {
			if equipmentID == equipmentInProject {
				return fmt.Errorf("unable to affect equipment to project with project_id: %s, because the equipment is already in the project", projectID)
			}

			project.BidzEquipments = append(project.BidzEquipments, equipmentID)
		}
	}

	return s.db.UpdateProject(ctx, project)
}

// AffectOneMemberToProject affects one member to a project.
func (s *Service) AffectOneMemberToProject(ctx context.Context, ownerID string, projectID string, input models.Project) error {
	project, err := s.db.GetProjectByID(ctx, projectID)
	if err != nil {
		return fmt.Errorf("unable to get project with project_id: %s error: %w", projectID, err)
	}

	if project.OwnerID != ownerID {
		return fmt.Errorf("unable to affect member to project with project_id: %s, because the project is not owned by %s", projectID, ownerID)
	}

	for _, memberID := range input.Members {
		for _, memberInProject := range project.Members {
			if memberID == memberInProject {
				return fmt.Errorf("unable to affect member to project with project_id: %s, because the equipment is already in the project", projectID)
			}

			project.Members = append(project.Members, memberID)
		}

		member, err := s.db.GetMemberByID(ctx, memberID, ownerID)
		if err != nil {
			return fmt.Errorf("unable to get member with member_id: %s error: %w", memberID, err)
		}

		member.Projects = append(member.Projects, projectID)

		err = s.UpdateMemberLodger(ctx, ownerID, memberID, member)
		if err != nil {
			return fmt.Errorf("unable to update member error: %w", err)
		}
	}

	return s.db.UpdateProject(ctx, project)
}

func pos(slice []string, value string) int {
	for p, v := range slice {
		if v == value {
			return p
		}
	}

	return -1
}

func RemoveIndex(s []string, index int) []string {
	return append(s[:index], s[index+1:]...)
}
