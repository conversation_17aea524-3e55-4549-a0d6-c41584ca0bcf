package service

import (
	"context"
	"fmt"
	"log"
	"strings"

	"cloud.google.com/go/firestore"
	"github.com/vima-inc/derental/models"
)

// EquipmentDataService handles equipment data operations
type EquipmentDataService struct {
	firestoreClient  *firestore.Client
	equipmentDetails map[string]EquipmentDetail
}

// EquipmentDetail represents the structure from Single_equipment_details_data_generation.js (English only)
type EquipmentDetail struct {
	EquipmentName string `json:"equipment_name"`
	Section1      struct {
		En string `json:"en"`
	} `json:"section1"`
	Section2 struct {
		En string `json:"en"`
	} `json:"section2"`
}

// NewEquipmentDataService creates a new equipment data service
func NewEquipmentDataService(firestoreClient *firestore.Client) *EquipmentDataService {
	service := &EquipmentDataService{
		firestoreClient:  firestoreClient,
		equipmentDetails: make(map[string]EquipmentDetail),
	}

	// Equipment details loading is temporarily disabled to avoid parsing issues
	// The ARA classification works perfectly with basic equipment information
	log.Printf("Using basic equipment information for classification")

	return service
}

// normalizeEquipmentName normalizes equipment names for consistent lookup
func (s *EquipmentDataService) normalizeEquipmentName(name string) string {
	// Convert to lowercase and trim spaces
	normalized := strings.ToLower(strings.TrimSpace(name))

	// Remove common punctuation and extra spaces
	normalized = strings.ReplaceAll(normalized, ".", "")
	normalized = strings.ReplaceAll(normalized, ",", "")
	normalized = strings.ReplaceAll(normalized, "-", " ")
	normalized = strings.ReplaceAll(normalized, "_", " ")

	// Replace multiple spaces with single space
	for strings.Contains(normalized, "  ") {
		normalized = strings.ReplaceAll(normalized, "  ", " ")
	}

	return normalized
}

// GetAllToolerBidzEquipment retrieves all equipment from the tooler_bidz_equipment_inventory collection
func (s *EquipmentDataService) GetAllToolerBidzEquipment(ctx context.Context) ([]models.ToolerBidzEquipment, error) {
	iter := s.firestoreClient.Collection("tooler_bidz_equipment_inventory").Documents(ctx)
	defer iter.Stop()

	var equipment []models.ToolerBidzEquipment
	for {
		doc, err := iter.Next()
		if err != nil {
			if err.Error() == "no more items in iterator" {
				break
			}
			return nil, fmt.Errorf("failed to iterate equipment: %w", err)
		}

		var eq models.ToolerBidzEquipment
		if err := doc.DataTo(&eq); err != nil {
			log.Printf("Failed to unmarshal equipment %s: %v", doc.Ref.ID, err)
			continue
		}

		if eq.ID == "" {
			eq.ID = doc.Ref.ID
		}

		equipment = append(equipment, eq)
	}

	return equipment, nil
}

// GetUnclassifiedEquipment retrieves equipment that hasn't been ARA classified yet
func (s *EquipmentDataService) GetUnclassifiedEquipment(ctx context.Context) ([]models.ToolerBidzEquipment, error) {
	// Get all equipment and filter for unclassified ones
	// This is more reliable than using Where clause on a field that might not exist
	iter := s.firestoreClient.Collection("tooler_bidz_equipment_inventory").Documents(ctx)
	defer iter.Stop()

	var equipment []models.ToolerBidzEquipment
	for {
		doc, err := iter.Next()
		if err != nil {
			if err.Error() == "no more items in iterator" {
				break
			}
			return nil, fmt.Errorf("failed to iterate unclassified equipment: %w", err)
		}

		var eq models.ToolerBidzEquipment
		if err := doc.DataTo(&eq); err != nil {
			log.Printf("Failed to unmarshal equipment %s: %v", doc.Ref.ID, err)
			continue
		}

		if eq.ID == "" {
			eq.ID = doc.Ref.ID
		}

		// Only include equipment that hasn't been classified yet
		if eq.ARALevel1ID == 0 {
			equipment = append(equipment, eq)
		}
	}

	return equipment, nil
}

// GetEquipmentForClassification retrieves equipment for classification based on force reclassify setting
func (s *EquipmentDataService) GetEquipmentForClassification(ctx context.Context, forceReclassify bool) ([]models.ToolerBidzEquipment, error) {
	if forceReclassify {
		// Get all equipment when force reclassify is enabled
		return s.GetAllEquipment(ctx)
	} else {
		// Get only unclassified equipment by default
		return s.GetUnclassifiedEquipment(ctx)
	}
}

// GetAllEquipment retrieves all equipment from the database
func (s *EquipmentDataService) GetAllEquipment(ctx context.Context) ([]models.ToolerBidzEquipment, error) {
	iter := s.firestoreClient.Collection("tooler_bidz_equipment_inventory").Documents(ctx)
	defer iter.Stop()

	var equipment []models.ToolerBidzEquipment
	for {
		doc, err := iter.Next()
		if err != nil {
			if err.Error() == "no more items in iterator" {
				break
			}
			return nil, fmt.Errorf("failed to iterate all equipment: %w", err)
		}

		var eq models.ToolerBidzEquipment
		if err := doc.DataTo(&eq); err != nil {
			log.Printf("Failed to unmarshal equipment %s: %v", doc.Ref.ID, err)
			continue
		}

		if eq.ID == "" {
			eq.ID = doc.Ref.ID
		}

		equipment = append(equipment, eq)
	}

	return equipment, nil
}

// GetEquipmentByIDs retrieves specific equipment by their IDs
func (s *EquipmentDataService) GetEquipmentByIDs(ctx context.Context, ids []string) ([]models.ToolerBidzEquipment, error) {
	var equipment []models.ToolerBidzEquipment

	for _, id := range ids {
		doc, err := s.firestoreClient.Collection("tooler_bidz_equipment_inventory").Doc(id).Get(ctx)
		if err != nil {
			log.Printf("Failed to get equipment %s: %v", id, err)
			continue
		}

		var eq models.ToolerBidzEquipment
		if err := doc.DataTo(&eq); err != nil {
			log.Printf("Failed to unmarshal equipment %s: %v", id, err)
			continue
		}

		if eq.ID == "" {
			eq.ID = doc.Ref.ID
		}

		equipment = append(equipment, eq)
	}

	return equipment, nil
}

// PrepareEquipmentForClassification prepares equipment data for AI classification
func (s *EquipmentDataService) PrepareEquipmentForClassification(equipment []models.ToolerBidzEquipment) []models.EquipmentForClassification {
	var prepared []models.EquipmentForClassification

	for i, eq := range equipment {
		description := s.buildEquipmentDescription(eq)

		prepared = append(prepared, models.EquipmentForClassification{
			ID:          eq.ID,
			Name:        eq.NameEN,
			Description: description,
			ImageURL:    eq.ImageLink,
			Index:       i + 1,
		})
	}

	return prepared
}

// PrepareEquipmentForClassificationWithImageFallback prepares equipment data for AI classification with image fallback
func (s *EquipmentDataService) PrepareEquipmentForClassificationWithImageFallback(ctx context.Context, equipment []models.ToolerBidzEquipment) ([]models.EquipmentForClassification, error) {
	// First prepare equipment normally
	prepared := s.PrepareEquipmentForClassification(equipment)

	// Create image fallback service
	imageFallbackService := NewImageFallbackService(s.firestoreClient)

	// Find alternative images for problematic ones
	updatedEquipment, err := imageFallbackService.FindAlternativeImages(ctx, prepared)
	if err != nil {
		log.Printf("Warning: Image fallback service failed: %v", err)
		// Continue with original equipment if fallback fails
		return prepared, nil
	}

	return updatedEquipment, nil
}

// buildEquipmentDescription builds a comprehensive description for equipment
func (s *EquipmentDataService) buildEquipmentDescription(eq models.ToolerBidzEquipment) string {
	var description strings.Builder

	// Start with the equipment name
	description.WriteString(fmt.Sprintf("Equipment: %s", eq.NameEN))

	// Add existing description if available
	if eq.DescriptionEN != "" {
		description.WriteString(fmt.Sprintf(". Description: %s", eq.DescriptionEN))
	}

	// Add category information if available
	if len(eq.Category) > 0 {
		description.WriteString(fmt.Sprintf(". Current Category: %s", strings.Join(eq.Category, ", ")))
	}

	if len(eq.SubCategory) > 0 {
		description.WriteString(fmt.Sprintf(". Sub-category: %s", strings.Join(eq.SubCategory, ", ")))
	}

	// Try to get detailed description from the equipment details file
	normalizedName := s.normalizeEquipmentName(eq.NameEN)
	if detail, exists := s.equipmentDetails[normalizedName]; exists {
		description.WriteString(fmt.Sprintf(". Detailed Description: %s %s", detail.Section1.En, detail.Section2.En))
	} else {
		// Try partial matching for similar equipment names
		if partialDetail := s.findPartialMatch(eq.NameEN); partialDetail != nil {
			description.WriteString(fmt.Sprintf(". Related Description: %s %s", partialDetail.Section1.En, partialDetail.Section2.En))
		}
	}

	// Add aliases if available
	if len(eq.Alias.En) > 0 {
		description.WriteString(fmt.Sprintf(". Also known as: %s", strings.Join(eq.Alias.En, ", ")))
	}

	return description.String()
}

// findPartialMatch attempts to find a partial match for equipment names
func (s *EquipmentDataService) findPartialMatch(equipmentName string) *EquipmentDetail {
	normalizedName := s.normalizeEquipmentName(equipmentName)
	words := strings.Fields(normalizedName)

	// Try to find matches with individual words
	for detailName, detail := range s.equipmentDetails {
		detailWords := strings.Fields(detailName)

		// Check if any significant words match
		matchCount := 0
		for _, word := range words {
			if len(word) > 3 { // Only consider words longer than 3 characters
				for _, detailWord := range detailWords {
					if strings.Contains(detailWord, word) || strings.Contains(word, detailWord) {
						matchCount++
						break
					}
				}
			}
		}

		// If we have a good match ratio, return this detail
		if matchCount > 0 && float64(matchCount)/float64(len(words)) > 0.5 {
			return &detail
		}
	}

	return nil
}

// UpdateEquipmentWithARAClassification updates equipment with ARA classification results
func (s *EquipmentDataService) UpdateEquipmentWithARAClassification(ctx context.Context, equipmentID string, result models.ARAClassificationResult) error {
	_, err := s.firestoreClient.Collection("tooler_bidz_equipment_inventory").Doc(equipmentID).Update(ctx, []firestore.Update{
		{Path: "ara_level1_id", Value: result.ARALevel1ID},
		{Path: "ara_level2_id", Value: result.ARALevel2ID},
		{Path: "ara_level1_name", Value: result.ARALevel1Name},
		{Path: "ara_level2_name", Value: result.ARALevel2Name},
		{Path: "ara_classified_at", Value: firestore.ServerTimestamp},
	})

	if err != nil {
		return fmt.Errorf("failed to update equipment %s with ARA classification: %w", equipmentID, err)
	}

	return nil
}

// GetEquipmentStats returns statistics about equipment classification
func (s *EquipmentDataService) GetEquipmentStats(ctx context.Context) (map[string]int, error) {
	stats := make(map[string]int)

	// Count total equipment
	totalIter := s.firestoreClient.Collection("tooler_bidz_equipment_inventory").Documents(ctx)
	totalCount := 0
	for {
		_, err := totalIter.Next()
		if err != nil {
			if err.Error() == "no more items in iterator" {
				break
			}
			return nil, fmt.Errorf("failed to count total equipment: %w", err)
		}
		totalCount++
	}
	totalIter.Stop()
	stats["total_equipment"] = totalCount

	// Count classified equipment
	classifiedIter := s.firestoreClient.Collection("tooler_bidz_equipment_inventory").
		Where("ara_level1_id", "!=", "").
		Documents(ctx)
	classifiedCount := 0
	for {
		_, err := classifiedIter.Next()
		if err != nil {
			if err.Error() == "no more items in iterator" {
				break
			}
			return nil, fmt.Errorf("failed to count classified equipment: %w", err)
		}
		classifiedCount++
	}
	classifiedIter.Stop()
	stats["classified_equipment"] = classifiedCount
	stats["unclassified_equipment"] = totalCount - classifiedCount

	return stats, nil
}
