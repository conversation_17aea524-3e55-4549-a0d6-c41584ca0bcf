package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	db "github.com/vima-inc/derental/db/mocks"
	mailer "github.com/vima-inc/derental/mailer/mocks"
	"github.com/vima-inc/derental/models"
	payment "github.com/vima-inc/derental/payment/mocks"
)

func TestService_HandleWebhookCharge(t *testing.T) {
	ctx := context.Background()

	dbMock := new(db.Database)
	paymentMock := new(payment.Payment)

	orderID := "2dea06be-0149-45d8-ac6a-11e25adc594e"
	paymentIntentID := "pi_3Oj18TCsESVKI3Vo1w51UJ04"

	paymentMock.On("ChargeComplete", mock.Anything, mock.Anything).Return(
		models.ChargeResponse{
			OrderID:         orderID,
			PaymentIntentID: paymentIntentID,
		}, nil)

	dbMock.On("GetOrderByID", ctx, orderID).Return(models.Order{}, nil)

	dbMock.On("UpdateOrder", ctx, mock.MatchedBy(func(order models.Order) bool {
		assert.Equal(t, models.OrderStatusAuthorized, order.Status)
		assert.Equal(t, paymentIntentID, order.Payment.PaymentIntentID)

		return true
	})).Return(nil)

	service := New(WithDB(dbMock), WithPayment(paymentMock))

	err := service.handleCharge([]byte("stripe data"), "signature")
	require.NoError(t, err)
}

func TestService_HandleWebhookCheckout(t *testing.T) {
	ctx := context.Background()

	dbMock := new(db.Database)
	paymentMock := new(payment.Payment)
	mailerMock := new(mailer.MailerMockAsync)
	mailerMock.Wg.Add(3)

	mailerMock.On("SendFromTemplate", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
	mailerMock.On("SendFromTemplate", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
	mailerMock.On("SendFromTemplate", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)

	paymentIntentID := "pi_3Oj18TCsESVKI3Vo1w51UJ04"

	paymentMock.On("CheckoutComplete", mock.Anything, mock.Anything).Return(
		models.CheckoutResponse{
			PaymentIntentID: paymentIntentID,
			Amount:          1100,
			PaidAmount:      1125,
			TaxAmount:       147,
		}, nil)

	paymentMock.On("GetCoupon", "coupon").Return(
		&models.Coupon{
			ID:         "coupon",
			PercentOff: 10,
		}, nil)

	dbMock.On("GetOrderByPaymentIntentID", ctx, paymentIntentID).Return(
		models.Order{
			Payment: models.Payment{
				PaymentIntentID: "pi_3Oj18TCsESVKI3Vo1w51UJ04",
			},
			Status:    models.OrderStatusAuthorized,
			BookingID: "bookingID",
			Items: []models.Item{
				{
					Price:    1100,
					Quantity: 1,
					Coupon:   "coupon",
				},
			},
		}, nil)

	dbMock.On("UpdateOrder", ctx, mock.MatchedBy(func(order models.Order) bool {
		assert.Equal(t, models.OrderStatusAuthorized, order.Status)
		assert.Equal(t, int64(1100), order.Payment.Amount)
		assert.Equal(t, int64(1125), order.Payment.AmountCaptured)
		assert.Equal(t, int64(110), order.Payment.Discount)
		assert.Equal(t, int64(147), order.Payment.Tax)

		return true
	})).Return(nil)

	dbMock.On("GetBookEquipmentByID", ctx, "bookingID").Return(
		models.BookEquipment{}, nil)

	dbMock.On("UpdateBookEquipment", ctx, mock.MatchedBy(func(booking models.BookEquipment) bool {
		assert.Equal(t, models.BookingPending, booking.Status)
		assert.Equal(t, 11.0, booking.Amount)
		assert.Equal(t, 11.25, booking.PaidAmount)
		assert.Equal(t, 1.1, booking.DiscountAmount)

		return true
	})).Return(nil)

	dbMock.On("GetEquipperByID", ctx, mock.Anything).Return(models.Equipper{}, nil)
	dbMock.On("GetLodgerByID", ctx, mock.Anything).Return(models.Lodger{}, nil)

	service := New(WithDB(dbMock), WithPayment(paymentMock), WithMailer(mailerMock))

	err := service.handleCheckout([]byte("stripe data"), "signature")
	require.NoError(t, err)
}
