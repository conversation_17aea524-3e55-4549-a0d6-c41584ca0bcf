package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	db "github.com/vima-inc/derental/db/mocks"
	mailer "github.com/vima-inc/derental/mailer/mocks"
	"github.com/vima-inc/derental/models"
)

const (
	// equipmentID is the ID of the equipment that is used in the tests.
	equipmentID = "equipmentID"
)

func TestService_SendBidzRequest(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	mailDataFromBidzRequestRenter := map[string]string{
		"renter_name":               "lodger",
		"equipper_name":             "equipper",
		"equipment_name":            "Not specified",
		"type_of_propulsion":        "",
		"consumption":               "Not specified",
		"brand":                     "Not specified",
		"weight":                    "Not specified",
		"length_bidz":               "Not specified",
		"height":                    "Not specified",
		"kw":                        "Not specified",
		"cfm":                       "Not specified",
		"volt":                      "Not specified",
		"force":                     "Not specified",
		"usage_hours":               "Not specified",
		"btu":                       "Not specified",
		"capacity":                  "Not specified",
		"description":               "Not specified",
		"equipment_image_link":      "Not specified",
		"credit_check_form":         "Not specified",
		"contact":                   "Not specified",
		"payment_method":            string(models.PaymentMethodCreditAccount),
		"delivery_preference":       "Not specified",
		"start_date":                "0001-01-01",
		"return_date":               "0001-01-01",
		"billing_address_address":   "Not specified",
		"billing_address_zip_code":  "Not specified",
		"billing_address_city":      "Not specified",
		"billing_address_state":     "Not specified",
		"delivery_address_address":  "Not specified",
		"delivery_address_zip_code": "Not specified",
		"delivery_address_state":    "Not specified",
		"delivery_address_city":     "Not specified",
		"po_number":                 "Not specified",
		"equippers_emails":          "<EMAIL>",
		"equipment_utility":         "Not specified",
		"width":                     "Not specified",
		"on_click":                  "",
	}
	mailDataFromBidzRequestEquipper := map[string]string{
		"renter_name":               "lodger",
		"equipper_name":             "equipper",
		"equipment_name":            "Not specified",
		"type_of_propulsion":        "",
		"consumption":               "Not specified",
		"brand":                     "Not specified",
		"weight":                    "Not specified",
		"length_bidz":               "Not specified",
		"height":                    "Not specified",
		"kw":                        "Not specified",
		"cfm":                       "Not specified",
		"volt":                      "Not specified",
		"force":                     "Not specified",
		"usage_hours":               "Not specified",
		"btu":                       "Not specified",
		"capacity":                  "Not specified",
		"description":               "Not specified",
		"equipment_image_link":      "Not specified",
		"credit_check_form":         "Not specified",
		"contact":                   "Not specified",
		"payment_method":            string(models.PaymentMethodCreditAccount),
		"delivery_preference":       "Not specified",
		"start_date":                "0001-01-01",
		"return_date":               "0001-01-01",
		"billing_address_address":   "Not specified",
		"billing_address_zip_code":  "Not specified",
		"billing_address_city":      "Not specified",
		"billing_address_state":     "Not specified",
		"delivery_address_address":  "Not specified",
		"delivery_address_zip_code": "Not specified",
		"delivery_address_state":    "Not specified",
		"delivery_address_city":     "Not specified",
		"po_number":                 "Not specified",
		"equippers_emails":          "<EMAIL>",
		"equipment_utility":         "Not specified",
		"width":                     "Not specified",
		"on_click":                  "?sign_in=true",
	}
	dbMock := new(db.Database)

	dbMock.On("GetLodgerByID", ctx, "lodgerID").
		Return(models.Lodger{
			ID:      "lodgerID",
			Company: "lodger",
			Email:   "<EMAIL>",
		}, nil)

	dbMock.On("GetAllEquippersByCategoryCoverageArea", ctx, []string{"category"}, []string{"coverageArea"}).
		Return([]models.Equipper{
			{
				ID:      "equipperID",
				Company: "equipper",
				Email:   "<EMAIL>",
			},
		}, nil)

	dbMock.On("AddBidsRequest", ctx, "lodgerID", mock.MatchedBy(func(request models.BidsRequest) bool {
		assert.Equal(t, "requestID", request.ID)
		assert.Equal(t, "lodgerID", request.LodgerID)
		assert.Equal(t, "equipperID", request.EquipperID)
		assert.Equal(t, equipmentID, request.EquipmentID)
		assert.Equal(t, "<EMAIL>", request.EquipperEmail)
		assert.Equal(t, "<EMAIL>", request.LodgerEmail)

		return true
	})).Return(nil)

	mailerMock := new(mailer.MailerMockAsync)
	mailerMock.Wg.Add(2)

	mailerMock.On("SendFromTemplate", ctx, "sendgrid_send_renter_bidz_request_id", []string{"<EMAIL>"}, mailDataFromBidzRequestRenter).
		Return(nil)

	mailerMock.On("SendFromTemplate", ctx, "sendgrid_send_equipper_bidz_request_id", mock.Anything, mailDataFromBidzRequestEquipper).
		Return(nil)

	requestInput := models.BidsRequest{
		ID:               "requestID",
		EquipperID:       "equipperID",
		LodgerID:         "lodgerID",
		EquipmentID:      equipmentID,
		EquipperEmail:    "<EMAIL>",
		LodgerEmail:      "<EMAIL>",
		TypeOfPropulsion: []string{},
		Brand:            "",
		Weight:           "",
		Category:         []string{"category"},
		CoverageArea:     []string{"coverageArea"},
		PaymentMethod:    models.PaymentMethodCreditAccount,
	}

	Service := New(WithDB(dbMock), WithMailer(mailerMock))

	err := Service.SendBidzRequest(ctx, requestInput.LodgerID, requestInput)
	assert.NoError(t, err)
}

func TestService_AddOfferRequest(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	mailDataFromBidzOffer := map[string]string{
		"renter_name":               "Not specified",
		"equipper_name":             "Not specified",
		"equipment_name":            "Not specified",
		"equipment_utility":         "Not specified",
		"type_of_propulsion":        "",
		"price_per_day":             "0",
		"price_per_month":           "0",
		"price_per_week":            "0",
		"brand":                     "Not specified",
		"weight":                    "Not specified",
		"length_bidz":               "Not specified",
		"height":                    "Not specified",
		"kw":                        "Not specified",
		"cfm":                       "Not specified",
		"volt":                      "Not specified",
		"force":                     "Not specified",
		"usage_hours":               "Not specified",
		"btu":                       "Not specified",
		"capacity":                  "Not specified",
		"consumption":               "Not specified",
		"description":               "Not specified",
		"equipment_image_link":      "Not specified",
		"credit_check_form":         "Not specified",
		"contact":                   "Not specified",
		"payment_method":            string(models.PaymentMethodCreditAccount),
		"delivery_preference":       "Not specified",
		"start_date":                "0001-01-01",
		"return_date":               "0001-01-01",
		"billing_address_address":   "Not specified",
		"billing_address_zip_code":  "Not specified",
		"billing_address_city":      "Not specified",
		"billing_address_state":     "Not specified",
		"delivery_address_address":  "Not specified",
		"delivery_address_zip_code": "Not specified",
		"delivery_address_state":    "Not specified",
		"delivery_address_city":     "Not specified",
		"po_number":                 "Not specified",
		"equippers_emails":          "<EMAIL>",
		"width":                     "Not specified",
		"bids_offer_cancel_comment": "Not specified",
		"on_click":                  "?sign_in=true",
		"delivery_cost":             "0",
		"total_amount":              "0",
	}

	dbMock := new(db.Database)
	dbMock.On("AddOfferRequest", ctx, "equipperID", mock.MatchedBy(func(request models.BidzOffer) bool {
		assert.Equal(t, "offerID", request.ID)
		assert.Equal(t, "lodgerID", request.LodgerID)
		assert.Equal(t, "equipperID", request.EquipperID)
		assert.Equal(t, equipmentID, request.EquipmentID)
		assert.Equal(t, "<EMAIL>", request.EquipperEmail)
		assert.Equal(t, "<EMAIL>", request.LodgerEmail)

		return true
	})).Return(nil)

	dbMock.On("GetLodgerByID", ctx, "lodgerID").
		Return(models.Lodger{
			ID:      "lodgerID",
			Company: "lodger",
			Email:   "<EMAIL>",
		}, nil)

	mailerMock := new(mailer.MailerMockAsync)
	mailerMock.Wg.Add(2)

	mailerMock.On("SendFromTemplate", ctx, "sendgrid_send_renter_bidz_offer_id", []string{"<EMAIL>"}, mailDataFromBidzOffer).
		Return(nil)

	Service := New(WithDB(dbMock), WithMailer(mailerMock))

	input := models.BidzOffer{
		ID:               "offerID",
		EquipperID:       "equipperID",
		LodgerID:         "lodgerID",
		EquipmentID:      equipmentID,
		EquipperEmail:    "<EMAIL>",
		LodgerEmail:      "<EMAIL>",
		TypeOfPropulsion: []string{},
		Brand:            "",
		Weight:           "",
		Status:           "pending",
		ProjectID:        "projectID",
		PaymentMethod:    models.PaymentMethodCreditAccount,
	}

	err := Service.AddOfferRequest(ctx, "equipperID", input)
	assert.NoError(t, err)
}

func TestService_AcceptOffer(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	mailDataFromBidzOfferCancel := map[string]string{
		"renter_name":               "Not specified",
		"equipper_name":             "Not specified",
		"equipment_name":            "Not specified",
		"equipment_utility":         "Not specified",
		"type_of_propulsion":        "",
		"price_per_day":             "0",
		"price_per_month":           "0",
		"price_per_week":            "0",
		"brand":                     "Not specified",
		"weight":                    "Not specified",
		"length_bidz":               "Not specified",
		"height":                    "Not specified",
		"kw":                        "Not specified",
		"cfm":                       "Not specified",
		"volt":                      "Not specified",
		"force":                     "Not specified",
		"usage_hours":               "Not specified",
		"btu":                       "Not specified",
		"capacity":                  "Not specified",
		"consumption":               "Not specified",
		"description":               "Not specified",
		"equipment_image_link":      "Not specified",
		"credit_check_form":         "Not specified",
		"contact":                   "Not specified",
		"payment_method":            string(models.PaymentMethodCreditAccount),
		"delivery_preference":       "Not specified",
		"start_date":                "0001-01-01",
		"return_date":               "0001-01-01",
		"billing_address_address":   "Not specified",
		"billing_address_zip_code":  "Not specified",
		"billing_address_city":      "Not specified",
		"billing_address_state":     "Not specified",
		"delivery_address_address":  "Not specified",
		"delivery_address_zip_code": "Not specified",
		"delivery_address_state":    "Not specified",
		"delivery_address_city":     "Not specified",
		"po_number":                 "Not specified",
		"equippers_emails":          "<EMAIL>",
		"width":                     "Not specified",
		"bids_offer_cancel_comment": "Not specified",
		"on_click":                  "",
		"delivery_cost":             "0",
		"total_amount":              "0",
	}

	dbMock := new(db.Database)
	dbMock.On("GetLodgerByID", ctx, "lodgerID").
		Return(models.Lodger{
			ID:      "lodgerID",
			Company: "lodger",
			Email:   "<EMAIL>",
		}, nil)

	dbMock.On("GetEquipperByID", ctx, "equipperID").
		Return(models.Equipper{
			ID:      "equipperID",
			Company: "equipper",
			Email:   "<EMAIL>",
		}, nil)

	dbMock.On("GetBidsOfferID", ctx, "offerID").Return(models.BidzOffer{
		ID:            "offerID",
		LodgerID:      "lodgerID",
		EquipperID:    "equipperID",
		EquipmentID:   equipmentID,
		EquipperEmail: "<EMAIL>",
		LodgerEmail:   "<EMAIL>",
		Status:        "pending",
		ProjectID:     "projectID",
		Category:      []string{"category"},
		PaymentMethod: models.PaymentMethodCreditAccount,
	}, nil)

	dbMock.On("UpdateOffer", ctx, mock.MatchedBy(func(request models.BidzOffer) bool {
		assert.Equal(t, "lodgerID", request.LodgerID)
		assert.Equal(t, "equipperID", request.EquipperID)
		assert.Equal(t, equipmentID, request.EquipmentID)
		assert.Equal(t, "<EMAIL>", request.EquipperEmail)
		assert.Equal(t, "<EMAIL>", request.LodgerEmail)
		assert.Equal(t, "<EMAIL>", request.LodgerEmail)
		assert.Equal(t, models.PaymentMethodCreditAccount, request.PaymentMethod)

		return true
	})).Return(nil)

	dbMock.On("GetProjectByID", ctx, "projectID").Return(models.Project{
		ID: "projectID",
	},
		nil)

	dbMock.On("UpdateProject", ctx, mock.MatchedBy(func(request models.Project) bool {
		assert.Equal(t, "projectID", request.ID)

		return true
	}),
	).Return(nil)

	dbMock.On("GetBidsOfferByEquipmentID", ctx, equipmentID).Return([]models.BidzOffer{
		{
			ID:            "offerID",
			LodgerID:      "lodgerID",
			EquipperID:    "equipperID",
			EquipmentID:   equipmentID,
			EquipperEmail: "<EMAIL>",
			LodgerEmail:   "<EMAIL>",
			PaymentMethod: models.PaymentMethodCreditAccount,
		},
	}, nil)

	dbMock.On("GetBidsRequestByEquipmentID", ctx, equipmentID).Return([]models.BidsRequest{
		{
			ID:            "requestID",
			LodgerID:      "lodgerID",
			EquipperID:    "equipperID",
			EquipmentID:   equipmentID,
			EquipperEmail: "<EMAIL>",
			LodgerEmail:   "<EMAIL>",
			PaymentMethod: models.PaymentMethodCreditAccount,
		},
	}, nil)

	dbMock.On("GetAllEquippersByCategory", ctx, []string{"category"}).
		Return([]models.Equipper{
			{
				ID:      "equipperID",
				Company: "equipper",
				Email:   "<EMAIL>",
			},
		}, nil)

	dbMock.On("UpdateRequest", ctx, mock.MatchedBy(func(request models.BidsRequest) bool {
		assert.Equal(t, "lodgerID", request.LodgerID)
		assert.Equal(t, "equipperID", request.EquipperID)
		assert.Equal(t, equipmentID, request.EquipmentID)
		assert.Equal(t, "<EMAIL>", request.EquipperEmail)
		assert.Equal(t, "<EMAIL>", request.LodgerEmail)

		return true
	})).Return(nil)

	mailerMock := new(mailer.MailerMockAsync)
	mailerMock.Wg.Add(2)

	mailerMock.On("SendFromTemplate", ctx, "sendgrid_send_confirmation_equipper_bidz_offer_id", []string{"<EMAIL>"}, mailDataFromBidzOfferCancel).
		Return(nil)

	mailerMock.On("SendFromTemplate", ctx, "sendgrid_send_confirmation_renter_bidz_offer_id", []string{"<EMAIL>"}, mailDataFromBidzOfferCancel).
		Return(nil)

	Service := New(WithDB(dbMock), WithMailer(mailerMock))

	_, err := Service.AcceptOffer(ctx, "lodgerID", "offerID")
	assert.NoError(t, err)
}

func TestService_RejectOffer(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	dbMock := new(db.Database)

	dbMock.On("GetBidsOfferID", ctx, "offerID").Return(models.BidzOffer{
		ID:            "offerID",
		LodgerID:      "lodgerID",
		EquipperID:    "equipperID",
		EquipmentID:   equipmentID,
		EquipperEmail: "<EMAIL>",
		LodgerEmail:   "<EMAIL>",
		Status:        "pending",
		ProjectID:     "projectID",
		Category:      []string{"category"},
	}, nil)

	dbMock.On("UpdateOffer", ctx, mock.MatchedBy(func(offer models.BidzOffer) bool {
		assert.Equal(t, "lodgerID", offer.LodgerID)
		assert.Equal(t, "equipperID", offer.EquipperID)
		assert.Equal(t, equipmentID, offer.EquipmentID)
		assert.Equal(t, "<EMAIL>", offer.EquipperEmail)
		assert.Equal(t, "<EMAIL>", offer.LodgerEmail)
		assert.Equal(t, models.OfferRejected, offer.Status)

		return true
	})).Return(nil)

	Service := New(WithDB(dbMock))

	err := Service.RejectOffer(ctx, "lodgerID", "offerID")
	assert.NoError(t, err)
}

func TestService_AcceptRequest(t *testing.T) {
	t.Parallel()

	ctx := context.Background()

	dbMock := new(db.Database)

	dbMock.On("GetBidsRequestID", ctx, "requestID").Return(models.BidsRequest{
		ID:            "requestID",
		LodgerID:      "lodgerID",
		EquipperID:    "equipperID",
		EquipmentID:   equipmentID,
		EquipperEmail: "<EMAIL>",
		LodgerEmail:   "<EMAIL>",
		Status:        "pending",
	}, nil)

	dbMock.On("UpdateRequest", ctx, mock.MatchedBy(func(request models.BidsRequest) bool {
		assert.Equal(t, "lodgerID", request.LodgerID)
		assert.Equal(t, "equipperID", request.EquipperID)
		assert.Equal(t, equipmentID, request.EquipmentID)
		assert.Equal(t, "<EMAIL>", request.EquipperEmail)
		assert.Equal(t, "<EMAIL>", request.LodgerEmail)
		assert.Equal(t, models.RequestAccepted, request.Status)

		return true
	})).Return(nil)

	Service := New(WithDB(dbMock))

	err := Service.AcceptRequest(ctx, "equipperID", "requestID")
	assert.NoError(t, err)
}

func TestService_RejectRequest(t *testing.T) {
	t.Parallel()

	ctx := context.Background()

	dbMock := new(db.Database)

	dbMock.On("GetBidsRequestID", ctx, "requestID").Return(models.BidsRequest{
		ID:            "requestID",
		LodgerID:      "lodgerID",
		EquipperID:    "equipperID",
		EquipmentID:   equipmentID,
		EquipperEmail: "<EMAIL>",
		LodgerEmail:   "<EMAIL>",
		Status:        "pending",
	}, nil)

	dbMock.On("UpdateRequest", ctx, mock.MatchedBy(func(request models.BidsRequest) bool {
		assert.Equal(t, "lodgerID", request.LodgerID)
		assert.Equal(t, "equipperID", request.EquipperID)
		assert.Equal(t, equipmentID, request.EquipmentID)
		assert.Equal(t, "<EMAIL>", request.EquipperEmail)
		assert.Equal(t, "<EMAIL>", request.LodgerEmail)
		assert.Equal(t, models.RequestRejected, request.Status)

		return true
	})).Return(nil)

	requestInput := models.BidsRequest{
		ID:               "requestID",
		EquipperID:       "equipperID",
		LodgerID:         "lodgerID",
		EquipmentID:      equipmentID,
		EquipperEmail:    "<EMAIL>",
		LodgerEmail:      "<EMAIL>",
		TypeOfPropulsion: []string{},
		Brand:            "",
		Weight:           "",
		Category:         []string{"category"},
		CoverageArea:     []string{"coverageArea"},
	}

	Service := New(WithDB(dbMock))

	err := Service.RejectRequest(ctx, "equipperID", "requestID", requestInput)
	assert.NoError(t, err)
}

func TestService_CancelBidzOffer(t *testing.T) {
	t.Parallel()

	projects := []models.Project{
		{
			ID: "projectID",
		},
	}

	ctx := context.Background()

	mailDataFromBidzOfferCancel := map[string]string{
		"renter_name":               "Not specified",
		"equipper_name":             "Not specified",
		"equipment_name":            "Not specified",
		"equipment_utility":         "Not specified",
		"type_of_propulsion":        "",
		"price_per_day":             "0",
		"price_per_month":           "0",
		"price_per_week":            "0",
		"brand":                     "Not specified",
		"weight":                    "Not specified",
		"length_bidz":               "Not specified",
		"height":                    "Not specified",
		"kw":                        "Not specified",
		"cfm":                       "Not specified",
		"volt":                      "Not specified",
		"force":                     "Not specified",
		"usage_hours":               "Not specified",
		"btu":                       "Not specified",
		"capacity":                  "Not specified",
		"consumption":               "Not specified",
		"description":               "Not specified",
		"equipment_image_link":      "Not specified",
		"credit_check_form":         "Not specified",
		"contact":                   "Not specified",
		"payment_method":            string(models.PaymentMethodCreditAccount),
		"delivery_preference":       "Not specified",
		"start_date":                "0001-01-01",
		"return_date":               "0001-01-01",
		"billing_address_address":   "Not specified",
		"billing_address_zip_code":  "Not specified",
		"billing_address_city":      "Not specified",
		"billing_address_state":     "Not specified",
		"delivery_address_address":  "Not specified",
		"delivery_address_zip_code": "Not specified",
		"delivery_address_state":    "Not specified",
		"delivery_address_city":     "Not specified",
		"po_number":                 "Not specified",
		"equippers_emails":          "<EMAIL>",
		"width":                     "Not specified",
		"bids_offer_cancel_comment": "Not specified",
		"on_click":                  "",
		"delivery_cost":             "0",
		"total_amount":              "0",
	}

	dbMock := new(db.Database)

	dbMock.On("GetBidsOfferID", ctx, "offerID").Return(models.BidzOffer{
		ID:            "offerID",
		LodgerID:      "lodgerID",
		EquipperID:    "equipperID",
		EquipmentID:   equipmentID,
		EquipperEmail: "<EMAIL>",
		LodgerEmail:   "<EMAIL>",
		Status:        "pending",
		ProjectID:     "projectID",
		PaymentMethod: models.PaymentMethodCreditAccount,
	}, nil)

	dbMock.On("UpdateOffer", ctx, mock.MatchedBy(func(request models.BidzOffer) bool {
		assert.Equal(t, "lodgerID", request.LodgerID)
		assert.Equal(t, "equipperID", request.EquipperID)
		assert.Equal(t, equipmentID, request.EquipmentID)
		assert.Equal(t, "<EMAIL>", request.EquipperEmail)
		assert.Equal(t, "<EMAIL>", request.LodgerEmail)
		assert.Equal(t, models.OfferCanceled, request.Status)

		return true
	})).Return(nil)

	dbMock.On("GetLodgerByID", ctx, "lodgerID").
		Return(models.Lodger{
			ID:      "lodgerID",
			Company: "lodger",
			Email:   "<EMAIL>",
		}, nil)

	dbMock.On("GetProjectsByEquipmentID", ctx, "offerID").
		Return([]models.Project{
			{
				ID: "projectID",
			},
		}, nil)

	dbMock.On("BatchUpdateProjects", ctx, projects).
		Return(nil)

	mailerMock := new(mailer.MailerMockAsync)
	mailerMock.Wg.Add(2)

	mailerMock.On("SendFromTemplate", ctx, "sendgrid_send_canceled_renter_bidz_offer_id", []string{"<EMAIL>"}, mailDataFromBidzOfferCancel).
		Return(nil)

	input := models.BidzOffer{
		ID:               "offerID",
		EquipperID:       "equipperID",
		LodgerID:         "lodgerID",
		EquipmentID:      equipmentID,
		EquipperEmail:    "<EMAIL>",
		LodgerEmail:      "<EMAIL>",
		TypeOfPropulsion: []string{},
		Brand:            "",
		Weight:           "",
		Status:           "pending",
		ProjectID:        "projectID",
		PaymentMethod:    models.PaymentMethodCreditAccount,
	}

	Service := New(WithDB(dbMock), WithMailer(mailerMock))

	err := Service.CancelBidzOffer(ctx, "equipperID", "offerID", input)
	assert.NoError(t, err)
}
