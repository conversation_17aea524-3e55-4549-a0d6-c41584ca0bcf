package service

import (
	"context"
	"fmt"
	"log"
	"net/url"

	"github.com/vima-inc/derental/mailer"
	"github.com/vima-inc/derental/models"
)

func ReplaceMemberEmptyString(member models.Member) models.Member {
	attributes := []*string{
		&member.FirstName,
		&member.Email,
	}

	for _, attribute := range attributes {
		if *attribute == "" {
			*attribute = NotSpecified
		}
	}

	return member
}

func ReplaceProjectEmptyString(project models.Project) models.Project {
	attributes := []*string{
		&project.Name,
		&project.CreditCheckForm.CreditCheckFormPath,
		&project.DeliveryAddress.State,
		&project.DeliveryAddress.City,
		&project.DeliveryAddress.ZipCode,
		&project.DeliveryAddress.Address,
		&project.BillingAddress.State,
		&project.BillingAddress.City,
		&project.BillingAddress.ZipCode,
		&project.BillingAddress.Address,
	}

	for _, attribute := range attributes {
		if *attribute == "" {
			*attribute = NotSpecified
		}
	}

	return project
}

func mailDataFromInviteMember(frontendURL string, member models.Member, lodgerID string, company string, photoURL string, project models.Project) map[string]string {
	newProject := ReplaceProjectEmptyString(project)

	newMember := ReplaceMemberEmptyString(member)

	return map[string]string{
		"lodger_name":               newMember.FirstName,
		"member_id":                 newMember.ID,
		"lodger_id":                 lodgerID,
		"email":                     member.Email,
		"company":                   company,
		"photo_url":                 photoURL,
		"type":                      string(newMember.Type),
		"on_click":                  frontendURL,
		"project_name":              newProject.Name,
		"project_start_date":        newProject.StartDate.Format("2006-01-02"),
		"project_end_date":          newProject.EndDate.Format("2006-01-02"),
		"billing_address_address":   newProject.BillingAddress.Address,
		"billing_address_zip_code":  newProject.BillingAddress.ZipCode,
		"billing_address_city":      newProject.BillingAddress.City,
		"billing_address_state":     newProject.BillingAddress.State,
		"delivery_address_address":  newProject.DeliveryAddress.Address,
		"delivery_address_zip_code": newProject.DeliveryAddress.ZipCode,
		"delivery_address_city":     newProject.DeliveryAddress.City,
		"delivery_address_state":    newProject.DeliveryAddress.State,
		"credit_check_form":         newProject.CreditCheckForm.CreditCheckFormPath,
	}
}

// GetMembersLodger returns lodger members.
func (s *Service) GetMembersLodger(ctx context.Context, uid string) (models.Members, error) {
	lodger, err := s.db.GetLodgerByID(ctx, uid)
	if err != nil {
		return models.Members{}, fmt.Errorf("failed to get lodger by id: %s error: %w", uid, err)
	}

	if lodger.MemberOf == "" {
		return models.Members{}, fmt.Errorf("renter %s is not a member of any team", uid)
	}

	return s.db.GetMembers(ctx, lodger.MemberOf)
}

// GetMembersEquipper returns equipper members.
func (s *Service) GetMembersEquipper(ctx context.Context, uid string) (models.Members, error) {
	lodger, err := s.db.GetEquipperByID(ctx, uid)
	if err != nil {
		return models.Members{}, fmt.Errorf("failed to get equipper by id: %s error: %w", uid, err)
	}

	if lodger.MemberOf == "" {
		return models.Members{}, fmt.Errorf("equipper %s is not a member of any team", uid)
	}

	return s.db.GetMembers(ctx, lodger.MemberOf)
}

// GetMemberByID returns a member by id.
func (s *Service) GetMemberByID(ctx context.Context, memberID string, lodgerID string) (models.Member, error) {
	return s.db.GetMemberByID(ctx, memberID, lodgerID)
}

// AddMemberLodger add a new member.
func (s *Service) AddMemberLodger(ctx context.Context, lodgerID string, member models.Member) error {
	lodger, err := s.db.GetLodgerByID(ctx, lodgerID)
	if err != nil {
		return fmt.Errorf("failed to get lodger by id: %s error: %w", lodgerID, err)
	}

	if lodger.MemberOf == "" {
		owner := models.Member{
			Email:            lodger.Email,
			FirstName:        lodger.FirstName,
			LastName:         lodger.LastName,
			LodgerID:         lodger.ID,
			Type:             models.Admin,
			MembershipStatus: models.MemberOwner,
			PrivilegeLevel:   models.OwnerPrivilegeLevel,
			PhoneNumber:      lodger.PhoneNumber,
			PhotoURL:         lodger.PhotoURL,
		}

		owner, err = s.db.AddMember(ctx, lodgerID, owner)
		if err != nil {
			return fmt.Errorf("failed to send invitation error: %w", err)
		}

		lodger.MemberOf = lodgerID
		lodger.MemberID = owner.ID

		if err := s.db.UpdateLodger(ctx, lodger); err != nil {
			return fmt.Errorf("failed to update lodger: %s error: %w", lodgerID, err)
		}
	}

	connectedMember, err := s.db.GetMemberByID(ctx, lodger.MemberID, lodger.MemberOf)
	if err != nil {
		return fmt.Errorf("failed to get member by id: %s error: %w", lodger.MemberID, err)
	}

	privilegesLevel := models.GetPrivilegeLevel(member)
	member.PrivilegeLevel = privilegesLevel

	if connectedMember.PrivilegeLevel <= member.PrivilegeLevel {
		return fmt.Errorf("member can not invite a member with higher privilege level than his own :%s ", member.ID)
	}

	member, err = s.db.AddMember(ctx, lodger.MemberOf, member)
	if err != nil {
		return fmt.Errorf("failed to send invitation error: %w", err)
	}

	var project models.Project

	if len(member.Projects) > 0 {
		project, err = s.db.GetProjectByID(ctx, member.Projects[0])
		if err != nil {
			return fmt.Errorf("failed to get project by id: %s error: %w", project.ID, err)
		}
	}

	onClickLink := fmt.Sprintf("%s/%s?member_id=%s&lodger_id=%s&email=%s&lodgerAddress=%s;%s;%s;%s;%s&company=%s", s.frontendURL, "SignUpAsLodger", member.ID, connectedMember.LodgerID, url.QueryEscape(member.Email), lodger.Address.Address, lodger.Address.ZipCode, lodger.Address.City, lodger.Address.State, lodger.Address.Country, lodger.Company)

	if lodger.CommunicationPreferences == models.French {
		err = s.mailer.SendFromTemplate(ctx, mailer.SendInvitationMemberLodgerIDFR, []string{member.Email}, mailDataFromInviteMember(onClickLink, member, connectedMember.LodgerID, lodger.Company, lodger.PhotoURL, project))
		if err != nil {
			log.Print(fmt.Errorf("unable to send invite email to lodger, %s error: %w", member.ID, err))
		}
	} else {
		err = s.mailer.SendFromTemplate(ctx, mailer.SendInvitationMemberLodgerID, []string{member.Email}, mailDataFromInviteMember(onClickLink, member, connectedMember.LodgerID, lodger.Company, lodger.PhotoURL, project))
		if err != nil {
			log.Print(fmt.Errorf("unable to send invite email to lodger, %s error: %w", member.ID, err))
		}
	}

	return nil
}

// UpdateMember updates an existing member.
func (s *Service) UpdateMemberLodger(ctx context.Context, uid string, memberID string, member models.Member) error {
	lodger, err := s.db.GetLodgerByID(ctx, uid)
	if err != nil {
		return fmt.Errorf("failed to get lodger by id: %s error: %w", uid, err)
	}

	connectedMember, err := s.db.GetMemberByID(ctx, lodger.MemberID, lodger.MemberOf)
	if err != nil {
		return fmt.Errorf("failed to get member by id: %s error: %w", memberID, err)
	}

	memberToUpdate, err := s.db.GetMemberByID(ctx, memberID, lodger.MemberOf)
	if err != nil {
		return fmt.Errorf("failed to get member by id: %s error: %w", memberID, err)
	}

	if connectedMember.PrivilegeLevel < memberToUpdate.PrivilegeLevel {
		return fmt.Errorf("cannot update member with higher privilege level than the member :%s", memberID)
	}

	member.PrivilegeLevel = models.GetPrivilegeLevel(member)

	return s.db.UpdateMember(ctx, connectedMember.LodgerID, member)
}

// UpdateMember updates an existing member.
func (s *Service) UpdateMemberEquipper(ctx context.Context, uid string, memberID string, member models.Member) error {
	equipper, err := s.db.GetEquipperByID(ctx, uid)
	if err != nil {
		return fmt.Errorf("failed to get equipper by id: %s error: %w", uid, err)
	}

	connectedMember, err := s.db.GetMemberByID(ctx, equipper.MemberID, equipper.MemberOf)
	if err != nil {
		return fmt.Errorf("failed to get member by id: %s error: %w", memberID, err)
	}

	memberToUpdate, err := s.db.GetMemberByID(ctx, memberID, equipper.MemberOf)
	if err != nil {
		return fmt.Errorf("failed to get member by id: %s error: %w", memberID, err)
	}

	if connectedMember.PrivilegeLevel < memberToUpdate.PrivilegeLevel {
		return fmt.Errorf("cannot update member with higher privilege level than the member :%s", memberID)
	}

	member.PrivilegeLevel = models.GetPrivilegeLevel(member)

	return s.db.UpdateMember(ctx, connectedMember.LodgerID, member)
}

// DeleteMember deletes an existing member by id.
func (s *Service) DeleteMemberLodger(ctx context.Context, uid string, newMemberID string, memberID ...string) error {
	lodger, err := s.db.GetLodgerByID(ctx, uid)
	if err != nil {
		return fmt.Errorf("failed to get lodger by id: %s error: %w", uid, err)
	}

	connectedMember, err := s.db.GetMemberByID(ctx, lodger.MemberID, lodger.MemberOf)
	if err != nil {
		return fmt.Errorf("failed to get member by id: %s error: %w", memberID, err)
	}

	if newMemberID == "" {
		err = s.DeleteMemberWithoutAffectHisProject(ctx, memberID, lodger, connectedMember)
		if err != nil {
			return fmt.Errorf("failed to delete member: %s error: %w", memberID, err)
		}
	} else {
		err = s.DeleteMemberAndAffectProject(ctx, memberID, lodger, connectedMember, newMemberID)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *Service) DeleteMemberAndAffectProject(ctx context.Context, memberID []string, lodger models.Lodger, connectedMember models.Member, newMemberID string) error {
	memberToDelete, err := s.db.GetMemberByID(ctx, memberID[0], lodger.MemberOf)
	if err != nil {
		return fmt.Errorf("failed to get member by id: %s error: %w", memberID, err)
	}

	if connectedMember.PrivilegeLevel <= memberToDelete.PrivilegeLevel {
		return fmt.Errorf("cannot delete member with higher privilege level than the member :%s", memberID)
	}

	if memberToDelete.MembershipStatus != models.MemberPending {
		err = s.MemberAffect(ctx, memberID[0], memberToDelete, newMemberID)
		if err != nil {
			return err
		}
	}

	err = s.db.DeleteMember(ctx, connectedMember.LodgerID, memberID[0])
	if err != nil {
		return fmt.Errorf("failed to delete member: %s error: %w", memberID, err)
	}

	return nil
}

func (s *Service) DeleteMemberWithoutAffectHisProject(ctx context.Context, memberID []string, lodger models.Lodger, connectedMember models.Member) error {
	for _, id := range memberID {
		memberToUpdate, err := s.db.GetMemberByID(ctx, id, lodger.MemberOf)
		if err != nil {
			return fmt.Errorf("failed to get member by id: %s error: %w", memberID, err)
		}

		if connectedMember.PrivilegeLevel <= memberToUpdate.PrivilegeLevel {
			return fmt.Errorf("cannot delete member with higher privilege level than the member :%s", memberID)
		}

		if memberToUpdate.MembershipStatus != models.MemberPending {
			err = s.MemberCleanup(ctx, id, memberToUpdate)
			if err != nil {
				return err
			}
		}

		if err := s.db.DeleteMember(ctx, connectedMember.LodgerID, id); err != nil {
			return fmt.Errorf("failed to delete member: %s error: %w", id, err)
		}
	}

	return nil
}

// MemberAffect affect project of member to another member and delete the first member.
func (s *Service) MemberAffect(ctx context.Context, id string, memberToDelete models.Member, newMemberID string) error {
	lodgerToDelete, err := s.db.GetLodgerByMemberID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get lodger by member id: %s error: %w", id, err)
	}

	lodgerToUpdate, err := s.db.GetLodgerByMemberID(ctx, newMemberID)
	if err != nil {
		return fmt.Errorf("failed to get selected lodger by member id: %s error: %w", id, err)
	}

	lodgerToUpdate.Projects = append(lodgerToUpdate.Projects, lodgerToDelete.Projects...)

	err = s.db.UpdateLodger(ctx, lodgerToUpdate)
	if err != nil {
		return fmt.Errorf("failed to update selected lodger ")
	}

	newMember, err := s.db.GetMemberByID(ctx, newMemberID, lodgerToUpdate.MemberOf)
	if err != nil {
		return fmt.Errorf("failed to get member id: %s error: %w", newMemberID, err)
	}

	newMember.Projects = append(newMember.Projects, lodgerToDelete.Projects...)

	err = s.db.UpdateMember(ctx, newMemberID, newMember)
	if err != nil {
		return fmt.Errorf("failed to update selected member")
	}

	if lodgerToDelete.Projects != nil {
		for _, project := range memberToDelete.Projects {
			err := s.DeleteMemberFromProject(ctx, project, memberToDelete.ID)
			if err != nil {
				return fmt.Errorf("failed to delete member from project: %s error: %w", project, err)
			}
		}
	}

	if err := s.db.DeleteLodger(ctx, lodgerToDelete.ID); err != nil {
		return fmt.Errorf("failed to update renter: %s error: %w", id, err)
	}

	return nil
}

// MemberCleanup delete the deleted member from the projects.
func (s *Service) MemberCleanup(ctx context.Context, id string, memberToUpdate models.Member) error {
	lodger, err := s.db.GetLodgerByMemberID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get lodger by member id: %s error: %w", id, err)
	}

	if lodger.Projects != nil {
		for _, project := range memberToUpdate.Projects {
			err := s.DeleteMemberFromProject(ctx, project, memberToUpdate.ID)
			if err != nil {
				return fmt.Errorf("failed to delete member from project: %s error: %w", project, err)
			}
		}
	}

	lodger.MemberOf = ""
	lodger.MemberID = ""
	lodger.Projects = []string{}

	if err := s.db.UpdateLodger(ctx, lodger); err != nil {
		return fmt.Errorf("failed to update lodger: %s error: %w", id, err)
	}

	return nil
}

// DeleteMemberEquipper deletes an existing member by id.
func (s *Service) DeleteMemberEquipper(ctx context.Context, uid string, memberID ...string) error {
	equipper, err := s.db.GetEquipperByID(ctx, uid)
	if err != nil {
		return fmt.Errorf("failed to get lodger by id: %s error: %w", uid, err)
	}

	connectedMember, err := s.db.GetMemberByID(ctx, equipper.MemberID, equipper.MemberOf)
	if err != nil {
		return fmt.Errorf("failed to get member by id: %s error: %w", memberID, err)
	}

	for _, id := range memberID {
		memberToUpdate, err := s.db.GetMemberByID(ctx, id, equipper.MemberOf)
		if err != nil {
			return fmt.Errorf("failed to get member by id: %s error: %w", memberID, err)
		}

		if connectedMember.PrivilegeLevel <= memberToUpdate.PrivilegeLevel {
			return fmt.Errorf("cannot delete member with higher privilege level than the member :%s", memberID)
		}

		if memberToUpdate.MembershipStatus != models.MemberPending {
			equipper, err := s.db.GetEquipperByMemberID(ctx, id)
			if err != nil {
				return fmt.Errorf("failed to get equipper by member id: %s error: %w", id, err)
			}

			equipper.MemberOf = ""
			equipper.MemberID = ""

			if err := s.db.UpdateEquipper(ctx, equipper); err != nil {
				return fmt.Errorf("failed to update equipper: %s error: %w", id, err)
			}
		}

		if err := s.db.DeleteMember(ctx, connectedMember.LodgerID, id); err != nil {
			return fmt.Errorf("failed to delete member: %s error: %w", id, err)
		}
	}

	return nil
}

// MemberCleanup delete the deleted member from the projects.
func (s *Service) DeleteMemberFromProject(ctx context.Context, projectID string, memberID string) error {
	project, err := s.db.GetProjectByID(ctx, projectID)
	if err != nil {
		return fmt.Errorf("failed to get project by id: %s error: %w", projectID, err)
	}

	for index, id := range project.Members {
		if id == memberID {
			project.Members = append(project.Members[:index], project.Members[index+1:]...)

			break
		}
	}

	return s.db.UpdateProject(ctx, project)
}

// GetSelectionList returns a selection list of members with privileges less than or equal to the given member.
func (s *Service) GetSelectionList(ctx context.Context, memberID string, memberOf string) ([]models.Member, error) {
	member, err := s.GetMemberByID(ctx, memberID, memberOf)
	if err != nil {
		return nil, fmt.Errorf("failed to get member by id: %s error: %w", memberID, err)
	}

	return s.db.GetSelectionList(ctx, member)
}
