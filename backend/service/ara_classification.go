package service

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/vima-inc/derental/external/openrouter"
	"github.com/vima-inc/derental/models"
)

// ARAClassificationService handles ARA classification operations
type ARAClassificationService struct {
	openRouterClient     *openrouter.Client
	araTaxonomyService   *ARATaxonomyService
	equipmentDataService *EquipmentDataService
	cache                *ClassificationCache
	config               models.ARAClassificationConfig
}

// ClassificationCache provides caching for classification results
type ClassificationCache struct {
	cache map[string]models.ARAClassificationResult
	mutex sync.RWMutex
}

// NewClassificationCache creates a new classification cache
func NewClassificationCache() *ClassificationCache {
	return &ClassificationCache{
		cache: make(map[string]models.ARAClassificationResult),
	}
}

// Get retrieves a cached classification result
func (c *ClassificationCache) Get(equipmentName string) (models.ARAClassificationResult, bool) {
	c.mutex.RLock()         // Acquire read lock
	defer c.mutex.RUnlock() // Release read lock when done
	result, exists := c.cache[equipmentName]
	return result, exists
}

// Set stores a classification result in cache
func (c *ClassificationCache) Set(equipmentName string, result models.ARAClassificationResult) {
	c.mutex.Lock()         // Acquire write lock (blocks all other access)
	defer c.mutex.Unlock() // Release write lock when done
	c.cache[equipmentName] = result
}

// NewARAClassificationService creates a new ARA classification service
func NewARAClassificationService(
	openRouterClient *openrouter.Client,
	araTaxonomyService *ARATaxonomyService,
	equipmentDataService *EquipmentDataService,
	config models.ARAClassificationConfig,
) *ARAClassificationService {
	return &ARAClassificationService{
		openRouterClient:     openRouterClient,
		araTaxonomyService:   araTaxonomyService,
		equipmentDataService: equipmentDataService,
		cache:                NewClassificationCache(),
		config:               config,
	}
}

// ClassifyAllEquipment classifies all unclassified equipment in batches
func (s *ARAClassificationService) ClassifyAllEquipment(ctx context.Context) (*models.ARAClassificationStats, error) {
	startTime := time.Now()
	stats := &models.ARAClassificationStats{
		StartTime: startTime,
	}

	// Get equipment for classification (respects force reclassify setting)
	equipment, err := s.equipmentDataService.GetEquipmentForClassification(ctx, s.config.ForceReclassify)
	if err != nil {
		return stats, fmt.Errorf("failed to get equipment for classification: %w", err)
	}

	stats.TotalEquipment = len(equipment)
	if s.config.ForceReclassify {
		log.Printf("Found %d equipment items (force reclassify enabled)", len(equipment))
	} else {
		log.Printf("Found %d unclassified equipment items", len(equipment))
	}

	if len(equipment) == 0 {
		log.Println("No unclassified equipment found")
		endTime := time.Now()
		stats.EndTime = &endTime
		stats.ProcessingTime = endTime.Sub(startTime)
		return stats, nil
	}

	// Get ARA taxonomy for classification
	araTypes, err := s.araTaxonomyService.GetTaxonomyForClassification(ctx)
	if err != nil {
		return stats, fmt.Errorf("failed to get ARA taxonomy: %w", err)
	}

	log.Printf("Loaded %d ARA Level2 types for classification", len(araTypes))

	// Create batches
	batches := s.createBatches(equipment)
	stats.TotalBatches = len(batches)

	log.Printf("Created %d batches with batch size %d", len(batches), s.config.BatchSize)

	// Process batches
	for i, batch := range batches {
		log.Printf("Processing batch %d/%d (%d equipment items)", i+1, len(batches), len(batch))

		if s.config.DryRun {
			// Estimate cost for this batch
			prepared, prepErr := s.equipmentDataService.PrepareEquipmentForClassificationWithImageFallback(ctx, batch)
			if prepErr != nil {
				prepared = s.equipmentDataService.PrepareEquipmentForClassification(batch)
			}
			cost := s.openRouterClient.EstimateCost(prepared, araTypes, s.config)
			stats.EstimatedCost += cost
			stats.ProcessedBatches++
			stats.ProcessedEquipment += len(batch)
			log.Printf("Dry run: Batch %d would cost approximately $%.4f", i+1, cost)
			continue
		}

		err := s.processBatch(ctx, batch, araTypes, stats)
		if err != nil {
			log.Printf("Failed to process batch %d: %v", i+1, err)
			continue
		}

		stats.ProcessedBatches++

		// Rate limiting between batches
		if i < len(batches)-1 {
			time.Sleep(time.Duration(s.config.RetryDelaySeconds) * time.Second)
		}
	}

	endTime := time.Now()
	stats.EndTime = &endTime
	stats.ProcessingTime = endTime.Sub(startTime)

	log.Printf("Classification completed. Processed %d/%d equipment in %v",
		stats.ProcessedEquipment, stats.TotalEquipment, stats.ProcessingTime)

	return stats, nil
}

// ClassifyEquipmentByIDs classifies specific equipment by their IDs
func (s *ARAClassificationService) ClassifyEquipmentByIDs(ctx context.Context, equipmentIDs []string) (*models.ARAClassificationStats, error) {
	startTime := time.Now()
	stats := &models.ARAClassificationStats{
		StartTime: startTime,
	}

	// Get equipment by IDs
	equipment, err := s.equipmentDataService.GetEquipmentByIDs(ctx, equipmentIDs)
	if err != nil {
		return stats, fmt.Errorf("failed to get equipment by IDs: %w", err)
	}

	stats.TotalEquipment = len(equipment)

	if len(equipment) == 0 {
		log.Println("No equipment found for provided IDs")
		endTime := time.Now()
		stats.EndTime = &endTime
		stats.ProcessingTime = endTime.Sub(startTime)
		return stats, nil
	}

	// Get ARA taxonomy
	araTypes, err := s.araTaxonomyService.GetTaxonomyForClassification(ctx)
	if err != nil {
		return stats, fmt.Errorf("failed to get ARA taxonomy: %w", err)
	}

	// Create batches
	batches := s.createBatches(equipment)
	stats.TotalBatches = len(batches)

	// Process batches
	for i, batch := range batches {
		err := s.processBatch(ctx, batch, araTypes, stats)
		if err != nil {
			log.Printf("Failed to process batch %d: %v", i+1, err)
			continue
		}

		stats.ProcessedBatches++

		// Rate limiting between batches
		if i < len(batches)-1 {
			time.Sleep(time.Duration(s.config.RetryDelaySeconds) * time.Second)
		}
	}

	endTime := time.Now()
	stats.EndTime = &endTime
	stats.ProcessingTime = endTime.Sub(startTime)

	return stats, nil
}

// createBatches creates batches of equipment for processing
func (s *ARAClassificationService) createBatches(equipment []models.ToolerBidzEquipment) [][]models.ToolerBidzEquipment {
	var batches [][]models.ToolerBidzEquipment

	for i := 0; i < len(equipment); i += s.config.BatchSize {
		end := i + s.config.BatchSize
		if end > len(equipment) {
			end = len(equipment)
		}
		batches = append(batches, equipment[i:end])
	}

	return batches
}

// processBatch processes a single batch of equipment
func (s *ARAClassificationService) processBatch(ctx context.Context, batch []models.ToolerBidzEquipment, araTypes []models.ARALevel2Type, stats *models.ARAClassificationStats) error {
	// Check cache first if enabled
	var uncachedEquipment []models.ToolerBidzEquipment
	var cachedResults []models.ARAClassificationResult

	if s.config.UseCache {
		for _, eq := range batch {
			if cached, exists := s.cache.Get(eq.NameEN); exists {
				cached.EquipmentID = eq.ID
				cachedResults = append(cachedResults, cached)
				stats.ProcessedEquipment++
				stats.SuccessfullyClassified++
			} else {
				uncachedEquipment = append(uncachedEquipment, eq)
			}
		}

		// Update cached results in database
		for _, result := range cachedResults {
			err := s.equipmentDataService.UpdateEquipmentWithARAClassification(ctx, result.EquipmentID, result)
			if err != nil {
				log.Printf("Failed to update cached classification for equipment %s: %v", result.EquipmentID, err)
			}
		}

		if len(uncachedEquipment) == 0 {
			log.Printf("All equipment in batch found in cache")
			return nil
		}

		batch = uncachedEquipment
	}

	// Prepare equipment for classification with image fallback
	prepared, prepErr := s.equipmentDataService.PrepareEquipmentForClassificationWithImageFallback(ctx, batch)
	if prepErr != nil {
		log.Printf("Warning: Image fallback failed, using basic preparation: %v", prepErr)
		prepared = s.equipmentDataService.PrepareEquipmentForClassification(batch)
	}

	// Retry logic
	var response *models.ARAClassificationResponse
	var err error

	for attempt := 1; attempt <= s.config.MaxRetries; attempt++ {
		response, err = s.openRouterClient.ClassifyEquipmentBatch(ctx, prepared, araTypes, s.config)
		if err == nil && response.Success {
			break
		}

		log.Printf("Batch classification attempt %d failed: %v", attempt, err)
		if attempt < s.config.MaxRetries {
			time.Sleep(time.Duration(s.config.RetryDelaySeconds) * time.Second)
		}
	}

	if err != nil || !response.Success {
		stats.FailedClassifications += len(batch)
		return fmt.Errorf("failed to classify batch after %d attempts: %w", s.config.MaxRetries, err)
	}

	// Process and validate results
	for _, result := range response.Classifications {
		// Validate the classification result
		err := s.araTaxonomyService.ValidateClassificationResult(ctx, &result)
		if err != nil {
			log.Printf("Invalid classification result for equipment index %d: %v", result.EquipmentIndex, err)
			stats.FailedClassifications++
			continue
		}

		// Enrich with names
		err = s.araTaxonomyService.EnrichClassificationResult(ctx, &result)
		if err != nil {
			log.Printf("Failed to enrich classification result: %v", err)
		}

		// Update equipment in database
		err = s.equipmentDataService.UpdateEquipmentWithARAClassification(ctx, result.EquipmentID, result)
		if err != nil {
			log.Printf("Failed to update equipment %s: %v", result.EquipmentID, err)
			stats.FailedClassifications++
			continue
		}

		// Cache the result if caching is enabled
		if s.config.UseCache && result.EquipmentIndex > 0 && result.EquipmentIndex <= len(batch) {
			equipmentName := batch[result.EquipmentIndex-1].NameEN
			s.cache.Set(equipmentName, result)
		}

		stats.ProcessedEquipment++
		stats.SuccessfullyClassified++
	}

	return nil
}

// EstimateClassificationCost estimates the cost for classifying equipment
func (s *ARAClassificationService) EstimateClassificationCost(ctx context.Context, equipmentIDs []string) (float64, error) {
	var equipment []models.ToolerBidzEquipment
	var err error

	if len(equipmentIDs) == 0 {
		// Estimate for equipment based on force reclassify setting
		equipment, err = s.equipmentDataService.GetEquipmentForClassification(ctx, s.config.ForceReclassify)
	} else {
		// Estimate for specific equipment
		equipment, err = s.equipmentDataService.GetEquipmentByIDs(ctx, equipmentIDs)
	}

	if err != nil {
		return 0, fmt.Errorf("failed to get equipment: %w", err)
	}

	// Get ARA taxonomy
	araTypes, err := s.araTaxonomyService.GetTaxonomyForClassification(ctx)
	if err != nil {
		return 0, fmt.Errorf("failed to get ARA taxonomy: %w", err)
	}

	// Create batches and estimate cost
	batches := s.createBatches(equipment)
	totalCost := 0.0

	for _, batch := range batches {
		prepared, prepErr := s.equipmentDataService.PrepareEquipmentForClassificationWithImageFallback(ctx, batch)
		if prepErr != nil {
			prepared = s.equipmentDataService.PrepareEquipmentForClassification(batch)
		}
		batchCost := s.openRouterClient.EstimateCost(prepared, araTypes, s.config)
		totalCost += batchCost
	}

	return totalCost, nil
}
