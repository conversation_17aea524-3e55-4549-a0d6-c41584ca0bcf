package service

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/vima-inc/derental/mailer"
	"github.com/vima-inc/derental/models"
)

// AddLead adds a new lead.
func (s *Service) AddLead(ctx context.Context, lead models.Lead) error {
	err := s.db.AddLead(ctx, lead)
	if err != nil {
		return fmt.Errorf("unable to add lead: %w", err)
	}

	go func() {
		newCtx := context.Background()

		err := s.mailer.SendFromTemplate(newCtx, mailer.SendgridLeadTemplateID, []string{"<EMAIL>"}, mailDataFromLead(lead))
		if err != nil {
			log.Print(fmt.Errorf("unable to send lead information email error: %w", err))
		}
	}()

	return nil
}

// Sending Leads attributes to Tooler's Team.
func mailDataFromLead(lead models.Lead) map[string]string {
	return map[string]string{
		"name":           lead.Name,
		"email":          lead.Email,
		"phone_number":   lead.PhoneNumber,
		"reach_out_date": time.Now().Format("2006-01-02"),
	}
}
