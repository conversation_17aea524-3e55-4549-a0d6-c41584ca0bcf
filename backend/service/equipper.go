package service

import (
	"cmp"
	"context"
	"fmt"
	"io"
	"log"
	"path/filepath"
	"slices"
	"sync"

	"github.com/vima-inc/derental/models"
)

// GetEquipperByID returns an equipper by id.
func (s *Service) GetEquipperByID(ctx context.Context, id string) (models.Equipper, error) {
	return s.db.GetEquipperByID(ctx, id)
}

// GetEquipperByEmail returns an equipper by email.
func (s *Service) GetEquipperByEmail(ctx context.Context, email string) (models.Equipper, error) {
	return s.db.GetEquipperByEmail(ctx, email)
}

// GetAllEquipper returns all equippers.
func (s *Service) GetAllEquipper(ctx context.Context) ([]models.Equipper, error) {
	if s.cache != nil {
		if v, ok := s.cache.Get("all_equippers"); ok {
			return v.([]models.Equipper), nil
		}

		equippers, err := s.db.GetAllEquipper(ctx)
		if err != nil {
			return nil, err
		}

		s.cache.Set("all_equippers", equippers, 60)
	}

	return s.db.GetAllEquipper(ctx)
}

// GetAllEquippersByCategory returns all equippers by category.
func (s *Service) GetAllEquippersByCategory(ctx context.Context, category []string) ([]models.Equipper, error) {
	return s.db.GetAllEquippersByCategory(ctx, category)
}

// GetEquippersByCountry returns all equippers by country.
func (s *Service) GetEquippersByCountry(ctx context.Context, countryCode string) ([]models.Equipper, error) {
	return s.db.GetEquippersByCountry(ctx, countryCode)
}

// AddEquipper adds a new equipper.
func (s *Service) AddEquipper(ctx context.Context, equipper models.Equipper) error {
	return s.db.AddEquipper(ctx, equipper)
}

// UpdateEquipper updates an existing equipper.
func (s *Service) UpdateEquipper(ctx context.Context, equipper models.Equipper) error {
	dbEquipper, err := s.db.GetEquipperByID(ctx, equipper.ID)
	if err != nil {
		return fmt.Errorf("error getting equipper: %w", err)
	}

	equipper.HasInventory = dbEquipper.HasInventory
	equipper.HasRequests = dbEquipper.HasRequests
	equipper.Address = dbEquipper.Address

	if dbEquipper.Email != equipper.Email {
		err := s.auth.UpdateEmail(ctx, equipper.ID, equipper.Email)
		if err != nil {
			return fmt.Errorf("error updating email: %w", err)
		}
	}

	if dbEquipper.Address != equipper.Address || dbEquipper.Email != equipper.Email || !isEqual(dbEquipper.CoverageArea, equipper.CoverageArea) || dbEquipper.Company != equipper.Company {
		var equipments []models.Equipment

		equipments, err = s.GetAllEquipmentsByEquipperID(ctx, equipper.ID)
		if err != nil {
			return fmt.Errorf("unable get all equipments by equipper id error: %w", err)
		}

		wg := sync.WaitGroup{}

		wg.Add(len(equipments))

		for _, equipment := range equipments {
			go func(equipment models.Equipment) {
				defer wg.Done()

				equipment.EquipperEmail = equipper.Email
				equipment.EquipperName = equipper.Company
				equipment.Address.Address = equipper.Address.Address
				equipment.Address.City = equipper.Address.City
				equipment.Address.State = equipper.Address.State
				equipment.Address.Country = equipper.Address.Country
				equipment.Address.CountryCode = equipper.Address.CountryCode
				equipment.Address.ZipCode = equipper.Address.ZipCode
				equipment.Address.Latitude = equipper.Address.Latitude
				equipment.Address.Longitude = equipper.Address.Longitude
				equipment.CoverageArea = equipper.CoverageArea

				err = s.UpdateEquipment(ctx, equipment.EquipperID, equipment)
				if err != nil {
					log.Printf("error updating equipment: %v", fmt.Errorf("error updating equipment: %w", err))
				}
			}(equipment)
		}

		wg.Wait()
	}

	return s.db.UpdateEquipper(ctx, equipper)
}

// DeleteEquipper deletes an existing equipper by id.
func (s *Service) DeleteEquipper(ctx context.Context, id string) error {
	err := s.auth.DeleteUser(ctx, id)
	if err != nil {
		return fmt.Errorf("error deleting user: %w", err)
	}

	return s.db.DeleteEquipper(ctx, id)
}

// UploadPhoto save equipper photo in storage and update photo url with new path.
func (s *Service) UploadPhoto(ctx context.Context, equipperID string, fileName string, data io.Reader) error {
	path := fmt.Sprintf("equipper/%s%s", equipperID, filepath.Ext(fileName))

	photoURL, err := s.storage.Write(ctx, s.storage.DefaultBucket(), path, data)
	if err != nil {
		return fmt.Errorf("error uploading photo: %w", err)
	}

	equipper, err := s.db.GetEquipperByID(ctx, equipperID)
	if err != nil {
		return fmt.Errorf("error getting equipper: %w", err)
	}

	equipper.PhotoURL = photoURL

	err = s.db.UpdateEquipper(ctx, equipper)
	if err != nil {
		return fmt.Errorf("error updating equipper: %w", err)
	}

	if equipper.MemberOf != "" || equipper.MemberID != "" {
		member, err := s.GetMemberByID(ctx, equipper.MemberID, equipper.MemberOf)
		if err != nil {
			return fmt.Errorf("can not get member by id error %w", err)
		}

		member.PhotoURL = photoURL

		err = s.UpdateMemberEquipper(ctx, equipper.ID, equipper.MemberID, member)
		if err != nil {
			return fmt.Errorf("can not update member error %w", err)
		}
	}

	return nil
}

func isEqual[T cmp.Ordered](a, b []T) bool {
	slices.Sort(a)
	slices.Sort(b)

	return slices.Equal(a, b)
}

// GetEquipperByUserName returns an equipper by userName.
func (s *Service) GetEquipperByUserName(ctx context.Context, userName string) (models.Equipper, error) {
	return s.db.GetEquipperByUserName(ctx, userName)
}
