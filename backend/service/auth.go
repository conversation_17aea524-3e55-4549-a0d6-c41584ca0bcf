package service

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"time"

	"github.com/vima-inc/derental/auth"
	"github.com/vima-inc/derental/mailer"
	"github.com/vima-inc/derental/models"
)

const (
	minToken = 100000
	maxToken = 999999

	rating = 4

	claimUserType  = "user_type"
	ClaimInventory = "has_inventory"
)

// SignIn validates the provided credentials and returns the token.
func (s *Service) SignIn(ctx context.Context, input models.SignInInput) (models.AuthResult, error) {
	res, err := s.auth.SignIn(ctx, input.Email, input.Password)
	if err != nil {
		return models.AuthResult{}, err
	}

	claims, err := s.auth.GetUserClaims(ctx, res.ID)
	if err != nil {
		return models.AuthResult{}, err
	}

	authResp := models.AuthResult{
		ID:           res.ID,
		Token:        res.Token,
		Email:        res.Email,
		RefreshToken: res.RefreshToken,
		ExpiresIn:    res.ExpiresIn,
	}

	if userType, ok := claims[claimUserType].(string); ok {
		switch userType {
		case string(models.LodgerUserType):
			authResp.UserType = string(models.LodgerUserType)
			lodger, err := s.GetLodgerByID(ctx, authResp.ID)
			if err != nil {
				return models.AuthResult{}, err
			}
			authResp.ImageLink = lodger.PhotoURL
			authResp.Country = lodger.Address.Country
			authResp.Lang = lodger.CommunicationPreferences
		case string(models.EquipperUserType):
			authResp.UserType = string(models.EquipperUserType)
			equipper, err := s.GetEquipperByID(ctx, authResp.ID)
			if err != nil {
				return models.AuthResult{}, err
			}
			authResp.Country = equipper.Address.Country
			authResp.ImageLink = equipper.PhotoURL
			authResp.Lang = equipper.CommunicationPreferences
			authResp.HasInventory = equipper.HasInventory
		}
	}

	return authResp, nil
}

// SignUp creates a new user and returns the id and the token for the user.
func (s *Service) SignUp(ctx context.Context, input models.SignUpInput, memberID string, lodgerID string) (models.AuthResult, error) {
	authenticationResult, err := s.auth.SignUp(ctx, input.Email, input.Password)
	if err != nil {
		return models.AuthResult{}, err
	}

	var userType models.UserType

	if memberID != "" {

		member, err := s.db.GetMemberByID(ctx, memberID, lodgerID)
		if err != nil {
			_ = s.auth.DeleteUser(ctx, authenticationResult.ID)
			return models.AuthResult{}, fmt.Errorf("failed to check member: %v", err)
		}
		if member.ID == "" {
			return models.AuthResult{}, fmt.Errorf("member with ID %s does not exist", memberID)
		}

	}

	switch {
	case input.IsLodger:
		err = s.db.AddLodger(ctx, models.Lodger{
			ID:                       authenticationResult.ID,
			Type:                     models.LodgerType(input.Type),
			UserName:                 input.UserName,
			Email:                    input.Email,
			FirstName:                input.FirstName,
			LastName:                 input.LastName,
			FullName:                 input.FullName,
			Company:                  input.Company,
			PhoneNumber:              input.PhoneNumber,
			PhotoURL:                 input.PhotoURL,
			MemberOf:                 lodgerID,
			Currency:                 input.Currency,
			MemberID:                 memberID,
			CommunicationPreferences: input.CommunicationPreferences,
			Address: models.Address{
				Address:     input.Address,
				Address2:    input.Address2,
				ZipCode:     input.ZipCode,
				City:        input.City,
				Country:     input.Country,
				CountryCode: input.CountryCode,
				State:       input.State,
			},
		})
		if err != nil {
			return models.AuthResult{}, fmt.Errorf("cannot add lodger: %v", err)
		}

		userType = models.LodgerUserType
	default:
		var user bool
		var auth models.AuthResult
		userType, user, auth, err = s.AddDBEquipper(ctx, input, authenticationResult, lodgerID, memberID)
		if user {
			return auth, err
		}
	}

	if err != nil {
		_ = s.auth.DeleteUser(ctx, authenticationResult.ID)
		return models.AuthResult{}, err
	}

	if memberID != "" && input.IsLodger {
		authResult, err := s.signUpMembershipHandling(ctx, userType, lodgerID, input, memberID)
		if err != nil {
			return authResult, err
		}
	}

	if err := s.auth.SetCustomUserClaims(ctx, authenticationResult.ID, map[string]interface{}{
		claimUserType: userType,
	}); err != nil {
		return models.AuthResult{}, err
	}

	switch userType {
	case models.LodgerUserType:
		s.SendEmail(ctx, input, authenticationResult, mailer.SendWelcomeTemplateIDFR, mailer.SendWelcomeTemplateID)
	case models.EquipperUserType:
		s.SendEmail(ctx, input, authenticationResult, mailer.SendgridSendEquipperWelcomeTemplateIDFR, mailer.SendgridSendEquipperWelcomeTemplateID)
	}

	return models.AuthResult{
		ID:           authenticationResult.ID,
		Token:        authenticationResult.Token,
		Email:        authenticationResult.Email,
		RefreshToken: authenticationResult.RefreshToken,
		ExpiresIn:    authenticationResult.RefreshToken,
		UserType:     string(userType),
	}, nil
}

func (s *Service) AddDBEquipper(ctx context.Context, input models.SignUpInput, authenticationResult auth.AuthenticationResult, lodgerID string, memberID string) (models.UserType, bool, models.AuthResult, error) {
	user, err := s.db.CheckEquipperByUserName(ctx, input.UserName)
	if err != nil {
		return "", true, models.AuthResult{}, err
	}

	var userType models.UserType

	if !user {
		err := s.db.AddEquipper(ctx, models.Equipper{
			ID:                       authenticationResult.ID,
			UserName:                 input.UserName,
			Email:                    input.Email,
			Company:                  input.Company,
			PhoneNumber:              input.PhoneNumber,
			PhotoURL:                 input.PhotoURL,
			MemberOf:                 lodgerID,
			MemberID:                 memberID,
			Categories:               input.Categories,
			Currency:                 input.Currency,
			CommunicationPreferences: input.CommunicationPreferences,
			HasInventory:             false,
			HasRequests:              false,
			Address: models.Address{
				Address:     input.Address,
				Address2:    input.Address2,
				ZipCode:     input.ZipCode,
				City:        input.City,
				Country:     input.Country,
				CountryCode: input.CountryCode,
				State:       input.State,
			},
			CoverageArea: input.CoverageArea,
			WorkHours:    input.WorkHours,
			Rating:       rating,
		})
		if err != nil {
			return "", true, models.AuthResult{}, err
		}

		userType = models.EquipperUserType
	} else {
		err = s.auth.DeleteUser(ctx, authenticationResult.ID)
		if err != nil {
			return models.EquipperUserType, true, models.AuthResult{}, err
		}

		return models.EquipperUserType, true, models.AuthResult{}, fmt.Errorf("equipper already exists")
	}

	return userType, false, models.AuthResult{}, nil
}

func (s *Service) signUpMembershipHandling(ctx context.Context, userType models.UserType, lodgerID string, input models.SignUpInput, memberID string) (models.AuthResult, error) {
	member, err := s.UpdateMemberStatus(ctx, userType, lodgerID, input, memberID)
	if err != nil {
		return models.AuthResult{}, err
	}

	project, err := s.db.GetProjectByID(ctx, member.Projects[0])
	if err != nil {
		return models.AuthResult{}, fmt.Errorf("failed to get project by id: %s error: %w", project.ID, err)
	}

	project.Members = append(project.Members, member.ID)

	err = s.db.UpdateProject(ctx, project)
	if err != nil {
		return models.AuthResult{}, fmt.Errorf("failed to update project: %s error: %w", project.ID, err)
	}

	lodger, err := s.db.GetLodgerByMemberID(ctx, member.ID)
	if err != nil {
		return models.AuthResult{}, err
	}

	lodger.Projects = append(lodger.Projects, project.ID)

	err = s.db.UpdateLodger(ctx, lodger)
	if err != nil {
		return models.AuthResult{}, err
	}

	return models.AuthResult{}, nil
}

// UpdateMemberStatus updates the member status of the user.
func (s *Service) UpdateMemberStatus(ctx context.Context, userType models.UserType, lodgerID string, input models.SignUpInput, memberID string) (models.Member, error) {
	member, err := s.GetMemberByID(ctx, memberID, lodgerID)
	if err != nil {
		return models.Member{}, fmt.Errorf("can not get member by id error %w", err)
	}

	member.MembershipStatus = models.MemberAccepted
	member.LodgerID = lodgerID
	member.PhoneNumber = input.PhoneNumber

	if input.Company != "" {
		member.FirstName = input.Company
	}

	member.FirstName = input.FirstName
	member.LastName = input.LastName
	input.IsMember = true

	if userType == models.LodgerUserType {
		err = s.UpdateMemberLodger(ctx, lodgerID, memberID, member)
		if err != nil {
			return models.Member{}, fmt.Errorf("can not update member error %w", err)
		}
	} else {
		err = s.UpdateMemberEquipper(ctx, lodgerID, memberID, member)
		if err != nil {
			return models.Member{}, fmt.Errorf("can not update member error %w", err)
		}
	}

	return member, nil
}

// ExistByEmail check if the user exists and returns the uid of the user.
//func (s *Service) ExistByEmail(ctx context.Context, email string) (string, error) {
//	return s.auth.ExistByEmail(ctx, email)
//}

// ForgotPassword adds a new password reset.
func (s *Service) ForgotPassword(ctx context.Context, email string) error {
	user, err := s.GetUserByEmail(ctx, email)
	var com_pref models.CommunicationPreferences
	if err != nil {
		return err
	}

	token := fmt.Sprintf("%06d", rand.Intn(maxToken-minToken+1)+minToken)

	switch u := user.(type) {
	case *models.Lodger:
		err = s.db.AddPasswordReset(ctx, models.PasswordReset{
			ID:        u.ID,
			Email:     email,
			CreatedAt: time.Now(),
			Token:     token,
		})
		com_pref = u.CommunicationPreferences
	case models.Equipper:
		err = s.db.AddPasswordReset(ctx, models.PasswordReset{
			ID:        u.ID,
			Email:     email,
			CreatedAt: time.Now(),
			Token:     token,
		})
		com_pref = u.CommunicationPreferences
	default:
		return fmt.Errorf("unexpected user type: %T", u)
	}

	if err != nil {
		return fmt.Errorf("can not add password reset error %w", err)
	}

	if com_pref == models.French {
		err = s.mailer.SendFromTemplate(ctx, mailer.ForgotPasswordTemplateIDFR, []string{email}, map[string]string{"code": token})
		if err != nil {
			log.Print(fmt.Errorf("unable to send forget password email to user, %s error: %w", email, err))
		}
	} else {
		err = s.mailer.SendFromTemplate(ctx, mailer.ForgotPasswordTemplateID, []string{email}, map[string]string{"code": token})
		if err != nil {
			log.Print(fmt.Errorf("unable to send forget password email to user, %s error: %w", email, err))
		}
	}

	if err != nil {
		return err
	}

	return nil
}

func (s *Service) GetUserByEmail(ctx context.Context, email string) (interface{}, error) {
	lodger, err := s.db.GetLodgerByEmail(ctx, email)
	if err == nil && lodger.ID != "" {
		return lodger, nil
	}

	equipper, err := s.db.GetEquipperByEmail(ctx, email)
	if err == nil && equipper.ID != "" {
		return equipper, nil
	}

	return nil, fmt.Errorf("unable to find user by email: %s", email)
}

// ValidateToken validates the token.
func (s *Service) ValidateToken(ctx context.Context, input models.PasswordResetInput) error {
	_, err := s.validateToken(ctx, input)
	if err != nil {
		return err
	}

	return nil
}

// ResetPassword resets the password of the user.
func (s *Service) ResetPassword(ctx context.Context, input models.PasswordResetInput) error {
	passwordReset, err := s.validateToken(ctx, input)
	if err != nil {
		return err
	}

	err = s.auth.UpdatePassword(ctx, passwordReset.ID, input.Password)
	if err != nil {
		return err
	}

	return s.db.DeletePasswordReset(ctx, input.Email)
}

func (s *Service) validateToken(ctx context.Context, input models.PasswordResetInput) (models.PasswordReset, error) {
	passwordReset, err := s.db.GetPasswordReset(ctx, input.Email)
	if err != nil {
		return models.PasswordReset{}, err
	}

	if passwordReset.Token != input.Token || passwordReset.Email != input.Email || passwordReset.CreatedAt.Add(time.Minute*10).Before(time.Now()) {
		return models.PasswordReset{}, fmt.Errorf("invalid token")
	}

	return passwordReset, nil
}

// CheckIfEmailExists returns true if the email exists.
func (s *Service) CheckIfEmailExists(ctx context.Context, email string, memberOf string) bool {
	uid, _ := s.auth.ExistByEmail(ctx, email)
	if uid != "" {
		return false
	}

	invitation, err := s.db.InvitationExists(ctx, email, memberOf)
	if err != nil {
		return false
	}

	return invitation
}

// ExistByEmail check if the user exists and returns the uid of the user.
func (s *Service) ExistByEmail(ctx context.Context, email string) (string, error) {
	return s.auth.ExistByEmail(ctx, email)
}

// SendEmail sends an email to the user.
func (s *Service) SendEmail(ctx context.Context, input models.SignUpInput, authenticationResult auth.AuthenticationResult, templateFR string, templateEN string) {
	onClickLink := fmt.Sprintf("%s?sign_in=true", s.frontendURL)

	if input.CommunicationPreferences == models.French {
		err := s.mailer.SendFromTemplate(ctx, templateFR, []string{authenticationResult.Email}, mailDataFrom(input, onClickLink))
		if err != nil {
			log.Print(auth.AuthenticationResult{}, fmt.Errorf("unable to send welcome email to user, %s error: %w", authenticationResult.ID, err))
		}
	} else {
		err := s.mailer.SendFromTemplate(ctx, templateEN, []string{authenticationResult.Email}, mailDataFrom(input, onClickLink))
		if err != nil {
			log.Print(auth.AuthenticationResult{}, fmt.Errorf("unable to send welcome email to user, %s error: %w", authenticationResult.ID, err))
		}
	}
}

func mailDataFrom(user models.SignUpInput, frontendURL string) map[string]string {
	return map[string]string{
		"full_name": user.FullName,
		"company":   user.Company,
		"on_click":  frontendURL,
	}
}
