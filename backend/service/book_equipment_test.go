package service

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	db "github.com/vima-inc/derental/db/mocks"
	mailer "github.com/vima-inc/derental/mailer/mocks"
	"github.com/vima-inc/derental/models"
	payment "github.com/vima-inc/derental/payment/mocks"
)

func TestService_BookEquipment(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	lodgerID := "lodgerID"
	equipmentID := "equipmentID"
	equipperEmail := ""
	availableFrom := time.Now().Add(-1 * time.Hour * 24)   // 1 day ago
	availableTo := time.Now().Add(1 * time.Hour * 24 * 10) // 10 days from now

	mailDataFromBookEquipment := map[string]string{
		"lodger_contact":             "Not specified",
		"lodger_name":                "Not specified",
		"lumen":                      "Not specified",
		"equipper_name":              "Not specified",
		"equipment_name":             "Excavator",
		"start_date":                 time.Now().Format("2006-01-02"),
		"end_date":                   time.Now().Add(1 * time.Hour * 24).Format("2006-01-02"),
		"currency":                   "",
		"amount":                     "0",
		"equipment_image":            "link",
		"equipper_image":             "Not specified",
		"weight":                     "Not specified",
		"height":                     "Not specified",
		"btu":                        "Not specified",
		"volt":                       "Not specified",
		"type_of_propulsion":         "",
		"length":                     "Not specified",
		"on_click":                   "?sign_in=true",
		"price_per_day":              "0",
		"price_per_month":            "0",
		"price_per_week":             "0",
		"price_per_day_special":      "0",
		"price_per_month_special":    "0",
		"price_per_week_special":     "0",
		"_fees":                      "0",
		"sub_total":                  "0",
		"total_amount":               "0",
		"description":                "Not specified",
		"brand":                      "Not specified",
		"force":                      "Not specified",
		"usage_hours":                "Not specified",
		"width":                      "Not specified",
		"po_number":                  "Not specified",
		"payment_method":             string(models.PaymentMethodCreditAccount),
		"credit_check_form":          "Not specified",
		"delivery_address_address":   "Not specified",
		"delivery_address_City":      "Not specified",
		"delivery_address_State":     "Not specified",
		"delivery_address_country":   "Not specified",
		"delivery_address_zipCode":   "Not specified",
		"delivery_cost":              "0",
		"billing_address_City":       "Not specified",
		"billing_address_State":      "Not specified",
		"billing_address_address":    "Not specified",
		"billing_address_country":    "Not specified",
		"billing_address_zipCode":    "Not specified",
		"delivery_preference":        "Not specified",
		"cancel_comment":             "",
		"capacity":                   "Not specified",
		"consumption":                "Not specified",
		"tps":                        "0",
		"tvq":                        "0",
		"equipper_equipment_picture": "",
		"brand_model":                "Not specified",
		"drive_type":                 "",
		"diameter":                   "Not specified",
		"cut_diameter":               "Not specified",
		"watt":                       "Not specified",
		"cfm":                        "Not specified",
		"platform_height":            "Not specified",
		"working_height":             "Not specified",
		"horizontal_outreach":        "Not specified",
		"platform_capacity":          "Not specified",
		"platform_dimension":         "Not specified",
		"platform_extension":         "Not specified",
		"extension_capacity":         "Not specified",
		"platform_rotation":          "Not specified",
		"machine_rotation":           "Not specified",
		"machine_width":              "Not specified",
		"machine_length":             "Not specified",
		"machine_height":             "Not specified",
		"closed_machine_height":      "Not specified",
		"closed_machine_length":      "Not specified",
		"closed_machine_width":       "Not specified",
		"basket_capacity":            "Not specified",
		"basket_length":              "Not specified",
		"basket_width":               "Not specified",
		"legs_location":              "Not specified",
		"floor_height":               "Not specified",
		"cabin_height":               "Not specified",
		"wheelbase":                  "Not specified",
		"wheel_size":                 "Not specified",
		"plate_dimension":            "Not specified",
		"decibel":                    "Not specified",
		"roll_width":                 "Not specified",
		"compaction":                 "Not specified",
		"vibrations":                 "Not specified",
		"pressure":                   "Not specified",
		"frequency":                  "Not specified",
		"tilting_capacity":           "Not specified",
		"operation_capacity":         "Not specified",
		"tank_capacity":              "Not specified",
		"digging_depth":              "Not specified",
		"dumping_height":             "Not specified",
		"digging_radius":             "Not specified",
		"technical_data_sheet":       "Not specified",
		"equipment_usages":           "Not specified",
		"company_name":               "",
		"receiver_info_name":         "",
		"receiver_info_phone_number": "",
		"internal_id":                "",
	}

	dbMock := new(db.Database)
	dbMock.On("GetEquipmentByID", ctx, equipmentID).
		Return(models.Equipment{
			ID:            equipmentID,
			NameEN:        "Excavator",
			ImageLink:     "link",
			AvailableFrom: availableFrom,
			AvailableTo:   pointer(availableTo),
			EquipperEmail: equipperEmail,
			Status:        models.EquipmentAvailable,
		}, nil)

	dbMock.On("GetBookEquipmentByLodgerID", ctx, lodgerID).
		Return([]models.BookEquipment{}, nil)

	dbMock.On("GetLodgerByID", ctx, lodgerID).
		Return(models.Lodger{
			ID:    lodgerID,
			Email: "<EMAIL>",
		}, nil)

	dbMock.On("GetEquipperByID", ctx, "equipperID").
		Return(models.Equipper{
			ID: "equipperID",
		}, nil)

	dbMock.On("GetPromotionCodeByEquipperIDAndLodgerID", ctx, "equipperID", "lodgerID").Return(
		models.Promotion{}, nil)
	dbMock.On("GetCreditCheckFormByID", ctx, mock.Anything).Return(models.CreditCheckForm{
		ActiveBooking: false,
		Name:          "test",
	}, nil)
	dbMock.On("UpdateCreditCheckForm", ctx, mock.Anything).Return(nil)
	dbMock.On("UpdateEquipper", ctx, mock.MatchedBy(func(equipper models.Equipper) bool {
		assert.Equal(t, "equipperID", equipper.ID)
		assert.Equal(t, true, equipper.HasRequests)

		return true
	})).Return(nil)

	dbMock.On("AddBookEquipment", ctx, mock.MatchedBy(func(request models.BookEquipment) bool {
		assert.Equal(t, lodgerID, request.LodgerID)
		assert.Equal(t, "equipperID", request.EquipperID)
		assert.Equal(t, equipperEmail, request.EquipperEmail)
		assert.Equal(t, "<EMAIL>", request.LodgerEmail)

		return true
	})).Return("", nil)

	dbMock.On("UpdateBookEquipment", ctx, mock.MatchedBy(func(request models.BookEquipment) bool {
		assert.Equal(t, "equipperID", request.EquipperID)
		assert.Equal(t, "lodgerID", request.LodgerID)
		assert.Equal(t, equipmentID, request.EquipmentID)
		assert.Equal(t, "<EMAIL>", request.LodgerEmail)

		return true
	})).Return(nil)

	mailerMock := new(mailer.MailerMockAsync)
	mailerMock.Wg.Add(3)

	mailerMock.On("SendFromTemplate", ctx, "booking_equipper_request_template_id", mock.Anything, mailDataFromBookEquipment).
		Return(nil)

	mailerMock.On("SendFromTemplate", ctx, "booking_lodger_request_template_id", []string{"<EMAIL>"}, mailDataFromBookEquipment).
		Return(nil)

	mailerMock.On("SendFromTemplate", ctx, "booking_lodger_request_template_id", []string{"<EMAIL>"}, mailDataFromBookEquipment).
		Return(nil)

	service := New(WithDB(dbMock), WithMailer(mailerMock))

	input := models.BookEquipmentRequest{
		EquipmentID:   equipmentID,
		EquipperID:    "equipperID",
		StartDate:     time.Now(),
		EndDate:       time.Now().Add(1 * time.Hour * 24),
		PaymentMethod: models.PaymentMethodCreditAccount,
		CreditCheckForm: models.CreditCheckForm{
			ID: "CreditCheckFormID",
		},
	}

	_, err := service.BookEquipment(ctx, lodgerID, input)
	require.NoError(t, err)

	if mailerMock.CurrentCount() != 3 {
		t.Errorf("expected 3 emails to be sent, got %d", mailerMock.CurrentCount())
	}

	dbMock.AssertExpectations(t)
	mailerMock.AssertExpectations(t)
}

func TestService_CancelBooking(t *testing.T) {
	t.Parallel()

	projects := []models.Project{
		{
			ID: "projectID",
		},
	}

	ctx := context.Background()

	mailDataFromCancelBooking := map[string]string{
		"lodger_name":                "Not specified",
		"lodger_contact":             "Not specified",
		"equipper_name":              "Not specified",
		"equipment_name":             "Excavator",
		"start_date":                 "0001-01-01",
		"end_date":                   "0001-01-01",
		"currency":                   "",
		"amount":                     "0",
		"equipment_image":            "Not specified",
		"equipper_image":             "Not specified",
		"weight":                     "Not specified",
		"height":                     "Not specified",
		"btu":                        "Not specified",
		"volt":                       "Not specified",
		"type_of_propulsion":         "",
		"length":                     "Not specified",
		"on_click":                   "",
		"price_per_day":              "0",
		"price_per_month":            "0",
		"price_per_week":             "0",
		"price_per_day_special":      "0",
		"price_per_month_special":    "0",
		"price_per_week_special":     "0",
		"_fees":                      "0",
		"sub_total":                  "0",
		"total_amount":               "0",
		"description":                "Not specified",
		"brand":                      "Not specified",
		"force":                      "Not specified",
		"usage_hours":                "Not specified",
		"width":                      "Not specified",
		"po_number":                  "Not specified",
		"payment_method":             string(models.PaymentMethodCreditCard),
		"credit_check_form":          "Not specified",
		"delivery_address_address":   "Not specified",
		"delivery_address_City":      "Not specified",
		"delivery_address_State":     "Not specified",
		"delivery_address_country":   "Not specified",
		"delivery_address_zipCode":   "Not specified",
		"delivery_cost":              "0",
		"billing_address_City":       "Not specified",
		"billing_address_State":      "Not specified",
		"billing_address_address":    "Not specified",
		"billing_address_country":    "Not specified",
		"billing_address_zipCode":    "Not specified",
		"delivery_preference":        "Not specified",
		"cancel_comment":             "",
		"capacity":                   "Not specified",
		"consumption":                "Not specified",
		"tps":                        "0",
		"tvq":                        "0",
		"equipper_equipment_picture": "",
		"brand_model":                "Not specified",
		"drive_type":                 "",
		"diameter":                   "Not specified",
		"cut_diameter":               "Not specified",
		"watt":                       "Not specified",
		"cfm":                        "Not specified",
		"platform_height":            "Not specified",
		"working_height":             "Not specified",
		"horizontal_outreach":        "Not specified",
		"platform_capacity":          "Not specified",
		"platform_dimension":         "Not specified",
		"platform_extension":         "Not specified",
		"extension_capacity":         "Not specified",
		"platform_rotation":          "Not specified",
		"machine_rotation":           "Not specified",
		"machine_width":              "Not specified",
		"machine_length":             "Not specified",
		"machine_height":             "Not specified",
		"closed_machine_height":      "Not specified",
		"closed_machine_length":      "Not specified",
		"closed_machine_width":       "Not specified",
		"basket_capacity":            "Not specified",
		"basket_length":              "Not specified",
		"basket_width":               "Not specified",
		"legs_location":              "Not specified",
		"floor_height":               "Not specified",
		"cabin_height":               "Not specified",
		"wheelbase":                  "Not specified",
		"wheel_size":                 "Not specified",
		"plate_dimension":            "Not specified",
		"decibel":                    "Not specified",
		"roll_width":                 "Not specified",
		"compaction":                 "Not specified",
		"vibrations":                 "Not specified",
		"lumen":                      "Not specified",
		"pressure":                   "Not specified",
		"frequency":                  "Not specified",
		"tilting_capacity":           "Not specified",
		"operation_capacity":         "Not specified",
		"tank_capacity":              "Not specified",
		"digging_depth":              "Not specified",
		"dumping_height":             "Not specified",
		"digging_radius":             "Not specified",
		"technical_data_sheet":       "Not specified",
		"equipment_usages":           "Not specified",
		"company_name":               "",
		"receiver_info_name":         "",
		"receiver_info_phone_number": "",
		"internal_id":                "",
	}

	dbMock := new(db.Database)

	dbMock.On("GetBookEquipmentByID", ctx, "bookingID").Return(models.BookEquipment{
		ID:            "bookingID",
		EquipmentID:   equipmentID,
		EquipmentName: "Excavator",
		EquipperID:    "equipperID",
		LodgerID:      "lodgerID",
		EquipperEmail: "<EMAIL>",
		LodgerEmail:   "<EMAIL>",
		PaymentMethod: models.PaymentMethodCreditCard,
	}, nil)
	dbMock.On("UpdateBookEquipment", ctx, mock.MatchedBy(func(request models.BookEquipment) bool {
		assert.Equal(t, "bookingID", request.ID)
		assert.Equal(t, "equipperID", request.EquipperID)
		assert.Equal(t, "lodgerID", request.LodgerID)
		assert.Equal(t, equipmentID, request.EquipmentID)
		assert.Equal(t, "<EMAIL>", request.EquipperEmail)
		assert.Equal(t, "<EMAIL>", request.LodgerEmail)

		return true
	})).Return(nil)

	dbMock.On("GetEquipmentByID", ctx, equipmentID).Return(models.Equipment{
		ID:     equipmentID,
		Status: "available",
	}, nil)

	dbMock.On("UpdateEquipment", ctx, mock.MatchedBy(func(request models.Equipment) bool {
		assert.Equal(t, equipmentID, request.ID)
		assert.Equal(t, models.EquipmentStatus("available"), request.Status)

		return true
	})).Return(nil)

	dbMock.On("GetLodgerByID", ctx, "lodgerID").
		Return(models.Lodger{
			ID:      "lodgerID",
			Company: "lodger",
			Email:   "<EMAIL>",
		}, nil)

	dbMock.On("GetProjectsByEquipmentID", ctx, equipmentID).
		Return([]models.Project{
			{
				ID: "projectID",
			},
		}, nil)

	dbMock.On("BatchUpdateProjects", ctx, projects).
		Return(nil)

	dbMock.On("GetOrderByBookingID", ctx, "bookingID").
		Return(models.Order{}, nil)

	dbMock.On("UpdateOrder", ctx, mock.Anything).
		Return(nil)

	mailerMock := new(mailer.MailerMockAsync)
	mailerMock.Wg.Add(1)

	mailerMock.On("SendFromTemplate", ctx, "sendgrid_send_renter_booking_canceled_template_id", []string{"<EMAIL>"}, mailDataFromCancelBooking).
		Return(nil)

	paymentMock := new(payment.Payment)
	paymentMock.On("Cancel", mock.Anything).
		Return(nil)

	input := models.BookEquipment{
		ID:            "bookingID",
		EquipmentID:   equipmentID,
		EquipperID:    "equipperID",
		LodgerID:      "lodgerID",
		EquipperEmail: "<EMAIL>",
		LodgerEmail:   "<EMAIL>",
		PaymentMethod: models.PaymentMethodCreditCard,
	}

	Service := New(WithDB(dbMock), WithMailer(mailerMock), WithPayment(paymentMock))

	err := Service.CancelBooking(ctx, "equipperID", "bookingID", input)
	assert.NoError(t, err)

	mailerMock.Wg.Wait()
}

func pointer[T any](input T) *T {
	return &input
}
