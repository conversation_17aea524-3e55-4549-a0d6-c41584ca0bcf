package pubsub

import (
	"context"
	"fmt"

	gpubsub "cloud.google.com/go/pubsub"

	ibroker "github.com/vima-inc/derental/broker"
)

type pubsub struct {
	client *gpubsub.Client
}

// New creates a new pubsub service.
func New(ctx context.Context, projectID string) (ibroker.Broker, error) {
	client, err := gpubsub.NewClient(ctx, projectID)
	if err != nil {
		return nil, fmt.Errorf("unable to create pubsub client: %w", err)
	}

	return &pubsub{
		client: client,
	}, nil
}

// Publish publishes a message to a topic.
func (p *pubsub) Publish(ctx context.Context, topic string, data []byte) error {
	t := p.client.Topic(topic)
	result := t.Publish(ctx, &gpubsub.Message{
		Data: data,
	})

	_, err := result.Get(ctx)
	if err != nil {
		return fmt.Errorf("unable to publish message: %w", err)
	}

	return nil
}
