package airtable

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
)

const baseURL = "https://api.airtable.com/v0/apptfKgkg4NbaZXEN"

type Field struct {
	ID string `json:"id"`
}

type FieldType interface {
	GetID() string
	SetID(string)
}

// Client is a client for the Client API.
type Client[T FieldType] struct {
	httpClient *http.Client
	apiKey     string
	table      string
}

type Airtable[T FieldType] interface {
	// Get retrieves a record from the Airtable API .
	GetAll(ctx context.Context) ([]T, error)
}

// New returns a new Airtable client.
func New[T FieldType](apiKey, table string) *Client[T] {
	return &Client[T]{
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		apiKey: apiKey,
		table:  table,
	}
}

type airtableResponse[T FieldType] struct {
	Records records[T] `json:"records"`
	Offset  string     `json:"offset"`
}

type records[T FieldType] []struct {
	record[T]
}

type record[T FieldType] struct {
	ID          string     `json:"id,omitempty"`
	Fields      T          `json:"fields"`
	CreatedTime *time.Time `json:"createdTime,omitempty"`
}

// Get retrieves all records from the Airtable table.
func (a *Client[T]) GetAll(ctx context.Context) ([]T, error) {
	table := a.table

	t := &url.URL{Path: table}

	tableEncoded := t.String()

	var currentOffset string

	records := make([]T, 0)

	for {
		var url string
		if currentOffset != "" {
			url = fmt.Sprintf("%s/%s?offset=%s", baseURL, tableEncoded, currentOffset)
		} else {
			url = fmt.Sprintf("%s/%s", baseURL, tableEncoded)
		}

		record, offset, err := a.get(ctx, url)
		if err != nil {
			return nil, fmt.Errorf("failed to get records: %w", err)
		}

		records = append(records, record...)

		if offset == "" {
			break
		}

		currentOffset = offset
	}

	return records, nil
}

func (a *Client[T]) get(ctx context.Context, url string) ([]T, string, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", a.apiKey))

	res, err := a.httpClient.Do(req)
	if err != nil {
		return nil, "", fmt.Errorf("failed to do request: %w", err)
	}

	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, "", fmt.Errorf("failed to read response body: %w", err)
	}

	if res.StatusCode != http.StatusOK {
		return nil, "", fmt.Errorf("failed to get data: %s", res.Status)
	}

	var resp airtableResponse[T]
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, "", fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	offset := resp.Offset
	records := make([]T, 0, len(resp.Records))

	for _, r := range resp.Records {
		f := r.Fields
		f.SetID(r.ID)

		records = append(records, f)
	}

	return records, offset, nil
}
