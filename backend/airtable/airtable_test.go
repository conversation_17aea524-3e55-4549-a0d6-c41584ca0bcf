package airtable

import (
	"context"
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

type testModel struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

func (t testModel) GetID() string {
	return t.ID
}

func (t *testModel) SetID(id string) {
	t.ID = id
}

type RoundTripFunc func(req *http.Request) *http.Response

func (f RoundTripFunc) RoundTrip(req *http.Request) (*http.Response, error) {
	return f(req), nil
}

func TestGetAll(t *testing.T) {
	t.Parallel()

	want := []testModel{
		{
			ID:   "rec1",
			Name: "test1",
		},
		{
			ID:   "rec2",
			Name: "test2",
		},
	}
	hclient := &http.Client{
		Transport: RoundTripFunc(func(req *http.Request) *http.Response {
			return &http.Response{
				StatusCode: http.StatusOK,
				Body: io.NopCloser(strings.NewReader(`
			{
			"records": [
				{ "id": "rec1" , "fields": { "name": "test1" } },
				{ "id": "rec2" , "fields": { "name": "test2" } }
			]
			}`)),
			}
		}),
	}

	a := &Client[*testModel]{
		httpClient: hclient,
	}

	got, err := a.GetAll(context.Background())
	require.NoError(t, err)

	for i := range got {
		require.Equal(t, want[i].ID, got[i].GetID())
		require.Equal(t, want[i].Name, got[i].Name)
	}
}
