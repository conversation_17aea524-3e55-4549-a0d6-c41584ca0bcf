package models

import "time"

// ARALevel1Category represents a Level 1 ARA category
type ARALevel1Category struct {
	ID          int    `json:"id" firestore:"id"`
	Name        string `json:"name" firestore:"level1_description"`
	Description string `json:"description,omitempty" firestore:"-"`
}

// ARALevel2Type represents a Level 2 ARA type
type ARALevel2Type struct {
	ID          int    `json:"id" firestore:"id"`
	Name        string `json:"name" firestore:"level2_description"`
	Description string `json:"description,omitempty" firestore:"-"`
	Level1ID    int    `json:"level1_id" firestore:"ara_set_level1_id"`
	Level1Name  string `json:"level1_name,omitempty" firestore:"-"`
}

// ARALevel3Specification represents a Level 3 ARA specification
type ARALevel3Specification struct {
	ID          string `json:"id" firestore:"id"`
	Name        string `json:"name" firestore:"name"`
	Description string `json:"description" firestore:"description"`
	Level1ID    string `json:"level1_id" firestore:"level1_id"`
	Level2ID    string `json:"level2_id" firestore:"level2_id"`
	Level1Name  string `json:"level1_name" firestore:"level1_name"`
	Level2Name  string `json:"level2_name" firestore:"level2_name"`
}

// ARAClassificationRequest represents a request to classify equipment
type ARAClassificationRequest struct {
	Equipment []EquipmentForClassification `json:"equipment"`
	ARATypes  []ARALevel2Type              `json:"ara_types"`
	BatchSize int                          `json:"batch_size"`
}

// EquipmentForClassification represents equipment data for classification
type EquipmentForClassification struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	ImageURL    string `json:"image_url,omitempty"` // URL to equipment image for vision analysis
	Index       int    `json:"index"`               // Position in batch for response mapping
}

// ARAClassificationResponse represents the response from AI classification
type ARAClassificationResponse struct {
	Classifications []ARAClassificationResult `json:"classifications"`
	Success         bool                      `json:"success"`
	Error           string                    `json:"error,omitempty"`
}

// ARAClassificationResult represents a single equipment classification result
type ARAClassificationResult struct {
	EquipmentIndex int    `json:"equipment_index"`
	EquipmentID    string `json:"equipment_id,omitempty"`
	ARALevel1ID    int    `json:"ara_level1_id"`
	ARALevel2ID    int    `json:"ara_level2_id"`
	ARALevel1Name  string `json:"ara_level1_name,omitempty"`
	ARALevel2Name  string `json:"ara_level2_name,omitempty"`
	Confidence     string `json:"confidence,omitempty"`
	Reasoning      string `json:"reasoning,omitempty"`
}

// ARAClassificationBatch represents a batch of equipment for processing
type ARAClassificationBatch struct {
	ID          string                       `json:"id"`
	Equipment   []EquipmentForClassification `json:"equipment"`
	Results     []ARAClassificationResult    `json:"results"`
	Status      BatchStatus                  `json:"status"`
	CreatedAt   time.Time                    `json:"created_at"`
	ProcessedAt *time.Time                   `json:"processed_at,omitempty"`
	Error       string                       `json:"error,omitempty"`
}

// BatchStatus represents the status of a classification batch
type BatchStatus string

const (
	BatchStatusPending    BatchStatus = "pending"
	BatchStatusProcessing BatchStatus = "processing"
	BatchStatusCompleted  BatchStatus = "completed"
	BatchStatusFailed     BatchStatus = "failed"
)

// ARAClassificationConfig represents configuration for ARA classification
type ARAClassificationConfig struct {
	BatchSize         int     `json:"batch_size"`
	MaxTokensPerBatch int     `json:"max_tokens_per_batch"`
	Model             string  `json:"model"`
	Temperature       float64 `json:"temperature"`
	MaxRetries        int     `json:"max_retries"`
	RetryDelaySeconds int     `json:"retry_delay_seconds"`
	UseCache          bool    `json:"use_cache"`
	DryRun            bool    `json:"dry_run"`
	ForceReclassify   bool    `json:"force_reclassify"`
}

// DefaultARAClassificationConfig returns default configuration
func DefaultARAClassificationConfig() ARAClassificationConfig {
	return ARAClassificationConfig{
		BatchSize:         15,
		MaxTokensPerBatch: 4000,
		Model:             "anthropic/claude-3-haiku",
		Temperature:       0.1,
		MaxRetries:        3,
		RetryDelaySeconds: 2,
		UseCache:          true,
		DryRun:            false,
		ForceReclassify:   false,
	}
}

// OpenRouterRequest represents a request to OpenRouter API
type OpenRouterRequest struct {
	Model       string              `json:"model"`
	Messages    []OpenRouterMessage `json:"messages"`
	Temperature float64             `json:"temperature,omitempty"`
	MaxTokens   int                 `json:"max_tokens,omitempty"`
	Stream      bool                `json:"stream,omitempty"`
}

// OpenRouterMessage represents a message in OpenRouter request
type OpenRouterMessage struct {
	Role    string      `json:"role"`
	Content interface{} `json:"content"` // Can be string or []OpenRouterContent for vision
}

// OpenRouterContent represents content in a vision-enabled message
type OpenRouterContent struct {
	Type     string              `json:"type"` // "text" or "image_url"
	Text     string              `json:"text,omitempty"`
	ImageURL *OpenRouterImageURL `json:"image_url,omitempty"`
}

// OpenRouterImageURL represents an image URL in OpenRouter content
type OpenRouterImageURL struct {
	URL    string `json:"url"`
	Detail string `json:"detail,omitempty"` // "low", "high", or "auto"
}

// OpenRouterResponse represents a response from OpenRouter API
type OpenRouterResponse struct {
	ID      string             `json:"id"`
	Object  string             `json:"object"`
	Created int64              `json:"created"`
	Model   string             `json:"model"`
	Choices []OpenRouterChoice `json:"choices"`
	Usage   OpenRouterUsage    `json:"usage"`
	Error   *OpenRouterError   `json:"error,omitempty"`
}

// OpenRouterChoice represents a choice in OpenRouter response
type OpenRouterChoice struct {
	Index        int               `json:"index"`
	Message      OpenRouterMessage `json:"message"`
	FinishReason string            `json:"finish_reason"`
}

// OpenRouterUsage represents usage statistics from OpenRouter
type OpenRouterUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// OpenRouterError represents an error from OpenRouter API
type OpenRouterError struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Code    string `json:"code"`
}

// ARAClassificationStats represents statistics for classification process
type ARAClassificationStats struct {
	TotalEquipment         int           `json:"total_equipment"`
	ProcessedEquipment     int           `json:"processed_equipment"`
	SuccessfullyClassified int           `json:"successfully_classified"`
	FailedClassifications  int           `json:"failed_classifications"`
	TotalBatches           int           `json:"total_batches"`
	ProcessedBatches       int           `json:"processed_batches"`
	TotalTokensUsed        int           `json:"total_tokens_used"`
	EstimatedCost          float64       `json:"estimated_cost"`
	ProcessingTime         time.Duration `json:"processing_time"`
	StartTime              time.Time     `json:"start_time"`
	EndTime                *time.Time    `json:"end_time,omitempty"`
}
