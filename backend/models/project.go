package models

import (
	"fmt"
	"time"
)

// PopulatedProject is a struct representing a Project to display.
type PopulatedProject struct {
	ID              string          `json:"id" firestore:"id"`
	Name            string          `json:"name" firestore:"name"`
	DeliveryAddress Address         `json:"delivery_address" firestore:"delivery_address"`
	BillingAddress  Address         `json:"billing_address" firestore:"billing_address"`
	StartDate       time.Time       `json:"start_date" firestore:"start_date"`
	EndDate         time.Time       `json:"end_date" firestore:"end_date"`
	Owner           Lodger          `json:"owner_id" firestore:"owner_id"`
	Creator         Lodger          `json:"creator_id" firestore:"creator_id"`
	Equipments      []BookEquipment `json:"equipments" firestore:"equipments"`
	BidzEquipments  []BidzOffer     `json:"bidz_equipment" firestore:"bidz_equipment"`
	Members         []Member        `json:"members" firestore:"members"`
	CreditCheckForm CreditCheckForm `json:"credit_check_form" firestore:"credit_check_form"`
}

// Project is a struct representing a Project.
type Project struct {
	ID                string          `json:"id" firestore:"id"`
	Name              string          `json:"name" firestore:"name"`
	DeliveryAddress   Address         `json:"delivery_address" firestore:"delivery_address"`
	BillingAddress    Address         `json:"billing_address" firestore:"billing_address"`
	StartDate         time.Time       `json:"start_date" firestore:"start_date"`
	EndDate           time.Time       `json:"end_date" firestore:"end_date"`
	OwnerID           string          `json:"owner_id" firestore:"owner_id"`
	CreatorID         string          `json:"creator_id" firestore:"creator_id"`
	Equipments        []string        `json:"equipments" firestore:"equipments"`
	BidzEquipments    []string        `json:"bidz_equipment" firestore:"bidz_equipment"`
	Members           []string        `json:"members" firestore:"members"`
	CreditCheckFormID string          `json:"credit_check_form_id" firestore:"credit_check_form_id"`
	CreditCheckForm   CreditCheckForm `json:"credit_check_form" firestore:"credit_check_form"`
}

// Validate validates the project fields.
func (p *Project) Validate() error {
	if p.CreatorID == "" {
		return fmt.Errorf("renter id is required")
	}

	return nil
}
