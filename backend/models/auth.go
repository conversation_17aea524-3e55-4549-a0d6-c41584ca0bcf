package models

import (
	"fmt"
	"time"
)

// UserType is the type of users.
type UserType string

// Constants for the UserType.
const (
	LodgerUserType   UserType = "lodger"
	EquipperUserType UserType = "equipper"
)

// SignInInput is the request input for SignIn.
type SignInInput struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// SignUpInput is the request input for SignUp.
type SignUpInput struct {
	Type                     string                   `json:"type"`
	Email                    string                   `json:"email"`
	FirstName                string                   `json:"first_name"`
	LastName                 string                   `json:"last_name"`
	UserName                 string                   `json:"user_name"`
	Company                  string                   `json:"company"`
	PhoneNumber              string                   `json:"phone_number"`
	PhotoURL                 string                   `json:"photo_url"`
	Address                  string                   `json:"address"`
	Address2                 string                   `json:"address2"`
	ZipCode                  string                   `json:"zip_code"`
	City                     string                   `json:"city"`
	Country                  string                   `json:"country"`
	CountryCode              string                   `json:"country_code"`
	State                    string                   `json:"state"`
	Password                 string                   `json:"password"`
	Status                   bool                     `json:"status"`
	<PERSON><PERSON><PERSON><PERSON>                 bool                     `json:"is_lodger"`
	IsMember                 bool                     `json:"is_member"`
	CommunicationPreferences CommunicationPreferences `json:"communication_preferences"`
	Categories               []string                 `json:"categories"`
	CoverageArea             []string                 `json:"coverage_area"`
	WorkHours                []WorkHours              `json:"work_hours"`
	FullName                 string                   `json:"full_name"`
	Currency                 string                   `json:"currency"`
}

// Validate validates the signup fields.
func (s *SignUpInput) Validate() error {
	if s.IsLodger {
		err := ValidateLodgerType(s.Type)
		if err != nil {
			return err
		}
	}

	if s.Password == "" {
		return fmt.Errorf("password is required")
	}

	if s.Email == "" {
		return fmt.Errorf("email is required")
	}

	return nil
}

// PasswordReset is a struct that holds the data for a password reset.
type PasswordReset struct {
	ID        string    `json:"id" firestore:"id"`
	CreatedAt time.Time `json:"created_at" firestore:"created_at"`
	Email     string    `json:"email" firestore:"email"`
	Token     string    `json:"token" firestore:"token"`
}

// PasswordResetInput is the request input for PasswordReset.
type PasswordResetInput struct {
	Email    string `json:"email"`
	Password string `json:"password"`
	Token    string `json:"token" firestore:"token"`
}

// AuthResult is the response from auth method.
type AuthResult struct {
	ID           string                   `json:"localId"`
	Token        string                   `json:"idToken"`
	Email        string                   `json:"email"`
	RefreshToken string                   `json:"refreshToken"`
	ExpiresIn    string                   `json:"expiresIn"`
	UserType     string                   `json:"userType"`
	HasInventory bool                     `json:"has_inventory"`
	Country      string                   `json:"country"`
	Lang         CommunicationPreferences `json:"lang"`
	ImageLink    string                   `json:"imageUrl"`
}
