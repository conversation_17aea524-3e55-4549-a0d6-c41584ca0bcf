package models

// Address is a struct representing a Address.
type Address struct {
	Name        string  `json:"name" firestore:"-"`
	Address     string  `json:"address" firestore:"address"`
	Address2    string  `json:"address2" firestore:"address2"`
	ZipCode     string  `json:"zip_code" firestore:"zip_code"`
	City        string  `json:"city" firestore:"city"`
	Country     string  `json:"country" firestore:"country"`
	CountryCode string  `json:"country_code" firestore:"country_code"`
	State       string  `json:"state" firestore:"state"`
	Latitude    float64 `json:"latitude" firestore:"latitude"`
	Longitude   float64 `json:"longitude" firestore:"longitude"`
}
