package models

// Promotion represents a promotion code.
type Promotion struct {
	ID         string  `json:"id" firestore:"id"`
	Code       string  `json:"code" firestore:"code"`
	PercentOff float64 `json:"percent_off" firestore:"percent_off"`
	Currency   string  `json:"currency" firestore:"currency"`
	Duration   string  `json:"duration" firestore:"duration"`
	EquipperID string  `json:"equipper_id" firestore:"equipper_id"`
	LodgerID   string  `json:"lodger_id" firestore:"lodger_id"`
}

// Coupon represents a coupon.
type Coupon struct {
	ID         string  `json:"id" firestore:"id"`
	Name       string  `json:"name" firestore:"name"`
	PercentOff float64 `json:"percent_off" firestore:"percent_off"`
	AmountOff  int64   `json:"amount_off" firestore:"amount_off"`
	Currency   string  `json:"currency" firestore:"currency"`
}
