package models

import "fmt"

// LodgerType represents the type of Lodger.
type LodgerType string

const (
	// Private represents an individual lodger.
	Private LodgerType = "private"
	// Pro represents a company lodger.
	Pro LodgerType = "pro"
)

// CommunicationPreferences represents the communication preferences of a Lodger ro equipper.
type CommunicationPreferences string

const (
	// English represents the English language.
	English CommunicationPreferences = "english"
	// French represents the French language.
	French CommunicationPreferences = "french"
)

// Lodger is a struct representing a Lodger.
type Lodger struct {
	ID                       string                   `json:"id" firestore:"id"`
	Type                     LodgerType               `json:"type" firestore:"type"`
	UserName                 string                   `json:"user_name" firestore:"user_name"`
	Email                    string                   `json:"email" firestore:"email"`
	FirstName                string                   `json:"first_name" firestore:"first_name"`
	LastName                 string                   `json:"last_name" firestore:"last_name"`
	Company                  string                   `json:"company" firestore:"company"`
	Status                   bool                     `json:"status" firestore:"status"`
	PhoneNumber              string                   `json:"phone_number" firestore:"phone_number"`
	PhotoURL                 string                   `json:"photo_url" firestore:"photo_url"`
	Address                  Address                  `json:"address" firestore:"address"`
	Description              string                   `json:"description" firestore:"description"`
	MemberOf                 string                   `json:"member_of" firestore:"member_of"`
	MemberID                 string                   `json:"member_id" firestore:"member_id"`
	Projects                 []string                 `json:"projects" firestore:"projects"`
	CommunicationPreferences CommunicationPreferences `json:"communication_preferences" firestore:"communication_preferences"`
	FullName                 string                   `json:"full_name" firestore:"full_name"`
	Currency                 string                   `json:"currency" firestore:"currency"`
	RecipientList            []string                 `json:"recipient_list" firestore:"recipient_list"`
	CustomerID               string                   `json:"customer_id" firestore:"customer_id"`
}

// Validate validates the lodger fields.
func (l *Lodger) Validate() error {
	if l.Type != Private && l.Type != Pro {
		return fmt.Errorf("invalid lodger type: %s", l.Type)
	}

	return nil
}

// ValidateLodgerType validates the type of a Lodger.
func ValidateLodgerType(lodgerType string) error {
	if lodgerType != string(Private) && lodgerType != string(Pro) {
		return fmt.Errorf("invalid lodger type: %s", lodgerType)
	}

	return nil
}
