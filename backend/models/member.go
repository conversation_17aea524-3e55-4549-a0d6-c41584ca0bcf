package models

import "fmt"

// MemberType is the type of a member.
type MemberType string

// Member Type.
const (
	Admin             = "admin"
	PowerCollaborator = "power collaborator"
	Collaborator      = "collaborator"
	AccountManager    = "account manager"
)

// MemberStatus represents the status of a invitation.
type MemberStatus string

// MemberStatus constants.
const (
	MemberPending  MemberStatus = "pending"
	MemberAccepted MemberStatus = "accepted"
	MemberOwner    MemberStatus = "owner"
)

// MemberPrivilegeLevel represents the privilege level of a member.
type MemberPrivilegeLevel int

// MemberPrivilegeLevels constants.
const (
	OwnerPrivilegeLevel             = 10
	AdminPrivilegeLevel             = 4
	PowerCollaboratorPrivilegeLevel = 3
	CollaboratorPrivilegeLevel      = 2
	AccountManagerPrivilegeLevel    = 2
)

// Member is a struct representing a Member.
type Member struct {
	ID               string               `json:"id" firestore:"id"`
	Email            string               `json:"email" firestore:"email"`
	FirstName        string               `json:"first_name" firestore:"first_name"`
	LastName         string               `json:"last_name" firestore:"last_name"`
	PhoneNumber      string               `json:"phone_number" firestore:"phone_number"`
	Type             MemberType           `json:"type" firestore:"type"`
	MembershipStatus MemberStatus         `json:"membership_status" firestore:"membership_status"`
	LodgerID         string               `json:"lodger_id" firestore:"lodger_id"`
	PhotoURL         string               `json:"photo_url" firestore:"photo_url"`
	PrivilegeLevel   MemberPrivilegeLevel `json:"privilege_level" firestore:"privilege_level"`
	Projects         []string             `json:"projects" firestore:"projects"`
}

// Validate validates the member fields.
func (m *Member) Validate() error {
	if m.Type != Admin &&
		m.Type != PowerCollaborator &&
		m.Type != Collaborator &&
		m.Type != AccountManager {
		return fmt.Errorf("invalid member type: %s", m.Type)
	}

	if m.Email == "" {
		return fmt.Errorf("email is required")
	}

	if m.MembershipStatus != MemberAccepted &&
		m.MembershipStatus != MemberPending {
		return fmt.Errorf("invalid member status")
	}

	return nil
}

// Members is a struct representing a Members.
type Members struct {
	Admins             []Member `json:"admins,omitempty"`
	PowerCollaborators []Member `json:"power_collaborators,omitempty"`
	Collaborators      []Member `json:"collaborators,omitempty"`
	AccountManagers    []Member `json:"account_managers,omitempty"`
}

// GetPrivilegeLevel return member privilege level.
func GetPrivilegeLevel(m Member) MemberPrivilegeLevel {
	if m.MembershipStatus == MemberOwner {
		return OwnerPrivilegeLevel
	}

	switch m.Type {
	case Admin:
		return AdminPrivilegeLevel
	case PowerCollaborator:
		return PowerCollaboratorPrivilegeLevel
	case Collaborator:
		return CollaboratorPrivilegeLevel
	case AccountManager:
		return AccountManagerPrivilegeLevel
	default:
		return 0
	}
}
