package models

import "time"

// CreditCheckForm is a struct representing credit check form.
type CreditCheckForm struct {
	ID                      string              `json:"id" firestore:"id"`
	Name                    string              `json:"name" firestore:"name"`
	LodgerID                string              `json:"user" firestore:"user"`
	Company                 Company             `json:"company" firestore:"company"`
	CreditReferences        []CreditReference   `json:"credit_references" firestore:"credit_references"`
	AccountsPayable         []Employee          `json:"accounts_payable" firestore:"accounts_payable"`
	InChargeOfRentals       []Employee          `json:"in_charge_of_rentals" firestore:"in_charge_of_rentals"`
	Bond                    Bond                `json:"bond" firestore:"bond"`
	USCreditAppData         map[string]string   `json:"us_credit_app_data" firestore:"us_credit_app_data"`
	BankruptcyEquipmentInfo []map[string]string `json:"bankruptcy_equipment_info" firestore:"bankruptcy_equipment_info"`
	BankruptcyTradeInfo     []map[string]string `json:"bankruptcy_trade_info" firestore:"bankruptcy_trade_info"`
	BankruptcyCashDownInfo  map[string]string   `json:"bankruptcy_cashDown_info" firestore:"bankruptcy_cashDown_info"`
	Attachments             Attachments         `json:"attachments" firestore:"attachments"`
	CreditCheckFormPath     string              `json:"credit_check_form_path" firestore:"credit_check_form_path"`
	CreatedAt               time.Time           `json:"created_at" firestore:"created_at"`
	UpdatedAt               time.Time           `json:"updated_at" firestore:"updated_at"`
	CreatedBy               string              `json:"created_by" firestore:"created_by"`
	ActiveBooking           bool                `json:"active_booking" firestore:"active_booking"`
}

// Company is a struct representing company information in the credit check form.
type Company struct {
	SIN                   string  `json:"sin" firestore:"sin"`
	CompanyName           string  `json:"company_name" firestore:"company_name"`
	YearOfLocation        string  `json:"year_of_location" firestore:"year_of_location"`
	Address               Address `json:"address" firestore:"address"`
	Telephone             string  `json:"telephone" firestore:"telephone"`
	Fax                   string  `json:"fax" firestore:"fax"`
	NumberOfEmployees     int     `json:"number_of_employees" firestore:"number_of_employees"`
	PropertyInsurance     string  `json:"property_insurance" firestore:"property_insurance"`
	RequiredOrderNumber   bool    `json:"required_order_number" firestore:"required_order_number"`
	RequiredProjectNumber string  `json:"required_project_number" firestore:"required_project_number"`
	TaxFreeEntity         string  `json:"tax_free_entity" firestore:"tax_free_entity"`
	CompanyPresident      string  `json:"company_president" firestore:"company_president"`
	CompanyPresidentEmail string  `json:"company_president_email" firestore:"company_president_email"`
}

// CreditReference is a struct representing credit reference in credit check form.
type CreditReference struct {
	Equipper  string  `json:"equipper" firestore:"equipper"`
	Telephone string  `json:"telephone" firestore:"telephone"`
	Email     string  `json:"email" firestore:"email"`
	Address   Address `json:"address" firestore:"address"`
}

// Employee is a struct representing employee in credit check form.
type Employee struct {
	Name      string  `json:"name" firestore:"name"`
	Title     string  `json:"title" firestore:"title"`
	Telephone string  `json:"telephone" firestore:"telephone"`
	Email     string  `json:"email" firestore:"email"`
	Ext       string  `json:"ext" firestore:"ext"`
	Cell      string  `json:"cell" firestore:"cell"`
	Type      string  `json:"type" firestore:"type"`
	Address   Address `json:"address" firestore:"address"`
}

// Bond is a struct representing Bond, authorized person / authorized signature in credit check form.
type Bond struct {
	Name             string `json:"name" firestore:"name"`
	Function         string `json:"function" firestore:"function"`
	Telephone        string `json:"telephone" firestore:"telephone"`
	Email            string `json:"email" firestore:"email"`
	Ext              string `json:"ext" firestore:"ext"`
	Cell             string `json:"cell" firestore:"cell"`
	HasOtherAccounts string `json:"has_other_accounts" firestore:"has_other_accounts"`
	Signature        string `json:"signature" firestore:"signature"`
	Date             string `json:"date" firestore:"date"`
}

type Attachments struct {
	UserID               string `json:"user_id" firestore:"user_id"`
	Insurance            string `json:"insurance" firestore:"insurance"`
	PartnershipAgreement string `json:"partnership_agreement" firestore:"partnership_agreement"`
	SecondApplicantID    string `json:"second_applicant_id" firestore:"second_applicant_id"`
}
