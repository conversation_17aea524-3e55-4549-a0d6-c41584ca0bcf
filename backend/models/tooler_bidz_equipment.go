package models

import "time"

// ToolerBidzEquipment represents the equipment for bidz.
type ToolerBidzEquipment struct {
	ID            string   `json:"id" firestore:"id"`
	NameEN        string   `json:"name_en" firestore:"name_en"`
	NameFR        string   `json:"name_fr" firestore:"name_fr"`
	Description   string   `json:"description" firestore:"description"`
	DescriptionEN string   `json:"description_en" firestore:"description_en"`
	DescriptionFR string   `json:"description_fr" firestore:"description_fr"`
	Brand         string   `json:"brand" firestore:"brand"`
	Model         string   `json:"model" firestore:"model"`
	Category      []string `json:"category" firestore:"category"`
	SubCategory   []string `json:"sub_category" firestore:"sub_category"`
	<PERSON><PERSON>    `json:"alias" firestore:"alias"`
	ImageLink     string   `json:"image_link" firestore:"image_link"`
	// ARA Classification fields
	ARALevel1ID     int        `json:"ara_level1_id" firestore:"ara_level1_id"`
	ARALevel2ID     int        `json:"ara_level2_id" firestore:"ara_level2_id"`
	ARALevel1Name   string     `json:"ara_level1_name" firestore:"ara_level1_name"`
	ARALevel2Name   string     `json:"ara_level2_name" firestore:"ara_level2_name"`
	ARAClassifiedAt *time.Time `json:"ara_classified_at" firestore:"ara_classified_at"`
}

// Alias is the alias for the equipments.
type Alias struct {
	En []string `json:"en" firestore:"en"`
	Fr []string `json:"fr" firestore:"fr"`
}
