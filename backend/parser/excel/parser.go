package excelparser

import (
	"fmt"
	"io"

	"github.com/mitchellh/mapstructure"
	"github.com/xuri/excelize/v2"

	"github.com/vima-inc/derental/parser"
)

type client struct {
	sheetName string
	columns   map[int]string
	startRow  int
}

// New creates a new excel parser.
func New(sheetName string, startRow int, columns map[int]string) (parser.Parser, error) {
	if sheetName == "" {
		return nil, fmt.Errorf("sheet name is required")
	}

	return &client{
		sheetName: sheetName,
		startRow:  startRow,
		columns:   columns,
	}, nil
}

// Parse parses a excel file.
func (c *client) Parse(reader io.Reader, out interface{}) error {
	f, err := excelize.OpenReader(reader)
	if err != nil {
		return fmt.Errorf("unable to parse excel file: %w", err)
	}

	r, err := f.GetRows(c.sheetName)
	if err != nil {
		return fmt.Errorf("unable to read excel file: %w", err)
	}

	rows := r[c.startRow:]
	data := make([]map[string]interface{}, 0, len(rows))

	for _, cols := range rows {
		newElement := make(map[string]interface{})

		for i, cell := range cols {
			field := c.columns[i]
			newElement[field] = cell
		}

		if len(newElement) == 0 {
			continue
		}

		data = append(data, newElement)
	}

	decoder, err := mapstructure.NewDecoder(&mapstructure.DecoderConfig{
		Result:  out,
		TagName: "excel",
	})
	if err != nil {
		return fmt.Errorf("unable to create decoder: %w", err)
	}

	err = decoder.Decode(data)
	if err != nil {
		return fmt.Errorf("unable to decode data: %w", err)
	}

	return nil
}
