package ses

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"html/template"
	"io"
	"mime/multipart"
	"net/textproto"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/sesv2"
	"github.com/aws/aws-sdk-go-v2/service/sesv2/types"
	"github.com/gabriel-vasile/mimetype"

	"github.com/vima-inc/derental/mailer"
	isotrage "github.com/vima-inc/derental/storage"
)

type storage interface {
	ReadWithMetadata(ctx context.Context, bucket string, path string) (io.ReadCloser, isotrage.Metadata, error)
}

type client struct {
	client    *sesv2.Client
	from      string
	templates map[string]string
	storage   storage
	bucket    string
}

// New returns a new instance of Email.
func New(key, secret, region string, sender string, storage storage, bucket string) (mailer.Mailer, error) {
	cfg := aws.Config{
		Credentials: credentials.NewStaticCredentialsProvider(key, secret, ""),
		Region:      region,
	}

	s := sesv2.NewFromConfig(cfg)

	return &client{
		client:  s,
		from:    sender,
		storage: storage,
		bucket:  bucket,
	}, nil
}

// SetTemplate sets the template for the given name.
func (c *client) SetTemplateIDs(templateIDs map[string]string) {
	c.templates = templateIDs
}

// Send sends an email.
func (e *client) SendFromTemplate(ctx context.Context, templateName string, to []string, params map[string]string) error {
	templateID, exist := e.templates[templateName]
	if !exist {
		return fmt.Errorf("template %s not found", templateName)
	}

	body, subject, err := e.loadTemplate(ctx, templateID, params)
	if err != nil {
		return fmt.Errorf("error loading template %s: %w", templateName, err)
	}

	_, err = e.client.SendEmail(ctx, &sesv2.SendEmailInput{
		FromEmailAddress: aws.String(e.from),
		ReplyToAddresses: []string{e.from},
		Destination: &types.Destination{
			ToAddresses: to,
		},
		Content: &types.EmailContent{
			Simple: &types.Message{
				Subject: &types.Content{
					Data:    aws.String(subject),
					Charset: aws.String("utf-8"),
				},
				Body: &types.Body{
					Html: &types.Content{
						Data:    &body,
						Charset: aws.String("utf-8"),
					},
				},
			},
		},
	})
	if err != nil {
		return fmt.Errorf("error sending email: %w", err)
	}

	return nil
}

// SendFromTemplateWithAttachement sends an email with attachment.
func (e *client) SendFromTemplateWithAttachement(ctx context.Context, templateName string, to []string, params map[string]string, attachment map[string][]byte) error {
	templateID, exist := e.templates[templateName]
	if !exist {
		return fmt.Errorf("template %s not found", templateName)
	}

	body, subject, err := e.loadTemplate(ctx, templateID, params)
	if err != nil {
		return fmt.Errorf("error loading template %s: %w", templateName, err)
	}

	data, err := createRawEmailContent(e.from, to, subject, body, attachment)
	if err != nil {
		return fmt.Errorf("error creating raw email content: %w", err)
	}

	_, err = e.client.SendEmail(ctx, &sesv2.SendEmailInput{
		FromEmailAddress: aws.String(e.from),
		ReplyToAddresses: []string{e.from},
		Destination: &types.Destination{
			ToAddresses: to,
		},
		Content: &types.EmailContent{
			Raw: &types.RawMessage{
				Data: data,
			},
		},
	})
	if err != nil {
		return fmt.Errorf("error sending email: %w", err)
	}

	return nil
}
func (e *client) SendFromTemplateWithObject(ctx context.Context, templateName string, to []string, params map[string]any) error {
	templateID, exist := e.templates[templateName]
	if !exist {
		return fmt.Errorf("template %s not found", templateName)
	}

	body, subject, err := e.loadTemplatewithObject(ctx, templateID, params)
	if err != nil {
		return fmt.Errorf("error loading template %s: %w", templateName, err)
	}

	_, err = e.client.SendEmail(ctx, &sesv2.SendEmailInput{
		FromEmailAddress: aws.String(e.from),
		ReplyToAddresses: []string{e.from},
		Destination: &types.Destination{
			ToAddresses: to,
		},
		Content: &types.EmailContent{
			Simple: &types.Message{
				Subject: &types.Content{
					Data:    aws.String(subject),
					Charset: aws.String("utf-8"),
				},
				Body: &types.Body{
					Html: &types.Content{
						Data:    &body,
						Charset: aws.String("utf-8"),
					},
				},
			},
		},
	})
	if err != nil {
		return fmt.Errorf("error sending email: %w", err)
	}

	return nil
}

func (e *client) loadTemplate(ctx context.Context, fileName string, data map[string]string) (string, string, error) {
	html, metadata, err := e.storage.ReadWithMetadata(ctx, e.bucket, "templates/"+fileName+".html")
	if err != nil {
		return "", "", fmt.Errorf("error reading template: %w", err)
	}

	defer html.Close()

	body, err := io.ReadAll(html)
	if err != nil {
		return "", "", fmt.Errorf("error reading template: %w", err)
	}

	tmpl, err := template.New(fileName).Parse(string(body))
	if err != nil {
		return "", "", fmt.Errorf("error parsing template: %w", err)
	}

	var tpl bytes.Buffer
	if err := tmpl.Execute(&tpl, data); err != nil {
		return "", "", fmt.Errorf("error executing template: %w", err)
	}

	return tpl.String(), metadata["subject"], nil
}

func (e *client) loadTemplatewithObject(ctx context.Context, fileName string, data map[string]any) (string, string, error) {
	html, metadata, err := e.storage.ReadWithMetadata(ctx, e.bucket, "templates/"+fileName+".html")
	if err != nil {
		return "", "", fmt.Errorf("error reading template: %w", err)
	}

	defer html.Close()

	body, err := io.ReadAll(html)
	if err != nil {
		return "", "", fmt.Errorf("error reading template: %w", err)
	}

	tmpl, err := template.New(fileName).Parse(string(body))
	if err != nil {
		return "", "", fmt.Errorf("error parsing template: %w", err)
	}

	var tpl bytes.Buffer
	if err := tmpl.Execute(&tpl, data); err != nil {
		return "", "", fmt.Errorf("error executing template: %w", err)
	}

	return tpl.String(), metadata["subject"], nil
}

func createRawEmailContent(sender string, to []string, subject, bodyHTML string, attachments map[string][]byte) ([]byte, error) {
	var emailBody strings.Builder

	writer := multipart.NewWriter(&emailBody)
	boundary := writer.Boundary()

	fmt.Fprintf(&emailBody, "From: %s\r\n", sender)
	fmt.Fprintf(&emailBody, "To: %s\r\n", strings.Join(to, ", "))
	fmt.Fprintf(&emailBody, "Subject: %s\r\n", subject)
	fmt.Fprintf(&emailBody, "MIME-Version: 1.0\r\n")
	fmt.Fprintf(&emailBody, "Content-Type: multipart/mixed; boundary=%s\r\n", boundary)
	fmt.Fprintf(&emailBody, "\r\n")

	htmlPart := textproto.MIMEHeader{
		"Content-Type": {"text/html; charset=UTF-8"},
	}
	htmlWriter, err := writer.CreatePart(htmlPart)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTML part: %v", err)
	}
	if _, err := htmlWriter.Write([]byte(bodyHTML)); err != nil {
		return nil, fmt.Errorf("failed to write HTML part: %v", err)
	}

	for name, data := range attachments {
		encodedAttachment := base64.StdEncoding.EncodeToString(data)
		mimeType := mimetype.Detect(data).String()

		attachmentPart := textproto.MIMEHeader{
			"Content-Type":              {mimeType},
			"Content-Disposition":       {fmt.Sprintf("attachment; filename=%s", name)},
			"Content-Transfer-Encoding": {"base64"},
		}
		attachmentWriter, err := writer.CreatePart(attachmentPart)
		if err != nil {
			return nil, fmt.Errorf("failed to create attachment part: %v", err)
		}
		if _, err := attachmentWriter.Write([]byte(encodedAttachment)); err != nil {
			return nil, fmt.Errorf("failed to write attachment part: %v", err)
		}
	}

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close writer: %v", err)
	}

	return []byte(emailBody.String()), nil
}
