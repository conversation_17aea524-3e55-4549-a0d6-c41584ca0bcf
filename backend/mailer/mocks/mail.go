// Code generated by mockery v2.12.2. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"

	testing "testing"
)

// Mailer is an autogenerated mock type for the Mailer type
type Mailer struct {
	mock.Mock
}

// SendFromTemplate provides a mock function with given fields: ctx, templateID, to, params
func (_m *Mailer) SendFromTemplate(ctx context.Context, templateID string, to []string, params map[string]string) error {
	ret := _m.Called(ctx, templateID, to, params)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, []string, map[string]string) error); ok {
		r0 = rf(ctx, templateID, to, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}



// SetTemplateIDs provides a mock function with given fields: templateIDs
func (_m *Mailer) SetTemplateIDs(templateIDs map[string]string) {
	_m.Called(templateIDs)
}

// NewMailer creates a new instance of Mail<PERSON>. It also registers the testing.TB interface on the mock and a cleanup function to assert the mocks expectations.
func NewMailer(t testing.TB) *Mailer {
	mock := &Mailer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
