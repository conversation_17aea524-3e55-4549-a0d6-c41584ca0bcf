package mocks

import (
	context "context"
	"sync"

	mock "github.com/stretchr/testify/mock"
)

// MailerMockAsync is an autogenerated mock type for the Mailer type it used with go routine tests.
type MailerMockAsync struct {
	Wg        sync.WaitGroup
	CallCount int
	mock.Mock
}

// SendFromTemplate provides a mock function with given fields: ctx, templateID, to, params.
func (_m *MailerMockAsync) SendFromTemplate(ctx context.Context, templateID string, to []string, params map[string]string) error {
	defer _m.Wg.Done()
	_m.CallCount++
	args := _m.Called(ctx, templateID, to, params)

	return args.Error(0)
}

// SendFromTemplateWithObject provides a mock function with given fields: ctx, templateID, to, params.
func (_m *MailerMockAsync) SendFromTemplateWithObject(ctx context.Context, templateID string, to []string, params map[string]any) error {
	defer _m.Wg.Done()
	_m.CallCount++
	args := _m.Called(ctx, templateID, to, params)

	return args.Error(0)

}

// SendFromTemplateWithAttachement provides a mock function with given fields: ctx, templateID, to, params.
func (_m *MailerMockAsync) SendFromTemplateWithAttachement(ctx context.Context, templateID string, to []string, params map[string]string, attachment map[string][]byte) error {
	defer _m.Wg.Done()
	_m.CallCount++
	args := _m.Called(ctx, templateID, to, params, attachment)

	return args.Error(0)
}

// CurrentCount returns the number of calls made to the mocks.
func (_m *MailerMockAsync) CurrentCount() int {
	_m.Wg.Wait() // wait for all the call to happen. This will block until Wg.Done() is called.

	return _m.CallCount
}

// SetTemplateIDs provides a mock function with given fields: templateIDs.
func (_m *MailerMockAsync) SetTemplateIDs(templateIDs map[string]string) {
	defer _m.Wg.Done()
	_m.CallCount++
	_m.Called(templateIDs)
}
