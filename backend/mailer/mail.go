package mailer

import "context"

const (
	// ForgotPasswordTemplateID is the template ID of the forgot password template.
	ForgotPasswordTemplateID = "forgot_password_template_id"
	// ForgotPasswordTemplateID is the template ID of the forgot password template.
	ForgotPasswordTemplateIDFR = "forgot_password_template_id_fr"
	// BookingAcceptTemplateID is the template ID of the booking Accept template.
	BookingAcceptTemplateID = "booking_accept_template_id"
	// BookingDeclineTemplateID is the template ID of the booking decline template.
	BookingDeclineTemplateID = "booking_decline_template_id"
	// BookingLodgerRequestTemplateID is the template ID of the booking request template.
	SendgridBookingLodgerRequestID = "booking_lodger_request_template_id"
	// BookingEquipperRequestTemplateID is the template ID of the booking request template.
	SendgridBookingEquipperRequestID = "booking_equipper_request_template_id"
	// SendInvitationMemberID is the template ID of the invitation request template.
	SendInvitationMemberID = "send_invitation_member_id"
	// SendInvitationMemberID is the template ID of the invitation request template to lodger.
	SendInvitationMemberLodgerID = "send_invitation_member_lodger_id"
	// SendInvitationMemberLodgerIDFR is the template ID of the invitation request template to lodger.
	SendInvitationMemberLodgerIDFR = "send_invitation_member_lodger_id_fr"
	// SendRenterBidzRequestID is the template ID of the send renter bidz request template.
	SendRenterBidzRequestID = "sendgrid_send_renter_bidz_request_id"
	// SendEquipperBidzRequestID is the template ID of the send equipper bidz request template.
	SendEquipperBidzRequestID = "sendgrid_send_equipper_bidz_request_id"
	// SendgridSendRenterBidzOfferID is the template ID of the send renter bidz offer template.
	SendgridSendRenterBidzOfferID = "sendgrid_send_renter_bidz_offer_id"
	// SendgridSendConfirmationEquipperBidzOfferID is the template ID of the send bidz offer template.
	SendgridSendConfirmationEquipperBidzOfferID = "sendgrid_send_confirmation_equipper_bidz_offer_id"
	// SendgridSendConfirmationRenterBidzOfferID is the template ID of the send bidz offer template.
	SendgridSendConfirmationRenterBidzOfferID = "sendgrid_send_confirmation_renter_bidz_offer_id"
	// SendRenterBidzRequestID is the template ID of the send renter bidz request template.
	SendRenterBidzRequestIDFR = "sendgrid_send_renter_bidz_request_id_fr"
	// SendEquipperBidzRequestID is the template ID of the send equipper bidz request template.
	SendEquipperBidzRequestIDFR = "sendgrid_send_equipper_bidz_request_id_fr"
	// SendgridSendRenterBidzOfferID is the template ID of the send renter bidz offer template.
	SendgridSendRenterBidzOfferIDFR = "sendgrid_send_renter_bidz_offer_id_fr"
	// SendgridSendConfirmationEquipperBidzOfferID is the template ID of the send bidz offer template.
	SendgridSendConfirmationEquipperBidzOfferIDFR = "sendgrid_send_confirmation_equipper_bidz_offer_id_fr"
	// SendgridSendConfirmationRenterBidzOfferID is the template ID of the send bidz offer template.
	SendgridSendConfirmationRenterBidzOfferIDFR = "sendgrid_send_confirmation_renter_bidz_offer_id_fr"
	// SendgridSendCanceledRenterBidzOfferID is the template ID of the send bidz offer template.
	SendgridSendCanceledEquipperBidzOfferID = "sendgrid_send_canceled_equipper_bidz_offer_id"
	// SendgridSendCanceledRenterBidzOfferID is the template ID of the send bidz offer template.
	SendgridSendCanceledRenterBidzOfferID = "sendgrid_send_canceled_renter_bidz_offer_id"
	// SendgridSendCanceledRenterBidzOfferID is the template ID of the send bidz offer template.
	SendgridSendCanceledEquipperBidzOfferIDFR = "sendgrid_send_canceled_equipper_bidz_offer_id_fr"
	// SendgridSendCanceledRenterBidzOfferID is the template ID of the send bidz offer template.
	SendgridSendCanceledRenterBidzOfferIDFR = "sendgrid_send_canceled_renter_bidz_offer_id_fr"
	// SendWelcomeTemplateIDFR is the template ID of the send user after the signup.
	SendWelcomeTemplateIDFR = "sendgrid_welcome_template_id_fr"
	// SendWelcomeTemplateID is the template ID of the send user after the signup.
	SendWelcomeTemplateID = "sendgrid_welcome_template_id"
	// SendCancelBookingTemplateID is the template ID of the cancel booking template.
	SendgridCancelBookingTemplateID = "sendgrid_send_renter_booking_canceled_template_id"
	// SendCancelBookingTemplateIDFR is the template ID of the  cancel booking template.
	SendgridCancelBookingTemplateIDFR = "sendgrid_send_renter_booking_canceled_template_id_fr"
	// SendgridBookingAcceptTemplateIDFR is the template ID of the booking accept template.
	SendgridBookingAcceptTemplateIDFR = "sendgrid_booking_accept_template_id_fr"
	// SendgridBookingDeclineTemplateIDFR is the template ID of the booking decline template.
	SendgridBookingDeclineTemplateIDFR = "sendgrid_booking_decline_template_id_fr"
	// SendgridBookingLodgerRequestIDFR is the template ID of the lodger booking request template.
	SendgridBookingLodgerRequestIDFR = "sendgrid_booking_lodger_request_template_id_fr"
	// SendgridBookingEquipperRequestIDFR is the template ID of the equipper booking request template.
	SendgridBookingEquipperRequestIDFR = "sendgrid_booking_equipper_request_template_id_fr"
	// SendLeadTemplateID is the template ID of the send lead information to tooler.
	SendgridLeadTemplateID = "sendgrid_leave_template_id"
	// SendgridToolerTeamEmail is the email of tooler team.
	SendgridToolerTeamEmail = "sendgrid_tooler_team_email_template_id"
	// SendgridRentalExtensionConfirmationID is the template ID of the rental extension confirmation template.
	SendgridRentalExtensionConfirmation = "sendgrid_rental_extension_confirmation_id"
	// SendgridRentalExtensionConfirmationIDFR is the template ID of the rental extension confirmation template.
	SendgridRentalExtensionConfirmationFR = "sendgrid_rental_extension_confirmation_fr"
	// SendgridRandomEquippersBidzRequestID is the template ID of the send random equippers bidz request template.
	SendgridRandomEquippersBidzRequestID = "sendgrid_random_equippers_bidz_request_id"
	// SendgridRandomEquippersBidzRequestIDFR is the template ID of the send random equippers bidz request template.
	SendgridRandomEquippersBidzRequestIDFR = "sendgrid_random_equippers_bidz_request_id_fr"
	// SendgridSendRenterWelcomeTemplateID is the template ID of the send renter welcome template.
	SendgridSendEquipperWelcomeTemplateIDFR = "sendgrid_send_equipper_welcome_template_id_fr"
	// SendgridSendRenterWelcomeTemplateID is the template ID of the send renter welcome template.
	SendgridSendEquipperWelcomeTemplateID = "sendgrid_send_equipper_welcome_template_id"
	// SendgridSendAutomaticUpdateTemplateID is the template ID of the send Automatic Update equipment to  equipper.
	SendgridSendAutomaticUpdateTemplateID = "sendgrid_send_automatic_update_template_id"
	// SendgridSendAutomaticUpdateTemplateIDFR is the template ID of the send Automatic Update equipment to  equipper.
	SendgridSendAutomaticUpdateTemplateIDFR = "sendgrid_send_automatic_update_template_id_fr"
	// SendgridSendEquipperCSVReportID is the template ID of the send equipper csv report.
	SendgridSendEquipperCSVReportID = "sendgrid_send_equipper_csv_report_id"
	// SendgridSendEquipperCSVReportIDFR is the template ID of the send renter csv report.
	SendgridSendEquipperCSVReportIDFR = "sendgrid_send_equipper_csv_report_id_fr"
	// SendEquipmentChangeTemplateID is the template ID of the send equipment change notification.
	SendEquipmentChangeTemplateID = "sendgrid_send_equipment_change_template_id"
)

// Mailer is an interface for sending mail.
type Mailer interface {
	SetTemplateIDs(templateIDs map[string]string)
	SendFromTemplate(ctx context.Context, templateID string, to []string, params map[string]string) error
	SendFromTemplateWithObject(ctx context.Context, templateID string, to []string, params map[string]any) error
	SendFromTemplateWithAttachement(ctx context.Context, templateName string, to []string, params map[string]string, attachment map[string][]byte) error
}
