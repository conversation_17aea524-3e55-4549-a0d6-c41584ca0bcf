package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/vima-inc/derental/api/middleware"
	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

func getLodger(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		lodger, err := svc.GetLodgerByID(c.Request.Context(), lodgerID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.<PERSON>(http.StatusOK, lodger)
	}
}

func updateLodger(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var lodger models.Lodger

		err := c.BindJSON(&lodger)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		if lodgerID != lodger.ID {
			c.JSON(http.StatusBadRequest, gin.H{"error": "unable to update lodger"})

			return
		}

		err = lodger.Validate()
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})

			return
		}

		err = svc.UpdateLodger(c.Request.Context(), lodger)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func deleteLodger(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		err := svc.DeleteLodger(c.Request.Context(), lodgerID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func uploadLodgerProfilePicture(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		file, header, err := c.Request.FormFile("file")
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}
		defer file.Close()

		err = svc.UploadLodgerPhoto(c.Request.Context(), lodgerID, header.Filename, file)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func getAllLodgers(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		lodgers, err := svc.GetAllLodgers(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, lodgers)
	}
}

func GetAllLodgersByCountry(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		country := c.Param("country")
		lodgers, err := svc.GetAllLodgersByCountry(c.Request.Context(), country)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, lodgers)
	}
}
func getLodgerByID(
	svc *service.Service,
) gin.HandlerFunc {
	return func(c *gin.Context) {
		lodgerID := c.Param("id")

		lodger, err := svc.GetLodgerByID(c.Request.Context(), lodgerID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}
		c.JSON(http.StatusOK, lodger)
	}
}
