package api

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/vima-inc/derental/api/middleware"
	"github.com/vima-inc/derental/db/mocks"
	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

// TestAdminDomainValidation tests the domain validation logic
func TestAdminDomainValidation(t *testing.T) {
	tests := []struct {
		name     string
		email    string
		expected bool
	}{
		{
			name:     "Valid admin domain",
			email:    "<EMAIL>",
			expected: true,
		},
		{
			name:     "Invalid domain - gmail",
			email:    "<EMAIL>",
			expected: false,
		},
		{
			name:     "Invalid domain - yahoo",
			email:    "<EMAIL>",
			expected: false,
		},
		{
			name:     "Invalid domain - similar but wrong",
			email:    "<EMAIL>",
			expected: false,
		},
		{
			name:     "Empty email",
			email:    "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test the domain validation logic
			isValid := strings.HasSuffix(tt.email, "@derentalequipment.com")
			assert.Equal(t, tt.expected, isValid)
		})
	}
}

func TestGetMasterInventory(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Setup
	mockDB := &mocks.Database{}
	mockService := service.New(service.WithDB(mockDB))

	// Mock data
	expectedInventory := []models.ToolerBidzEquipment{
		{
			ID:            "1",
			NameEN:        "Test Equipment",
			NameFR:        "Équipement de test",
			DescriptionEN: "Test description",
			Category:      []string{"Construction"},
			SubCategory:   []string{"Heavy Machinery"},
		},
	}

	// Setup mock expectations
	mockDB.On("GetAllToolerBidzEquipment", mock.Anything).Return(expectedInventory, nil)

	// Create request
	req := httptest.NewRequest(http.MethodGet, "/admin/master-inventory", nil)
	w := httptest.NewRecorder()

	// Create Gin context
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Set up admin email in context (simulating AdminAuthorization middleware)
	ctx := context.WithValue(c.Request.Context(), middleware.EmailKey, "<EMAIL>")
	c.Request = c.Request.WithContext(ctx)

	// Call handler
	handler := getMasterInventory(mockService)
	handler(c)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response, "data")
	assert.Contains(t, response, "total")

	// Verify mock was called
	mockDB.AssertExpectations(t)
}

func TestGetAdminDashboardStats(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Setup
	mockDB := &mocks.Database{}
	mockService := service.New(service.WithDB(mockDB))

	// Mock data
	mockInventory := []models.ToolerBidzEquipment{{ID: "1"}, {ID: "2"}}
	mockEquippers := []models.Equipper{{ID: "1"}}
	mockLodgers := []models.Lodger{{ID: "1"}}

	// Setup mock expectations - these are called by GetAdminDashboardStats
	mockDB.On("GetAllToolerBidzEquipment", mock.Anything).Return(mockInventory, nil)
	mockDB.On("GetAllEquipper", mock.Anything).Return(mockEquippers, nil)
	mockDB.On("GetAllLodgers", mock.Anything).Return(mockLodgers, nil)
	mockDB.On("CountAllEquipments", mock.Anything).Return(0, nil)

	// Create request
	req := httptest.NewRequest(http.MethodGet, "/admin/dashboard/stats", nil)
	w := httptest.NewRecorder()

	// Create Gin context
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Set up admin email in context (simulating AdminAuthorization middleware)
	ctx := context.WithValue(c.Request.Context(), middleware.EmailKey, "<EMAIL>")
	c.Request = c.Request.WithContext(ctx)

	// Call handler
	handler := getAdminDashboardStats(mockService)
	handler(c)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check expected fields
	assert.Equal(t, float64(2), response["total_master_inventory"])
	assert.Equal(t, float64(1), response["total_equippers"])
	assert.Equal(t, float64(1), response["total_lodgers"])
	assert.Equal(t, float64(0), response["total_equipments"])
	assert.Equal(t, float64(0), response["total_bookings"])

	// Verify mocks were called
	mockDB.AssertExpectations(t)
}
