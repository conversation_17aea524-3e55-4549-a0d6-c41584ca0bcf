package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/vima-inc/derental/api/middleware"
	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

func getEquipperByID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		equipperID := c.Param("equipper_id")

		resp, err := svc.GetEquipperByID(c.Request.Context(), equipperID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func getEquipperByEmail(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		equipperID := c.<PERSON>m("email")

		resp, err := svc.GetEquipperByEmail(c.Request.Context(), equipperID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func getEquipper(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		equipper, err := svc.GetEquipperByID(c.Request.Context(), equipperID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, equipper)
	}
}

func getAllEquippersByCategory(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var bidzRequest models.BidsRequest

		err := c.BindJSON(&bidzRequest)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		category := bidzRequest.Category

		equipper, err := svc.GetAllEquippersByCategory(c.Request.Context(), category)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, equipper)
	}
}

func getEquippersByCountry(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		countryCode := c.Param("country")

		equipper, err := svc.GetEquippersByCountry(c.Request.Context(), countryCode)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, equipper)
	}
}

func updateEquipper(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var equipper models.Equipper

		err := c.BindJSON(&equipper)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		if equipperID != equipper.ID {
			c.JSON(http.StatusBadRequest, gin.H{"error": "unable to update equipper"})

			return
		}

		err = svc.UpdateEquipper(c.Request.Context(), equipper)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func deleteEquipper(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		err := svc.DeleteEquipper(c.Request.Context(), equipperID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func getEquipmentsByEquipperID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var limit int

		l, err := strconv.ParseInt(c.Query("limit"), 10, 32)
		if err == nil {
			limit = int(l)
		}

		lastID := c.Query("last_id")

		resp, err := svc.GetEquipmentsByEquipperID(c.Request.Context(), equipperID, limit, lastID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func getBookedEquipmentsByEquipperID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var limit int

		l, err := strconv.ParseInt(c.Query("limit"), 10, 32)
		if err == nil {
			limit = int(l)
		}

		lastID := c.Query("last_id")

		equipments, err := svc.GetBookedEquipmentsByEquipperID(c.Request.Context(), equipperID, limit, lastID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, equipments)
	}
}

func uploadProfilePicture(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		file, header, err := c.Request.FormFile("file")
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}
		defer file.Close()

		err = svc.UploadPhoto(c.Request.Context(), equipperID, header.Filename, file)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func getEquipperByUserName(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		userName := c.Param("userName")

		equipper, err := svc.GetEquipperByUserName(c.Request.Context(), userName)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, equipper)
	}
}

func getAllEquipper(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		equipper, err := svc.GetAllEquipper(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, equipper)
	}
}
