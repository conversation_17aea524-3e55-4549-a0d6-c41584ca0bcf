package api

import (
	"context"
	"encoding/json"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/vima-inc/derental/api/middleware"
	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

func getEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		equipmentID := c.Param("equipment_id")

		resp, err := svc.GetEquipmentByID(c.Request.Context(), equipmentID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func getEquipmentsByEquipperIDAndEquipmentName(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		equipperID := c.Query("equipper_id")

		equipmentName := c.Query("name")

		resp, err := svc.GetAvailableEquipmentsByEquipperIDAndEquipmentNameORAlias(c.Request.Context(), equipperID, equipmentName)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func addEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var equipment models.Equipment

		err := c.BindJSON(&equipment)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		equipment, err = svc.AddEquipment(c.Request.Context(), equipperID, equipment)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, equipment)

	}
}

func uploadImageEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {

		equipmentID := c.Param("equipment_id")

		file, header, err := c.Request.FormFile("file")

		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}
		defer file.Close()

		err = svc.UploadImageEquipment(c.Request.Context(), equipmentID, header.Filename, file)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func updateEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var equipment models.Equipment

		err := c.BindJSON(&equipment)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = svc.UpdateEquipment(c.Request.Context(), equipperID, equipment)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func updateBatchOfEquipmentByName(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var data struct {
			EquipmentNames      []string `json:"equipment_names"`
			MinimumRentalPeriod int      `json:"minimum_rental_period"`
		}

		err := c.BindJSON(&data)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)
		}

		err = svc.UpdateBatchOfEquipmentByName(c.Request.Context(), equipperID, data.EquipmentNames, data.MinimumRentalPeriod)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func deleteEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		equipmentID := c.Param("equipment_id")

		err := svc.DeleteEquipment(c.Request.Context(), equipperID, equipmentID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func bookEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var bookEquipment models.BookEquipmentRequest

		err := c.BindJSON(&bookEquipment)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		checkoutURL, err := svc.BookEquipment(c.Request.Context(), lodgerID, bookEquipment)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, gin.H{"checkout_url": checkoutURL})
	}
}

func acceptBookEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		bookEquipmentID := c.Param("book_equipment_id")
		if bookEquipmentID == "" {
			c.Status(http.StatusBadRequest)

			return
		}

		var bookEquipment models.BookEquipmentRequest

		err := c.BindJSON(&bookEquipment)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = svc.AcceptBooking(c.Request.Context(), equipperID, bookEquipmentID, bookEquipment)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func rejectBookEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		bookEquipmentID := c.Param("book_equipment_id")
		if bookEquipmentID == "" {
			c.Status(http.StatusBadRequest)

			return
		}

		err := svc.RejectBooking(c.Request.Context(), equipperID, bookEquipmentID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func cancelBookEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		userID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		bookEquipmentID := c.Param("book_equipment_id")
		if bookEquipmentID == "" {
			c.Status(http.StatusBadRequest)

			return
		}

		var booking models.BookEquipment

		err := c.BindJSON(&booking)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = svc.CancelBooking(c.Request.Context(), userID, bookEquipmentID, booking)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func getBookEquipmentByLodgerID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		bookEquipments, err := svc.GetBookingByLodgerID(c.Request.Context(), lodgerID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, bookEquipments)
	}
}

func getBookedEquipmentByStatusAndEquipperID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var limit int

		l, err := strconv.ParseInt(c.Query("limit"), 10, 32)
		if err == nil {
			limit = int(l)
		}

		lastID := c.Query("last_id")
		status := models.BookingStatus(c.Query("status"))

		res := status.IsValid()
		if !res {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid status"})

			return
		}

		bookEquipments, err := svc.GetBookedByStatusAndEquipperID(c.Request.Context(), equipperID, status, limit, lastID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, bookEquipments)
	}
}

func getBookedEquipmentByStatusAndLodgerID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var limit int

		l, err := strconv.ParseInt(c.Query("limit"), 10, 32)
		if err == nil {
			limit = int(l)
		}

		lastID := c.Query("last_id")
		status := models.BookingStatus(c.Query("status"))

		res := status.IsValid()
		if !res {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid status"})

			return
		}

		bookEquipments, err := svc.GetBookedEquipmentByStatusAndLodgerID(c.Request.Context(), lodgerID, status, limit, lastID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, bookEquipments)
	}
}

func uploadEquipmentsFromFileNotification(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var message models.FileUploadNotification

		err := json.NewDecoder(c.Request.Body).Decode(&message)
		if err != nil {
			log.Printf("unable to decode PubSub message: %v", err)
			c.Status(http.StatusOK)
			_ = c.Error(err)

			return
		}

		if message.Message.Attributes.EventType != string(models.FinalizeEvent) {
			log.Printf("ignoring PubSub message of type %v", message)
			c.Status(http.StatusOK)

			return
		}

		// Process equipment upload asynchronously to avoid PubSub timeout
		// Respond immediately to PubSub while processing continues in background
		go func() {
			// Use background context instead of request context to avoid HTTP timeouts
			// The equipment upload process has its own internal timeouts
			ctx := context.Background()

			log.Printf("Starting equipment upload from file: %s", message.Message.Attributes.ObjectID)

			err := svc.AddEquipmentsFromFile(ctx, message.Message.Attributes.ObjectID)
			if err != nil {
				log.Printf("unable to add equipments from file: %v", err)
				return
			}

			log.Printf("Equipment upload completed successfully for file: %s", message.Message.Attributes.ObjectID)
		}()

		// Respond immediately to PubSub
		log.Printf("PubSub message acknowledged, processing equipment upload in background")
		c.Status(http.StatusOK)
	}
}

func uploadEquipments(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		file, header, err := c.Request.FormFile("file")
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}
		defer file.Close()

		_, err = svc.SaveEquipmentsFile(c.Request.Context(), equipperID, header.Filename, file)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func airTableSynchronization(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}
		var data struct {
			Currency string `json:"currency"`
		}

		err := c.BindJSON(&data)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)
		}

		err = svc.SyncAirTable(c.Request.Context(), equipperID, data.Currency)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func updateBookingEquipmentStatusAfterReturnEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		userID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		bookEquipmentID := c.Param("book_equipment_id")
		if bookEquipmentID == "" {
			c.Status(http.StatusBadRequest)

			return
		}

		err := svc.UpdateBookingStatusAfterReturnEquipment(c.Request.Context(), userID, bookEquipmentID)
		if err != nil {
			c.Status(http.StatusInternalServerError)
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func deleteEquipmentsByEquipperID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		err := svc.DeleteEquipmentsByEquipperID(c.Request.Context(), equipperID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func changeEquipmentStatus(s *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		_, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		equipmentID := c.Param("equipment_id")
		if equipmentID == "" {
			c.Status(http.StatusBadRequest)

			return
		}

		var equipment models.Equipment

		err := json.NewDecoder(c.Request.Body).Decode(&equipment)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = s.ChangeEquipmentStatus(c.Request.Context(), equipmentID, equipment.Status)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func processEquipmentBookingsAndNotifyEquippers(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		log.Printf("Received request to process equipment bookings and notify equippers")

		ctx := c.Request.Context()

		err := svc.ProcessEquipmentBookingsAndNotifyEquippers(ctx)
		if err != nil {
			log.Printf("Error processing equipment bookings and notifying equippers: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})

			return
		}

		log.Printf("Successfully processed equipment bookings and notified equippers")

		c.Status(http.StatusOK)
	}
}
