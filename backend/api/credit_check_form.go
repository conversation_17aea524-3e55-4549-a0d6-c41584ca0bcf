package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/vima-inc/derental/api/middleware"
	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

func getCreditCheckFormByID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		creditCheckFormID := c.Param("credit_check_form_id")

		resp, err := svc.GetCreditCheckFormByID(c.Request.Context(), creditCheckFormID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.<PERSON>rror(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func getCreditCheckFormByLodgerID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		resp, err := svc.GetCreditCheckFormByLodgerID(c.Request.Context(), lodgerID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func addCreditCheckForm(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		userID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var creditCheckForm models.CreditCheckForm
		creditCheckForm.LodgerID = userID

		err := c.BindJSON(&creditCheckForm)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		newCreditCheckForm, err := svc.AddCreditCheckForm(c.Request.Context(), creditCheckForm, userID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, newCreditCheckForm)
	}
}

func deleteCreditCheckForm(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		creditCheckFormID := c.Param("credit_check_form_id")

		err := svc.DeleteCreditCheckForm(c.Request.Context(), lodgerID, creditCheckFormID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func updateCreditCheckForm(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		creditCheckFormID := c.Param("credit_check_form_id")

		var creditCheckForm models.CreditCheckForm

		err := c.BindJSON(&creditCheckForm)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = svc.UpdateCreditCheckForm(c.Request.Context(), creditCheckForm, creditCheckFormID, lodgerID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func uploadCreditCheckForm(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		file, _, err := c.Request.FormFile("file")
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		defer file.Close()

		creditCheckFormID := c.Param("credit_check_form_id")

		err = svc.UploadCreditCheckForm(c.Request.Context(), lodgerID, creditCheckFormID, file)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func uploadPDF(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}
		attachmentName := c.Param("attachment_name")
		creditCheckFormID := c.Param("credit_check_form_id")

		file, _, err := c.Request.FormFile("file")
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		defer file.Close()

		err = svc.UploadPDF(c.Request.Context(), lodgerID, attachmentName, creditCheckFormID, file)

		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func getCreditCheckFormAttachment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		userID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		bookingID := c.Param("booking_id")
		attachmentName := c.Param("attachment_name")
		creditCheckFormID := c.Param("credit_check_form_id")

		resp, err := svc.GetCreditCheckFormAttachment(c.Request.Context(), userID, bookingID, creditCheckFormID, attachmentName)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, gin.H{"url": resp})
	}
}
