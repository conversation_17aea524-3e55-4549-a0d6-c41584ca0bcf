package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/vima-inc/derental/api/middleware"
	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

func getProjectByLodgerID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		projects, err := svc.GetProjectByLodgerID(c.Request.Context(), lodgerID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, projects)
	}
}

func getProjectByID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		projectID := c.Param("project_id")

		project, err := svc.GetProjectByID(c.Request.Context(), projectID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, project)
	}
}

func addProject(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		creatorID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var project models.Project

		err := c.BindJSON(&project)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		project.CreatorID = creatorID

		err = project.Validate()
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = svc.AddProject(c.Request.Context(), project)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func updateProject(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		ownerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var project models.Project

		err := c.BindJSON(&project)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		projectID := c.Param("project_id")
		if projectID == "" {
			c.Status(http.StatusBadRequest)

			return
		}

		project.ID = projectID

		err = svc.UpdateProject(c.Request.Context(), ownerID, project)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func deleteProject(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		projectID := c.Param("project_id")

		err := svc.DeleteProject(c.Request.Context(), projectID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func affectOneEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		ownerTD, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		projectID := c.Param("project_id")

		var project models.Project

		err := c.BindJSON(&project)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = svc.AffectOneEquipmentToProject(c.Request.Context(), ownerTD, projectID, project)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func affectOneBidzEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		ownerTD, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		projectID := c.Param("project_id")

		var project models.Project

		err := c.BindJSON(&project)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = svc.AffectOneBidzEquipmentToProject(c.Request.Context(), ownerTD, projectID, project)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func affectOneMember(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		ownerTD, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		projectID := c.Param("project_id")

		var project models.Project

		err := c.BindJSON(&project)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = svc.AffectOneMemberToProject(c.Request.Context(), ownerTD, projectID, project)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}
