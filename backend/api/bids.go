package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/vima-inc/derental/api/middleware"
	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

func sendBitsRequest(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var bidsRequest models.BidsRequest

		err := c.BindJ<PERSON>(&bidsRequest)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		bidsRequest.Status = models.RequestPending

		err = svc.SendBidzRequest(c.Request.Context(), lodgerID, bidsRequest)
		if err != nil {
			c.<PERSON><PERSON>(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func getAllBidsRequest(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var limit int

		l, err := strconv.ParseInt(c.Query("limit"), 10, 32)
		if err == nil {
			limit = int(l)
		}

		lastID := c.Query("last_id")
		status := models.BidsRequestStatus(c.Query("status"))
		res := status.IsValid()

		if !res {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid status"})

			return
		}

		resp, err := svc.GetAllBidsRequest(c.Request.Context(), limit, lastID, status, "")
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func getAllBidsRequestByEquipperID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)
		}

		var limit int

		l, err := strconv.ParseInt(c.Query("limit"), 10, 32)
		if err == nil {
			limit = int(l)
		}

		lastID := c.Query("last_id")
		status := models.BidsRequestStatus(c.Query("status"))

		if equipperID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "equipper_id is required"})

			return
		}

		resp, err := svc.GetAllBidsRequest(c.Request.Context(), limit, lastID, status, equipperID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func getAllBidsRequestByLodgerID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var limit int

		l, err := strconv.ParseInt(c.Query("limit"), 10, 32)
		if err == nil {
			limit = int(l)
		}

		lastID := c.Query("last_id")
		status := models.BidsRequestStatus(c.Query("status"))

		resp, err := svc.GetAllLodgerBidsRequest(c.Request.Context(), limit, lastID, status, lodgerID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func getBidsRequestID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.Param("request_id")

		resp, err := svc.GetBidsRequestID(c.Request.Context(), requestID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func addOfferRequest(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var bidzOffer models.BidzOffer

		err := c.BindJSON(&bidzOffer)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		bidzOffer.Status = models.OfferPending

		err = svc.AddOfferRequest(c.Request.Context(), equipperID, bidzOffer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func getAllBidsOffer(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var limit int

		l, err := strconv.ParseInt(c.Query("limit"), 10, 32)
		if err == nil {
			limit = int(l)
		}

		status := models.BidsOfferStatus(c.Query("status"))

		res := status.IsValid()
		if !res {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid status"})

			return
		}

		lastID := c.Query("last_id")

		resp, err := svc.GetAllBidsOffer(c.Request.Context(), limit, lastID, status, nil)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func getAllBidsOfferByEquipperID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)
		}

		var limit int

		l, err := strconv.ParseInt(c.Query("limit"), 10, 32)
		if err == nil {
			limit = int(l)
		}

		status := models.BidsOfferStatus(c.Query("status"))

		res := status.IsValid()
		if !res {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid status"})

			return
		}

		lastID := c.Query("last_id")

		if equipperID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "equipper_id is required"})

			return
		}

		where := map[string]interface{}{
			"equipper_id": equipperID,
		}

		resp, err := svc.GetAllBidsOffer(c.Request.Context(), limit, lastID, status, where)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func getAllBidsOfferByLodgerID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)
		}

		var limit int

		l, err := strconv.ParseInt(c.Query("limit"), 10, 32)
		if err == nil {
			limit = int(l)
		}

		status := models.BidsOfferStatus(c.Query("status"))

		res := status.IsValid()
		if !res {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid status"})

			return
		}

		lastID := c.Query("last_id")

		resp, err := svc.GetAllBidsLodgerOffer(c.Request.Context(), limit, lastID, status, lodgerID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func getBidsOfferID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		offerID := c.Param("offer_id")

		resp, err := svc.GetBidsOfferID(c.Request.Context(), offerID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func acceptBidsOffer(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		bidsOfferID := c.Param("bids_offer_id")
		if bidsOfferID == "" {
			c.Status(http.StatusBadRequest)

			return
		}

		checkoutURL, err := svc.AcceptOffer(c.Request.Context(), lodgerID, bidsOfferID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, gin.H{"checkout_url": checkoutURL})
	}
}

func rejectBidsOffer(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		lodgerID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		bidsOfferID := c.Param("bids_offer_id")
		if bidsOfferID == "" {
			c.Status(http.StatusBadRequest)

			return
		}

		err := svc.RejectOffer(c.Request.Context(), lodgerID, bidsOfferID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func acceptBidsRequest(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		bidsRequestID := c.Param("bids_request_id")
		if bidsRequestID == "" {
			c.Status(http.StatusBadRequest)

			return
		}

		err := svc.AcceptRequest(c.Request.Context(), equipperID, bidsRequestID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func rejectBidsRequest(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		bidsRequestID := c.Param("bids_request_id")
		if bidsRequestID == "" {
			c.Status(http.StatusBadRequest)

			return
		}

		var bidsRequest models.BidsRequest

		err := c.BindJSON(&bidsRequest)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})

			return
		}

		err = svc.RejectRequest(c.Request.Context(), equipperID, bidsRequestID, bidsRequest)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func cancelBidzOffer(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		equipperID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		bidzOfferID := c.Param("bids_offer_id")
		if bidzOfferID == "" {
			c.Status(http.StatusBadRequest)

			return
		}

		var bidzOffer models.BidzOffer

		err := c.BindJSON(&bidzOffer)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = svc.CancelBidzOffer(c.Request.Context(), equipperID, bidzOfferID, bidzOffer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}
