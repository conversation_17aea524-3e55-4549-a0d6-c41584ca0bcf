package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/vima-inc/derental/api/middleware"
	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

func getMembersLodger(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		userID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		teamType := c.Param("team_type")
		if teamType != "lodger" && teamType != "equipper" {
			c.Status(http.StatusBadRequest)
		}

		resp, err := svc.GetMembersLodger(c.Request.Context(), userID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.<PERSON>rror()})
			_ = c.Error(err)

			return
		}

		c.<PERSON>(http.StatusOK, resp)
	}
}

func getMembersEquipper(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		userID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		teamType := c.Param("team_type")
		if teamType != "lodger" && teamType != "equipper" {
			c.Status(http.StatusBadRequest)
		}

		resp, err := svc.GetMembersEquipper(c.Request.Context(), userID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func addMember(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		userID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var member models.Member
		member.MembershipStatus = models.MemberPending

		err := c.BindJSON(&member)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = member.Validate()
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})

			return
		}

		_, err = svc.GetLodgerByID(c.Request.Context(), userID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		} else {
			err = svc.AddMemberLodger(c.Request.Context(), userID, member)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				_ = c.Error(err)

				return
			}
		}

		c.Status(http.StatusOK)
	}
}

func updateMember(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		userID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var member models.Member

		err := c.BindJSON(&member)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		memberID := c.Param("member_id")

		err = member.Validate()
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		_, err = svc.GetLodgerByID(c.Request.Context(), userID)
		if err != nil {
			err = svc.UpdateMemberEquipper(c.Request.Context(), userID, memberID, member)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				_ = c.Error(err)

				return
			}
		} else {
			err = svc.UpdateMemberLodger(c.Request.Context(), userID, memberID, member)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				_ = c.Error(err)

				return
			}
		}

		c.Status(http.StatusOK)
	}
}

func deleteMember(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		userID, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var request struct {
			MemberIDs []string `json:"member_ids"`
			NewMember string   `json:"new_member_id"`
		}

		err := c.BindJSON(&request)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		_, err = svc.GetLodgerByID(c.Request.Context(), userID)
		if err != nil {
			err = svc.DeleteMemberEquipper(c.Request.Context(), userID, request.MemberIDs...)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				_ = c.Error(err)

				return
			}
		} else {
			err = svc.DeleteMemberLodger(c.Request.Context(), userID, request.NewMember, request.MemberIDs...)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				_ = c.Error(err)

				return
			}
		}

		c.Status(http.StatusOK)
	}
}

func getMemberByID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		_, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		memberID := c.Param("member_id")
		teamID := c.Param("member_of")

		resp, err := svc.GetMemberByID(c.Request.Context(), memberID, teamID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func getSelectionList(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		_, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		memberID := c.Param("member_id")
		teamID := c.Param("member_of")

		resp, err := svc.GetSelectionList(c.Request.Context(), memberID, teamID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func invitationExists(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		_, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		email := c.Param("email")
		teamID := c.Param("member_of")

		resp := svc.CheckIfEmailExists(c.Request.Context(), email, teamID)

		c.JSON(http.StatusOK, gin.H{"canUseEmail": resp})
	}
}
