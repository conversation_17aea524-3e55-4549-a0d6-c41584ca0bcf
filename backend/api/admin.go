package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/vima-inc/derental/api/middleware"
	"github.com/vima-inc/derental/service"
)

// adminSignin handles admin authentication with domain validation
func adminSignin(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var body struct {
			Email    string `json:"email"`
			Password string `json:"password"`
		}

		err := c.BindJSON(&body)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		// Validate domain before attempting authentication
		if !isValidAdminDomain(body.Email) {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access restricted to @derentalequipment.com domain"})
			return
		}

		resp, err := svc.AdminSignIn(c.Request.Context(), body.Email, body.Password)
		if err != nil {
			c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.<PERSON>r(err)
			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

// getMasterInventory returns all master inventory items for admin dashboard
func getMasterInventory(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		masterInventory, err := svc.GetAllToolerBidzEquipment(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":  masterInventory,
			"total": len(masterInventory),
		})
	}
}

// getAdminDashboardStats returns dashboard statistics for admin
func getAdminDashboardStats(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		stats, err := svc.GetAdminDashboardStats(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, stats)
	}
}

// isValidAdminDomain checks if email has the required domain
func isValidAdminDomain(email string) bool {
	return len(email) > 22 && email[len(email)-22:] == "@derentalequipment.com"
}

// getARALevel1Categories returns all ARA Level 1 categories for admin filters
func getARALevel1Categories(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		categories, err := svc.GetAllARALevel1Categories(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":  categories,
			"total": len(categories),
		})
	}
}

// getARALevel2Types returns all ARA Level 2 types for admin filters
func getARALevel2Types(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		types, err := svc.GetAllARALevel2Types(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":  types,
			"total": len(types),
		})
	}
}
