package middleware

import (
	"context"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

type uidContextKey string
type emailContextKey string

const (
	bearer = "Bearer "

	// UIDKey is the key for the uid of the current user in the context.
	UIDKey uidContextKey = "uid"
	// EmailKey is the key for the email of the current user in the context.
	EmailKey emailContextKey = "email"
)

type auth interface {
	ValidateToken(ctx context.Context, token string) (string, error)
}

type adminAuth interface {
	ValidateTokenWithEmail(ctx context.Context, token string) (string, string, error)
}

// Authorization verifies the token provided is Authorization header.
func Authorization(auth auth) gin.HandlerFunc {
	return func(c *gin.Context) {
		authorization := c.Request.Header.Get("Authorization")
		if authorization == "" || !strings.HasPrefix(authorization, bearer) {
			c.AbortWithStatus(http.StatusUnauthorized)

			return
		}

		token := strings.TrimPrefix(authorization, bearer)

		uid, err := auth.ValidateToken(c.Request.Context(), token)
		if err != nil {
			c.AbortWithStatus(http.StatusUnauthorized)

			return
		}

		ctx := context.WithValue(c.Request.Context(), UIDKey, uid)
		c.Request = c.Request.WithContext(ctx)

		c.Next()
	}
}

// AdminAuthorization verifies the token and checks if the user has @derentalequipment.com domain.
func AdminAuthorization(auth adminAuth) gin.HandlerFunc {
	return func(c *gin.Context) {
		authorization := c.Request.Header.Get("Authorization")
		if authorization == "" || !strings.HasPrefix(authorization, bearer) {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		token := strings.TrimPrefix(authorization, bearer)

		uid, email, err := auth.ValidateTokenWithEmail(c.Request.Context(), token)
		if err != nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		// Check if email has @derentalequipment.com domain
		if !strings.HasSuffix(email, "@derentalequipment.com") {
			c.AbortWithStatus(http.StatusForbidden)
			return
		}

		ctx := context.WithValue(c.Request.Context(), UIDKey, uid)
		ctx = context.WithValue(ctx, EmailKey, email)
		c.Request = c.Request.WithContext(ctx)

		c.Next()
	}
}
