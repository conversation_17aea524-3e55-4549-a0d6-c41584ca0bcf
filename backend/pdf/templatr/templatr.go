package templatr

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

const baseURL = "https://api.templatr.app"

// Client is the client for the templatr service.
type Client struct {
	httpclient *http.Client
	apiKey     string
}

// New returns a new instance of Client.
func New(apiKey string) *Client {
	return &Client{
		httpclient: &http.Client{
			Timeout: 60 * time.Second,
		},
		apiKey: apiKey,
	}
}

// GeneratePDF generates a PDF from the given template and value.
func (c *Client) GeneratePDF(ctx context.Context, templateID string, value any) ([]byte, error) {
	j, err := json.Marshal(value)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal value: %w", err)
	}

	u := fmt.Sprintf("%s/pdf/%s?api_key=%s&format=pdf", baseURL, templateID, c.apiKey)

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, u, bytes.NewReader(j))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpclient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var buf bytes.Buffer
	_, err = buf.ReadFrom(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	return buf.Bytes(), nil
}
