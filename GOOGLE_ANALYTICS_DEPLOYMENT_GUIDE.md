# Google Analytics Deployment Configuration Guide

## GitHub Secrets Configuration

### Required GitHub Secrets

You need to add the following secret to your GitHub repository:

#### 1. GOOGLE_ANALYTICS_ID
- **Value**: `G-8JNQD6EW3L`
- **Description**: Your Google Analytics 4 tracking ID
- **Used in**: All environment workflows (dev, demo, prod)

### How to Add GitHub Secrets

1. Go to your GitHub repository
2. Click **Settings** tab
3. In the left sidebar, click **Secrets and variables** > **Actions**
4. Click **New repository secret**
5. Add the secret:
   - **Name**: `GOOGLE_ANALYTICS_ID`
   - **Secret**: `G-8JNQD6EW3L`
6. Click **Add secret**

## Environment Variable Configuration

### Local Development (.env)
```bash
# Google Analytics Configuration
VITE_GOOGLE_ANALYTICS_ID=G-8JNQD6EW3L

# Environment (affects GA loading)
VITE_ENV=development  # GA will NOT load
# VITE_ENV=production  # GA will load
```

### Production Environment
The following environment variables are automatically set by GitHub Actions:

```bash
VITE_ENV=production
VITE_GOOGLE_ANALYTICS_ID=G-8JNQD6EW3L
```

## Deployment Workflows Updated

### Files Modified
The following GitHub Actions workflow files have been updated to include the Google Analytics environment variable:

1. **`.github/workflows/prod_web.yml`**
2. **`.github/workflows/dev_web.yml`**
3. **`.github/workflows/demo_web.yml`**

### Changes Made
Added this line to the environment variable section of each workflow:
```yaml
VITE_GOOGLE_ANALYTICS_ID= ${{ secrets.GOOGLE_ANALYTICS_ID }}
```

## Deployment Steps

### Step 1: Verify GitHub Secret
```bash
# Check if secret is properly set (run in GitHub Actions or locally)
echo "GA ID configured: ${GOOGLE_ANALYTICS_ID:+YES}"
```

### Step 2: Deploy to Development
```bash
# Development deployment (GA should NOT load)
git push origin develop
# or trigger your dev deployment workflow
```

**Expected Behavior:**
- Google Analytics scripts should NOT load
- Verification panel should show development warnings
- PostHog should continue working normally

### Step 3: Deploy to Production
```bash
# Production deployment (GA should load)
git push origin main
# or trigger your production deployment workflow
```

**Expected Behavior:**
- Google Analytics scripts should load
- Tracking should be active
- Both PostHog and GA should work together

### Step 4: Verify Deployment
1. Check deployment logs for environment variables
2. Visit the deployed application
3. Use verification panel or browser tools to confirm GA loading
4. Monitor Google Analytics Real-Time reports

## Environment-Specific Behavior

### Development Environment
- **VITE_ENV**: `development` (or not `production`)
- **GA Behavior**: Scripts do NOT load
- **Verification**: Use verification panel to confirm
- **Purpose**: Prevent development traffic from polluting analytics

### Demo Environment  
- **VITE_ENV**: `production`
- **GA Behavior**: Scripts load and track
- **Purpose**: Test GA integration before production

### Production Environment
- **VITE_ENV**: `production`
- **GA Behavior**: Scripts load and track
- **Purpose**: Track real user traffic for marketing analysis

## Rollback Procedures

### Emergency Rollback (Disable GA)
If you need to quickly disable Google Analytics:

#### Option 1: Remove GitHub Secret
1. Go to GitHub repository settings
2. Remove `GOOGLE_ANALYTICS_ID` secret
3. Redeploy application
4. GA will stop loading immediately

#### Option 2: Environment Variable Override
Add this to your deployment workflow temporarily:
```yaml
VITE_GOOGLE_ANALYTICS_ID=""  # Empty string disables GA
```

#### Option 3: Code-Level Disable
Temporarily modify the GoogleAnalyticsProvider:
```javascript
// In src/shared/components/GoogleAnalyticsProvider.jsx
// Change this line:
const isProduction = import.meta.env.VITE_ENV === 'production';
// To this:
const isProduction = false; // Temporarily disable GA
```

### Gradual Rollback
If you want to disable GA for specific environments only:

1. **Disable for Development**: Already disabled by default
2. **Disable for Demo**: Remove GA ID from demo workflow
3. **Disable for Production**: Remove GA ID from prod workflow

## Monitoring and Verification

### Post-Deployment Checklist

#### Immediate Verification (within 5 minutes)
- [ ] Application loads without errors
- [ ] Verification panel shows correct environment
- [ ] Browser console shows no GA-related errors
- [ ] Network tab shows appropriate GA requests (prod only)

#### Short-term Verification (within 1 hour)
- [ ] Google Analytics Real-Time reports show traffic
- [ ] Page view events are being tracked
- [ ] PostHog continues to work normally
- [ ] No performance degradation observed

#### Long-term Monitoring (within 24 hours)
- [ ] GA dashboard shows consistent data flow
- [ ] Marketing campaign attribution working
- [ ] No user-reported issues
- [ ] Server performance remains stable

### Monitoring Commands

#### Check Environment Variables in Production
```bash
# SSH into production server or check deployment logs
echo "Environment: $VITE_ENV"
echo "GA ID: $VITE_GOOGLE_ANALYTICS_ID"
```

#### Verify GA Loading in Browser
```javascript
// Run in browser console on production
console.log('GA loaded:', !!window.gtag);
console.log('Environment:', import.meta.env.VITE_ENV);
console.log('GA ID:', import.meta.env.VITE_GOOGLE_ANALYTICS_ID);
```

## Troubleshooting Deployment Issues

### Issue: GA Not Loading in Production
**Possible Causes:**
1. GitHub secret not set correctly
2. Environment variable not passed to build
3. Deployment workflow not updated
4. Browser caching old version

**Solutions:**
1. Verify GitHub secret exists and has correct value
2. Check deployment logs for environment variables
3. Clear browser cache and hard refresh
4. Check Network tab for GA script requests

### Issue: GA Loading in Development
**Possible Causes:**
1. Local .env has `VITE_ENV=production`
2. Environment detection logic error

**Solutions:**
1. Check local .env file
2. Verify environment detection in GoogleAnalyticsProvider
3. Clear browser cache

### Issue: Deployment Fails
**Possible Causes:**
1. Syntax error in workflow files
2. Missing GitHub secret
3. Build process errors

**Solutions:**
1. Check GitHub Actions logs
2. Verify workflow YAML syntax
3. Test build locally with same environment variables

## Security Considerations

### Environment Variable Security
- ✅ **VITE_GOOGLE_ANALYTICS_ID**: Safe to expose (public tracking ID)
- ✅ **VITE_ENV**: Safe to expose (environment indicator)

### Privacy Compliance
The GA implementation includes privacy-friendly settings:
- `anonymize_ip: true` - IP anonymization
- `cookie_flags: 'SameSite=None;Secure'` - Secure cookie handling
- Production-only loading - No development tracking

### Data Protection
- Development traffic is not tracked
- Only production user interactions are analyzed
- Complies with GDPR requirements for analytics

## Next Steps After Deployment

1. **Configure GA4 Goals**: Set up conversion tracking for marketing campaigns
2. **Link to Google Ads**: Connect for campaign attribution
3. **Set Up Audiences**: Create remarketing audiences for LinkedIn campaigns
4. **Configure Enhanced Ecommerce**: Track equipment rental conversions
5. **Set Up Custom Events**: Track specific user interactions
6. **Create Dashboards**: Build reports for marketing team
7. **Train Team**: Educate marketing team on new analytics capabilities

## Support and Maintenance

### Regular Maintenance Tasks
- Monitor GA data quality monthly
- Review and update tracking configuration quarterly
- Test tracking after major application updates
- Verify compliance with privacy regulations

### Contact Information
- **Technical Issues**: Development team
- **Analytics Configuration**: Marketing team
- **Privacy Compliance**: Legal team

This deployment guide ensures a smooth, secure, and monitored rollout of Google Analytics tracking for your marketing campaigns.
