const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
// Make sure you have your service account key file
const serviceAccount = require('./path-to-your-service-account-key.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

async function createAdminUser(email, password) {
  try {
    // Validate domain
    if (!email.endsWith('@derentalequipment.com')) {
      throw new Error('Admin users must have @derentalequipment.com email domain');
    }

    // Create user
    const userRecord = await admin.auth().createUser({
      email: email,
      password: password,
      emailVerified: true, // Set to true for admin users
    });

    console.log('Successfully created admin user:', userRecord.uid);
    console.log('Email:', userRecord.email);
    
    return userRecord;
  } catch (error) {
    console.error('Error creating admin user:', error);
    throw error;
  }
}

// Usage
const adminEmail = process.argv[2];
const adminPassword = process.argv[3];

if (!adminEmail || !adminPassword) {
  console.log('Usage: node create-admin-user.js <email> <password>');
  console.log('Example: node create-admin-user.js <EMAIL> mypassword123');
  process.exit(1);
}

createAdminUser(adminEmail, adminPassword)
  .then(() => {
    console.log('Admin user created successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Failed to create admin user:', error.message);
    process.exit(1);
  });
