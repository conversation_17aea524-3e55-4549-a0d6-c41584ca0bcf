# CI/CD Workflow Documentation

## Overview

This repository uses GitHub Actions to automate the build, test, and deployment processes. The workflow ensures code quality through automated testing and provides a seamless deployment pipeline across multiple environments.

## Workflow Diagram

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              CI/CD Pipeline Flow                             │
└─────────────────────────────────────────────────────────────────────────────┘

                                  Developer Push
                                        │
                                        ▼
                            ┌───────────────────────┐
                            │   Build & Test (CI)   │
                            │     (build.yml)       │
                            │  ・Frontend tests     │
                            │  ・Backend tests      │
                            │  ・Linting            │
                            └───────────────────────┘
                                        │
                                        ▼
                                  Tests Pass?
                                   /        \
                                 No          Yes
                                 │            │
                                 ▼            ▼
                            Build Fails   PR Ready
                                            │
                                            ▼
                            ┌───────────────────────┐
                            │    Merge PR to        │
                            │    main/master        │
                            └───────────────────────┘
                                        │
                                        ▼
                            ┌───────────────────────┐
                            │  Auto Deploy to DEV   │
                            │  (deploy-dev.yml)     │
                            │  ・Firebase Hosting   │
                            │  ・Google Cloud Run   │
                            └───────────────────────┘
                                        │
                                        ▼
                                  Create Tag
                                   (v1.0.0)
                                        │
                                        ▼
                            ┌───────────────────────┐
                            │ Auto Deploy PREPROD   │
                            │    (deploy.yml)       │
                            │  ・Firebase Hosting   │
                            │  ・Google Cloud Run   │
                            └───────────────────────┘
                                        │
                                        ▼
                            ┌───────────────────────┐
                            │   Manual Approval     │
                            │      Required         │
                            └───────────────────────┘
                                        │
                                        ▼
                            ┌───────────────────────┐
                            │  Deploy PRODUCTION    │
                            │    (deploy.yml)       │
                            │  ・Firebase Hosting   │
                            │  ・Google Cloud Run   │
                            └───────────────────────┘
```

## Workflow Architecture

### 1. Build Pipeline (`build.yml`)

**Trigger:** Every push to any branch

**Purpose:** Ensures code quality and catches issues early

**Process:**
- Detects changes in frontend (`src/`) or backend (`backend/`) directories
- Runs conditional builds based on what was modified
- Frontend: Builds React app, runs tests and linting
- Backend: Builds Go modules, runs tests with race detection, and golangci-lint

### 2. Development Deployment (`deploy-dev.yml`)

**Trigger:** When a pull request is merged into `main` or `master` branch

**Purpose:** Automatically deploy to development/integration environment

**Process:**
- Detects which services were modified (frontend/backend)
- Backend: Builds Docker images and deploys to Google Cloud Run
- Frontend: Builds and deploys to Firebase Hosting
- Deploys multiple backend services:
  - `tooler` (main API)
  - `tooler-equipment-uploader`
  - `derental-crone-job`

### 3. Production Deployment (`deploy.yml`)

**Trigger:** When a Git tag is created (format: `v*` or `release/*`)

**Environments:**
- **Pre-production:** Deploys automatically upon tag creation
- **Production:** Requires manual approval through GitHub environment protection

**Process:**
1. Tag creation triggers pre-production deployment
2. Both frontend and backend are deployed to pre-production
3. After pre-production deployment succeeds, production deployment awaits approval
4. Once approved, deploys to production environment

## Environment Strategy

| Environment | Trigger | Approval Required | Purpose |
|------------|---------|------------------|---------|
| Development | PR merge to main/master | No | Integration testing |
| Pre-production | Git tag creation | No | Final testing before production |
| Production | After pre-prod success | Yes | Live environment |

## Technology Stack

- **Frontend:** React application with Yarn package manager
- **Backend:** Go microservices architecture
- **Infrastructure:** 
  - Google Cloud Run (backend services)
  - Firebase Hosting (frontend)
  - Docker for containerization

## File Change Detection

The workflows use path filters to optimize builds:

**Frontend paths monitored:**
- `src/**` - React source code
- `public/**` - Public assets
- `package.json`, `yarn.lock` - Dependencies
- `vite.config.*` - Build configuration
- Root config files (`.eslintrc*`, `.prettierrc*`, etc.)

**Backend paths monitored:**
- `backend/**` - All Go code and related files

## Security

- All sensitive data stored as GitHub Secrets
- Environment-specific secrets for each deployment stage
- Service account authentication for cloud deployments

## Workflow Files

- `.github/workflows/build.yml` - Continuous Integration
- `.github/workflows/deploy-dev.yml` - Development deployment
- `.github/workflows/deploy.yml` - Pre-production and Production deployment

## Creating a Release

1. Ensure all changes are merged to main/master
2. Create and push a tag:
   ```bash
   git tag v1.0.0
   git push origin v1.0.0
   ```
3. Pre-production deployment starts automatically
4. Review pre-production environment
5. Approve production deployment in GitHub Actions UI

## Monitoring

- Build status visible in GitHub Actions tab
- Each workflow run provides detailed logs
- Failed builds block merges (when configured with branch protection)

## Best Practices

1. Always ensure builds pass before merging PRs
2. Test thoroughly in development before creating release tags
3. Use semantic versioning for tags (e.g., v1.2.3)
4. Review pre-production environment before approving production deployment
5. Keep secrets up to date and rotate regularly

## Troubleshooting

**Build Failures:**
- Check GitHub Actions logs for specific error messages
- Ensure all dependencies are properly declared
- Verify that tests pass locally before pushing

**Deployment Issues:**
- Verify that all required secrets are configured
- Check Google Cloud and Firebase quotas
- Ensure service accounts have proper permissions

**Path Filter Issues:**
- Review the `paths-filter` configuration if changes aren't detected
- Remember that filters use glob patterns