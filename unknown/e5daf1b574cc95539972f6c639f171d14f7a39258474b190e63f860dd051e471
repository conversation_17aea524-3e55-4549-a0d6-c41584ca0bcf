import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FieldArray, Formik, Form } from 'formik';
import * as Yup from 'yup';
import { CREATE_MEMBER } from '../../../helpers/Url_prefixes';
import { removeRedundancy } from '../../../helpers/Array_helpers';
import RenderIf from '../../Render_if';
import InviteMemberForm from './Invite_member_form';
import CustomButton from '../../buttons/Custom_button';
import { isEmpty } from 'lodash';

export default function InviteMemberModal({
  show,
  type,
  onInviteClick,
  projectsOptions,
  handleClose,
  CheckIfMemberExists,
  showSuccess,
  showFailure,
  setErrorPrefix
}) {
  const { t } = useTranslation();
  const [successCount, setSuccessCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const validationSchema = Yup.object({
    users: Yup.array().of(
      Yup.object().shape({
        email: Yup.string()
          .required(t('Email_required'))
          .email(t('Please_enter_a_valid_email'))
          .test('unique', t('Email_already_exists'), async (value) => {
            const response = await CheckIfMemberExists(
              sessionStorage.getItem('member_of'),
              value
            );

            if (response.status === 200) {
              return response.data?.canUseEmail
                ? t('Email_already_exists')
                : false;
            }
          }),
        projects: Yup.object().required(t('This_field_is_required')).nullable()
      })
    )
  });

  const initialValues = {
    users: [
      {
        email: '',
        projects: null,
        type: type
      }
    ]
  };

  async function onSubmit(event) {
    setIsLoading(true);
    let tempSuccessCount = 0;
    const users = removeRedundancy(event.users);
    users?.map(async (user) => {
      user.type = type;
      user.projects = [user.projects?.value];
      const response = await onInviteClick(user);
      if (response.status === 200 || response.status === 201) {
        tempSuccessCount++;
        setIsLoading(true);
      } else {
        setErrorPrefix(CREATE_MEMBER);
        showFailure(
          `${t(
            'Add_admin_modal_main_message'
          )} ${tempSuccessCount} ${type.replaceAll('_', ' ')}`,
          response,
          t('Add_admin_modal_secondary_message')
        );
        return;
      }
      setSuccessCount(tempSuccessCount);
      if (tempSuccessCount === users.length) {
        setIsLoading(false);
        showSuccess(t('All_invitations_sent_successfully'));
      }
    });
  }

  return (
    <RenderIf condition={show}>
      <div className="modal">
        <div className="modal-content no-title-margeTop add-memberModal">
          <button
            className="close-button"
            onClick={handleClose}
            disabled={isLoading}
          >
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19.25 5.25L5.75 18.75"
                stroke="#333333"
                strokeWidth="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M19.25 18.75L5.75 5.25"
                stroke="#333333"
                strokeWidth="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
          <div className="row">
            <div className="col-lg-10 mx-auto">
              <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={onSubmit}
              >
                {(formik) => (
                  <Form>
                    <h2 className="t-header-h5 c-fake-black credit-title">
                      {t('Invite_new_member')}
                    </h2>
                    <div className="row">
                      <FieldArray
                        name="users"
                        render={(arrayHelpers) => (
                          <InviteMemberForm
                            arrayHelpers={arrayHelpers}
                            projectsOptions={projectsOptions}
                            formik={formik}
                            t={t}
                          />
                        )}
                      />
                    </div>

                    {!isLoading ? (
                      <div className="text-center fixed-button-modal">
                        <CustomButton
                          className={
                            isEmpty(projectsOptions) || isLoading
                              ? 'round-button bold c-near-grey bg-neutrals-gray '
                              : 'round-button bold yellow '
                          }
                          disabled={isLoading || isEmpty(projectsOptions)}
                          type="submit"
                          isLoading={isLoading}
                          textButton={t('Send_invitation')}
                        />
                      </div>
                    ) : (
                      <div className="text-center fixed-button-modal">
                        <CustomButton
                          type="submit"
                          className={
                            isLoading
                              ? 'round-button bold c-near-grey bg-neutrals-gray'
                              : 'round-button bold yellow '
                          }
                          disabled={isLoading}
                          textButton={`processing ${successCount}/${
                            removeRedundancy(formik.values.users).length
                          }`}
                          isLoading={isLoading}
                        />
                      </div>
                    )}
                  </Form>
                )}
              </Formik>
            </div>
          </div>
        </div>
      </div>
    </RenderIf>
  );
}
