import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckCircle } from '@fortawesome/fontawesome-free-regular';
import { faTimes } from '@fortawesome/fontawesome-free-solid';
import { useTranslation } from 'react-i18next';

export default function SuccessPopUp(props) {
  const { t } = useTranslation();
  if (!props.show) {
    return null;
  }
  return (
    <div className="modal">
      <div className="modal-content">
        <div className="row">
          <div className="col-lg-11 mx-auto text-center">
            <button
              className="close-button"
              onClick={props.closeIcon ? props.closeIcon : props.onClose}
            >
              <FontAwesomeIcon icon={faTimes} color="c-primary-color" />
            </button>
            <div className="text-center">
              <FontAwesomeIcon
                icon={faCheckCircle}
                className="success-signup-icon"
                size="3x"
                color="#ECA869"
              />
            </div>
            <div className="text-center">
              <h3 className="success-signup-title">{t('Success_text')}</h3>
            </div>
            <div className="text-center">
              <p className="success-signup-title">{props.message}</p>
            </div>
            <div className="text-center fixed-button-modal">
              <button
                className="button-signup fwb-700 round-button yellow bold c-primary-color"
                onClick={props.function ? props.function : props.onClose}
              >
                {props.buttonText ? props.buttonText : t('Close_button')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
