//go:generate mockery --name=Payment --output=./mocks --case=underscore
package payment

import (
	"github.com/vima-inc/derental/models"
)

// Payment represents the payment service
type Payment interface {
	CreateCustomer(name, email string, address models.Address) (string, error)
	Authorize(order models.Order, successURL string, cancelURL string, autoCapture bool) (string, error)
	Capture(paymentIntentID string) error
	Cancel(paymentIntentID string) error
	ChargeComplete(payload []byte, signature string) (models.ChargeResponse, error)
	CheckoutComplete(payload []byte, signature string) (models.CheckoutResponse, error)
	GetCoupon(code string) (*models.Coupon, error)
	CreateCoupon(code string, percentOff float64, currency string, duration string) (string, error)
	DeleteCoupon(id string) error
	SignWebhookPayload(payload []byte, signature string) ([]byte, error)
}
