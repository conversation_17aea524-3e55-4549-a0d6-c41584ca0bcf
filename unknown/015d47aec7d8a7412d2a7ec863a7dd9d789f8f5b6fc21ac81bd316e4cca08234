package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

const (
	stripeHeader = "Stripe-Signature"
)

func handleOrderCompleteWebhook(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		body, err := c.GetRawData()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = svc.HandleWebhook(body, c.<PERSON>eader(stripeHeader))
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.<PERSON>SON(http.StatusOK, gin.H{"message": "ok"})
	}
}

func handleOrderCompleteNotification(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var webhookEvent models.WebhookEvent
		err := c.BindJSON(&webhookEvent)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = svc.HandlePubSub(webhookEvent.Payload, webhookEvent.Signature)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "ok"})
	}
}
