import React, { useEffect, useState } from 'react';
import { NavItem, NavLink } from 'reactstrap';
import { getCookies } from '../../helpers/Cookies';

export default function LoginLogoutButton({ t, logout, showmodal }) {
  const token = getCookies('token');
  const [auth, setAuth] = useState(false);

  useEffect(() => {
    setAuth(token);
  }, [token]);

  return (
    <>
      {auth ? (
        <NavItem>
          <NavLink
            className=" nav-link-Login c-fake-black"
            role="button"
            onClick={logout}
          >
            {t('Logout')}
          </NavLink>
        </NavItem>
      ) : (
        <NavItem>
          <NavLink
            className=" nav-link-Login c-fake-black"
            role="button"
            onClick={showmodal}
          >
            {t('Login')}
          </NavLink>
        </NavItem>
      )}
    </>
  );
}
