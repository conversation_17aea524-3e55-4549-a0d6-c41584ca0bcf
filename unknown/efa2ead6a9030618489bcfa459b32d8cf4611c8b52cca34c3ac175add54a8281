import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { formatPhoneNumber } from 'react-phone-number-input';
import Location from '../../../style/assets/img/company_spotlight/location.svg';
import Phone from '../../../style/assets/img/company_spotlight/phone.svg';
import Email from '../../../style/assets/img/company_spotlight/mail.svg';
import Clock from '../../../style/assets/img/company_spotlight/SuitcaseSimple.svg';
import CustomImage from '../images/Custom_image';
import RenderIf from '../Render_if';
import OpenHoursModal from '../modals/Open_hours_modal';
import { days } from '../../helpers/Data_helper';
import { RatingCard } from './Rating_card';
import { Link } from 'react-router-dom';
import { cutString } from '../../helpers/String_helps';
import CustomTooltip from '../tooltips/Tooltip';

export default function SpotlightHead({
  equipper,
  t,
  ratingData,
  isRandomBooking
}) {
  const [show, setShow] = useState(false);
  const handleShowModal = () => {
    setShow(!show);
  };

  return (
    <div className="company-spotlight--head">
      {!isRandomBooking ? (
        <div className="container">
          <div className="row top-row align-items-center">
            <div className="col-lg-4 bg-mobile border-mobile-top border-r">
              <div className="d-flex align-items-center">
                <div className="company-spotlight--head_image">
                  <CustomImage
                    imageUrl={equipper.photo_url}
                    alt="logo company"
                    className="image-head"
                    isUser
                  />
                </div>
                <div className="company-spotlight--head_title">
                  <div className="spotlight-title">
                    <h2 className="t-subheading-1 c-fake-black">
                      {equipper.company}
                    </h2>
                  </div>
                  {ratingData && (
                    <div className="rate">
                      <RatingCard t={t} ratingData={ratingData} />
                    </div>
                  )}
                  <RenderIf condition={show}>
                    <OpenHoursModal
                      handleShowModal={handleShowModal}
                      t={t}
                      isForShow
                      workHours={
                        equipper.work_hours ? equipper.work_hours : days
                      }
                    />
                  </RenderIf>
                  <div className="d-flex infos">
                    <p className="t-sub-title-3 c-new-green mb-lg-0 mb-4 confirmation">
                      <strong className="t-caption-small weight-600">
                        {t('Confirmation_under_30min')}
                      </strong>
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-8">
              <div className="row">
                <div className="col-md-6 bg-mobile border-mobile-bottom border-right border-l">
                  <div className="info-details">
                    <div className="d-flex infos email-address">
                      <img src={Email} alt="email" />

                      <CustomTooltip placement="top" text={equipper.email}>
                        <p className="t-body-regular c-blue-grey">
                          {cutString(equipper.email, 35)}
                        </p>
                      </CustomTooltip>
                    </div>
                    <div className="d-flex infos">
                      <img src={Phone} alt="phone" />
                      <p className="t-body-regular c-blue-grey">
                        {equipper.email === import.meta.env.DERENTAL_BIDZ_EMAIL
                          ? '+****************'
                          : formatPhoneNumber(equipper.phone_number)}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="col-md-6">
                  <div className="info-details">
                    <div className="d-flex infos">
                      <img
                        src={Location}
                        alt="location"
                        className="location-icon"
                      />
                      <p className="t-body-regular c-blue-grey">
                        {equipper.address &&
                          `${equipper.address.address},${equipper.address.country},${equipper.address.state} ${equipper.address.city},${equipper.address.zip_code}`}
                      </p>
                    </div>
                    <div
                      className="d-flex infos"
                      onClick={() => handleShowModal()}
                    >
                      <img src={Clock} alt="clock" />
                      <p className="t-body-regular c-blue-grey">
                        <strong className="t-body-regular c-blue-grey opening-hour">
                          {t('Opening_hours_text')}
                        </strong>
                      </p>
                    </div>
                    <div>
                      <RenderIf condition={isRandomBooking}>
                        <div className="mt-lg-3 mt-2 mb-lg-3 center-mobile">
                          <Link to={`/EquipperSpotlight/${equipper.user_name}`}>
                            <button className="c-fake-black round-button yellow hover_black d-flex align-items-center with-arrow">
                              {t('Explore_all_equipments')}
                            </button>
                          </Link>
                        </div>
                      </RenderIf>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="container-spotlight-head">
          <div className="row top-row align-items-center">
            <div className="col-lg-3 bg-mobile border-mobile-top border-r">
              <div className="d-flex align-items-center">
                <div className="company-spotlight--head_image">
                  <CustomImage
                    imageUrl={equipper.photo_url}
                    alt="logo company"
                    className="image-head"
                    isUser
                  />
                </div>
                <div className="company-spotlight--head_title">
                  <div className="spotlight-title">
                    <h2 className="t-subheading-1 c-fake-black">
                      {equipper.company}
                    </h2>
                  </div>
                  {ratingData && (
                    <div className="rate">
                      <RatingCard t={t} ratingData={ratingData} />
                    </div>
                  )}
                  <RenderIf condition={show}>
                    <OpenHoursModal
                      handleShowModal={handleShowModal}
                      t={t}
                      isForShow
                      workHours={
                        equipper.work_hours ? equipper.work_hours : days
                      }
                    />
                  </RenderIf>
                  <div className="d-flex infos">
                    <p className="t-sub-title-3 c-new-green mb-lg-0 mb-4 confirmation">
                      <strong className="t-caption-small weight-600">
                        {t('Confirmation_under_30min')}
                      </strong>
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-3 border-l">
              <div className="d-lg-flex">
                <div className="bg-mobile border-mobile-bottom border-right mb-lg-0 mb-2">
                  <div className="info-details">
                    <div className="d-flex infos email-address">
                      <img src={Email} alt="email" />
                      <CustomTooltip placement="top" text={equipper.email}>
                        <p className="t-body-regular c-blue-grey">
                          {cutString(equipper.email, 28)}
                        </p>
                      </CustomTooltip>
                    </div>
                    <div className="d-flex infos">
                      <img src={Phone} alt="phone" />
                      <p className="t-body-regular c-blue-grey">
                        {equipper.email === import.meta.env.DERENTAL_BIDZ_EMAIL
                          ? '+****************'
                          : formatPhoneNumber(equipper.phone_number)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-3">
              <div className="d-lg-flex">
                <div className="info-details">
                  <div className="d-flex infos">
                    <img
                      src={Location}
                      alt="location"
                      className="location-icon"
                    />
                    <p className="t-body-regular c-blue-grey">
                      {equipper.address &&
                        `${equipper.address.address},${equipper.address.state},${equipper.address.city},${equipper.address.zip_code}`}
                    </p>
                  </div>
                  <div
                    className="d-flex infos"
                    onClick={() => handleShowModal()}
                  >
                    <img src={Clock} alt="clock" />
                    <p className="t-body-regular c-blue-grey">
                      <strong className="t-body-regular c-blue-grey opening-hour">
                        {t('Opening_hours_text')}
                      </strong>
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-3">
              <RenderIf condition={isRandomBooking}>
                <div className="mt-lg-3 mt-4 mb-lg-3 center-mobile">
                  <Link to={`/EquipperSpotlight/${equipper.user_name}`}>
                    <button className="c-fake-black round-button yellow hover_black d-flex align-items-center w-lg-auto w-100 justify-content-center with-arrow-white">
                      {t('Explore_all_equipments')}
                    </button>
                  </Link>
                </div>
              </RenderIf>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

SpotlightHead.defaultProps = {
  photo_url: '',
  ratingData: null,
  equipper: {
    email: '',
    phone_number: '',
    address: {
      address: '',
      city: '',
      state: ''
    },
    work_hours: []
  }
};

SpotlightHead.propTypes = {
  equipper: PropTypes.shape({
    email: PropTypes.string,
    phone_number: PropTypes.string,
    address: PropTypes.shape({
      address: PropTypes.string,
      city: PropTypes.string,
      state: PropTypes.string
    }),
    work_hours: PropTypes.arrayOf(PropTypes.string)
  }),
  photo_url: PropTypes.string,
  ratingData: PropTypes.shape({
    average_rating: PropTypes.number,
    total_rating: PropTypes.number
  }),
  t: PropTypes.func.isRequired
};
