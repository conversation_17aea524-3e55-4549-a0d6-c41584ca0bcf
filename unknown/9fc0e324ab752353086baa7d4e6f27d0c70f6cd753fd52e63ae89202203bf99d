import React, { useContext } from 'react';
import AxiosFactory, {
  METHOD_GET,
  METHOD_POST,
  METHOD_PUT
} from '../helpers/Context_helpers';
import {
  GET_EQUIPPER_INFO,
  UPDATE_EQUIPPER_INFO,
  UPLOAD_PROFILE_PHOTO_EQUIPPER
} from '../helpers/Url_constants';

const EquipperContext = React.createContext();

export function useEquipper() {
  return useContext(EquipperContext);
}

export default function EquipperProvider({ children }) {
  async function GetEquipperPersonalInfo() {
    return await AxiosFactory({
      url: GET_EQUIPPER_INFO,
      method: METHOD_GET
    });
  }

  async function UpdateEquipperPersonalInfo(data) {
    return await AxiosFactory({
      url: UPDATE_EQUIPPER_INFO,
      method: METHOD_PUT,
      data
    });
  }

  async function UploadEquipperPhoto(data) {
    return await AxiosFactory({
      url: UPLOAD_PROFILE_PHOTO_EQUIPPER,
      method: METHOD_POST,
      data
    });
  }

  async function GetAllEquipperInfo() {
    return await AxiosFactory({
      url: '/public/equippers',
      method: METHOD_GET
    });
  }

  async function GetEquippersByCountry(country) {
    return await AxiosFactory({
      url: `/public/equippers/country/${country}`,
      method: METHOD_GET
    });
  }

  const value = {
    GetEquipperPersonalInfo,
    UpdateEquipperPersonalInfo,
    GetAllEquipperInfo,
    GetEquippersByCountry,
    UploadEquipperPhoto
  };

  return (
    <EquipperContext.Provider value={value}>
      {children}
    </EquipperContext.Provider>
  );
}
