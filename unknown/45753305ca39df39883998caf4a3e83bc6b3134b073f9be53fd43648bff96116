import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import CustomImage from '../../../shared/components/images/Custom_image.jsx';
import {
  format,
  itemsToReturnEquipmentDetails,
  priceData
} from '../../../shared/helpers/Data_helper.js';
import CopyIcon from '../../../style/assets/img/Icons/Copy.svg';
import FormatPrice from '../../../shared/helpers/Price_helper.js';
import ArrowLeft from '../../../style/assets/img/Icons/ArrowLeft.svg';
import { useEquipment } from '../../../shared/context/Equipment_context.jsx';
import { firstLetterUpperCase } from '../../../shared/helpers/String_helps.js';
import ToloIsLoading from '../../../shared/components/cards/Tolo_is_loading.jsx';
import RenderIf from '../../../shared/components/Render_if.jsx';
import { StatusBox } from '../../../shared/components/equipment/Equipment_management_table.jsx';
import ConfirmationModal from '../../../shared/components/modals/Confirmation_modal.jsx';
import SuccessPopUp from '../../../shared/components/modals/Success_pop_up.jsx';
import Popup from '../../../shared/components/modals/Popup.jsx';
import { isArray } from 'lodash';
import { Popper, Paper } from '@mui/material';
import Typography from '@mui/material/Typography';

export default function OneEquipmentDetails({ t, detectLanguage }) {
  const {
    GetEquipmentByID,
    UpdateBookingStatusAfterReturnEquipment,
    UpdateEquipmentStatus
  } = useEquipment();

  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const anchorElId = open && 'simple-popper';
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
    setTimeout(() => {
      setAnchorEl(null);
    }, 500);
  };

  const [item, setItem] = useState({});
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const { id } = useParams();

  const [action, setAction] = useState('');
  const [status, setStatus] = useState('');
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [response, setResponse] = useState(null);
  const [show, setShow] = useState(false);

  const onCloseSuccess = async () => {
    setShowSuccessModal(false);
    setShowConfirmationModal(false);
    if (action.includes('update')) {
      window.location.reload();
    }
  };

  const handleShowConfirmation = (action, status) => {
    setShowConfirmationModal(true);
    setAction(action);
    setStatus(status);
  };

  async function updateBookingStatus(id) {
    const res = await UpdateBookingStatusAfterReturnEquipment(id);
    if (res.status === 200) {
      setShowSuccessModal(true);
    } else {
      setResponse(res);
      setShow({
        ...show,
        showError: true
      });
    }
  }

  async function updateEquipmentStatus(id, status) {
    const res = await UpdateEquipmentStatus(id, status);
    if (res.status === 200) {
      item.status = status;
      setShowSuccessModal(true);
    } else {
      setResponse(res);
      setShow({
        ...show,
        showError: true
      });
    }
  }

  const message = {
    update: t('Are_you_sure_you_want_to_update_status'),
    updateStatus: t('Update_equipment_status_message')
  };

  const handleAction = useCallback(
    (id, status) => {
      switch (action) {
        case 'update':
          return updateBookingStatus(id);
        case 'updateStatus':
          return updateEquipmentStatus(id, status);
        default:
          break;
      }
    },
    [action, id]
  );

  function goBack() {
    navigate('/equipperManagementPortal/equipmentManagement');
  }
  const getOneEquipment = async () => {
    setIsLoading(true);
    const { status, data } = await GetEquipmentByID(id);
    if (status === 200) {
      setItem(data);
    }
    setIsLoading(false);
  };

  useEffect(async () => {
    await getOneEquipment();
  }, [id]);

  if (isLoading) {
    return <ToloIsLoading />;
  }
  const filledAttributes = () => {
    return Object.keys(item)?.filter(
      (keyName) =>
        itemsToReturnEquipmentDetails(keyName) &&
        item[keyName] !== undefined &&
        item[keyName] !== '' &&
        item[keyName] !== null &&
        item[keyName] !== 0
    );
  };

  const splitAttributes = (attributes) => {
    const leftAttributes = [];
    const rightAttributes = [];

    const filteredAttributes = attributes.filter((attr) => {
      if (detectLanguage === 'fr' && attr === 'description') {
        return false;
      }
      if (detectLanguage !== 'fr' && attr === 'description_fr') {
        return false;
      }
      return true;
    });

    filteredAttributes.forEach((attr) => {
      if (attr === 'internal_id') {
        rightAttributes.unshift(attr);
      } else if (attr === 'status') {
        rightAttributes.unshift(attr);
      } else if (attr === 'category') {
        rightAttributes.unshift(attr);
      } else if (attr === 'description') {
        rightAttributes.unshift(attr);
      } else if (attr === 'available_from') {
        rightAttributes.unshift(attr);
      } else if (attr === 'sub_category') {
        rightAttributes.unshift(attr);
      } else if (attr === 'preferred_equipment_name') {
        leftAttributes.unshift(attr);
      } else if (attr === 'alias.en') {
        leftAttributes.unshift(attr);
      } else if (leftAttributes.length <= rightAttributes.length) {
        leftAttributes.push(attr);
      } else {
        rightAttributes.push(attr);
      }
    });

    return [leftAttributes, rightAttributes];
  };

  const translateAndFormat = (val) => {
    return t(firstLetterUpperCase(val?.toString())?.replaceAll(' ', '_'));
  };

  const formatAttributeValue = (attrName, value) => {
    return attrName?.includes('available')
      ? value?.slice(0, 10)
      : isArray(value)
      ? value?.map((el) => translateAndFormat(el)).join(', ')
      : format(value);
  };

  const handleCopy = (event, value) => {
    handleClick(event);
    navigator.clipboard.writeText(format(value));
  };

  const attributes = filledAttributes();
  const [leftAttributes, rightAttributes] = splitAttributes(attributes);
  return (
    <>
      <div className="panel one-equipment" id="p3">
        <div className="team-managements white-bg">
          <button
            onClick={() => goBack()}
            className="round-button black  transparent extrabold m-2 d-lg-none d-flex align-items-center"
          >
            <img src={ArrowLeft} alt="arrow icon" className="mr-2" />
            <span>{t('Back_to_equipment')}</span>
          </button>
          <div className="result-box with-border d-lg-flex justify-content-around">
            <div
              className={`col-lg-3 ${
                leftAttributes.length > 0 &&
                ((item.alias && item.alias.length > 0) ||
                  item.alias?.en?.length > 0)
                  ? 'col-lg-3'
                  : 'col-lg-4'
              }`}
            >
              <div className="result-box with-border h-lg-100 relative">
                <div className="align-items-center justify-content-between margin-lr-0">
                  <div className="w-100 result-box__imageContent col-6 col-lg-5 h-100 d-lg-flex flex-column justify-content-center align-items-center">
                    <div className="result-box__image d-flex justify-content-center">
                      <CustomImage
                        imageUrl={
                          item.equipper_equipment_picture &&
                          !item.equipper_equipment_picture.includes(
                            'equipment_library/Empty_state_equipment.png'
                          )
                            ? item.equipper_equipment_picture
                            : item.image_link
                        }
                        className="w-75"
                        alt={t('Cant_load_image')}
                      />
                    </div>
                    <div className="result-box__top-left">
                      <h2 className="t-subheading-1 c-fake-black text-center">
                        {item.preferred_equipment_name
                          ? item.preferred_equipment_name
                          : item.name}
                      </h2>
                    </div>
                  </div>
                  <div className="w-100 result-box__imageContent col-6 col-lg-5 h-100 d-lg-flex flex-column justify-content-center align-items-center">
                    <div className="col-lg-12 p-0">
                      <div className="d-lg-block">
                        <div className="top-content">
                          <div className="catalogue-prices flex w-100 text-center">
                            {priceData(item, t).map((item, key) => (
                              <div
                                className="catalogue-prices--content c-near-grey padding-right-10 col-4"
                                key={key}
                              >
                                <span className="d-block t-caption-small c-neutrals-gray">
                                  {item.placeholder}
                                </span>
                                <p className="c-white t-subheading-3 bold c-fake-black">
                                  {FormatPrice(item.value)}
                                </p>
                                <span className="d-block t-caption-small c-fake-black">
                                  {item.currency?.toUpperCase()}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="text-center mt-4">
                      {item?.status === 'booked' && (
                        <button
                          className="round-button yellow bold d-inline-flex align-items-center justify-content-center w-100"
                          onClick={() =>
                            handleShowConfirmation('update', 'available')
                          }
                        >
                          {t('Make_it_available')}
                        </button>
                      )}
                      {item?.status === 'available' && (
                        <button
                          className="round-button yellow bold d-inline-flex align-items-center justify-content-center w-100"
                          onClick={() =>
                            handleShowConfirmation('updateStatus', 'idle')
                          }
                        >
                          {t('Make_it_idle')}
                        </button>
                      )}
                      {item?.status === 'idle' && (
                        <button
                          className="round-button yellow bold d-inline-flex align-items-center justify-content-center w-100"
                          onClick={() =>
                            handleShowConfirmation('updateStatus', 'available')
                          }
                        >
                          {t('Make_it_available')}
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {leftAttributes.length > 0 &&
              ((item.alias && item.alias.length > 0) ||
                item.alias?.en?.length > 0) && (
                // Conditionally render leftAttributes
                <div className="col-lg-4 result-box with-border">
                  {leftAttributes.map((attr, key) => {
                    if (attr === 'alias') {
                      return null;
                    }
                    return (
                      <div
                        className="box-technical-information__content d-flex col-12"
                        key={key}
                      >
                        <p className="t-body-small c-fake-black bold col-6">
                          {t(firstLetterUpperCase(attr)).replace(':', ' ')} :
                        </p>
                        <strong className="t-body-small c-fake-black">
                          {formatAttributeValue(
                            attr === 'description' || attr === 'description_fr'
                              ? detectLanguage === 'fr'
                                ? 'description_fr'
                                : 'description'
                              : attr,
                            item[
                              attr === 'description' ||
                              attr === 'description_fr'
                                ? detectLanguage === 'fr'
                                  ? 'description_fr'
                                  : 'description'
                                : attr
                            ]
                          )}
                          {(attr === 'description' ||
                            attr === 'description_fr' ||
                            attr === 'internal_id') && (
                            <>
                              <button
                                className="transparent"
                                onClick={(event) =>
                                  handleCopy(event, item[attr])
                                }
                              >
                                <img src={CopyIcon} alt="copy icon" />
                              </button>
                              <Popper
                                id={anchorElId}
                                open={open}
                                anchorEl={anchorEl}
                                placement="right"
                              >
                                <Paper className="result-box">
                                  <Typography className="t-body-small c-fake-black m-2 p-1 ">
                                    {t('Copied')}
                                  </Typography>
                                </Paper>
                              </Popper>
                            </>
                          )}
                        </strong>
                      </div>
                    );
                  })}
                </div>
              )}

            <div
              className={`col-lg-4 result-box with-border ${
                leftAttributes.length > 0 &&
                ((item.alias && item.alias.length > 0) ||
                  item.alias?.en?.length > 0)
                  ? 'col-lg-4'
                  : 'col-lg-6'
              }`}
            >
              {rightAttributes.map((attr, key) => (
                <div
                  className="box-technical-information__content d-flex col-12 d-flex align-items-center"
                  key={key}
                >
                  <p className="t-body-small c-fake-black bold col-6 ">
                    {t(firstLetterUpperCase(attr)).replace(':', ' ')} :
                  </p>

                  <RenderIf condition={attr === 'status'}>
                    <span>
                      <StatusBox item={item} t={t} />
                    </span>
                  </RenderIf>
                  <RenderIf condition={attr !== 'status'}>
                    <strong className="t-body-small c-fake-black ">
                      {formatAttributeValue(attr, item[attr])}
                      {(attr === 'description' ||
                        attr === 'description_fr') && (
                        <span
                          className="ml-1 copy-icon"
                          onClick={(event) => handleCopy(event, item[attr])}
                        >
                          <img src={CopyIcon} alt="file icon" />
                        </span>
                      )}
                    </strong>
                  </RenderIf>
                </div>
              ))}
            </div>
          </div>
          <button
            onClick={() => goBack()}
            className="round-button black  transparent extrabold m-2 d-lg-flex d-none align-items-center"
          >
            <img src={ArrowLeft} alt="arrow icon" className="mr-2" />
            <span>{t('Back_to_equipment')}</span>
          </button>
        </div>
      </div>
      <RenderIf condition={showConfirmationModal}>
        <ConfirmationModal
          show={showConfirmationModal}
          onClose={() => setShowConfirmationModal(false)}
          action={() => handleAction(id, status)}
          buttonText={t('Update_button')}
          message={message[action]}
          t={t}
        />
      </RenderIf>
      <RenderIf condition={showSuccessModal}>
        <SuccessPopUp
          onClose={() => onCloseSuccess()}
          show={showSuccessModal}
        />
      </RenderIf>
      <RenderIf condition={show}>
        <Popup
          t={t}
          onClose={() => setShow(false)}
          show={show.showError}
          response={response}
        />
      </RenderIf>
    </>
  );
}
