import React, { useEffect, useState } from 'react';
import { useEquipper } from '../../../shared/context/Equipper_context';
import PersonalInfo from '../../../shared/components/management_portal/Personal_info';
import EditPersonalInfo from '../../../shared/components/modals/Edit_personal_info';
import Popup from '../../../shared/components/modals/Popup';
import SuccessPopUp from '../../../shared/components/modals/Success_pop_up';
import LoadingModal from '../../../shared/components/modals/Loading_modal';
import { days } from '../../../shared/helpers/Data_helper';
import RenderIf from '../../../shared/components/Render_if';
import ToloIsLoading from '../../../shared/components/cards/Tolo_is_loading';
import axios from 'axios';
import { UPLOAD_PROFILE_PHOTO_EQUIPPER } from '../../../shared/helpers/Url_constants';
import { getCookies } from '../../../shared/helpers/Cookies';

export default function EquipperPersonalInfo({ loadedInfo, isLoading, t }) {
  const { UpdateEquipperPersonalInfo } = useEquipper();
  const [show, setShow] = useState(false);
  const [response, setResponse] = useState({ message: '' });
  const [showPopup, setShowPopup] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showProgress, setShowProgress] = useState(false);
  const [workHours, setWorkHours] = useState(days);

  useEffect(() => {
    if (loadedInfo) {
      if (loadedInfo.work_hours !== null) {
        setWorkHours(
          loadedInfo.work_hours?.map((item, index) => ({
            ...item,
            index
          }))
        );
      } else {
        setWorkHours(days);
      }
    }
  }, [loadedInfo]);

  const update = async (data) => {
    setShowProgress(true);
    let count = 0;
    const res = await UpdateEquipperPersonalInfo(data);
    if (res.status === 200 || res.status === 204) {
      setResponse(res);
      setTimeout(() => {
        const st = setInterval(() => {
          count++;
          if (count > 20 < 40) {
            clearInterval(st);
            setShowProgress(false);
            setShowSuccess(true);
          }
        }, 1000);
      }, 5000);
    } else {
      setResponse(res);
      setShowProgress(false);
      setShowPopup(true);
    }
  };

  const uploadImage = async (selectedFile) => {
    const formatData = new FormData();

    formatData.set('file', selectedFile);
    const header = {
      'Content-Type': 'multipart/form-data'
    };
    header.Authorization = `Bearer ${getCookies('token')}`;

    const res = await axios.post(
      import.meta.env.VITE_REACT_APP_BASE_URL + UPLOAD_PROFILE_PHOTO_EQUIPPER,
      formatData,
      { headers: header }
    );
    if (res.status === 200) {
      if (loadedInfo.photo_url !== '') {
        setResponse({ ...res, message: t('Upload_img_msg') });
      }
      setShowSuccess(true);
    } else {
      setResponse(res);
      setShowPopup(true);
    }
  };

  if (!loadedInfo) {
    return <ToloIsLoading />;
  }
  return (
    <RenderIf condition={loadedInfo}>
      <PersonalInfo
        isLoading={isLoading}
        loadedInfo={loadedInfo}
        t={t}
        showModal={() => setShow(true)}
      />
      <RenderIf condition={loadedInfo && show}>
        <EditPersonalInfo
          onClose={() => setShow(false)}
          loadedInfo={loadedInfo}
          t={t}
          show={show}
          updatePersonalInfo={update}
          uploadImage={uploadImage}
          isEquipper
          workHours={workHours}
          setWorkHours={setWorkHours}
          role="equipper"
        />
      </RenderIf>
      <LoadingModal show={showProgress} />
      <Popup
        t={t}
        show={showPopup}
        onClose={() => {
          setShowPopup(false);
          setShow(false);
          window.location.reload();
        }}
        response={response}
      />
      <SuccessPopUp
        onClose={() => {
          setShowSuccess(false);
          setShow(false);
          window.location.reload();
        }}
        message={response.message}
        show={showSuccess}
      />
    </RenderIf>
  );
}
