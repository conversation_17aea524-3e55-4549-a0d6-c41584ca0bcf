<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Equipment Change Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #363946;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background-color: #EAA676;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            color: #ffffff;
            margin: 0;
            font-size: 24px;
        }
        .content {
            padding: 30px;
        }
        .equipment-info {
            margin-bottom: 25px;
        }
        .field-name {
            font-weight: bold;
            color: #363946;
            min-width: 120px;
            display: inline-block;
        }
        .changes-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: #ffffff;
        }
        .changes-table th {
            background-color: #363946;
            padding: 12px;
            text-align: left;
            border: 1px solid #dee2e6;
            color: #ffffff;
        }
        .changes-table td {
            padding: 12px;
            border: 1px solid #dee2e6;
        }
        .changes-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .timestamp {
            margin-top: 20px;
            color: #363946;
            font-style: italic;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            padding: 20px;
            background-color: #f8f9fa;
            color: #363946;
            font-size: 12px;
            border-top: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Equipment Change Notification</h1>
        </div>
        
        <div class="content">
            <div class="equipment-info">
                <p><span class="field-name">Equipment:</span> {{.equipment_name}}</p>
                <p><span class="field-name">Equipment ID:</span> {{.equipment_id}}</p>
                <p><span class="field-name">Equipper Name:</span> {{.equipper_name}}</p>
            </div>

            <table class="changes-table">
                <thead>
                    <tr>
                        <th>Field</th>
                        <th>Old Value</th>
                        <th>New Value</th>
                    </tr>
                </thead>
                <tbody>
                    {{range .fields}}
                    <tr>
                        <td>{{.field}}</td>
                        <td>{{.old_value}}</td>
                        <td>{{.new_value}}</td>
                    </tr>
                    {{end}}
                </tbody>
            </table>

            <div class="timestamp">
                <span class="field-name">Changed At:</span> {{.changed_at}}
            </div>
        </div>

        <div class="footer">
            <p>This is an automated notification from derental equipment management system.</p>
        </div>
    </div>
</body>
</html>