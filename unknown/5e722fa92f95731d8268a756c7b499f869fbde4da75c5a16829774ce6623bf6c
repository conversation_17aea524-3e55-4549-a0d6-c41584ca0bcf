import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import Stepper from '@mui/material/Stepper';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import React from 'react';
import { Col } from 'react-bootstrap';
import { Label } from 'reactstrap';
import SearchInputAutoCompleteEquipment from '../../../features/search_result/Search_input_auto_complete_equipment';
import DatePicker from '../../../shared/components/date_picker/Date_picker';
import RenderIf from '../../../shared/components/Render_if';
import CustomButton from '../../../shared/components/buttons/Custom_button';
import { isDisabled } from '../../../shared/helpers/Date_helper';
import arrow from '../../../style/assets/img/Icons/ArrowRightWhite.svg';

const steps = [1, 2, 3];

export default function MobileSearch({
  searchResult,
  equipmentName,
  setEquipmentName,
  location,
  setLocation,
  detectLanguage,
  handleStartDateChange,
  handleEndDateChange,
  startDate,
  endDate,
  equipmentID,
  t,
  setEquipmentID
}) {
  const [activeStep, setActiveStep] = React.useState(0);
  const [skipped, setSkipped] = React.useState(new Set());

  const isStepSkipped = (step) => {
    return skipped.has(step);
  };
  const isSearchButtonEnabled =
    equipmentID &&
    location.isSelected &&
    !isDisabled(startDate, endDate) &&
    startDate &&
    endDate;

  const popperText = !equipmentID
    ? t('Select_equipment')
    : !location.isSelected
    ? t('Set_your_location')
    : isDisabled(startDate, endDate)
    ? t('Date_error_msg')
    : t('Search');
  const handleNext = () => {
    let newSkipped = skipped;
    if (isStepSkipped(activeStep)) {
      newSkipped = new Set(newSkipped.values());
      newSkipped.delete(activeStep);
    }

    setActiveStep((prevActiveStep) => prevActiveStep + 1);
    setSkipped(newSkipped);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const theme = createTheme({
    palette: {
      primary: { main: '#ECA869' }
    }
  });

  return (
    <form>
      <ThemeProvider theme={theme}>
        <Stepper activeStep={activeStep}>
          {steps?.map((label, index) => {
            const stepProps = {};
            const labelProps = {};
            if (isStepSkipped(index)) {
              stepProps.completed = false;
            }
            return (
              <Step key={label} {...stepProps}>
                <StepLabel {...labelProps} />
              </Step>
            );
          })}
        </Stepper>
        <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
          <Button
            color="inherit"
            disabled={activeStep === 0}
            onClick={handleBack}
            sx={{ mr: 1 }}
          >
            {t('Back_label')}
          </Button>
        </Box>
      </ThemeProvider>

      <RenderIf condition={activeStep === 0}>
        <Col
          sm={12}
          lg={12}
          className="search-mobile center-mobile relative padding-lr-0"
        >
          <SearchInputAutoCompleteEquipment
            attribute="equipment"
            placeholder={t('Search_placeholder')}
            value={equipmentName.requestedName || ''}
            onChange={setEquipmentName}
            detectLanguage={detectLanguage}
            t={t}
            indexName={import.meta.env.VITE_ALGOLIA_INDEX_NAME_STANDARDS}
            className="search-input-auto-complete width-100"
            setEquipmentID={setEquipmentID}
            isEquipment
          />
          <CustomButton
            textButton={
              <img
                src={arrow}
                className={equipmentID ? 'c-black' : 'c-near-grey'}
                alt="arrow"
              />
            }
            className={
              equipmentID ? 'arrow-mobile' : ' arrow-mobile c-near-grey'
            }
            onClick={() => {
              if (equipmentID) {
                handleNext();
              }
            }}
            textPopper={t('Select_element_from_dropdown')}
            disabled={!equipmentID}
          />
        </Col>
      </RenderIf>
      <RenderIf condition={activeStep === 1}>
        <Col
          sm={12}
          lg={12}
          className="search-mobile center-mobile relative padding-lr-0"
        >
          <SearchInputAutoCompleteEquipment
            attribute="location"
            placeholder={t('Set_your_location')}
            value={location.value}
            indexName={import.meta.env.VITE_ALGOLIA_INDEX_COVERAGE_AREAS_NAME}
            onChange={setLocation}
            t={t}
            className="search-input-auto-complete width-100"
          />
          <CustomButton
            textButton={
              <img
                src={arrow}
                className={location.isSelected ? 'c-black' : 'c-near-grey'}
                alt="arrow"
              />
            }
            className={
              location.isSelected ? 'arrow-mobile' : ' arrow-mobile c-near-grey'
            }
            onClick={() => {
              if (location.isSelected) {
                handleNext();
              }
            }}
            textPopper={t('Select_element_from_dropdown')}
            disabled={!location.isSelected}
          />
        </Col>
      </RenderIf>
      <RenderIf condition={activeStep === 2}>
        <Col
          sm={12}
          className="search-mobile-datepicker center-mobile relative padding-lr-0"
        >
          <DatePicker
            label={t('Rent_on')}
            handleStartDateChange={handleStartDateChange}
            handleEndDateChange={handleEndDateChange}
            startDate={startDate}
            endDate={endDate}
            startDateClassName="searchInput-navbar no-border"
            endDateClassName="searchInput-navbar no-border"
          />
          <Label className="return font-selector fwb label-padding ">
            {t('Return_on')}
          </Label>
          <CustomButton
            textButton={<FontAwesomeIcon icon="search" />}
            className={`button-search c-black ${
              isSearchButtonEnabled ? '' : 'disabled'
            }`}
            onClick={searchResult}
            textPopper={popperText}
            disabled={!isSearchButtonEnabled}
          />
        </Col>
      </RenderIf>
    </form>
  );
}
