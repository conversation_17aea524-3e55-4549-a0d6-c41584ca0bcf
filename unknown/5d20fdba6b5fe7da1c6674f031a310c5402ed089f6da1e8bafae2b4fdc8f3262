.search-mobile {
  input {
    border: 2px solid $yellow;
    border-radius: 18px;
    padding: 30px;
    width: 100%;
    background: $white !important;
    -webkit-appearance: none;
    appearance: none;
    font-size: 16px;
    max-height: 85px;
    font-weight: bold;
  }

  .arrow-mobile {
    position: absolute;
    right: 30px;
    top: calc(50% - 22px);
    width: 44px;
    height: 44px;
    background: $yellow;
    border-radius: 7px;

    img {
      position: relative;
      top: -3px;
    }
  }
}

@media (max-width: 992px) {
  .search-mobile-datepicker {
    .adjustPicker.new-datepicker {
      border-radius: 18px;
      padding: 30px;
      max-height: 85px;
      height: 85px;

      .react-datepicker-wrapper {
        top: auto;
        text-align: left;

        &:first-child {
          .react-datepicker__input-container {
            padding-left: 0;
            width: 82%;
          }
        }
      }

      .end_date {
        .react-datepicker-wrapper {
          .react-datepicker__input-container {
            border-right: 0;
          }
        }
      }
    }

    label {
      top: 15px;
      left: 35px !important;
    }

    label.return {
      left: 47% !important;
      top: 15px !important;
    }
  }
}

.center-mobile {
  @media (max-width: 992px) {
    .react-daterange-picker {
      width: 100%;
      border: 2px solid $yellow;
      border-radius: 20px;
      padding: 5px;
      background: $white;
    }
  }
}

.search-mobile-datepicker {
  width: 100%;

  .react-daterange-picker__wrapper {
    display: flex;
    flex-direction: row;
  }

  .adjustPicker {
    .react-daterange-picker__inputGroup {
      padding-bottom: 0;
      padding-right: 30px;
      font-size: 12px;
      color: $near-grey;
      position: relative;
      top: 7px;

      &:first-child {
        border-right: 2px solid $yellow;
      }
    }
  }

  label {
    position: absolute;
    left: 40px;
    font-size: 12px;
    margin-bottom: 15px;
    display: block;
    font-weight: 700;
    z-index: 1;
  }

  .button-search {
    width: 44px !important;
    height: 44px;
    position: absolute;
    top: calc(50% - 29px);
    right: 12px;
    border-radius: 7px;

    svg {
      width: 15px;
    }
  }

  img {
    @media (max-width: 1200px) {
      max-width: 80%;
      margin: 50px auto 0;
      display: block;
    }
  }
}
