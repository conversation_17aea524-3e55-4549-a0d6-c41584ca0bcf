import axios from 'axios';
import { clearCookies, getCookies } from './Cookies';

export const METHOD_GET = 'GET';
export const METHOD_POST = 'POST';
export const METHOD_PUT = 'PUT';
export const METHOD_DELETE = 'DELETE';

export default async function axiosFactory({
  url,
  method,
  data,
  isFake = false
}) {
  const header = getHeader(url, method, data);
  const urlToUse = isFake ? url : import.meta.env.VITE_REACT_APP_BASE_URL + url;
  let response = {};
  try {
    switch (method) {
      case METHOD_GET:
        response = await axios.get(urlToUse, header);
        break;
      case METHOD_POST:
        response = await axios.post(urlToUse, data, header);
        break;
      case METHOD_PUT:
        response = await axios.put(urlToUse, data, header);
        break;
      case METHOD_DELETE:
        response = await axios.delete(urlToUse, header);
        break;
      default:
        new Error('Method not supported');
    }
  } catch (error) {
    if (error.response.status === 401) {
      window.location.href = '/';
      clearCookies();
    }
    if (error.response) {
      response.error = error.response.data;
      response.status = error.response.status;
    } else if (error.request) {
      response.error = error.request;
    } else {
      response.error = 'Network Error';
      response.status = 307;
    }
  } finally {
    return {
      data: response.data,
      status: response.status,
      error: response.error
    };
  }
}

function getHeader(url, method, data) {
  const header = {
    'Content-Type': 'application/json'
  };
  if (!url.includes('/public')) {
    header.Authorization = `Bearer ${getCookies('token')}`;
  }
  if (method === METHOD_DELETE) {
    return { headers: header, data };
  }
  return { headers: header };
}
