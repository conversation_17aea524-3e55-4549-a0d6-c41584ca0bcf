package models

// Equipper is a struct representing a Equipper.
type Equipper struct {
	ID                       string                   `json:"id" firestore:"id"`
	UserName                 string                   `json:"user_name" firestore:"user_name"`
	Email                    string                   `json:"email" firestore:"email"`
	Company                  string                   `json:"company" firestore:"company"`
	Status                   bool                     `json:"status" firestore:"status"`
	HasInventory             bool                     `json:"has_inventory" firestore:"has_inventory"`
	HasRequests              bool                     `json:"has_requests" firestore:"has_requests"`
	PhoneNumber              string                   `json:"phone_number" firestore:"phone_number"`
	PhotoURL                 string                   `json:"photo_url" firestore:"photo_url"`
	Address                  Address                  `json:"address" firestore:"address"`
	Description              string                   `json:"description" firestore:"description"`
	MemberOf                 string                   `json:"member_of" firestore:"member_of"`
	MemberID                 string                   `json:"member_id" firestore:"member_id"`
	CommunicationPreferences CommunicationPreferences `json:"communication_preferences" firestore:"communication_preferences"`
	Categories               []string                 `json:"categories" firestore:"categories"`
	CoverageArea             []string                 `json:"coverage_area" firestore:"coverage_area"`
	WorkHours                []WorkHours              `json:"work_hours" firestore:"work_hours"`
	Rating                   float64                  `json:"rating" firestore:"rating"`
	Currency                 string                   `json:"currency" firestore:"currency"`
	RecipientList            []string                 `json:"recipient_list" firestore:"recipient_list"`
}

// WorkHours is a struct representing a WorkHours.
type WorkHours struct {
	DayOfWeek string `json:"day_of_week" firestore:"day_of_week"`
	Open      string `json:"open" firestore:"open"`
	Close     string `json:"close" firestore:"close"`
	DayOff    bool   `json:"day_off" firestore:"day_off"`
}
