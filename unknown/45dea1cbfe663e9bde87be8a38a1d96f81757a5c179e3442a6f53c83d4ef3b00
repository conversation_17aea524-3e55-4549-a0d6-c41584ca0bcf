import React, { useContext } from 'react';
import axiosFactory, {
  METHOD_DELETE,
  METHOD_GET,
  METHOD_POST,
  METHOD_PUT
} from '../helpers/Context_helpers';
import {
  ADD_CREDIT_CHECK_FORM,
  GET_MY_CREDIT_CHECK_FORM,
  UPLOAD_MY_CREDIT_CHECK_FORM
} from '../helpers/Url_constants';

const CreditCheckFormContext = React.createContext();

export function useCreditCheckFormContext() {
  return useContext(CreditCheckFormContext);
}
export default function CreditCheckFormProvider({ children }) {
  async function AddCreditCheckForm(data) {
    return await axiosFactory({
      method: METHOD_POST,
      url: ADD_CREDIT_CHECK_FORM,
      data
    });
  }
  async function UploadCreditCheckForm(data, id) {
    return await axiosFactory({
      method: METHOD_POST,
      url: `${UPLOAD_MY_CREDIT_CHECK_FORM}/${id}`,
      data
    });
  }
  async function UploadPDF(data, id, type) {
    return await axiosFactory({
      method: METHOD_POST,
      url: `${UPLOAD_MY_CREDIT_CHECK_FORM}/${id}/${type}`,
      data
    });
  }
  async function GetMyCreditCheckForm() {
    return await axiosFactory({
      method: METHOD_GET,
      url: GET_MY_CREDIT_CHECK_FORM
    });
  }
  async function GetCreditCheckFormAttachment(id, type, bookingId) {
    return await axiosFactory({
      method: METHOD_GET,
      url: `${GET_MY_CREDIT_CHECK_FORM}/attachment/${id}/${type}/${bookingId}`
    });
  }
  async function DeleteCreditCheckForm(id) {
    return await axiosFactory({
      method: METHOD_DELETE,
      url: `${GET_MY_CREDIT_CHECK_FORM}/${id}`
    });
  }
  async function UpdateCreditCheckForm(data, id) {
    return await axiosFactory({
      method: METHOD_PUT,
      url: `${GET_MY_CREDIT_CHECK_FORM}/${id}`,
      data
    });
  }
  const value = {
    AddCreditCheckForm,
    GetMyCreditCheckForm,
    DeleteCreditCheckForm,
    UpdateCreditCheckForm,
    GetCreditCheckFormAttachment,
    UploadPDF,
    UploadCreditCheckForm
  };
  return (
    <CreditCheckFormContext.Provider value={value}>
      {children}
    </CreditCheckFormContext.Provider>
  );
}
