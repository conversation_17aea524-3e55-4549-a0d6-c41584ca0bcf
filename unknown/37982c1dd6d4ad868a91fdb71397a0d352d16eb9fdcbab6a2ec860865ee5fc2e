import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Menu, Configure, RefinementList } from 'react-instantsearch-dom';
import { useSearchContext } from '../../shared/context/Search_context';
import { rating } from '../../shared/helpers/Rating_helper';
import SpotlightHead from '../../shared/components/cards/Spotlight_head';
import BreadCrumb from '../../shared/components/Bread_crumb';
import RecommendationsSection from '../../components/company_spotlight/Recommendations_section';
import CreditCheckFormProvider from '../../shared/context/Credit_check_form_context';
import ToloIsLoading from '../../shared/components/cards/Tolo_is_loading';
import ProjectProvider from '../../shared/context/Project_context';
import BookingModal from '../../shared/components/modals/Booking_modal';
import { CustomInfiteHitsEquipments } from './Equipments_list';
import InstantSearchAlgolia from '../../components/search_result/Instant_search_algolia';
import { isEmpty } from 'lodash';
import RenderIf from '../../shared/components/Render_if';
import { getSessionStorage } from '../../shared/helpers/Session_storage_helper';
import ScrollToTop from '../../shared/components/Scroll_to_top';
import SignInModal from '../../shared/components/modals/Sign_in_modal';
import { getCookies } from '../../shared/helpers/Cookies';
import { useLodger } from '../../shared/context/Lodger_context';

export default function CompanySpotlight({
  t,
  setShowFPModal,
  showFPModal,
  signIn,
  detectLanguage
}) {
  const params = useParams();
  const { getEquipperById } = useSearchContext();
  const descriptionEn = getSessionStorage('descriptionEn');
  const descriptionFr = getSessionStorage('descriptionFr');
  const currentRefinements = JSON.parse(
    sessionStorage.getItem('currentRefinements')
  );
  const [isLoading, setIsLoading] = useState(true);
  const [equipper, setEquipper] = useState(null);
  const [lodger, setLodger] = useState(null);
  const [hitsPerPage, setHitsPerPage] = useState(6);
  const [selectedEquipment, setSelectedEquipment] = useState({});
  const [showSignIn, setShowSignIn] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [ratingData, setRatingData] = useState();
  const [searchState, setSearchState] = useState({});
  const { GetLodgerPersonalInfo } = useLodger();

  const token = getCookies('token');
  const togglePopup = () => {
    setIsOpen(!isOpen);
  };

  useEffect(() => {
    setIsLoading(true);
    setSearchState({
      range: {
        available_from: {
          max: params.start_date
        }
      },
      menu: {
        equipper_id: params.equipperId,
        name: params.name_en,
        ...(!isEmpty(descriptionEn) && { description: `${descriptionEn}` }),
        ...(!isEmpty(descriptionFr) && { description_fr: `${descriptionFr}` })
      }
    });
    setIsLoading(false);

    (async () => {
      const res = await getEquipperById(params.equipperId);
      if (res.status === 200 && res.data) {
        setEquipper(res.data);
        setRatingData(rating(res.data?.user_name));
      }
    })();
  }, []);

  useEffect(() => {
    async function fetchData() {
      const { status, data } = await GetLodgerPersonalInfo();
      if (status === 200) {
        setLodger(data);
      }
    }

    if (token) {
      fetchData();
    }
  }, [token]);

  if (isLoading || !equipper) {
    return <ToloIsLoading />;
  }
  return (
    <ScrollToTop>
      <div className="company-spotlight">
        <BreadCrumb
          t={t}
          items={[
            {
              label: equipper && equipper.company
            }
          ]}
        />
        <RenderIf condition={isOpen && getCookies('token')}>
          <ProjectProvider>
            <CreditCheckFormProvider>
              <BookingModal
                handleClose={togglePopup}
                selectedEquipment={selectedEquipment}
                hoursPicker={equipper?.work_hours}
                data={{
                  ...lodger,
                  description_lodger: lodger?.description,
                  ...selectedEquipment,
                  currency: equipper?.currency || 'usd'
                }}
                isOpen={isOpen}
                t={t}
                detectLanguage={detectLanguage}
              />
            </CreditCheckFormProvider>
          </ProjectProvider>
        </RenderIf>
        <SignInModal
          setShowFPModal={setShowFPModal}
          showFPModal={showFPModal}
          setShow={setShowSignIn}
          show={showSignIn}
          signIn={signIn}
          t={t}
        />
        <RenderIf condition={equipper || !isLoading}>
          <div className="equipper-spotlight-cls pt-2 padding-r-0">
            <SpotlightHead
              equipper={equipper}
              t={t}
              isRandomBooking
              ratingData={ratingData}
            />
            <div className="container mt-4">
              <InstantSearchAlgolia
                searchState={searchState}
                onSearchStateChange={(searchState) => {
                  setSearchState(searchState);
                }}
                indexName={import.meta.env.VITE_ALGOLIA_INDEX_NAME}
              >
                <Configure
                  filters={`available_from <= ${params.start_date} AND is_active:true AND (status:available OR status:booked) AND equipper_id:${params.equipperId}`}
                />
                <Menu className="hidden" attribute="equipper_id" />{' '}
                <Menu className="hidden" attribute="name" />
                {!isEmpty(currentRefinements) &&
                  Object.keys(currentRefinements)?.map((item, index) => {
                    return (
                      <RefinementList
                        key={index}
                        className="hidden"
                        attribute={item}
                        defaultRefinement={currentRefinements[item]}
                      />
                    );
                  })}
                <CustomInfiteHitsEquipments
                  equipper={equipper}
                  t={t}
                  setHitsPerPage={setHitsPerPage}
                  hitsPerPage={hitsPerPage}
                  setSelectedEquipment={setSelectedEquipment}
                  setIsOpen={setIsOpen}
                  setShowSignIn={setShowSignIn}
                  detectLanguage={detectLanguage}
                />
              </InstantSearchAlgolia>
              <div className="row">
                <div className="col-lg-10 mx-auto">
                  <div className="company-spotlight--similarProduct">
                    <h2 className="t-header-h3 bold c-fake-black title-search">
                      {`${t('Discover_other_products_may_interset')}`}
                    </h2>
                    <RecommendationsSection
                      objectID={params.recommandationID}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </RenderIf>
      </div>
    </ScrollToTop>
  );
}
