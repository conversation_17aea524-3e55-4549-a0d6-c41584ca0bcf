import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { splitTeamList } from '../../helpers/Team_helper';

export default function DeleteModal(props) {
  const { t } = useTranslation();
  const members = splitTeamList(props.names, props.type);
  const [isLoading, setIsLoading] = useState(false);

  function handleDelete() {
    const memberIds = [];
    setIsLoading(true);
    members.forEach((member) => {
      memberIds.push(member.id);
    });
    props.onDelete(memberIds);
  }

  if (!props.show) {
    return null;
  }

  return (
    <div className="modal">
      <div className="modal-content no-title-margeTop delete-modal">
        <button className="close-button" onClick={props.handleClose}>
          <svg
            width="25"
            height="24"
            viewBox="0 0 25 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19.25 5.25L5.75 18.75"
              stroke="#333333"
              strokeWidth="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M19.25 18.75L5.75 5.25"
              stroke="#333333"
              strokeWidth="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
        <div className="row">
          <div className="col-lg-10 mx-auto">
            <div className="content-delete">
              <p className="t-header-medium c-grey-titles">
                {t('Are_you_sure_you_want_to_delete')}
              </p>
              <span className="t-header-medium c-grey-titles bold">
                {members?.map((item, index) =>
                  index === members.length - 1
                    ? `${item.email}`
                    : `${item.email}, `
                )}
              </span>
            </div>
            <div className="text-center btn-content fixed-button-modal">
              <button
                className="round-button black bold"
                onClick={props.handleClose}
              >
                {t('I_m_not_sure')}
              </button>
              <button
                className={
                  isLoading
                    ? 'round-button bold c-near-grey disabled'
                    : 'round-button bold yellow '
                }
                onClick={handleDelete}
                disabled={isLoading}
              >
                {t('Delete')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
