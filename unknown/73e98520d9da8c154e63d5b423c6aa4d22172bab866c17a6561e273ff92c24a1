package service

import (
	"context"
	"fmt"
	"io"
	"path/filepath"

	"github.com/vima-inc/derental/models"
)

func (s *Service) AddToolerBidzEquipment(ctx context.Context, toolerBidzEquipmentInventory models.ToolerBidzEquipment) error {
	return s.db.AddToolerBidzEquipment(ctx, toolerBidzEquipmentInventory)
}

func (s *Service) UpdateToolerBidzEquipment(ctx context.Context, toolerBidzEquipmentInventory models.ToolerBidzEquipment) error {
	return s.db.UpdateToolerBidzEquipment(ctx, toolerBidzEquipmentInventory)
}

func (s *Service) DeleteToolerBidzEquipment(ctx context.Context, id string) error {
	return s.db.DeleteToolerBidzEquipment(ctx, id)
}

func (s *Service) GetAllToolerBidzEquipment(ctx context.Context) ([]models.ToolerBidzEquipment, error) {
	return s.db.GetAllToolerBidzEquipment(ctx)
}

func (s *Service) UploadPhotoToEquipmentLibrary(ctx context.Context, id string, fileName string, data io.Reader) error {
	path := fmt.Sprintf("equipment_library/%s%s", id, filepath.Ext(fileName))

	photoURL, err := s.storage.Write(ctx, s.storage.DefaultBucket(), path, data)
	if err != nil {
		return fmt.Errorf("error uploading photo: %w", err)
	}

	toolerBidzEquipmentInventory, err := s.db.GetToolerBidzEquipmentByID(ctx, id)
	if err != nil {
		return fmt.Errorf("error getting Tooler Bidz Equipment Inventory: %w", err)
	}

	toolerBidzEquipmentInventory.ImageLink = photoURL

	err = s.db.UpdateToolerBidzEquipment(ctx, toolerBidzEquipmentInventory)
	if err != nil {
		return fmt.Errorf("error updating tooler bidz equipment inventory: %w", err)
	}

	return nil
}

func (s *Service) GetBidzEquipmentByID(ctx context.Context, id string) (models.ToolerBidzEquipment, error) {
	return s.db.GetToolerBidzEquipmentByID(ctx, id)
}

func (s *Service) DropToolerBidzEquipments(ctx context.Context) error {
	return s.db.DropToolerBidzEquipments(ctx)
}
