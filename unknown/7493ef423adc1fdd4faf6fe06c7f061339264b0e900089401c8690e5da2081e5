import React, { lazy } from 'react';

const Providers = {
  LODGER_PROVIDER: lazy(() => import('./Lodger_context')),
  TEAM_PROVIDER: lazy(() => import('./Team_context')),
  PROJECT_PROVIDER: lazy(() => import('./Project_context')),
  EQUIPMENT_PROVIDER: lazy(() => import('./Equipment_context')),
  REQUEST_PROVIDER: lazy(() => import('./Requests_context')),
  BIDZ_PROVIDER: lazy(() => import('./Bidz_context')),
  CREDIT_CHECKFORM_PROVIDER: lazy(() => import('./Credit_check_form_context'))
};

export default function SharedProvider({ children }) {
  return (
    <Providers.TEAM_PROVIDER>
      <Providers.LODGER_PROVIDER>
        <Providers.PROJECT_PROVIDER>
          <Providers.EQUIPMENT_PROVIDER>
            <Providers.BIDZ_PROVIDER>
              <Providers.REQUEST_PROVIDER>
                <Providers.CREDIT_CHECKFORM_PROVIDER>
                  {children}
                </Providers.CREDIT_CHECKFORM_PROVIDER>
              </Providers.REQUEST_PROVIDER>
            </Providers.BIDZ_PROVIDER>
          </Providers.EQUIPMENT_PROVIDER>
        </Providers.PROJECT_PROVIDER>
      </Providers.LODGER_PROVIDER>
    </Providers.TEAM_PROVIDER>
  );
}
