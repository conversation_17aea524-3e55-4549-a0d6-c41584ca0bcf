import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus } from '@fortawesome/fontawesome-free-solid';
import { useTranslation } from 'react-i18next';

export default function TableSearchBar(props) {
  const { t } = useTranslation();

  return (
    <div className="equipments-search">
      <div className="row">
        <div className="col-lg-12 text-end">
          <button
            className="round-button yellow bold"
            onClick={props.onClick}
          >
            <FontAwesomeIcon icon={faPlus} />
            {!props.buttonTitle
              ? t('Invite_text')
              : props.buttonTitle}
          </button>
        </div>
      </div>
    </div>
  );
}
