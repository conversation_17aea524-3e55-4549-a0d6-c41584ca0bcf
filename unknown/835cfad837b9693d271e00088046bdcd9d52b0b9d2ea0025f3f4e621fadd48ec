/*
  • Tabulation
  • Request box
  ---------- ---------- ---------- ---------- ----------
*/

/*
  • Tabulation
  ---------- ---------- ---------- ---------- ----------
*/
.tabulation__head.tabulation__head--centered {
  justify-content: center;
}

.tabulation {
  padding-top: 20px;

  @media (min-width: 1400px) {
    max-width: 1300px;
  }

  &__head {
    width: 100%;
    -webkit-overflow-scrolling: touch;
    position: relative;
    text-align: center;
    font-size: 20px;
    white-space: nowrap;
    display: flex;
    justify-content: center;
    align-items: center;
    @media (max-width: 992px) {
      overflow-x: auto;
      justify-content: start;
      overflow-y: hidden;
    }

    div.scrollmenu {
      @media (max-width: 992px) {
        overflow-x: auto;
        overflow-y: hidden;
      }
    }

    .nav-tab {
      img {
        max-width: 24px;
      }

      &.chevron-left {
        transform: rotate(180deg);
        position: relative;
        top: 4px;
      }
    }
  }

  .tab-input {
    display: none;

    &:checked + label:after,
    + label:first-child:after {
      /*  content: "";
height: 4px;
width: 100%;
position: absolute;
display: block;
bottom: 0;
opacity: 1;
left: 0;
transition: 0.25s ease;
background: $yellow;
border-radius: 10px;*/
    }

    // @for $i from 1 through 5 {
    //   &#tab#{$i}:checked {
    //     .line {
    //       left: #{($i - 1) * 25.15%};
    //     }

    //     ~ .panels #p#{$i} {
    //       opacity: 1;
    //       height: 100%;
    //       z-index: 1;
    //       position: relative;
    //       display: block;
    //     }
    //   }
    // }
  }

  .tab-label {
    justify-content: center;
    text-align: center;
    position: relative;
    transition: 0.25s background ease;
    cursor: pointer;
    padding: 0;
    margin: 0 10px;
    display: inline-block;
    width: auto;
    height: auto;
    color: $near-grey;

    &.active {
      font-weight: 700;
      color: $primary-color;

      &::after {
        content: '';
        width: 100%;
        height: 4px;
        background: $yellow;
        border-radius: 10px;
        position: absolute;
        display: block;
        left: 0;
        transition: 0.25s ease;
      }
    }
  }

  .line {
    position: absolute;
    height: 2px;
    width: 24.4%;
    top: 34px;
    left: 0;
    transition: 0.25s ease;
  }

  .panels {
    position: relative;
    min-height: 500px;
    margin-top: 50px;

    .panel {
      padding: 10px;
 
    }
  }

  &__filter-bar {
    border-radius: 10px;

    @media (min-width: 1200px) {
      display: flex;
      justify-content: flex-end;
      background: $white;
      padding: 20px 5px;
    }

    .filter-search {
      position: relative;
      width: 100%;
      margin-bottom: 12px;

      @media (min-width: 1200px) {
        margin-bottom: 0;
        width: max-content;
      }

      .submit-search {
        position: absolute;
        top: 1px;
        left: calc(97% - 38px);
        background: $light-grey;
        border-radius: 10px;
        width: 35px;
        height: 35px;

        @media (min-width: 768px) {
          right: 1px;
          left: auto;
        }

        @media (min-width: 992px) {
          top: 1px;
        }
      }

      .form-control {
        margin-bottom: 0;

        @media (min-width: 1200px) {
          min-width: 270px;
          margin-left: -9px;
        }

        @media (min-width: 1400px) {
          min-width: 350px;
        }

        &.sub-category {
          width: 100%;
          height: 38px;

          @media (max-width: 768px) {
            width: 90%;
          }
        }
      }
    }

    .radio-box {
      margin: 0 8px;
      display: inline-block;

      @media (max-width: 1200px) {
        margin-bottom: 15px;
      }

      [type='radio']:not(:checked) ~ label:before,
      [type='radio']:checked ~ label:before {
        @media (max-width: 992px) {
          top: 4px;
        }
      }

      [type='radio']:checked ~ label:after {
        @media (max-width: 992px) {
          top: 8px;
          left: 4px;
        }
      }
    }
  }

  .panel {
    margin-top: 20px;

    @media (max-width: 992px) {
      margin-top: 30px;
    }
  }
}

/*
  • Request box
  ---------- ---------- ---------- ---------- ----------
*/

.request-box {
  border-radius: 10px;
  background: $white;
  margin-bottom: 15px;
  padding: 15px 25px;

  .empty-state-container {
    margin: 0 !important;
    padding: 0;
  }

  @media (min-width: 992px) {
    padding: 15px 25px;
  }

  @media (min-width: 1200px) {
    padding: 15px 25px;
  }

  @media (min-width: 1300px) {
    padding: 15px 25px;
  }

  h2 {
    padding-left: 30px;
    position: relative;
    margin-bottom: 25px;

    &::before {
      content: '';
      width: 20px;
      height: 20px;
      position: absolute;
      left: 0;
      margin-right: 12px;
      border-radius: 10px;
    }

    span {
      margin-left: 10px;
    }
  }

  .modal-content {
    h2 {
      &:before {
        top: 5px;
      }
    }

    h3 {
      margin-right: 10px;
    }
  }

  .booking-modal {
    z-index: 99999999999;

    .infos-equipments {
      .addresses {
        margin-top: 0;
      }
    }

    &.modal-content {
      h3 {
        margin-bottom: 0;
      }
    }
  }

  &__card {
    padding: 16px;
    border-radius: 10px;
    border: 1px solid $yellow;
    min-height: 260px;
    margin-bottom: 25px;
    background: $white;

    @media (min-width: 992px) {
      background: none;
    }

    &-top {
      align-items: center;
      justify-content: space-between;
      width: 100%;

      p {
        margin-bottom: 0;
      }
    }

    &-middle {
      margin: 50px 0 30px;

      p {
        span {
          padding-right: 12px;
          min-width: 23px;
          text-align: center;

          img {
            width: 23px;
            height: 23px;
            border: 1px solid $near-grey;
            border-radius: 100%;
          }

          svg {
            font-size: 20px;
          }
        }
      }
    }

    &-bottom {
      display: inline-block;
      text-align: center;
      width: 100%;

      @media (min-width: 1200px) {
        display: flex;
        justify-content: end;
        width: auto;
      }

      button {
        margin-right: 16px;
        padding: 5px;
        width: 100%;
        margin-bottom: 15px;

        @media (min-width: 1200px) {
          width: auto;
          margin-bottom: 0;
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .pagination {
    margin-top: 35px;
  }

  &.pending {
    .request-box {
      &__card {
        &-bottom {
          button {
            width: auto;

            &:first-child {
              width: 100%;
              margin-bottom: 15px;

              @media (min-width: 1200px) {
                width: auto;
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }

    h2 {
      &::before {
        background: $yellow;
      }
    }
  }

  &.accepted {
    h2 {
      &::before {
        background: $green;
      }
    }

    .request-box__card {
      border-color: $green;
    }
  }

  &.rejected {
    h2 {
      &::before {
        background: $rose;
      }
    }

    .request-box__card {
      border-color: $rose;
    }
  }

  &.declined {
    h2 {
      &::before {
        background: $rose;
      }
    }

    .request-box__card {
      border-color: $rose;
    }
  }

  &.canceled {
    h2 {
      &::before {
        background: $grey;
      }
    }

    .request-box__card {
      border-color: $grey;
    }
  }

  .request-all {
    margin-top: 10px;
  }

  .body-content__top {
    margin: 30px 0 25px;
  }
}

.bidz-request-box {
  padding: 25px 15px;
  border-radius: 10px;
  background: $white;
  margin-bottom: 25px;

  @media (min-width: 992px) {
    margin-top: 22px;
    padding: 35px 20px;
  }

  @media (min-width: 1200px) {
    padding: 35px 15px;
  }

  @media (min-width: 1300px) {
    padding: 35px 30px;
  }

  h2 {
    padding-left: 40px;
    position: relative;
    margin-bottom: 25px;

    &::before {
      content: '';
      width: 28px;
      height: 28px;
      position: absolute;
      left: 0;
      margin-right: 12px;
      border-radius: 10px;
    }

    span {
      margin-left: 10px;
    }
  }

  &__card {
    padding: 16px;
    border-radius: 10px;
    border: 1px solid $yellow;
    min-height: 260px;
    margin-bottom: 25px;
    background: $white;

    @media (min-width: 992px) {
      background: none;
    }

    &-top {
      align-items: center;
      justify-content: space-between;
      width: 100%;

      p {
        margin-bottom: 0;
      }
    }

    &-middle {
      margin: 50px 0 30px;

      p {
        span {
          padding-right: 12px;
          min-width: 23px;
          text-align: center;

          img {
            width: 23px;
            height: 23px;
            border: 1px solid $near-grey;
            border-radius: 100%;
          }

          svg {
            font-size: 20px;
          }
        }
      }
    }

    &-bottom {
      display: inline-block;
      text-align: center;
      width: 100%;

      @media (min-width: 1200px) {
        display: flex;
        justify-content: end;
        width: auto;
      }

      button {
        margin-right: 16px;
        padding: 5px;
        width: 100%;
        margin-bottom: 15px;

        @media (min-width: 1200px) {
          width: auto;
          margin-bottom: 0;
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .pagination {
    margin-top: 35px;
  }

  &.pending {
    .request-box {
      &__card {
        &-bottom {
          button {
            width: auto;

            &:first-child {
              width: 100%;
              margin-bottom: 15px;

              @media (min-width: 1200px) {
                width: auto;
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }

    h2 {
      &::before {
        background: $yellow;
      }
    }
  }

  &.accepted {
    h2 {
      &::before {
        background: $green;
      }
    }

    .request-box__card {
      border-color: $green;
    }
  }

  .request-all {
    margin-top: 10px;

    @media (min-width: 1200px) {
      margin-top: 75px;
    }

    button {
      min-width: 265px;
      background: transparent;
    }
  }
}

.bidz-request-box.accepted h2::before,
.bidz-request-box.declined ::before,
.bidz-request-box.pending ::before,
.bidz-request-box.accepted ::before {
  background: transparent;
}

.head-content {
  @media (max-width: 1200px) {
    display: none;
  }

  h3 {
    @media (max-width: 1340px) {
      font-size: 15px;
    }
  }
}

.body-content {
  &__top {
    margin: 10px 0 25px;

    @media (min-width: 992px) {
      margin: 0 0 25px;
    }

    h3 {
      margin-bottom: 0;
      display: flex;
      align-items: center;
      width: 100%;

      &:after {
        content: '';
        height: 1px;
        background: $light-grey;
        width: 100%;
        flex: 1;
      }

      span {
        margin-right: 30px;
      }
    }
  }

  &__bottom {
    margin: 0 12px;

    img {
      width: 36px;
      height: 36px;
      border: 2px solid $check-grey;
      border-radius: 36px;
      margin-right: 13px;
      object-fit: contain;
    }

    .actions {
      text-align: right;
      display: flex;
      align-items: center;
      justify-content: end;

      @media (max-width: 992px) {
        margin-top: 30px;
      }

      .dropdown {
        button {
          width: 30px;
          height: 30px;
          min-width: 30px;
        }
      }

      .custom-dropdown-menu {
        span {
          width: 35px;
          height: 35px;

          img {
            width: 25px;
            height: 25px;
            border: none;
            margin: 0 auto;
          }
        }
      }

      &.bidz-actions {
        @media (min-width: 992px) {
          text-align: left;
          justify-content: center;
        }
      }

      a,
      button {
        font-size: 16px;
        margin-right: 15px;

        @media (max-width: 1350px) {
          font-size: 14px;
          margin-right: 7px;
          padding: 5px 5px;
          min-width: 110px;
        }

        @media (max-width: 992px) {
          font-size: 12px;
          min-width: 100px;
        }

        @media (max-width: 572px) {
          min-width: 90px;
        }
      }

      a {
        color: #4e4e4e;
        font-weight: 700;
      }
    }

    .bg-content {
      background: $light-grey;
      padding: 10px 10px 10px 15px;
      border-radius: 7px;
      margin-bottom: 10px;

      p {
        margin-bottom: 0;
      }
    }
  }
}

@media (max-width: 1200px) {
  .mobile-ml {
    margin-left: 49px;
    margin-top: 10px;
  }
}

@media (max-width: 1200px) {
  .mobile-ml {
    margin-left: 49px;
    margin-top: 10px;
  }
}

.pl-0 {
  padding-left: 0;
}

.swal2-confirm.custom-button {
  background-color: $yellow !important;
  border-color: $yellow !important;
  color: $white !important;
  width: 150px !important;
  height: 50px !important;
  font-size: 16px !important;
  border-radius: 5px;
}

.swal2-confirm.custom-button:hover {
  opacity: 0.8;
}

div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm:focus {
  box-shadow: 0 0 0 3px whitesmoke;
}
