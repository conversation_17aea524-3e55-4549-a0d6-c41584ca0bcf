import React from 'react';
import CustomImage from '../../images/Custom_image';
import RenderIf from '../../Render_if';
import { isEmpty } from 'lodash';

export default function RequestCardItem({
  data,
  role,
  setRequestDetailsModal,
  setSelectedRequest,
  member,
  t
}) {
  const notRole = role === 'equipper' ? 'lodger' : 'equipper';

  return (
    <div className="body-content__bottom">
      <div className="row">
        <div className="col-xl-10 offset-xl-2 bg-content">
          <div className="row align-items-center">
            <div className="col-xl-3 col-8 order-1 mb-2">
              <div className="d-flex align-items-center">
                <CustomImage
                  imageUrl={data[`${notRole}_image_link`]}
                  alt="profile"
                  isUser
                />
                <p className="t-body-regular">
                  {role === 'equipper'
                    ? data?.company_name || data?.lodger_name
                    : data?.equipper_name}
                </p>
              </div>
            </div>
            <RenderIf
              condition={
                role === 'lodger' &&
                member &&
                member.type === 'admin' &&
                !isEmpty(sessionStorage.getItem('member_of'))
              }
            >
              <div className="col-xl-3 col-8 order-1">
                <div className="d-flex align-items-center">
                  <CustomImage
                    imageUrl={data[`${role}_image_link`]}
                    alt="profile"
                    isUser
                  />
                  <p className="t-body-regular">{data?.lodger_name}</p>
                </div>
              </div>
            </RenderIf>
            <div className="col-xl-3 order-xl-3 order-2">
              <p className="t-body-regular mobile-ml">
                {`${data?.start_date?.slice(0, 10)} -
                                     ${data?.end_date?.slice(0, 10)}`}
              </p>
            </div>
            <div
              className={
                member?.type === 'admin'
                  ? 'col-xl-3  order-4'
                  : 'col-xl-6  order-4'
              }
            >
              <div className="actions bidz-actions">
                <button
                  className="round-button black shadow"
                  onClick={() => {
                    setRequestDetailsModal(true);
                    setSelectedRequest(data);
                  }}
                >
                  {t('View_details')}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
