package service

import (
	"context"
	"fmt"
	"io"
	"log"
	"strings"
	"time"

	"github.com/vima-inc/derental/models"
)

// GetCreditCheckFormByLodgerID returns the credit check form of a lodger.
func (s *Service) GetCreditCheckFormByLodgerID(ctx context.Context, lodgerID string) ([]models.CreditCheckForm, error) {
	lodger, err := s.db.GetLodgerByID(ctx, lodgerID)
	if err != nil {
		return []models.CreditCheckForm{}, fmt.Errorf("failed to get lodger: %w", err)
	}

	if lodger.MemberOf != "" {
		lodgerID = lodger.MemberOf
	}

	return s.db.GetCreditCheckFormByLodgerID(ctx, lodgerID)
}

// GetCreditCheckFormByID returns the credit check by id.
func (s *Service) GetCreditCheckFormByID(ctx context.Context, id string) (models.CreditCheckForm, error) {
	return s.db.GetCreditCheckFormByID(ctx, id)
}

// AddCreditCheckForm creates a credit check form.
func (s *Service) AddCreditCheckForm(ctx context.Context, creditCheckForm models.CreditCheckForm, lodgerID string) (models.CreditCheckForm, error) {
	lodger, err := s.db.GetLodgerByID(ctx, lodgerID)
	if err != nil {
		return models.CreditCheckForm{}, fmt.Errorf("failed to get lodger: %w", err)
	}
	creditCheckForm.CreatedBy = lodgerID
	if lodger.MemberOf != "" {
		creditCheckForm.LodgerID = lodger.MemberOf
	} else {
		creditCheckForm.LodgerID = lodgerID
	}
	creditCheckForm.ActiveBooking = false
	return s.db.AddCreditCheckForm(ctx, creditCheckForm)
}

// UpdateCreditCheckForm updates a credit check form.
func (s *Service) UpdateCreditCheckForm(ctx context.Context, creditCheckForm models.CreditCheckForm, creditCheckFormID string, lodgerID string) error {
	ccf, err := s.db.GetCreditCheckFormByID(ctx, creditCheckFormID)
	if err != nil {
		return fmt.Errorf("failed to get credit check form: %w", err)
	}

	if ccf.LodgerID != lodgerID {
		log.Printf("credit check form lodger: %s , connected lodger: %s", ccf.LodgerID, lodgerID)
	}

	lodger, err := s.GetLodgerByID(ctx, ccf.LodgerID)
	if err != nil {
		return fmt.Errorf("failed to get Lodger: %w", err)
	}
	creditCheckForm.ID = creditCheckFormID

	projects, err := s.db.GetProjectByCreditApplicationID(ctx, creditCheckFormID)

	if err != nil {
		return fmt.Errorf("failed to get projects: %w", err)
	}

	updatedProjects := make([]models.Project, 0)

	for _, project := range projects {
		project.CreditCheckForm.Name = creditCheckForm.Name

		updatedProjects = append(updatedProjects, project)
	}

	err = s.db.BatchUpdateProjects(ctx, updatedProjects)

	if err != nil {
		return fmt.Errorf("failed to update projects: %w", err)
	}

	if lodger.MemberOf != "" {
		shouldReturn, returnValue := UpdateCreditCheckFormMember(ctx, s, lodger, creditCheckForm)
		if shouldReturn {
			return returnValue
		}
	} else {
		err = s.db.UpdateCreditCheckForm(ctx, creditCheckForm)
		if err != nil {
			return fmt.Errorf("failed to update credit check form: %w", err)
		}
	}

	return nil
}

func UpdateCreditCheckFormMember(ctx context.Context, s *Service, lodger models.Lodger, creditCheckForm models.CreditCheckForm) (bool, error) {
	member, err := s.GetMemberByID(ctx, lodger.MemberID, lodger.MemberOf)
	if err != nil {
		return true, fmt.Errorf("failed to get member: %w", err)
	}

	if member.Type == "admin" || member.Type == "power_collaborator" || member.Type == "owner" {
		err = s.db.UpdateCreditCheckForm(ctx, creditCheckForm)
		if err != nil {
			return true, fmt.Errorf("failed to update credit check form: %w", err)
		}
	}

	return false, nil
}

// DeleteCreditCheckForm deletes a credit check form.
func (s *Service) DeleteCreditCheckForm(ctx context.Context, lodgerID string, id string) error {
	resp, err := s.db.GetCreditCheckFormByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get credit check form: %w", err)
	}

	projects, err := s.db.GetProjectsByCCFID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get projects: %w", err)
	}

	if len(projects) > 0 {
		projectsUpdated := []models.Project{}

		for _, project := range projects {
			project.CreditCheckForm = models.CreditCheckForm{}
			project.CreditCheckFormID = ""
			projectsUpdated = append(projectsUpdated, project)
		}

		err = s.db.BatchUpdateProjects(ctx, projectsUpdated)
		if err != nil {
			return fmt.Errorf("failed to batch update projects: %w", err)
		}
	}

	bookingRequests, err := s.db.GetBookingRequestsByCCFID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get booking requests: %w", err)
	}

	if len(bookingRequests) > 0 {
		bookingRequestsUpdated := []models.BookEquipment{}

		for _, bookingRequest := range bookingRequests {
			bookingRequest.CreditCheckForm = models.CreditCheckForm{}
			bookingRequest.CreditCheckFormID = ""
			bookingRequestsUpdated = append(bookingRequestsUpdated, bookingRequest)
		}

		err = s.db.BatchUpdateBookingRequests(ctx, bookingRequestsUpdated)
		if err != nil {
			return fmt.Errorf("failed to batch update booking requests: %w", err)
		}
	}

	if resp.LodgerID == lodgerID || resp.CreatedBy == lodgerID {
		return s.db.DeleteCreditCheckForm(ctx, id)
	}

	return fmt.Errorf("error: you are not allowed to delete this credit check form")
}

// UploadCreditCheckForm uploads a credit check form to storage and add its url to its collection.
func (s *Service) UploadCreditCheckForm(ctx context.Context, lodgerID string, creditCheckFormID string, data io.Reader) error {
	lodger, err := s.db.GetLodgerByID(ctx, lodgerID)
	if err != nil {
		return fmt.Errorf("failed to get lodger: %w", err)
	}
	creditCheckForm, err := s.db.GetCreditCheckFormByID(ctx, creditCheckFormID)
	if err != nil {
		return fmt.Errorf("failed to get credit check form: %w", err)
	}

	fileName := fmt.Sprintf("%s_%d", strings.ReplaceAll(creditCheckForm.Name, " ", "_"), time.Now().Unix())

	var storagePath string

	if lodger.MemberOf != "" {
		storagePath = fmt.Sprintf("lodger/%s/%s_%s", lodger.MemberOf, creditCheckFormID, fileName)
	} else {
		storagePath = fmt.Sprintf("lodger/%s/%s_%s", lodgerID, creditCheckFormID, fileName)
	}

	_, err = s.storage.Write(ctx, s.getPrivateInformationBucket(), storagePath, data)
	if err != nil {
		return fmt.Errorf("failed to upload credit check form: %w", err)
	}

	creditCheckForm.CreditCheckFormPath = fileName

	return s.db.UpdateCreditCheckForm(ctx, creditCheckForm)
}

// UploadPDF uploads a pdf to storage and add its url to its collection.
func (s *Service) UploadPDF(ctx context.Context, lodgerID string, attachmentName string, creditCheckFormID string, data io.Reader) error {
	lodger, err := s.db.GetLodgerByID(ctx, lodgerID)
	if err != nil {
		return fmt.Errorf("failed to get lodger: %w", err)
	}
	creditCheckForm, err := s.db.GetCreditCheckFormByID(ctx, creditCheckFormID)
	if err != nil {
		return fmt.Errorf("failed to get credit check form: %w", err)
	}

	fileName := fmt.Sprintf("%s_%d", attachmentName, time.Now().Unix())

	var storagePath string

	if lodger.MemberOf != "" {
		storagePath = fmt.Sprintf("lodger/%s/%s_%s", lodger.MemberOf, creditCheckFormID, fileName)
	} else {
		storagePath = fmt.Sprintf("lodger/%s/%s_%s", lodgerID, creditCheckFormID, fileName)
	}

	_, err = s.storage.Write(ctx, s.getPrivateInformationBucket(), storagePath, data)
	if err != nil {
		return fmt.Errorf("failed to upload pdf: %w", err)
	}

	switch attachmentName {
	case "insurance":
		creditCheckForm.Attachments.Insurance = fileName
	case "partnership_agreement":
		creditCheckForm.Attachments.PartnershipAgreement = fileName
	case "applicant_userID":
		creditCheckForm.Attachments.UserID = fileName
	case "secondary_applicant_userID":
		creditCheckForm.Attachments.SecondApplicantID = fileName
	}

	return s.db.UpdateCreditCheckForm(ctx, creditCheckForm)
}

// GetCreditCheckFormAttachment returns the signed url of a credit check attachment.
func (s *Service) GetCreditCheckFormAttachment(ctx context.Context, userID string, bookingID string, creditCheckFormID string, attachmentName string) (string, error) {
	ccf, err := s.db.GetCreditCheckFormByID(ctx, creditCheckFormID)
	if err != nil {
		return "", fmt.Errorf("failed to get credit check form: %w", err)
	}

	lodger, err := s.db.GetLodgerByID(ctx, userID)
	if err != nil {
		log.Printf("failed to get lodger: %v", err)
	}
	var lodgerID string

	if err == nil {
		if lodger.MemberOf != "" {
			lodgerID = lodger.MemberOf
			if ccf.LodgerID != lodger.MemberOf {
				return "", fmt.Errorf("error: you are not allowed to get this credit check form attachment")
			}
		} else {
			lodgerID = userID
			if ccf.LodgerID != userID {
				return "", fmt.Errorf("error: you are not allowed to get this credit check form attachment")
			}
		}
	}

	if bookingID != "-" {
		booking, err := s.db.GetBookEquipmentByID(ctx, bookingID)
		if err != nil {
			return "", fmt.Errorf("failed to get booking: %w", err)
		}

		if booking.CreditCheckFormID != creditCheckFormID {
			return "", fmt.Errorf("error: you are not allowed to get this credit check form attachment")
		}

		if booking.OwnerID != "" {
			lodgerID = booking.OwnerID
		} else {
			lodgerID = booking.LodgerID
		}
	}

	storagePath := fmt.Sprintf("lodger/%s/%s_%s", lodgerID, creditCheckFormID, attachmentName)

	url, err := s.storage.SignedURL(ctx, s.getPrivateInformationBucket(), storagePath)
	if err != nil {
		return "", fmt.Errorf("failed to get signed url: %w", err)
	}

	return url, nil
}
