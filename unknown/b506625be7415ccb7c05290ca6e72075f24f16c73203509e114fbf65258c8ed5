import React from 'react';
import { STATUS_MESSAGE } from '../../helpers/Message_helper';
import { getError<PERSON>ey } from '../../helpers/Error_messages';
import ErrorImg from '../../../style/assets/img/XCircle.png';

export default function Popup({
  show,
  onClose,
  response,
  optionalMessage,
  message,
  t,
  prefix
}) {
  if (!show) {
    return null;
  }
  return (
    <div className="modal">
      <div className="modal-content">
        <button className="close-button" onClick={onClose}>
          <svg
            width="25"
            height="24"
            viewBox="0 0 25 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19.25 5.25L5.75 18.75"
              stroke="#333333"
              strokeWidth="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M19.25 18.75L5.75 5.25"
              stroke="#333333"
              strokeWidth="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
        <div className="row">
          <div className="col-lg-10 mx-auto text-center">
            <h2 className="t-header-h5 c-fake-black credit-title">
              {response?.message}
            </h2>
            <img src={ErrorImg} alt="Tolo indicating error" />
            <h3 className="success-signup-title">
              {STATUS_MESSAGE[response?.status]}
            </h3>
            <h3 className="success-signup-title">
              {response?.error?.error && prefix
                ? t(getErrorKey(response?.error.error, prefix))
                : message}
            </h3>
            <h5>{optionalMessage}</h5>
            <div className="fixed-button-modal">
              <button
                className="button-signup round-button yellow bold c-primary-color"
                onClick={onClose}
              >
                {t('Close_button')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
