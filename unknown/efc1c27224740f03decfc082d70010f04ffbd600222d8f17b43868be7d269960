import React from 'react';
import Grid from '@mui/material/Grid';
import Box from '@mui/material/Box';
import Input from '../../../../shared/components/forms/Input.jsx';
import Typography from '@mui/material/Typography';
import MultiSelect from '../../../../shared/components/multi_select/Multi_select.jsx';

export default function StatusPrice({ t, formik }) {
  const statusOptions = [
    { label: 'Available', value: 'available' },
    { label: 'Idle', value: 'idle' }
  ];
  const isHidden =
    formik.values?.status !== undefined &&
    formik.values?.price?.day !== '' &&
    formik.values?.price?.week !== '' &&
    formik.values?.price?.month !== '';

  return (
    <Box>
      <Typography fontFamily="inherit" fontWeight="bold" variant="h6">
        {t('Status_price')}
      </Typography>
      <Box fontFamily="inherit" color="red" hidden={isHidden}>
        {t('Please_fill_in_fields')}
      </Box>
      <Grid marginTop="15px" container spacing={2}>
        <Grid item xs={12}>
          <div className="form-group">
            <label className="label-input t-body-regular c-fake-black">
              {t('Status')} <span className="c-red star-required">*</span>
            </label>
            <MultiSelect
              options={statusOptions}
              disabled={formik.initialValues?.status === 'booked'}
              handleChange={(item) => {
                item.value
                  ? formik.setFieldValue('status', item?.value)
                  : formik.setFieldValue('status', null);
              }}
              t={t}
              value={
                formik.initialValues?.status === 'booked'
                  ? [
                      {
                        label: 'Booked',
                        value: 'booked'
                      }
                    ]
                  : [
                      statusOptions.find(
                        (e) => e.value === formik.values?.status
                      )
                    ]
              }
              name="status"
            />
          </div>
        </Grid>
        <Grid item xs={12} sm={4}>
          <div className="form-group">
            <Input
              type="number"
              label={t('Price_day')}
              name="price.day"
              className="form-control"
              placeholder={t('Day')}
            />
          </div>
        </Grid>
        <Grid item xs={12} sm={4}>
          <div className="form-group">
            <Input
              type="number"
              label={t('Price_week')}
              name="price.week"
              className="form-control"
              placeholder={t('Week')}
            />
          </div>
        </Grid>
        <Grid item xs={12} sm={4}>
          <div className="form-group">
            <Input
              type="number"
              label={t('Price_four_week')}
              name="price.month"
              className="form-control"
              placeholder={t('Four_week')}
            />
          </div>
        </Grid>
      </Grid>
    </Box>
  );
}
