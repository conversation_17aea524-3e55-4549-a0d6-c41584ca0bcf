.modal {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  opacity: 1;
  transform: none !important;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  p {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .modal-content {
    width: 700px;
    z-index: 9999999999999999 !important;
    background-color: $white;
    border-radius: 12px;
    border: 1px solid #e5e6f7 !important;
    padding: 70px 20px 50px;
    height: fit-content;
    border: 0;
    box-shadow: 0 11px 40px 0 rgba(6, 28, 61, 0.07);
    &.mrp {
      width: 600px !important;
      @media (max-width: 992px) {
        width: 90% !important;
      }
    }
    &.booking-modal {
      width: 80%;
      padding: 44px 24px;
      @media (max-width: 992px) {
        width: 90%;
      }

      .error-message {
        position: relative;
      }

      .react-datepicker-wrapper {
        .react-datepicker__input-container {
          .searchInput-navbar {
            @media (min-width: 992px) {
              padding-left: 10px;
            }
          }
        }

        &:nth-child(2) {
          .react-datepicker__input-container {
            .searchInput-navbar {
              @media (min-width: 992px) {
                padding-left: 0;
              }
            }
          }
        }
      }

      .adjustPicker.new-datepicker {
        border: 0;
        border-radius: 0;
        padding: 0;
        background: transparent;
        height: auto;
        @media (max-width: 600px) {
          display: block;
        }

        .react-datepicker-wrapper {
          .react-datepicker__input-container {
            padding-left: 0;
            @media (max-width: 992px) {
              border-right: 0;
            }

            @media (max-width: 600px) {
              margin-left: 0;
              margin-bottom: 10px;
            }
          }
        }

        @media (max-width: 992px) {
          .start_date {
            .form-control {
              border-radius: 7px;
              border-right: 1px solid $light-gray;
            }

            &:after {
              display: none;
            }
          }
          .end_date {
            position: relative;

            .form-control {
              border-radius: 7px;
              border-left: 1px solid $light-gray;
              padding-left: 55px;
            }

            &:before {
              content: url('../style/assets/img/Icons/CalendarBlank.svg');
              width: 24px;
              display: inline-block;
              position: absolute;
              top: 13px;
              z-index: 10000000;
              left: 13px;
            }
          }
        }
      }

      .view-details-btn {
        min-width: 100%;
        font-size: 16px;
        background: transparent;
        @media (max-width: 992px) {
          min-width: auto;
          padding: 5px 15px;
        }

        &:hover {
          background: $black;
          color: $white;
        }
      }

      .view-details-btn--focus {
        min-width: 100%;
        font-size: 16px;
        background: $black;
        color: $white;
        @media (max-width: 992px) {
          min-width: auto;
          padding: 5px 15px;
        }
      }

      .PhoneInputInternationalIconGlobe {
        display: none;
      }

      .PhoneInputInput {
        padding-left: 20px;
        margin-left: 10px;
        border-left: 1px solid #e3e5e9;
      }
    }

    &.static-hours-modal {
      width: 365px;
      padding: 30px 50px;
      border: 1px solid $yellow;

      .round-button {
        display: none;
      }
    }

    @media (max-width: 768px) {
      width: 90%;
    }

    h3 {
      margin-bottom: 20px;
    }

    &.add-equipment-modal {
      @media (min-width: 992px) {
        padding-left: 64px;
        padding-right: 64px;
        padding-top: 20px;
      }

      h3 {
        margin-bottom: 8px;
      }

      p {
        margin-bottom: 24px !important;
      }
    }

    .desc-limit {
      max-width: 80%;
      text-align: center;
      margin-left: auto;
      margin-right: auto;
    }

    .full-width {
      width: 100%;
    }

    .width-200 {
      min-width: 200px;
      margin-top: 50px;
    }

    .link-forget {
      margin-top: 10px;

      a {
        cursor: pointer;
      }
    }

    &.edit-personal {
      min-width: 90%;
      height: 90vh;
      @media (min-width: 992px) {
        min-width: 900px;
      }

      .scrollbar {
        overflow-y: scroll;
        padding-right: 20px;
        height: calc(90vh - 120px);
        @media (min-width: 992px) {
          padding-right: 40px;
        }
        @media (max-height: 850px) {
          &::-webkit-scrollbar-track {
            border-radius: 30px;
            background-color: $light-grey;
          }

          &::-webkit-scrollbar {
            width: 6px;
            background-color: $light-grey;
            border-radius: 30px;
          }

          &::-webkit-scrollbar-thumb {
            border-radius: 30px;
            background-color: $yellow;
          }
        }

        p {
          margin-bottom: 1rem !important;
          margin-top: 0px;
        }
      }

      h3 {
        max-width: 550px;
        border-bottom: 1px solid $check-grey;
        padding-bottom: 25px;
        margin: 0 auto;
      }

      .change-logo {
        margin-top: 40px;

        &__image {
          border: 2px solid $check-grey;
          border-radius: 180px;
          @media (max-width: 992px) {
            text-align: center;
          }
        }

        &__content {
          text-align: left;
          @media (max-width: 992px) {
            margin-top: 10px;
            text-align: center;
          }

          p {
            margin-bottom: 40px;
            @media (max-width: 992px) {
              margin-bottom: 10px;
            }

            span {
              @media (max-width: 992px) {
                display: block;
              }
            }
          }
        }
      }

      .form {
        margin-top: 50px;

        .form-group {
          .form-control {
            width: 100%;
          }
        }
      }

      .button {
        .round-button {
          min-width: 50%;
          margin: 0 auto;
        }
      }
    }

    .link-modal {
      cursor: pointer;
      padding-left: 15px;

      &:hover {
        color: $primary-color;
      }
    }
  }

  .email-verified {
    margin-bottom: 50px;
  }

  .FPmodal-title {
    margin-top: 25px;
  }

  .btn-resend-code {
    margin-right: 10px;
    background: $white;
    @media (max-width: 380px) {
      margin-bottom: 10px;
      margin-right: 0;
    }
  }

  .input-code.react-code-input input {
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: $light-grey;
    background-clip: padding-box;
    border: 1px solid $check-grey;
    appearance: none;
    border-radius: 0.25rem;
    width: 35px;
    margin-right: 10px;

    &:focus-visible {
      outline: none;
    }
  }

  .booking-modal {
    .box-content {
      padding: 20px;
      border: 1px solid $medium-grey;
      border-radius: 30px;
      margin-bottom: 30px;
      @media (min-width: 768px) {
        padding: 30px;
        margin-bottom: 70px;
      }

      p {
        &:last-child {
          margin-bottom: 1rem;
        }
      }
    }

    .round-button {
      min-width: 170px;
      margin: 0 10px 15px;
      @media (min-width: 600px) {
        margin: 0 10px;
      }

      &.big-btn {
        min-width: 335px;
      }
    }

    .top-content {
      max-width: 70%;
      margin: 0 auto;
    }

    .result-box {
      padding: 13px;

      &.with-border {
        border: 1px solid #e3e5e9;
        border-radius: 12px;
        display: block;
        margin-bottom: 10px;
        padding: 10px 20px;

        .comission-price-details {
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .total-amount-price-details {
          &:before {
            content: '';
            width: 80%;
            height: 1px;
            background: #e3e5e9;
            margin: 20px auto;
            display: block;
          }
        }
      }

      &__top {
        @media (max-width: 992px) {
        }

        &-left {
          text-align: left;
        }
      }

      &__image {
        margin-bottom: 10px;

        img {
          border-radius: 30px;
          max-width: 130px;
        }
      }
    }

    .infos-equipments {
      .row-infos {
        margin-left: auto;
        margin-right: auto;

        .required-elem {
          .star-required {
            position: relative;
            top: 4px;
            left: 5px;
          }
        }

        p {
          margin-bottom: 0 !important;
        }
      }

      .comission-price-details {
        width: calc(100% - 26px);
        margin: 0 auto;
        @media (min-width: 768px) {
          padding: 2px 0;
        }

        p {
          @media (max-width: 768px) {
            margin-bottom: 5px !important;
          }
        }

        .total-amount-price-details {
          padding-left: 0;
          @media (min-width: 992px) {
            text-align: right;
            border-top: 1px solid grey;
            padding: 15px 0 0 0;
          }
        }
      }
    }

    .right-price-details {
      @media (min-width: 768px) {
        text-align: right;
      }
      @media (max-width: 768px) {
        p {
          margin-bottom: 0 !important;
        }
      }
    }

    .scrollbar {
      overflow-y: scroll;
      padding-right: 20px;
      height: calc(90vh - 120px);
      @media (min-width: 992px) {
        padding-right: 40px;
      }
      @media (max-height: 850px) {
        &::-webkit-scrollbar-track {
          border-radius: 30px;
          background-color: $light-grey;
        }

        &::-webkit-scrollbar {
          width: 6px;
          background-color: $light-grey;
          border-radius: 30px;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 30px;
          background-color: $yellow;
        }
      }

      p {
        margin-bottom: 1rem !important;
        margin-top: 0;
      }
    }

    .row-infos {
      &:last-child {
        border: 0;
      }

      &.check-row {
        p {
          @media (min-width: 735px) {
            width: 40%;
          }
        }

        .checks-content {
          display: inline-flex;
          align-items: center;
          width: 100%;
          @media (max-width: 735px) {
            margin-left: 10px;
          }
          @media (max-width: 572px) {
            display: block;
          }

          .radio-box {
            margin-bottom: 0;
            display: flex;
            align-items: center;
            margin-right: 40px;

            .check-label {
              padding-left: 35px;
            }
          }

          .radio-box [type='radio']:checked ~ label:before,
          .radio-box [type='radio']:not(:checked) ~ label:before {
            right: 25px;
            border: 3px solid $yellow;
            width: 20px;
            height: 20px;
          }

          .radio-box [type='radio']:checked ~ label:after,
          .radio-box [type='radio']:not(:checked) ~ label:after {
            right: auto;
            @media (min-width: 1200px) {
              left: 5px;
            }
          }

          .radio-box [type='radio']:checked ~ label,
          .radio-box [type='radio']:not(:checked) ~ label {
            display: inline-block !important;
            width: auto;
          }

          .radio-box [type='radio']:checked ~ label:after {
            top: -5px;
            width: 10px;
            height: 10px;
          }

          .radio-box [type='radio']:checked ~ label:before,
          .radio-box [type='radio']:not(:checked) ~ label:before {
            top: -10px;
          }
        }
      }

      .form-group {
        .form-control {
          @media (min-width: 768px) {
            min-width: 280px;
          }
        }

        &.pickup-address {
          .form-control {
            @media (min-width: 768px) {
              min-width: auto;
            }
          }
        }

        .special-price {
          .form-control {
            @media (min-width: 768px) {
              min-width: 180px;
            }
          }
        }
      }
    }

    .form-group {
      .form-control {
        margin-top: 10px;
        font-size: 14px !important;
        color: $neutrals-gray;
        @media (min-width: 992px) {
          font-size: 16px !important;
        }

        &::-webkit-input-placeholder {
          /* Edge */
          color: $neutrals-gray;
        }

        &:-ms-input-placeholder {
          /* Internet Explorer 10-11 */
          color: $neutrals-gray;
        }

        &::placeholder {
          color: $neutrals-gray;
        }

        @media (min-width: 768px) {
          height: 45px;
        }
      }
    }

    .css-junnbe-control {
      padding-top: 0;
      padding-bottom: 0;
      @media (max-width: 786px) {
        margin-top: 10px;
      }
    }

    .css-qc6sy-singleValue {
      color: $neutrals-gray;
    }

    .po-marge {
      @media (max-width: 786px) {
        margin-top: 10px !important;
      }
    }

    .css-r5dfyc-control,
    .css-1b7e6um-control,
    .brd-radius-50,
    .brd-radius-20 {
      border-radius: 6px !important;
      margin-top: 20px;
      background-color: transparent;
      color: $blue-grey;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      padding: 0 80px;
      margin-bottom: 0;

      svg {
        display: none;
      }
    }

    .label-check {
      input:checked + label:before,
      label:before {
        position: relative;
        top: -4px;
      }

      input:checked + label:after {
        top: 3px;
      }

      .error-message {
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 100%;
        padding-left: 3px;
        margin-top: 0;

        &:before {
          display: none;
        }

        &:after {
          content: url('../style/assets/img/Icons/Info.svg');
          position: absolute;
          right: -27px;
        }
      }
    }

    .adjustPicker.new-datepicker {
      margin-top: 10px;

      .react-datepicker-wrapper {
        top: 0;
      }

      .start_date {
        position: relative;

        &:before,
        &:after {
          width: 24px;
          display: inline-block;
          position: absolute;
          top: 13px;
        }

        &:before {
          content: url('../style/assets/img/Icons/CalendarBlank.svg');
          left: 13px;
          z-index: 10000000;
        }

        &:after {
          content: url('../style/assets/img/Icons/ArrowRight.svg');
          right: 3px;
        }

        .form-control {
          border-radius: 7px 0 0 7px;
          padding-left: 55px;
          border-right: 0;
        }
      }
      .hour_picker {
        position: relative;

        &:before,
        &:after {
          width: 24px;
          display: inline-block;
          position: absolute;
          top: 13px;
          z-index: 1;
        }
      }
      .end_date {
        .form-control {
          border-radius: 0 7px 7px 0;
          padding-left: 25px;
          border-left: 0;
        }
      }

      .form-control {
        border: 1px solid $light-gray;
        font-weight: 400;
        line-height: 24px;
        color: $fake-black;
        margin-top: 0;
      }
    }

    .equipper-spotlight-cls & {
      .result-box {
        &.with-border {
          box-shadow: none;
        }
      }
    }
  }

  .invite-row {
    margin-bottom: 1rem;

    .invite-input {
      margin-bottom: 0 !important;
    }

    .minus-icon {
      margin-top: 0 !important;
    }
  }

  &.add-scroll-filter {
    .scrollBar {
      overflow-y: scroll;
      padding-right: 20px;
      height: calc(80vh - 120px);
      @media (min-width: 992px) {
        padding-right: 40px;
      }
      @media (max-height: 850px) {
        &::-webkit-scrollbar-track {
          border-radius: 30px;
          background-color: $light-grey;
        }

        &::-webkit-scrollbar {
          width: 6px;
          background-color: $light-grey;
          border-radius: 30px;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 30px;
          background-color: $yellow;
        }
      }
    }
  }

  .search-inventory {
    @media (max-width: 992px) {
      margin-top: 20px;
    }
  }
}

.create-project-modal {
  label {
    margin-bottom: 10px;
  }

  .toggle-style {
    background-color: transparent !important;
    color: #9a9a9a !important;
    box-shadow: none;
    width: 100%;
    text-align: left;
    max-width: 100%;
    border: 1px solid #ced4da;
  }

  .show > .btn-primary.dropdown-toggle {
    border: 1px solid #ced4da;
  }

  .react-daterange-picker {
    justify-content: space-between;
    display: flex;
  }

  .adjustPicker {
    .react-daterange-picker__inputGroup {
      flex-grow: 1;
      box-sizing: content-box;
      display: block;
      padding: 0.375rem 0.75rem;
      font-size: 1rem;
      font-weight: 400;
      line-height: 1.5;
      background-color: #fff;
      background-clip: padding-box;
      appearance: none;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
      border-radius: 30px;
      height: 36px !important;
      width: auto;
      color: #9a9a9a;
      border: 1px solid #d1d1d1;
      min-width: calc(50% - 30px);
      @media (max-width: 992px) {
        min-width: 95%;
        margin: 0 auto 15px;
        padding: 0 0.75rem;
      }

      &:first-child {
        margin-right: 20px;
        @media (max-width: 992px) {
          margin-right: 0;
          margin-bottom: 50px;
        }
      }
    }
  }

  .scrollBarModal {
    overflow-x: hidden;
  }

  .padding-r-0 {
    padding-right: 12px !important;
    @media (min-width: 992px) {
      padding-right: 0 !important;
    }
  }

  .add-memberModal {
    .send-btn {
      @media (max-width: 992px) {
        margin-top: 25px;
        margin-bottom: 25px;
      }

      button {
        @media (max-width: 992px) {
          min-width: 100%;
        }
      }
    }
  }

  .date-picker-project {
    position: relative;

    .end-date-project,
    .start-date-project {
      min-width: 50%;
    }

    .end-date-project {
      padding-left: 20px;
    }

    .fwb {
      font-weight: 700;
    }

    .react-datepicker-wrapper {
      .react-datepicker__input-container {
        .searchInput-navbar {
          padding-left: 10px;
        }
      }

      &:nth-child(2) {
        .react-datepicker__input-container {
          .searchInput-navbar {
            @media (min-width: 992px) {
              padding-left: 0;
            }
          }
        }
      }
    }

    .adjustPicker.new-datepicker {
      border: 0;
      border-radius: 0;
      padding: 0;
      background: transparent;
      height: auto;

      .react-datepicker-wrapper {
        .react-datepicker__input-container {
          padding-left: 0;
        }
      }
    }
  }

  .adjustPicker {
    .react-datepicker-wrapper {
      &:nth-child(2) {
        .react-datepicker__input-container {
          @media (max-width: 992px) {
            margin-left: 0;
          }
        }
      }
    }
  }
}

textarea::-webkit-scrollbar {
  background-color: transparent;
}

textarea::-webkit-scrollbar-track {
  background-color: transparent;
}

textarea::-webkit-scrollbar-thumb {
  background-color: transparent;
}

textarea::placeholder {
  font-size: 16px;
  @media screen and (max-width: 992px) {
    font-size: 14px;
  }
}

.marg-top-20 {
  margin-top: 20px;
}

.request-box {
  .rental-confirmation {
    h2 {
      &:before {
        display: none;
      }
    }

    .rental-buttons {
      button {
        min-width: 220px;
        margin: 0 10px 15px;
      }
    }

    .company-spotlight--feedback {
      .feedback-box {
        padding: 25px 0;
        @media (min-width: 992px) {
          padding: 25px 15px;
        }

        .meter-box {
          text-align: left;
        }
      }
    }
  }
}

.modal {
  background: rgba(237, 237, 237, 0.1);
  backdrop-filter: blur(3px);

  .under-construction-modal {
    @media (min-width: 480px) {
      padding-left: 0;
      padding-right: 0;
    }

    h2 {
      margin-top: 40px;
    }

    p {
      margin: 20px 0 35px !important;
    }

    .round-button {
      @media (min-width: 480px) {
        min-width: 375px;
      }
    }

    img {
      max-height: 200px;
    }
  }

  .date-location-modal {
    width: 610px;
    padding: 50px 60px;
    @media (min-width: 992px) {
      padding: 50px 110px;
    }

    .round-button {
      width: 80%;
    }

    .top-content {
      p {
        margin-bottom: 10px !important;
      }
    }

    .form-group {
      &.location {
        .siac {
          position: relative;
        }

        .autocomplete-options-menu {
          position: absolute;
          width: 100%;
          z-index: 2;

          li {
            &:before {
              display: none;
            }
          }
        }
      }
    }
  }

  .date-location-modal,
  .add-memberModal {
    .react-daterange-picker__calendar--open {
      inset: auto auto 100% 0 !important;
    }

    .adjustPicker .react-daterange-picker__inputGroup {
      @media (min-width: 992px) {
        min-width: calc(50% - 35px);
      }
    }

    &.create-project-modal,
    .adjustPicker,
    .react-daterange-picker__inputGroup:first-child {
      @media (max-width: 992px) {
        margin-bottom: 20px;
      }
    }

    .adjustPicker {
      &.new-datepicker {
        @media (max-width: 768px) {
          display: block;
        }

        .react-datepicker-wrapper {
          @media (max-width: 768px) {
            margin-bottom: 16px;
          }
        }

        .form-control {
          border-radius: 7px;
          border: 1px solid $light-gray;
          padding-left: 45px;
          font-weight: 400;
          line-height: 24px;
          color: $fake-black;
        }

        .react-datepicker-wrapper {
          top: 0;

          .react-datepicker__input-container {
            border: 0;
          }
        }

        .start_date {
          position: relative;

          @media (min-width: 768px) {
            margin-right: 20px;
          }

          &:before,
          &:after {
            width: 24px;
            display: inline-block;
            position: absolute;
            top: 13px;
          }

          &:before {
            content: url('../style/assets/img/Icons/CalendarBlank.svg');
            left: 10px;
            z-index: 1;
          }

          @media (min-width: 768px) {
            &:after {
              content: url('../style/assets/img/Icons/ArrowRight.svg');
              right: -30px;
            }
          }
        }

        .end_date {
          position: relative;

          @media (min-width: 768px) {
            margin-left: 20px;
          }

          @media (max-width: 768px) {
            &:before {
              content: url('../style/assets/img/Icons/CalendarBlank.svg');
              left: 10px;
              width: 24px;
              display: inline-block;
              position: absolute;
              top: 13px;
              z-index: 10000000;
            }
          }

          .form-control {
            @media (min-width: 992px) {
              padding-left: 20px;
            }
          }
        }
      }
    }

    .adjustPicker
      .react-datepicker-wrapper:nth-child(2)
      .react-datepicker__input-container {
      margin-left: 0;
    }
  }

  @media (min-width: 768px) {
    .add-memberModal {
      .adjustPicker.new-datepicker {
        .start_date,
        .end_date {
          width: 50%;
        }

        .start_date {
          &:after {
            right: -35px;
          }
        }
      }

      .date-picker-project {
        padding-right: 0;
      }
    }
  }

  @media (max-width: 768px) {
    .add-memberModal {
      .date-label {
        .start-date-project,
        .end-date-project {
          display: none;
        }
      }
    }
  }

  .infos-hours {
    overflow: hidden;
    margin-top: 34px;

    p {
      margin-bottom: 0 !important;
    }

    label {
      padding-right: 20px;
    }
  }

  .open-hours-equipper {
    p {
      margin-bottom: 0 !important;
    }

    @media (max-width: 768px) {
      .border-mobile {
        border-bottom: 1px solid $light-grey;
        margin-bottom: 20px;

        &:last-child {
          border-bottom: 0;
        }
      }
    }
    @media (max-width: 572px) {
      .border-mobile {
        .form-group {
          .form-control {
            width: 100%;
            margin-bottom: 10px;
          }
        }
      }
    }
  }

  .static-hours-modal {
    .border-mobile {
      margin-bottom: 16px;
    }
  }

  .modify-profil-modal {
    .label-check {
      input {
        padding: 0;
        height: initial;
        width: initial;
        margin-bottom: 0;
        display: none;
        cursor: pointer;
      }

      label {
        position: relative;
        cursor: pointer;

        &:before {
          content: '';
          -webkit-appearance: none;
          background-color: transparent;
          border: 2px solid $check-grey;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05),
            inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
          padding: 10px;
          display: inline-block;
          position: relative;
          vertical-align: middle;
          cursor: pointer;
          margin-right: 5px;
          border-radius: 3px;
        }
      }
    }

    .label-check input:checked + label:after {
      content: '';
      display: block;
      position: absolute;
      top: 7px;
      left: 9px;
      width: 6px;
      height: 14px;
      border: solid $white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
    }

    .label-check input:checked + label:before {
      background: $yellow;
      border-color: $yellow;
    }
  }
}

.label-check {
  input {
    padding: 0;
    height: initial;
    width: initial;
    margin-bottom: 0;
    display: none;
    cursor: pointer;
  }

  label {
    position: relative;
    cursor: pointer;

    &:before {
      content: '';
      -webkit-appearance: none;
      background-color: transparent;
      border: 2px solid $check-grey;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05),
        inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
      padding: 6px;
      display: inline-block;
      position: relative;
      vertical-align: middle;
      cursor: pointer;
      margin-right: 5px;
      border-radius: 5px;
      width: 20px;
      height: 20px;
    }
  }
}

.label-check input:checked + label:after {
  content: '';
  display: block;
  position: absolute;
  top: 2px;
  left: 7px;
  width: 6px;
  height: 13px;
  border: solid $white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.label-check input:checked + label:before {
  background: $yellow;
  border-color: $yellow;
}

.h-90 {
  height: 90px !important;
}

.mr-10 {
  margin-right: 10px;
}

.pl-3 {
  padding-left: 3px;
}

@media (min-width: 735px) {
  .assurance-width {
    width: 70% !important;
  }
}

.project-info-lbl {
  margin-bottom: 10px;
}

.brd-radius-50 {
  border-radius: 50px !important;
}

.brd-radius-20 {
  border-radius: 20px !important;
}

.label-check {
  input {
    padding: 0;
    height: initial;
    width: initial;
    margin-bottom: 0;
    display: none;
    cursor: pointer;
  }

  label {
    position: relative;
    cursor: pointer;

    &:before {
      content: '';
      -webkit-appearance: none;
      background-color: transparent;
      border: 2px solid $check-grey;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05),
        inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
      padding: 10px;
      display: inline-block;
      position: relative;
      vertical-align: middle;
      cursor: pointer;
      margin-right: 5px;
      border-radius: 3px;
    }
  }
}

.label-check input:checked + label:after {
  content: '';
  display: block;
  position: absolute;
  top: 7px;
  left: 9px;
  width: 6px;
  height: 14px;
  border: solid $white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.label-check input:checked + label:before {
  background: $yellow;
  border-color: $yellow;
}

.row-infos {
  &:last-child {
    border: 0;
  }

  &.check-row {
    @media (min-width: 735px) {
      display: flex;
      align-items: center;
    }

    p {
      @media (min-width: 735px) {
        width: 40%;
      }
    }

    .checks-content {
      display: inline-flex;
      align-items: center;
      width: 100%;
      @media (max-width: 735px) {
        margin-left: 10px;
      }
      @media (max-width: 572px) {
        display: block;
      }

      .radio-box {
        margin-bottom: 0;
        display: flex;
        align-items: center;
        margin-right: 40px;

        .check-label {
          padding-left: 35px;
        }
      }

      .radio-box [type='radio']:checked ~ label:before,
      .radio-box [type='radio']:not(:checked) ~ label:before {
        right: 25px;
        border: 3px solid $yellow;
        width: 20px;
        height: 20px;
      }

      .radio-box [type='radio']:checked ~ label:after,
      .radio-box [type='radio']:not(:checked) ~ label:after {
        right: 30px;
        @media (min-width: 1200px) {
          right: 16px;
        }
      }

      .radio-box [type='radio']:checked ~ label,
      .radio-box [type='radio']:not(:checked) ~ label {
        display: inline-block !important;
        width: auto;
      }

      .radio-box [type='radio']:checked ~ label:after {
        top: -5px;
        width: 10px;
        height: 10px;
      }

      .radio-box [type='radio']:checked ~ label:before,
      .radio-box [type='radio']:not(:checked) ~ label:before {
        top: -10px;
      }
    }
  }

  .form-group {
    .form-control {
      @media (min-width: 768px) {
        min-width: 280px;
      }
    }

    .special-price {
      .form-control {
        @media (min-width: 768px) {
          min-width: 180px;
        }
      }
    }
  }
}

.p-10 {
  padding: 0 30px !important;
}

.text-justify {
  text-align: justify;
}

.mr-2 {
  margin-right: 10px;
}

.underline-red {
  text-decoration: underline;
  color: $red;
}

.container-card {
  -webkit-overflow-scrolling: auto !important;
}

.subTitle-modal {
  color: #42526b;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}

li.react-datepicker__time-list-item--selected {
  background-color: $yellow !important;
}

.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5); /* Optional dim effect */
  z-index: 9998;
  pointer-events: all; /* Prevent clicks */
}

.popup-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);

  @media (max-width: 768px) {
    padding: 15px;
    width: 90%;
    margin: 0 auto;

    h2 {
      font-size: 20px;
    }

    .flags {
      flex-direction: column;
      align-items: center;

      .flag {
        width: 100%;
        margin-bottom: 10px;
        padding: 15px;

        img {
          width: 50px;
        }

        span {
          font-size: 14px;
        }
      }
    }
  }
}

.flags {
  display: flex;
  justify-content: space-around;
  margin-top: 15px;
  flex-wrap: wrap;

  .flag {
    cursor: pointer;
    text-align: center;
    transition: transform 0.3s ease;
    background: linear-gradient(135deg, #ffad9846, #feb47b);
    border-radius: 100px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    opacity: 0;
    animation: fadeIn 0.5s forwards;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }

    img {
      width: 70px;
      height: auto;
      border-radius: 8px;
      transition: transform 0.3s ease;

      &:hover {
        transform: rotate(5deg);
      }
    }

    span {
      display: block;
      margin-top: 5px;
      font-size: 16px;
      font-weight: bold;
      color: #000;
      transition: color 0.3s ease;

      &:hover {
        color: #ff6347;
      }
    }
  }
}

.popup {
  position: fixed;
  z-index: 9999;
  pointer-events: all;
}
.icon {
  z-index: 100;
}
body.modal-open {
  pointer-events: none;
}

.popup-heading {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  transition: color 0.3s ease;

  &:hover {
    color: #ff6347;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.mobile-only {
  display: none !important; // Hide by default

  @media (max-width: 768px) {
    display: block !important; // Show on mobile
  }
}
