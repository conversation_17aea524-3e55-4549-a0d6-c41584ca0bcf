//go:build integration
// +build integration

package templatr

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGeneratePDF(t *testing.T) {
	t.<PERSON>llel()

	apiKey := os.<PERSON>env("TEMPLATR_API_KEY")
	assert.NotEmpty(t, apiKey)

	client := New(apiKey)
	assert.NotNil(t, client)

	ctx := context.Background()
	templateID := os.Getenv("TEMPLATR_TEMPLATE_ID")
	assert.NotEmpty(t, templateID)

	buf, err := client.GeneratePDF(ctx, templateID, map[string]any{
		"payment_date":   "01/10/2024",
		"tax_percentage": "20",
		"tax_value":      "100",
		"currency":       "$",
		"products": []map[string]any{
			{
				"name":         "Product 1",
				"qty":          2,
				"unit_price":   100,
				"total_amount": 14,
				"currency":     "$",
			},
		},
	})

	assert.NoError(t, err)

	os.Write<PERSON>ile("invoice.pdf", buf, 0644)

}
