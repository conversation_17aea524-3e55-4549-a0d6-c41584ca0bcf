package csvparser

import (
	"fmt"
	"io"

	"github.com/gocarina/gocsv"

	"github.com/vima-inc/derental/parser"
)

type client struct{}

// New creates a new csv parser.
func New() parser.Parser {
	return &client{}
}

// Parse parses a csv file.
func (c *client) Parse(reader io.Reader, out interface{}) error {
	err := gocsv.Unmarshal(reader, out)
	if err != nil {
		return fmt.Errorf("unable to parse csv: %w", err)
	}

	return nil
}
