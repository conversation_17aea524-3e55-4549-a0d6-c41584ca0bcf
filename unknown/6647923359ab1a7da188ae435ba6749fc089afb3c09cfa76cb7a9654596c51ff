import React, { useEffect, useMemo, useState } from 'react';
import { equipmentsDetails, pricesDetails } from '../../helpers/Data_helper';
import CustomButton from '../buttons/Custom_button';
import RequestDetail from '../cards/Request_details';
import RenderIf from '../Render_if';
import EquipmentsModal from './Equipments_modal';
import { useProjects } from '../../context/Project_context';

export default function BidzDetails({
  t,
  data,
  bidzModal,
  setBidzModal,
  acceptRequest,
  hasInventory,
  acceptBidzOffer,
  isOffer,
  detectLanguage,
  role
}) {
  const { GetProject } = useProjects();
  const [projectName, setProjectName] = useState('');
  const [price, setPrice] = useState({
    day: 0,
    week: 0,
    month: 0,
    delivery_drop_coast: 0,
    delivery_pickup_cost: 0
  });

  const equipments = useMemo(() => equipmentsDetails(t, data), [data, t]);
  const makeBidzOffer =
    role === 'equipper' &&
    data?.status === 'pending' &&
    !data?.price_per_day &&
    hasInventory === 'false';
  const isDisabled = !(price.day || price.week || price.month);

  const handleChange = (e) => {
    setPrice({
      ...price,
      [e.target.name]: e.target.value
    });
  };

  const acceptBidzRequest = () => {
    acceptRequest(data?.id, {
      ...data,
      price_per_day: parseFloat(price?.day),
      price_per_week: parseFloat(price?.week),
      price_per_month: parseFloat(price?.month),
      delivery_drop_coast: parseFloat(price?.delivery_drop_coast),
      delivery_pickup_cost: parseFloat(price?.delivery_pickup_cost)
    });
    setBidzModal(false);
  };

  useEffect(() => {
    async function getProjectName() {
      const response = await GetProject(data?.project_id);
      if (response.data) {
        setProjectName(response.data?.name);
      }
    }
    getProjectName();
  }, [data?.project_id]);

  const onConfirm = () => {
    switch (data?.status) {
      case 'pending':
        return role === 'equipper' && !isOffer
          ? acceptBidzRequest()
          : isOffer && role === 'lodger' && !data?.created_by_member
          ? acceptBidzOffer({
              ...data,
              currency: 'usd'
            })
          : setBidzModal(false);
      case 'accepted':
        return setBidzModal(false);
      case 'rejected':
        return setBidzModal(false);
      case 'canceled':
        return setBidzModal(false);
      case 'sent':
        return setBidzModal(false);
      default:
        return null;
    }
  };

  const textButton = makeBidzOffer
    ? t('Send_bid')
    : isOffer &&
      data?.status === 'pending' &&
      role === 'lodger' &&
      !data?.created_by_member
    ? t('Accept_booking')
    : t('Close_button');

  const priceDetails = useMemo(
    () =>
      pricesDetails(
        {
          ...data,
          ...data?.invoice
        },
        t
      ),
    [data, t]
  );

  return (
    <RenderIf condition={bidzModal}>
      <form className="form-group">
        <EquipmentsModal
          data={{
            ...data,
            projectName: projectName
          }}
          priceDetailsData={priceDetails}
          title={t('Quota_details')}
          formik={null}
          onClose={() => setBidzModal(false)}
          detectLanguage={detectLanguage}
          t={t}
          isEquipper={role === 'equipper'}
          isBidz
        >
          <div className="infos-equipments">
            <RequestDetail
              t={t}
              data={data}
              role={role}
              isEquipper={role === 'equipper'}
              isBidz
              priceDetails={priceDetails}
              equipments={equipments}
              makeBidzOffer={makeBidzOffer}
              handleChange={handleChange}
            />
          </div>

          <div className="text-center mt-4 fixed-button-modal">
            <CustomButton
              className={`round-button bold ${
                makeBidzOffer && isDisabled ? 'c-near-grey' : 'yellow'
              }`}
              onClick={() => onConfirm()}
              disabled={makeBidzOffer ? isDisabled : false}
              textButton={textButton}
              type={makeBidzOffer ? 'submit' : 'button'}
            />
          </div>
        </EquipmentsModal>
      </form>
    </RenderIf>
  );
}
