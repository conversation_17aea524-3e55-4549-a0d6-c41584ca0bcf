import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Formik, Field, Form, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { PHONE_NUMBER_REGEX } from '../../helpers/Regex';
import { handleKeyDown } from '../../helpers/Textarea';
import UploadImage from '../images/Upload_image';
import Modal from './Modal';
import OpenHoursModal from './Open_hours_modal';
import { categoriesOptions, isValid } from '../../helpers/Data_helper';
import GeoLocation from '../inputs/Geonames';
import cities from '../../helpers/Cities';
import { getCookies } from '../../helpers/Cookies';
import { userCurrency } from '../../helpers/Currency';
import { useLodger } from '../../context/Lodger_context';
import CustomButton from '../buttons/Custom_button';
import MultipleValueTextInput from 'react-multivalue-text-input';
import RenderIf from '../Render_if';
import { useTeamContext } from '../../context/Team_context';
import MultipleSelectCheckmarks from '../multi_select/Multi_select_mui';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle } from '@fortawesome/free-solid-svg-icons';
import CustomTooltip from '../tooltips/Tooltip';

export default function EditPersonalInfo({
  loadedInfo,
  updatePersonalInfo,
  onClose,
  workHours,
  isEquipper,
  uploadImage,
  setWorkHours,
  show,
  t
}) {
  const [showModal, setShowModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [members, setMembers] = useState([]);

  const { GetTeamLodger } = useTeamContext();
  const { existByEmail } = useLodger();

  const memberId = sessionStorage.getItem('member_id');
  const initialValues = {
    id: loadedInfo.id,
    type: loadedInfo.type,
    address: loadedInfo.address.address,
    zip_code: loadedInfo.address.zip_code,
    state: {
      label: loadedInfo.address.state,
      value: loadedInfo.address.state
    },
    full_name: loadedInfo.full_name,
    country: {
      label: loadedInfo.address.country,
      value: loadedInfo.address.country
    },
    currency: userCurrency(loadedInfo.address.country) || 'usd',
    city: {
      label: loadedInfo.address.city,
      value: loadedInfo.address.city
    },
    phone_number: loadedInfo.phone_number,
    user_name: loadedInfo.user_name,
    email: loadedInfo.email,
    description: loadedInfo.description,
    photo_url: loadedInfo.photo_url,
    workHours: loadedInfo.work_hours,
    company: loadedInfo.company,
    isEquipper: isEquipper,
    coverage_area: loadedInfo?.coverage_area,
    categories: loadedInfo?.categories,
    userType: loadedInfo.type,
    recipient_list: loadedInfo?.recipient_list || []
  };

  const validate = Yup.object({
    full_name: Yup.string().when('isEquipper', {
      is: (val) => val === false,
      then: Yup.string()
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        .required(t('This_field_is_required'))
    }),
    company:
      isEquipper || loadedInfo?.type === 'pro'
        ? Yup.string()
            .required(t('This_field_is_required'))
            .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
        : Yup.string(),
    address: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .required(t('This_field_is_required')),
    city: Yup.object().required(t('This_field_is_required')).nullable(),
    country: Yup.object().required(t('This_field_is_required')).nullable(),
    state: Yup.object().required(t('This_field_is_required')).nullable(),
    zip_code: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .required(t('This_field_is_required')),
    phone_number: Yup.string()
      .max(15, t('SignUp_phone_number_max_length'))
      .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
      .required(t('This_field_is_required')),
    email: Yup.string()
      .required(t('Email_required'))
      .email(t('Please_enter_a_valid_email'))
      .test('unique', t('Email_already_exists'), async (value) => {
        if (value !== loadedInfo.email) {
          const response = await existByEmail(value);
          return response.status !== 200;
        }
        return true;
      }),

    isEquipper: Yup.boolean(),
    userType: Yup.string(),
    categories: Yup.array().when('isEquipper', {
      is: (val) => val === true,
      then: Yup.array().required(t('This_field_is_required'))
    }),
    coverage_area: Yup.array().when('isEquipper', {
      is: (val) => val === true,
      then: Yup.array().required(t('This_field_is_required'))
    }),
    recipient_list: Yup.array()
      .nullable()
      .test('is-valid-array', (emails, context) => {
        if (!emails || emails.length === 0) return true;

        const errors = emails.reduce((acc, email) => {
          if (email === loadedInfo.email) {
            acc.push(`${t('Your_own_email_cannot_be_added')} (${email})`);
          } else if (!Yup.string().email().isValidSync(email)) {
            acc.push(email);
          }
          return acc;
        }, []);

        if (errors.length > 0) {
          return context.createError({
            message: `${t('Invalid_emails_found')} : ${errors.join(', ')} `,
            path: context.path
          });
        }

        return true;
      })
  });

  const update = async (values, setSubmitting) => {
    setIsLoading(true);
    await updatePersonalInfo({
      ...values,
      address: {
        address: values.address || '',
        zip_code: values.zip_code || '',
        city: values.city?.value || '',
        state: values.state?.value || '',
        country: values.country?.value || ''
      },
      currency: userCurrency(values.country?.value) || 'usd',
      categories: values.categories,
      coverage_area: values.coverage_area,
      communication_preferences: 'english',
      recipient_list: values?.recipient_list || [],
      work_hours: workHours
    });

    setSubmitting(false);
  };

  useEffect(async () => {
    if (memberId) {
      const {
        status,
        data: { admins, power_collaborators, collaborators }
      } = await GetTeamLodger();

      if (status === 200) {
        const team = [
          ...(admins?.filter(
            (member) =>
              member.membership_status !== 'owner' &&
              member.membership_status !== 'pending'
          ) || []),
          ...(power_collaborators?.filter(
            (member) => member.membership_status !== 'pending'
          ) || []),
          ...(collaborators?.filter(
            (member) => member.membership_status !== 'pending'
          ) || [])
        ];

        setMembers(
          team
            .filter((member) => member.email !== getCookies('email'))
            ?.map((member) => ({
              value: member.email,
              label: `${member.email} (${member.type})`
            }))
        );
      }
    }
  }, []);

  const onChange = (value, name, formik) => {
    formik.setFieldValue(name, value);
  };

  return (
    <>
      {show && (
        <Modal className="edit-personal" onClose={onClose} t={t}>
          <UploadImage
            data={loadedInfo}
            uploadImage={uploadImage}
            t={t}
            setShowModal={setShowModal}
            isEquipper={isEquipper}
          />

          {showModal && (
            <OpenHoursModal
              t={t}
              handleShowModal={() => setShowModal(false)}
              workHours={workHours}
              setWorkHours={setWorkHours}
            />
          )}
          <Formik
            initialValues={initialValues}
            onSubmit={(values, { setSubmitting }) =>
              update(values, setSubmitting)
            }
            validationSchema={validate}
          >
            {(formik) => {
              return (
                <Form>
                  <div className="form">
                    <div className="row">
                      {loadedInfo?.type !== 'private' && (
                        <div className={isEquipper ? 'col-lg-6' : 'col-lg-4'}>
                          <p className="form-group">
                            <label className="t-body-regular c-fake-black">
                              {t('Company_name')}{' '}
                              <span className="c-red star-required">*</span>
                            </label>
                            <Field
                              name="company"
                              type="text"
                              className={`form-control ${isValid(
                                formik,
                                'company'
                              )}`}
                              placeholder={t('Company_name')}
                            />
                            <ErrorMessage
                              name="company"
                              component="span"
                              className="error-message"
                            />
                          </p>
                        </div>
                      )}

                      {!isEquipper && (
                        <div
                          className={
                            loadedInfo?.type !== 'private'
                              ? 'col-lg-4'
                              : 'col-lg-6'
                          }
                        >
                          <p className="form-group">
                            <label className="t-body-regular c-fake-black">
                              {t('Person_name')}{' '}
                              <span className="c-red star-required">*</span>
                            </label>
                            <Field
                              name="full_name"
                              type="text"
                              className={`form-control ${isValid(
                                formik,
                                'full_name'
                              )}`}
                              placeholder={t('Person_name')}
                            />
                            <ErrorMessage
                              name="full_name"
                              component="span"
                              className="error-message"
                            />
                          </p>
                        </div>
                      )}
                      <div
                        className={
                          loadedInfo?.type === 'private'
                            ? 'col-lg-6'
                            : isEquipper
                            ? 'col-lg-6'
                            : 'col-lg-4'
                        }
                      >
                        <p className="form-group">
                          <label className="t-body-regular c-fake-black">
                            {t('Username')}{' '}
                          </label>
                          <Field
                            disabled
                            name="user_name"
                            type="text"
                            className="form-control"
                          />
                        </p>
                      </div>
                    </div>

                    <div className="mt-2 form-group">
                      <label className="t-body-regular c-fake-black">
                        {loadedInfo?.type === 'private'
                          ? t('Personal_address')
                          : t('Company_address')}
                      </label>
                      <GeoLocation
                        formik={formik}
                        t={t}
                        names={{
                          country: 'country',
                          state: 'state',
                          city: 'city',
                          address: 'address',
                          zip_code: 'zip_code'
                        }}
                        disabled
                        isEdit
                        initialValues={initialValues}
                      />
                    </div>
                    <div className="row mt-2">
                      <div className="col-lg-6">
                        <p className="form-group">
                          <label className="t-body-regular c-fake-black">
                            {t('Phone_number')}{' '}
                            <span className="c-red star-required">*</span>
                          </label>
                          <Field
                            name="phone_number"
                            type="text"
                            className={`form-control ${isValid(
                              formik,
                              'phone_number'
                            )}`}
                            placeholder={t('Phone_number')}
                          />
                          <ErrorMessage
                            name="phone_number"
                            component="span"
                            className="error-message"
                          />
                        </p>
                      </div>
                      <div className="col-lg-6">
                        <p className="form-group">
                          <label className="t-body-regular c-fake-black">
                            {t('Email_address')}{' '}
                            <span className="c-red star-required">*</span>
                          </label>
                          <Field
                            name="email"
                            type="text"
                            className={`form-control ${isValid(
                              formik,
                              'email'
                            )}`}
                            placeholder={t('Email_address')}
                          />
                          <ErrorMessage
                            name="email"
                            component="span"
                            className="error-message"
                          />
                        </p>
                      </div>
                    </div>

                    <RenderIf condition={!isEquipper}>
                      <div className="row mt-2">
                        <div className="col-lg-12">
                          <p className="form-group">
                            <label className="t-body-regular c-fake-black">
                              {t('Emails_notification_list')}{' '}
                            </label>
                            <MultipleSelectCheckmarks
                              options={members}
                              t={t}
                              name="recipient_list"
                              onChange={(value) =>
                                onChange(value, 'recipient_list', formik)
                              }
                              value={formik.values.recipient_list}
                              defaultValue={initialValues.recipient_list}
                              placeholder={t('Emails_notification_list')}
                            />
                          </p>
                        </div>
                      </div>
                    </RenderIf>
                    <RenderIf condition={isEquipper}>
                      <div className="row mt-2 ">
                        <div className="col-lg-12">
                          <p className="form-group">
                            <label className="label-input d-lg-block">
                              {t('Categories')}{' '}
                              <span className="c-red star-required">*</span>
                              <CustomTooltip
                                text={t('Categories_tooltip')}
                                placement="right"
                              >
                                <span className="c-near-grey m-1">
                                  <FontAwesomeIcon
                                    icon={faInfoCircle}
                                    className="c-near-grey"
                                  />
                                </span>
                              </CustomTooltip>
                            </label>

                            <MultipleSelectCheckmarks
                              options={categoriesOptions(t)}
                              t={t}
                              name="categories"
                              onChange={(value) =>
                                onChange(value, 'categories', formik)
                              }
                              value={formik.values.categories}
                              defaultValue={initialValues.categories}
                              placeholder={t('Categories')}
                            />
                          </p>
                        </div>
                      </div>

                      <div className="row mt-2 ">
                        <div className="col-lg-12">
                          <p className="form-group">
                            <label className="label-input d-lg-block">
                              {t('Coverage_area')}{' '}
                              <span className="c-red star-required">*</span>
                            </label>

                            <MultipleSelectCheckmarks
                              options={cities}
                              t={t}
                              name="coverage_area"
                              onChange={(value) =>
                                onChange(value, 'coverage_area', formik)
                              }
                              value={formik.values.coverage_area}
                              defaultValue={initialValues.coverage_area}
                              placeholder={t('Coverage_area')}
                            />
                          </p>
                        </div>
                      </div>

                      <div className="row mt-2 ">
                        <div className="col-lg-12">
                          <p className="form-group">
                            <label className="t-body-regular c-fake-black mb-2">
                              {t('Emails_notification_list')}{' '}
                            </label>

                            <MultipleValueTextInput
                              onItemAdded={(_, allItems) =>
                                formik.setFieldValue('recipient_list', allItems)
                              }
                              onItemDeleted={(_, allItems) =>
                                formik.setFieldValue('recipient_list', allItems)
                              }
                              name="recipient_list"
                              className="form-control "
                              values={formik.values?.recipient_list}
                              placeholder={t('Recipient_list_placeholder')}
                            />
                            <ErrorMessage
                              name="recipient_list"
                              component="span"
                              className="error-message"
                            />
                          </p>
                        </div>
                      </div>
                      <div className="row mt-2 ">
                        <div className="col-lg-12">
                          <p className="form-group">
                            <label className="t-body-regular c-fake-black">
                              {t('Company_spotlight_description')}{' '}
                              <span className="c-red star-required">*</span>
                            </label>
                            <Field
                              name="description"
                              as="textarea"
                              onKeyDown={(e) => {
                                handleKeyDown(e);
                              }}
                              className={`my-auto form-control ${isValid(
                                formik,
                                'description'
                              )}`}
                              placeholder={t('Company_spotlight_description')}
                            />
                            <ErrorMessage
                              name="description"
                              component="span"
                              className="error-message"
                            />
                          </p>
                        </div>
                      </div>
                    </RenderIf>
                    <div className="button text-center fixed-button-modal">
                      <CustomButton
                        type="submit"
                        isLoading={isLoading}
                        textButton={t('Update_button')}
                        className="round-button bold yellow "
                      />
                    </div>
                  </div>
                </Form>
              );
            }}
          </Formik>
        </Modal>
      )}
    </>
  );
}

EditPersonalInfo.propTypes = {
  loadedInfo: PropTypes.object,
  updatePersonalInfo: PropTypes.func,
  onClose: PropTypes.func,
  workHours: PropTypes.array,
  isEquipper: PropTypes.bool,
  uploadImage: PropTypes.func,
  setWorkHours: PropTypes.func,
  show: PropTypes.bool,
  t: PropTypes.func
};

EditPersonalInfo.defaultProps = {
  loadedInfo: {},
  workHours: [],
  isEquipper: false,
  show: false
};
