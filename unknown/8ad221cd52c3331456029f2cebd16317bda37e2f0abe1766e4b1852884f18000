import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import SignUpForm from '../../shared/components/forms/SignUp_form';
import SuccessPopUp from '../../shared/components/modals/Success_pop_up';
import { useAuth } from '../../shared/context/Auth_context';

export default function Signup({ showModal, t }) {
  const navigate = useNavigate();
  const { SignUp } = useAuth();
  const [error, setError] = useState(false);
  const [show, setShow] = useState(false);
  const [type, setType] = useState('');
  const [onAction, setOnAction] = useState('');
  const [successMessage, setSuccessMessage] = useState(false);

  const url = window.location.search;
  const Params = new URLSearchParams(url);
  const invitedMemberEmail = Params.get('email');
  const company = Params.get('company');
  const member_id = Params.get('member_id');
  const lodger_id = Params.get('lodger_id');
  const lodgerAddress = Params.get('lodgerAddress') || '';
  const [address, zip_code, city, state, country] = lodgerAddress?.split(';');

  const toHome = () => {
    navigate('/');
  };

  const goToLogin = () => {
    showModal();
    setShow(false);
    toHome();
  };

  
  const handleSubmit = async (event) => {
    setOnAction(true);
    const storage = localStorage.getItem('countries');
    const countries = JSON.parse(storage);
    const country = countries.find(
      (item) => item.value === event.country?.value
    );

    const user = {
      ...event,
      city: event.city?.value || '',
      full_name: event.full_name,
      state: event.state?.value || '',
      country: event.country?.value || '',
      country_code: country?.data?.country_code || '',
      coverage_area: [],
      categories: [],
      is_lodger: true,
      type: type || 'pro',
      communication_preferences: 'english'
    };
    setError('');

    const { error } = await SignUp(user, member_id, lodger_id);
    if (error) {
      setError(
        error.error.includes('failed to check member')
          ? 'Failed_to_check_member'
          : 'User_exist'
      );
      setOnAction(false);
    } else {
      setSuccessMessage(t('Signup_success'));
      setShow(true);
      setOnAction(false);
    }
  };

  return (
    <div className="register-container">
      <div className="register-image">
        <div className="container">
          <SuccessPopUp
            onClose={() => {
              setShow(false);
              toHome();
            }}
            closeIcon={() => setShow(false)}
            message={successMessage}
            show={show}
            buttonText={t('Login_button')}
            function={goToLogin}
          />
          <div className="row">
            <div className="col-lg-8 mx-auto">
              <h1 className="t-header-h5 c-fake-black">
                {t('Welcome_to_tooler_renter')}
              </h1>
              <p className="t-body-large c-fake-black">
                {t('Welcome_paragraph1')}
              </p>
            </div>
          </div>
          <div className="row">
            <div className="col-12">
              <SignUpForm
                handleSubmit={handleSubmit}
                invitedMember={{
                  member_id,
                  lodger_id,
                  invitedMemberEmail,
                  company,
                  address,
                  zip_code,
                  city,
                  state,
                  country
                }}
                t={t}
                setType={setType}
                onAction={onAction}
                error={error}
                type={type}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

Signup.propTypes = {
  showModal: PropTypes.func.isRequired,
  t: PropTypes.func.isRequired
};

Signup.defaultProps = {
  showModal: () => {},
  t: () => {}
};
