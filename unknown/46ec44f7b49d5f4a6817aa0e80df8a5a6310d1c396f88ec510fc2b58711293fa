import CreditOk from '../../../style/assets/img/credit_check_ok.svg';
import CustomLabel from '../../../shared/components/labels/Custom_label';
import NoResults from '../../search_result/No_results';
import { Dropdown } from 'react-bootstrap';
import RenderIf from '../../../shared/components/Render_if';
import CustomTooltip, {
  TOP
} from '../../../shared/components/tooltips/Tooltip';
import { getCookies } from '../../../shared/helpers/Cookies';
import { isEmpty } from 'lodash';
import CCAEmptyState from '../../../style/assets/img/empty_state/CCA_empty_state.svg';

const CreditApplicationTab = ({
  myCreditCheckForm,
  setSelectedCreditCheckForm,
  setIsForShow,
  switchPlace,
  setShowCreditFormModal,
  setShowConfirmationModal,
  t,
  userType
}) => {
  const currentUserID = getCookies('userId');
  return (
    <div className="white-bg credit-check-form-section">
      <div className="container">
        {myCreditCheckForm && !isEmpty(myCreditCheckForm) ? (
          <>
            <p className="t-header-h6 mb-4 c-primary-color d-lg-block d-none">
              {t('Saved_forms')}
            </p>
            <div className="row flex-lg-row flex-column-reverse">
              <div
                className={
                  userType === 'collaborator'
                    ? 'col-lg padding-l-0'
                    : 'col-lg-8 padding-l-0'
                }
              >
                {switchPlace && (
                  <CustomLabel
                    severity="info"
                    text={t('Upload_message')}
                    className="project-info-lbl"
                  />
                )}

                {myCreditCheckForm?.map((item, key) => (
                  <div className="credit-check-form-box" key={key}>
                    <div className="container">
                      <div className="row align-items-center">
                        <div className="col-xl-5 pad-l-mobile">
                          <div className="d-flex">
                            <img src={CreditOk} alt="credit check ok" />
                            <div className="credit-text">
                              <p className="t-base-medium bold c-primary-color">
                                {item.name.replaceAll('_', ' ')}
                              </p>
                              <p className="t-base-medium c-near-grey">
                                {t('Saved_on')} {item?.created_at?.slice(0, 10)}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="col-xl-7 pad-0">
                          <div className="d-flex box-right justify-content-end">
                            <RenderIf
                              condition={
                                !switchPlace ||
                                userType === 'collaborator' ||
                                !(
                                  (userType === 'admin' &&
                                    item?.created_by === currentUserID) ||
                                  getCookies('member').status === 'owner' ||
                                  (item?.created_by === currentUserID &&
                                    userType === 'power collaborator')
                                )
                              }
                            >
                              {item.credit_check_form_path !== '' && (
                                <CustomTooltip
                                  text={t('Download_credit_check_form')}
                                  placement={TOP}
                                >
                                  <a
                                    href={item.credit_check_form_path}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                  >
                                    <button
                                      className={
                                        'round-button yellow bold small c-primary-color '
                                      }
                                    >
                                      {t('Open_pdf')}
                                    </button>
                                  </a>
                                </CustomTooltip>
                              )}
                              <button
                                className="round-button black c-white small bold c-primary-color mr-2 "
                                onClick={() => {
                                  setSelectedCreditCheckForm(item);
                                  setIsForShow(true);
                                  setShowCreditFormModal(true);
                                }}
                              >
                                {t('View_details')}
                              </button>
                            </RenderIf>

                            <RenderIf
                              condition={
                                (userType === 'admin' &&
                                  item?.created_by === currentUserID) ||
                                getCookies('member').status === 'owner' ||
                                (item?.created_by === currentUserID &&
                                  userType === 'power collaborator')
                              }
                            >
                              <Dropdown>
                                <Dropdown.Toggle className="points-btn">
                                  ...
                                </Dropdown.Toggle>
                                <Dropdown.Menu>
                                  <RenderIf condition={switchPlace}>
                                    <Dropdown.Item
                                      onClick={() => {
                                        setSelectedCreditCheckForm(item);
                                        setIsForShow(true);
                                        setShowCreditFormModal(true);
                                      }}
                                    >
                                      {t('View_details')}
                                    </Dropdown.Item>
                                    {item.credit_check_form_path !== '' && (
                                      <Dropdown.Item
                                        href={item.credit_check_form_path}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                      >
                                        {t('Open_pdf')}
                                      </Dropdown.Item>
                                    )}
                                  </RenderIf>
                                  <RenderIf condition={!item.active_booking}>
                                    <Dropdown.Item
                                      onClick={() => {
                                        setSelectedCreditCheckForm(item);
                                        setShowConfirmationModal(true);
                                      }}
                                    >
                                      {t('Delete')}
                                    </Dropdown.Item>
                                  </RenderIf>

                                  <Dropdown.Item
                                    onClick={() => {
                                      setSelectedCreditCheckForm(item);
                                      setIsForShow(false);
                                      setShowCreditFormModal(true);
                                    }}
                                  >
                                    {t('Update')}
                                  </Dropdown.Item>
                                </Dropdown.Menu>
                              </Dropdown>
                            </RenderIf>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <RenderIf condition={userType !== 'collaborator'}>
                <div className="col-lg-4">
                  <div className="fill-credit">
                    <h4 className="t-header-h6 c-primary-color mb-4">
                      {t('Credit_para3')}
                    </h4>
                    <CustomTooltip
                      text={t('Create_new_credit_check_form')}
                      placement={TOP}
                    >
                      <button
                        className="round-button yellow bold c-primary-color"
                        onClick={() => {
                          setSelectedCreditCheckForm(null);
                          setIsForShow(false);
                          setShowCreditFormModal(true);
                        }}
                      >
                        {t('Credit_check_form')}
                      </button>
                    </CustomTooltip>
                  </div>
                </div>
              </RenderIf>
            </div>
          </>
        ) : (
          <NoResults
            message={t('You_have_no_credit_check_forms')}
            onClick={() => {
              setSelectedCreditCheckForm(null);
              setIsForShow(false);
              setShowCreditFormModal(true);
            }}
            buttonText={t('Credit_check_form')}
            image={CCAEmptyState}
          />
        )}
      </div>
    </div>
  );
};

export default CreditApplicationTab;
