/*
  • Company spotlight head
  ---------- ---------- ---------- ---------- ----------
*/

.company-spotlight {
  @media (min-width: 992px) {
    margin-bottom: 140px;
  }

  &--head {
    background: $white;
    margin-bottom: 10px;
    margin-top: 10px;

    @media (min-width: 768px) {
      margin-bottom: 0;
      margin-top: 0;
    }
    @media (min-width: 992px) {
      .border-r {
        position: relative;

        &:after {
          content: '';
          width: 1px;
          height: 113px;
          background: $border-grey;
          position: absolute;
          right: 4px;
          top: -5px;
        }
      }
      .border-l {
        position: relative;

        &:after {
          content: '';
          width: 1px;
          height: 113px;
          background: $border-grey;
          position: absolute;
          right: 4px;
          top: -30px;
        }
      }
    }

    .top-row {
      padding: 16px 0px 16px 0px;
    }

    @media (min-width: 992px) {
      /* &_title {
         //width: calc(100% - 165px);
         position: relative;

       /*&:after {
           content: "";
           width: 1px;
           height: 113px;
           background: $border-grey;
           position: absolute;
           right: -15px;
           top: -15px;
         }
      }
      .border-right {
        position: relative;

        &:after {
          content: "";
          width: 1px;
          height: 113px;
          background: $border-grey;
          position: absolute;
          right: -40px;
          top: -15px;
        }
      }*/
    }

    .spotlight-title {
      @media (min-width: 992px) {
        display: flex;
        align-items: end;
      }

      h2 {
        margin-top: 10px;
        margin-bottom: 5px;

        @media (min-width: 992px) {
          margin-top: 0;
        }
      }

      .rate {
        margin-bottom: 15px;

        @media (min-width: 992px) {
          margin-left: 10px;
          margin-bottom: 0;
        }

        svg {
          font-size: 18px;
          color: $yellow;
          width: 20px;

          @media (min-width: 992px) {
            font-size: 25px;
            width: 30px;
          }
        }

        span {
          margin-left: 10px;
        }

        p {
          margin-bottom: 0;
        }
      }
    }

    .top-company-spotlight {
      .rate {
        margin-bottom: 15px;

        @media (min-width: 992px) {
          margin-bottom: 0;
        }

        svg {
          font-size: 18px;
          color: $yellow;
          width: 20px;

          @media (min-width: 992px) {
            font-size: 25px;
            width: 20px;
          }
        }

        span {
          margin-left: 10px;
        }

        p {
          margin-bottom: 0;
        }
      }
    }

    .image-head {
      @media (max-width: 992px) {
        width: 100%;
        max-height: 100px;
        border-radius: 30px;
        object-fit: contain;
      }
    }

    .content-icons {
      display: flex;
      align-items: center;
      justify-content: end;

      @media (max-width: 992px) {
        position: absolute;
        top: 5px;
        right: 5px;
      }

      .icon {
        width: 35px;
        height: 35px;
        background: $white;
        box-shadow: 0 3px 20px $grey;
        border-radius: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        margin-bottom: 5px;

        @media (min-width: 992px) {
          width: 50px;
          height: 50px;
          margin-bottom: 0;
        }

        img {
          max-width: 18px;

          @media (min-width: 992px) {
            max-width: 30px;
          }
        }
      }
    }

    .info-details {
      .infos {
        margin-bottom: 10px;
        align-items: center;

        @media (min-width: 992px) {
          margin-bottom: 16px;
        }

        &:last-child {
          margin-bottom: 0;
        }

        p {
          margin-left: 8px;
          margin-bottom: 0;

          strong {
            position: relative;
          }
        }

        svg {
          font-size: 24px;
        }

        img {
          height: max-content;
          max-width: 24px;

          &.location-icon {
            max-width: 24px;
          }
        }

        &--content {
          .link {
            text-decoration: underline;

            &:hover {
              text-decoration: none;
            }

            svg {
              position: relative;
              top: 3px;
              font-size: 20px;
            }
          }
        }
      }
    }

    .spotlight-head-text {
      p {
        margin-bottom: 20px;
        font-weight: 400;

        @media (max-width: 768px) {
          font-size: 15px;
        }
      }

      ul {
        li {
          @media (max-width: 768px) {
            font-size: 15px;
          }
        }
      }
    }

    .info-box {
      background: transparent linear-gradient(77deg, #ffffff 0%, #f1f1f1 100%)
        0% 0% no-repeat padding-box;
      border-radius: 37px;
      padding: 25px 15px;
      margin-bottom: 10px;

      .spotlight-title {
        .rate {
          svg {
            font-size: 15px;

            @media (min-width: 992px) {
              font-size: 20px;
            }
          }
        }
      }

      &.user-infos {
        min-height: 140px;
        display: flex;
        align-items: center;

        h3,
        p {
          @media (max-width: 992px) {
            font-size: 16px;
          }
        }

        p {
          margin-bottom: 0;
        }
      }

      .avatar {
        width: 45px;
        height: 45px;
        border-radius: 45px;
        margin-right: 5px;
        object-fit: cover;
      }

      &--text {
        width: 50%;
        margin-bottom: 0;
      }
    }

    .hightlights {
      margin-top: 15px;

      h3 {
        margin-bottom: 20px;

        @media (min-width: 992px) {
          margin-bottom: 45px;
        }
      }
    }

    .icon-hitlights {
      margin-right: 18px;

      @media (max-width: 992px) {
        max-width: 35px;
      }
    }

    &_image {
      position: relative;
      max-width: 100%;

      @media (min-width: 572px) {
        max-width: 250px;
      }

      @media (min-width: 992px) {
        height: 100%;
        display: flex;
        align-items: center;
        border-radius: 30px;
        max-width: max-content;
      }

      .image-head {
        border-radius: 100px;
        width: 110px;
        margin-right: 20px;
        border: 1px solid $neutrals-lightest-gray;
        height: 110px;
        object-fit: contain;
      }
    }

    .rate {
      p {
        display: flex;
        align-items: center;
        font-size: 14px;

        span {
          margin-left: 8px;
        }
      }

      svg {
        width: 20px;
      }
    }

    .container {
      @media (max-width: 992px) {
        padding-right: 0;
        padding-left: 0;
      }
    }

    .container-spotlight-head {
      @media (min-width: 1200px) {
        max-width: 1225px;
        margin: 0 auto;
      }
      @media (min-width: 1300px) {
        max-width: 1245px;
        margin: 0 auto;
      }
    }
  }

  &--equipement,
  &--similarProduct {
    &.result-search {
      margin-top: 0;
      margin-bottom: 45px;
    }

    .result-box {
      padding: 10px;

      .top-content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;

        .result-box__top {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: center;
          height: 100%;

          @media (max-width: 992px) {
            flex-direction: column-reverse;
            align-items: center;
            .button {
              position: static;
            }
          }

          @media (max-width: 992px) {
            .button-content {
              width: 100%;

              button {
                width: 100%;
              }
            }
          }
        }

        .catalogue-prices {
          @media (max-width: 992px) {
            width: 100%;
          }

          &--content {
            @media (max-width: 992px) {
              text-align: center;
            }
          }
        }

        .link {
          text-decoration: underline;
          cursor: pointer;

          &:hover {
            text-decoration: none;
          }
        }
      }

      .result-left-bottom {
        @media (max-width: 992px) {
          margin-top: 0;
        }
      }
    }
  }

  &--similarProduct {
    .similarProduct-box {
      padding: 16px 16px;
      border-radius: 7px;
      border: 1px solid var(--Stroke, #e5ecf6);
      background: $white;
      box-shadow: 0 0px 11px 0px #061c3d04;

      .content-box {
        text-align: center;

        h3 {
          margin: 15px 0 0;
        }

        p {
          margin-top: 8px;
          margin-bottom: 20px;
        }
      }

      .round-button {
        min-width: auto;
      }

      p {
        @media (min-width: 992px) {
          min-height: 28px;
        }
      }
    }

    .similarProduct-box img {
      height: 100px;
      width: auto;
      border-radius: 15px;
      background-color: $white;

      @media (min-width: 992px) {
        height: auto;
        max-width: 50%;
        max-height: 150px;
      }
    }

    @media (min-width: 992px) {
      .equipper-spotlight-cls {
        .result-box {
          .top-content {
            .result-box__top {
              flex-direction: column-reverse;
            }
          }

          &__image {
            img {
              border: 0;
            }
          }

          .result-left-bottom {
            p {
              font-size: 12px;
            }
          }

          &.with-border {
            height: 100%;
          }
        }

        .catalogue-prices {
          width: 100%;
        }

        .button-content {
          width: 100%;

          button {
            width: 100%;
          }
        }

        .align-items-lg-start {
          align-items: center !important;
        }

        .u-margin-b-10 {
          margin-bottom: 10px;
        }

        .modal {
          .result-box {
            &.with-border {
              border: 1px solid $border-grey;
              height: auto;
              min-height: auto;
            }
          }
        }
      }
    }
  }

  &--equipement {
    @media (min-width: 992px) {
      .result-left-bottom {
        min-height: 60px;
      }
    }
  }

  &--feedback {
    margin-top: 25px;

    @media (min-width: 992px) {
      margin-top: 45px;
    }

    .feedback-box {
      padding: 25px 35px;
      background: $white;
      border-radius: 28px;

      h2 {
        margin-bottom: 20px;

        @media (min-width: 992px) {
          margin-bottom: 60px;
        }
      }

      meter#diskE::-webkit-meter-optimum-value {
        background: #fbb03b;
      }

      meter#diskS::-webkit-meter-optimum-value {
        background: #b0d1fc;
      }

      meter#diskD::-webkit-meter-optimum-value {
        background: #afebac;
      }

      meter::-webkit-meter-bar {
        background: #f1f1f1;
        border-color: #f1f1f1;
      }

      meter {
        width: 200px;
        height: 22px;

        @media (min-width: 572px) {
          width: 280px;
          height: 32px;
        }
      }

      .meter-box {
        margin-bottom: 10px;

        h3 {
          margin-bottom: 0;

          @media (min-width: 992px) {
            margin-bottom: 12px;
          }
        }

        span {
          margin-left: 10px;
        }
      }

      .opinion {
        margin-top: 35px;

        @media (min-width: 992px) {
          margin-top: 55px;
        }

        &--title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20px;

          span {
            text-decoration: underline;
            cursor: pointer;

            @media (max-width: 572px) {
              display: none;
            }

            &:hover {
              text-decoration: none;
            }
          }
        }

        &--box {
          display: flex;
          align-items: center;
          margin-bottom: 20px;

          span {
            text-decoration: underline;
            cursor: pointer;

            @media (max-width: 572px) {
              display: none;
            }

            &:hover {
              text-decoration: none;
            }
          }
        }

        .user-box {
          display: flex;
          padding: 25px 23px;
          background: transparent
            linear-gradient(67deg, #ffffff 0%, #f1f1f1 100%) 0 0 no-repeat
            padding-box;
          border-radius: 28px;
          height: 100%;
          position: relative;

          img {
            width: 50px;
            height: 50px;
            border-radius: 50px;
            margin-right: 13px;
            object-fit: cover;
          }

          &--right {
            width: 80%;

            h3,
            p {
              @media (max-width: 992px) {
                font-size: 15px;
              }
            }

            p {
              @media (max-width: 992px) {
                margin-bottom: 10px;
              }
            }

            .date {
              font-size: 14px;
              font-weight: 700;

              @media (max-width: 992px) {
                position: absolute;
                bottom: 0;
                right: 20px;
                font-size: 12px;
                color: $near-grey;
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }

  .title-search {
    @media (max-width: 992px) {
      margin-bottom: 15px;
    }
  }

  &--similarProduct {
    .title-search {
      margin-bottom: 0;
      margin-top: 20px;
      text-align: center;
    }

    .company-spotlight--search {
      @media (min-width: 992px) {
        padding-top: 0;
      }
    }
  }

  &--search {
    margin-bottom: 0;
    align-items: center;
    padding-top: 0;

    @media (min-width: 992px) {
      display: flex;
      padding-top: 55px;
    }

    h2 {
      margin-bottom: 0;
      padding: 0;

      @media (max-width: 992px) {
        display: none;
      }
    }

    .filter-search {
      position: relative;

      @media (max-width: 992px) {
        margin-top: 15px;
      }

      .submit-search {
        position: absolute;
        top: 5px;
        right: 7px;
        border-radius: 30px;
        width: 30px;
        height: 30px;
        background: $yellow;

        @media (min-width: 992px) {
          width: 35px;
          height: 35px;
          top: 14px;
          background: $light-grey;
        }

        svg {
          width: 16px;
          height: 16px;
          color: $black;

          @media (min-width: 992px) {
            width: 18px;
            height: 18px;
            color: $near-grey;
          }
        }
      }

      .form-control {
        min-width: 100%;
        height: 40px;

        @media (min-width: 572px) {
          min-width: 420px;
        }

        @media (min-width: 992px) {
          margin-left: 35px;
          height: 63px;
        }

        &::-webkit-input-placeholder {
          /* Edge */
          font-size: 16px;
          line-height: 146%;

          @media (min-width: 768px) {
            font-size: 20px;
          }
        }

        &:-ms-input-placeholder {
          /* Internet Explorer 10-11 */
          font-size: 16px;
          line-height: 146%;

          @media (min-width: 768px) {
            font-size: 20px;
          }
        }

        &::placeholder {
          font-size: 16px;
          line-height: 146%;

          @media (min-width: 768px) {
            font-size: 20px;
          }
        }
      }
    }
  }

  //breacrumb remove mobile
  .breadcrumb {
    @media (max-width: 992px) {
      display: none;
    }
  }
}

.available_inventory_right {
  .breadcrumb {
    .container {
      @media (min-width: 992px) {
        padding-left: 0;
      }
    }
  }
}

.equipper-spotlight-cls,
.available_inventory_right {
  .company-spotlight--head {
    @media (min-width: 768px) {
      border-bottom: 1px solid $border-grey;
    }

    @media (min-width: 1300px) {
      margin: 0px 100px;
    }

    .email-address {
      margin-bottom: 10px;
    }
  }

  .result-box {
    &__image {
      img {
        height: auto;
        @media (min-width: 992px) {
          height: 150px;
        }
      }
    }

    &.with-border {
      border-radius: 12px;
      border: 1px solid $border-grey;
      margin-bottom: 4px;
    }
  }

  .sub-category__items {
    ul {
      display: flex;
      padding-left: 0;

      li {
        font-family: $primary-font;
        font-weight: normal;
        font-size: 16px;
        line-height: 24px;

        min-width: fit-content;
        color: $blue-grey;
      }
    }
  }
}

.company-spotlight--similarProduct {
  .available_inventory_right {
    .similarProduct-box {
      border-radius: 12px;
      border: 1px solid $border-grey;

      img {
        height: 150px;
      }
    }
  }
}

.equipper-spotlight {
  .company-spotlight {
    &--head {
      @media (min-width: 992px) {
        margin-bottom: 30px;
      }
    }

    &--similarProduct {
      margin-top: 0;
    }
  }
}

details > summary {
  list-style: none;
}

details > summary::marker,
  /* Latest Chrome, Edge, Firefox */
details > summary::-webkit-details-marker

  /* Safari */ {
  display: none;
}

.filter-category {
  margin-bottom: 20px;

  .icon-details {
    width: 25px;
    height: 25px;
    background: #ffffff 0 0 no-repeat padding-box;
    box-shadow: 0 3px 20px #00000029;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50px;

    @media (min-width: 1200px) {
      width: 30px;
      height: 30px;
    }

    &.minus {
      display: none;
    }

    svg {
      font-size: 12px;

      @media (min-width: 992px) {
        font-size: 16px;
      }
    }
  }

  .menu-accordion {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;

    @media (min-width: 992px) {
      font-size: 20px;
    }
  }

  .accordionDrop {
    border-left: 1px solid $border-grey;
    border-right: 1px solid $border-grey;
    @media (min-width: 992px) {
      border-left: 0;
    }

    ul {
      padding-left: 0;

      li {
        text-align: left;
        padding: 19px 20px;
        border-bottom: 1px solid $border-grey;
        display: flex;
        align-items: center;

        &:first-child {
          border-top: 1px solid $border-grey;
        }

        &:hover {
          background: $yellow;
          color: $white;
          cursor: pointer;

          a {
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;

            &:before {
              content: '';
              display: inline-block;
              width: 10px;
              height: 38px;
              background: $yellow;
              border-radius: 8px;
              margin-right: 30px;
            }
          }
        }

        &:before {
          width: 24px;
          height: 24px;
          margin-right: 20px;
        }

        &.Elevation_and_scaffolding,
        &.Élévation_et_échafaudage {
          &:before {
            content: url('../style/assets/img/company_spotlight/scaffolding.svg');
          }

          &:hover,
          &.menu-active {
            &:before {
              content: url('../style/assets/img/company_spotlight/scaffolding-white.svg');
            }
          }
        }

        &.Earthmoving,
        &.Excavation {
          &:before {
            content: url('../style/assets/img/company_spotlight/excavator-black.svg');
          }

          &:hover,
          &.menu-active {
            &:before {
              content: url('../style/assets/img/company_spotlight/excavator-white.svg');
            }
          }
        }

        &.Energy_and_air,
        &.Énérgie_et_air {
          &:before {
            content: url('../style/assets/img/company_spotlight/pipe.svg');
          }

          &:hover,
          &.menu-active {
            &:before {
              content: url('../style/assets/img/company_spotlight/pipe-white.svg');
            }
          }
        }

        &.Handling,
        &.Manutention {
          &:before {
            content: url('../style/assets/img/company_spotlight/bulldozer.svg');
          }

          &:hover,
          &.menu-active {
            &:before {
              content: url('../style/assets/img/company_spotlight/bulldozer-white.svg');
            }
          }
        }

        &.Landscaping,
        &.Aménagement_paysager {
          &:before {
            content: url('../style/assets/img/company_spotlight/shovel.svg');
          }

          &:hover,
          &.menu-active {
            &:before {
              content: url('../style/assets/img/company_spotlight/shovel-white.svg');
            }
          }
        }

        &.Specialized_tooling,
        &.Outillage_spécialisé {
          &:before {
            content: url('../style/assets/img/company_spotlight/tool_box.svg');
          }

          &:hover,
          &.menu-active {
            &:before {
              content: url('../style/assets/img/company_spotlight/tool_box-white.svg');
            }
          }
        }

        &.Fluid_solutions,
        &.Gestion_de_fluides {
          &:before {
            content: url('../style/assets/img/company_spotlight/pipe.svg');
          }

          &:hover,
          &.menu-active {
            &:before {
              content: url('../style/assets/img/company_spotlight/pipe-white.svg');
            }
          }
        }

        &.Event_and_reception,
        &.Événement_et_réception {
          &:before {
            content: url('../style/assets/img/company_spotlight/Cabañas.svg');
          }

          &:hover,
          &.menu-active {
            &:before {
              content: url('../style/assets/img/company_spotlight/Cabañas-white.svg');
            }
          }
        }

        &.Site_solution,
        &.Solution_de_chantier {
          &:before {
            content: url('../style/assets/img/company_spotlight/house.svg');
          }

          &:hover,
          &.menu-active {
            &:before {
              content: url('../style/assets/img/company_spotlight/house-white.svg');
            }
          }
        }

        &.Waste_solution,
        &.Gestion_des_déchets {
          &:before {
            content: url('../style/assets/img/company_spotlight/dump_truck.svg');
          }

          &:hover,
          &.menu-active {
            &:before {
              content: url('../style/assets/img/company_spotlight/dump_truck-white.svg');
            }
          }
        }
      }
    }
  }
}

.width-filter-category {
  @media (min-width: 1300px) {
    width: 320px;
  }
}

.width-lg-screen {
  @media (min-width: 1300px) {
    width: calc(100% - 355px);
  }

  .overflow_categories_row {
    @media (max-width: 992px) {
      overflow-x: inherit;
    }
  }
}

.available_inventory_right,
.equipper-spotlight-cls {
  .sub-category__items {
    ul {
      margin-bottom: 0;

      li {
        margin-bottom: 0;

        span {
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px; /* 150% */
        }
      }
    }
  }

  .swiper-home {
    position: relative;
    padding-left: 25px;

    div.scrollmenu {
      max-width: 92%;
    }

    // large screen
    @media (min-width: 992px) {
      max-width: 100%;
      padding-left: 14px;
    }

    // .chevron-right,
    // .chevron-left {
    //   svg {
    //     display: none;
    //   }

    //   &:after {
    //     content: url("../style/assets/img/Icons/chevron-available-inventory.svg");
    //   }
    // }

    // .chevron-left {
    //   &:after {
    //     transform: rotate(-180deg);
    //     margin-left: 0;
    //     position: absolute;
    //     top: 0;
    //     left: 0px;
    //   }
    // }
  }
}

.sub-category__items {
  ul {
    li {
      display: inline-block;
      padding: 10px 16px;
      border: 1px solid $gray-white;
      border-radius: 6px;
      margin-right: 12px;
      margin-bottom: 12px;
      cursor: pointer;

      &.menu-active,
      &:hover {
        border-color: $yellow;
        background: rgba(236, 168, 105, 0.2);
        color: $fake-black;
        font-weight: normal;
      }
    }
  }
}

details[open] {
  .icon-details {
    &.minus {
      display: inline-flex;
    }

    &.plus {
      display: none;
    }
  }
}

@media (max-width: 768px) {
  .bg-mobile {
    background: $white;
  }

  .border-mobile-top {
    border-radius: 30px 30px 0 0;

    .info-details {
      @media (max-width: 992px) {
        margin-bottom: 10px;
      }
    }
  }

  .border-mobile-bottom {
    border-radius: 0 0 30px 30px;
  }
}

.menu-active {
  color: $white;
  background: $yellow;
}

.uic-HorizontalSlider-item {
  padding-right: 20px;
}

.search-inventory {
  padding-right: 0;

  @media (min-width: 992px) {
    margin-left: 10px;
  }

  .ais-SearchBox .ais-SearchBox-form {
    width: calc(100% - 10px);
    @media (min-width: 992px) {
      width: calc(100% - 35px);
    }
  }

  button.ais-SearchBox-reset {
    right: 50px;
  }
}

.ais-SearchBox {
  width: 100% !important;

  @media (min-width: 572px) {
    width: calc(100% - 12px);
  }

  .ais-SearchBox-form {
    background: transparent;
    height: 54px;

    &:before {
      background: url('../style/assets/img/Icons/search.svg');
      left: auto;
      right: 16px;
      width: 30px;
      height: 30px;
      background-repeat: no-repeat;
      background-position: center;
      border-radius: 7px;
      margin-top: 0;
      top: calc(50% - 15px);
    }
  }

  .ais-SearchBox-input {
    border-radius: 6px;
    border: 1px solid #e3e5e9;
    color: $neutrals-gray;
    box-shadow: none;
    padding: 18px 12px;
    font-size: 16px;
    line-height: 24px;

    &::placeholder {
      /* Chrome, Firefox, Opera, Safari 10.1+ */
      color: $neutrals-gray;
      font-size: 16px;
      line-height: 24px;
    }

    &:-ms-input-placeholder {
      /* Internet Explorer 10-11 */
      color: $neutrals-gray;
      font-size: 16px;
      line-height: 24px;
    }

    &::-ms-input-placeholder {
      /* Microsoft Edge */
      color: $neutrals-gray;
      font-size: 16px;
      line-height: 24px;
    }
  }
}

.p-lrm-12 {
  @media (max-width: 992px) {
    padding: 0 12px;
  }
}

.bidz-spotlight-head {
  font-weight: bold;
  line-height: 1.6;
}

.img-box {
  display: flex !important;
  justify-content: center !important;
}

@media (min-width: 992px) {
  .lg-absolute {
    position: relative;
    bottom: 0;
  }
}

// mobile screen
@media (max-width: 992px) {
  .me-3 {
    margin-right: 7px !important;
  }
}

.minimum_rental_period {
  border: 1px solid $border-grey;
  border-radius: 7px;
  padding: 7px 10px;
  width: fit-content;
  margin-top: 20px;
}

.button-content {
  display: flex;
  flex-direction: row; // Default to horizontal layout for larger screens
  justify-content: space-between; // Space them out evenly
  gap: 10px; // Space between buttons
  @media (max-width: 1270px) {
    flex-direction: column; // Stack buttons vertically for better layout
  }

  button {
    width: 100%; // Full width for all screens
    height: 40px; // Adjusted height for better display
    margin-bottom: 10px; // Added margin for spacing between buttons on mobile
  }
}
