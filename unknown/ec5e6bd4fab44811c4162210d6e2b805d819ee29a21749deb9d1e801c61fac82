.margin-top--20 {
  margin-top: -20px;
}

.ais-CurrentRefinements-item {
  width: inherit;
  border-radius: 28px;
  min-height: 40px;
  height: inherit;
  background-color: white;
  border: 2px solid $primary-color;
  color: $primary-color;
  font-size: 14px;
  margin-right: 5px;
  justify-content: space-between;
  font-weight: bold;
}

.ais-CurrentRefinements-item svg {
  margin-left: 15px;
}

.ais-CurrentRefinements-item:hover {
  color: $primary-color;
  background-color: $white;
  outline: none !important;
  border: 2px solid $primary-color;
  box-shadow: none;
}

.ais-CurrentRefinements-item:focus {
  outline: none !important;
  color: $primary-color;
  background-color: $light-grey;
  border: 2px solid $primary-color;
  box-shadow: none;
}

.ais-CurrentRefinements-item:active {
  outline: none !important;
  color: $primary-color;
  background-color: $white;
  border: 2px solid $primary-color;
  box-shadow: none;
}

.ais-CurrentRefinements-item::after {
  color: $primary-color;
  background-color: $white;
  outline: none !important;
  border: 2px solid $primary-color;
  box-shadow: none;
}

.ais-CurrentRefinements-item::content {
  color: $primary-color;
  background-color: white;
  outline: none !important;
  border: 2px solid $primary-color;
  box-shadow: none;
}

.ais-CurrentRefinements-item::selection {
  color: $primary-color;
  background-color: white;
  outline: none !important;
  border: 2px solid $primary-color;
  box-shadow: none;
}

/************************************* Integrateur / Pre-Dev Css "Filter.scss" File ***************************************/

/*
  • Sort by
  • Filter by
  • Result search
  • Accordion
  • Filter accessory
  • Load more and pagination
  ---------- ---------- ---------- ---------- ----------
*/

/*
  • Sort by
  ---------- ---------- ---------- ---------- ----------
*/

.sort-by {
  margin-bottom: 20px;
  margin-top: 25px;
  @media (min-width: 992px) {
    margin-bottom: 40px;
  }

  span {
    padding-right: 10px;
  }

  .filter-btn {
    font-weight: 800;
    border: 2px solid $yellow;
    border-radius: 35px;
    padding: 0 10px;
    display: inline-flex;
    min-width: 95px;
    height: 30px;
    align-items: center;
    justify-content: center;
    @media (min-width: 768px) {
      height: 50px;
      min-width: 136px;
    }

    i {
      padding-right: 10px;
    }
  }
}

/*
  • Filter by
  ---------- ---------- ---------- ---------- ----------
*/

.filter-by {
  span {
    margin-right: 10px;
  }

  .filter {
    border: 2px solid $primary-color;
    border-radius: 25px;
    padding: 10px;
    @media (max-width: 768px) {
      min-width: max-content;
      width: 100%;
    }

    i {
      margin-left: 10px;
    }
  }

  &--row {
    @media (max-width: 768px) {
      flex-wrap: nowrap;
    }
  }

  &--content {
    @media (max-width: 768px) {
      display: flex;
      justify-content: space-between;
      overflow: hidden;
      overflow-x: scroll;
    }
  }

  .ais-CurrentRefinements-delete {
    color: $primary-color;
  }
}

.title-filter {
  cursor: pointer;
  min-width: 75px;
  font-size: 16px !important;
}

/*
  • Result search
  ---------- ---------- ---------- ---------- ----------
*/

.title-search {
  margin-bottom: 10px;
  @media (min-width: 992px) {
    margin-top: 22px;
  }
}

.result-search,
.company-spotlight--similarProduct {
  &.new-search {
    margin-top: 0;

    .result-box {
      &__image {
        padding: 0;

        img {
          width: 77px;
          height: 77px;
          border: 1px solid $neutrals-lightest-gray;
          border-radius: 100%;
          margin-right: 20px;
          @media (max-width: 992px) {
            max-height: 100px;
          }
        }
      }

      .equipper-box-text {
        @media (max-width: 992px) {
          padding-left: 0;
        }
      }
    }

    .breadcrumb {
      .container {
        padding: 0;
      }
    }
  }

  .text {
    font-weight: 800;

    span {
      font-weight: 500;
    }
  }

  .result-box {
    border-radius: 16px;
    padding: 15px 20px;
    margin-bottom: 10px;
    width: 100%;
    border: 1px solid $border-grey;
    @media (min-width: 992px) {
      padding: 20px;
    }

    &__image {
      img {
        object-fit: contain;
        display: block;
        @media (max-width: 480px) {
          width: 100%;
        }
      }
    }

    &__bottom {
      justify-content: space-between;
      @media (min-width: 572px) {
        display: flex;
      }
    }

    &__top {
      justify-content: space-between;
      @media (max-width: 992px) {
        margin-top: 10px;
      }

      h2 {
        height: max-content;
        margin-top: 12px;
        margin-bottom: 18px;
      }

      p {
        margin-bottom: 8px;
        margin-top: 0;
      }
    }

    .result-left-bottom {
      p {
        margin-bottom: 10px;
        font-size: 12px;
      }
    }

    &.with-border {
      border: 1px solid $border-grey;
      @media (max-width: 992px) {
        border: 0;
      }

      .container {
        @media (max-width: 992px) {
          padding: 0;
        }
      }
    }

    &__bottom {
      height: max-content;
      margin-top: 0;
      @media (min-width: 572px) {
        margin-top: 16px;
        margin-bottom: 16px;
      }
    }

    &__rate {
      p {
        line-height: normal;
        margin-bottom: 0;

        svg {
          @media (max-width: 992px) {
            width: 15px;
          }
        }
      }

      i {
        svg {
          color: $yellow;
        }
      }
    }

    .bottom-content {
      button {
        @media (max-width: 572px) {
          width: 100%;
        }
      }

      span {
        display: block;
        text-decoration: underline;
        margin-bottom: 20px;
        text-align: right;
        cursor: pointer;

        &:hover {
          text-decoration: none;
        }
      }
    }

    .vertically-space {
      @media (min-width: 992px) {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
  }
}

.result-search {
  &.new-search {
    padding-top: 20px;
    @media (min-width: 992px) {
      padding-left: 10px;
    }

    .result-box {
      box-shadow: 0 11px 40px 0 rgba(6, 28, 61, 0.07);

      &__image {
        img {
          @media (max-width: 1200px) {
            margin: 0 0 10px 0;
          }
          @media (max-width: 992px) {
            margin-right: 20px;
          }
        }
      }
    }
  }

  .breadcrumb {
    padding: 0px;
  }
}

.results {
  .bottom-content {
    .location-icon {
      margin-right: 10px;
    }
  }

  .result-box {
    padding: 20px 10px;
    margin-top: 20px;

    &__rate {
      p {
        svg {
          color: $yellow;
        }

        span {
          margin-left: 5px;
        }
      }
    }

    .bottom-content {
      span {
        text-align: left;
        margin-top: 13px;
        margin-bottom: 0;
      }
    }
  }

  .load-more {
    margin-bottom: 50px;
    @media (min-width: 992px) {
      margin-top: 107px;
    }

    .round-button {
      min-width: 165px;
    }
  }
}

.results {
  .bottom-content {
    p {
      min-height: 50px;
    }
  }

  .result-box {
    padding: 15px;
    margin-top: 20px;

    &__rate {
      p {
        svg {
          color: $yellow;
        }

        span {
          margin-left: 5px;
        }
      }
    }

    .bottom-content {
      span {
        text-align: left;
        margin-top: 13px;
        margin-bottom: 0;
      }
    }
  }

  .load-more {
    margin-bottom: 50px;
    @media (min-width: 992px) {
      margin-top: 107px;
    }

    .round-button {
      min-width: 165px;
    }
  }
}

/*
  • Accordion
  ---------- ---------- ---------- ---------- ----------
*/
.advanced-filter {
  padding-right: 8px;
  border-right: 1px solid $border-grey;

  .result-box {
    padding-top: 20px;
    @media (min-width: 992px) {
      padding-top: 40px;
    }
  }
}

.accordion {
  border-radius: 28px;
  background: $white;
  @media (min-width: 572px) {
    min-height: 565px;
  }

  h2 {
    .btn {
      @media (min-width: 992px) {
        font-size: 15px;
      }

      svg {
        path {
          fill: $neutrals-gray;
        }
      }
    }
  }

  &.accordion-advancedFilter {
    .scrollBarModal {
      padding-right: 8px !important;
      overflow-x: hidden;
      height: calc(60vh - 120px);
    }

    @media (min-width: 1200px) {
      .scrollBarModal {
        overflow-x: hidden;
        height: 110vh;
      }
    }

    .check-label {
      @media (max-width: 1200px) {
        padding-left: 40px;
      }
    }

    .form-group {
      &.location {
        .wrapper {
          position: relative;

          ul {
            @media (max-width: 992px) {
              position: absolute;
              width: 100%;
              z-index: 2;
            }
          }
        }
      }
    }
  }

  .card {
    border: 0;
  }

  .head {
    display: flex;
    justify-content: space-between;

    > * {
      @media (max-width: 768px) {
        margin-bottom: 0;
      }
    }

    @media (min-width: 768px) {
      margin-bottom: 25px;
    }

    &.second-filter {
      margin-top: 20px;
      @media (min-width: 768px) {
        margin-top: 70px;
      }
    }
  }

  .btn {
    width: 100%;
    padding: 0;
    border-bottom: 0;
    text-decoration: none;

    &:hover,
    &:focus {
      outline: none;
      box-shadow: none;
      color: $primary-color;
    }
  }

  .card-header {
    border: 0;
    background: transparent;
    padding: 0px 0px 10px 0px;
    @media (max-width: 768px) {
      padding: 15px 0 0;
    }
  }

  .fa-stack {
    font-size: 18px;
    height: auto !important;
  }

  .btn-link {
    &:hover,
    &:focus {
      text-decoration: none;
    }
  }

  li + li {
    margin-top: 10px;
  }

  .bloc-more-filter {
    padding: 5px;
    padding-bottom: 40px;

    &:last-child {
      border: 0;
    }

    .radio-box {
      margin-bottom: 20px;
    }

    .check-box {
      &:last-child {
        label {
          margin-bottom: 0;
        }
      }
    }

    &.price {
      .more-filter {
        margin-bottom: 0;
      }
    }
  }

  .more-filter {
    margin-bottom: 45px;

    &:last-child {
      margin-bottom: 30px;
    }

    &__attribute {
      display: flex;
      margin-bottom: 20px;

      p {
        margin: 0;
        padding: 0;
      }

      span {
        font-size: 18px;
        display: inline-block;
        margin-left: 5px;
      }
    }
  }

  .card-body {
    @media (max-width: 768px) {
      padding: 10px 0 0;
    }

    .form-group {
      position: relative;

      &.location {
        @media (max-width: 992px) {
          margin-top: 15px;
        }
        @media (max-width: 572px) {
          margin-top: 20px;
        }

        i {
          position: absolute;
          top: 33px;
          left: 10px;

          svg {
            color: $near-grey;
          }
        }

        input {
          padding-left: 30px;
        }
      }

      .adjustPicker {
        &.new-datepicker {
          .react-datepicker-wrapper {
            @media (max-width: 992px) {
              top: -3px;
            }
          }
        }
      }
    }

    .round-button {
      padding: 2px 5px;
    }

    ul {
      padding-left: 0;
      @media (max-width: 992px) {
        margin-bottom: 5px;
      }
    }

    .check-label {
      font-size: 14px;

      span {
        max-width: 80%;
      }
    }

    .search-result-datepicker.advanced-filter {
      display: flex;
      align-items: center;
      padding: 0;
      margin: 15px 0;
      @media (max-width: 572px) {
        display: block;
      }

      .adjustPicker {
        &.new-datepicker {
          .react-datepicker-wrapper {
            min-width: 135px;
            top: 0;

            .form-control {
              height: 40px;
            }
          }
        }
      }

      .button-search-navbar {
        margin-left: 15px;
        margin-top: 0;
        @media (max-width: 572px) {
          width: 100%;
          margin-left: 0;
        }
      }
    }

    @media (max-width: 1200px) {
      .form-group {
        .adjustPicker {
          &.new-datepicker {
            border: 0;
            width: 92%;

            .react-datepicker-wrapper {
              top: auto;

              .react-datepicker__input-container {
                .searchInput-navbar {
                  height: 20px;
                  top: -2px;
                }
              }

              &:first-child {
                .react-datepicker__input-container {
                  border: 0;
                  padding: 0;
                }
              }
            }
          }
        }
      }
    }
    @media (max-width: 572px) {
      .form-group {
        .adjustPicker {
          &.new-datepicker {
            display: block;
            height: 110px;
            padding: 0;
            width: 100%;

            .react-datepicker-wrapper {
              min-width: 100%;
              margin-bottom: 10px;

              .react-datepicker__input-container {
                padding: 0;
                margin-left: 0;
              }
            }
          }
        }
      }
    }
  }

  .advanced_filter_date {
    .form-group {
      .form-control {
        border-radius: 0;
        background: none;
        border: 0;
      }

      &.advanced-filter {
        padding-right: 0;
        border-radius: 5px;
        color: $fake-black;

        .adjustPicker.new-datepicker {
          @media (max-width: 992px) {
            border: 0;
            height: auto;
            width: auto;
            padding: 0;
            .react-datepicker-wrapper {
              min-width: auto;

              &:first-child .react-datepicker__input-container {
                border: 0;
                padding-left: 0;
              }
            }
          }

          .react-datepicker-wrapper {
            top: 0;
            min-width: 123px;
          }

          .start_date {
            position: relative;
            padding-left: 26px;
            border: 1px solid $light-gray;
            border-right: 0;

            &:before,
            &:after {
              width: 24px;
              display: inline-block;
              position: absolute;
              top: 13px;
            }

            &:before {
              content: url('../style/assets/img/Icons/CalendarBlank.svg');
              left: 13px;
              z-index: 100000;
            }

            &:after {
              content: url('../style/assets/img/Icons/ArrowRight.svg');
              right: -3px;
              @media (min-width: 992px) {
                right: -9px;
              }
            }
          }

          .end_date {
            border: 1px solid $light-gray;
            border-left: 0;

            .form-group {
              padding: 0px;
            }
          }
        }

        .adjustPicker
          .react-datepicker-wrapper:nth-child(2)
          .react-datepicker__input-container {
          margin-left: 0;
        }
      }
    }

    .round-button {
      max-width: 255px;
      display: block;
      text-align: center;
      margin: 24px auto;
    }
  }
}

/*
  • Filter accessory
  ---------- ---------- ---------- ---------- ----------
*/

.filter-buttons {
  display: flex;
  align-items: center;
  justify-content: end;
  margin-bottom: 30px;
  @media (max-width: 572px) {
    display: block;
    text-align: center;
    margin-top: 50px;
  }

  button {
    @media (max-width: 572px) {
      width: 100%;
    }

    &:first-child {
      margin-right: 20px;
    }
  }
}

.radio-box {
  margin-bottom: 20px;
  @media (min-width: 992px) {
    margin-bottom: 50px;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.search-result-datepicker {
  .react-daterange-picker__inputGroup {
    border-radius: 30px;
    height: 36px;
    width: 100%;
    margin: 0 0 20px;
    box-sizing: border-box;
    @media (min-width: 768px) {
      height: 50px;
    }
  }

  .react-daterange-picker {
    width: 100%;
  }
}

.ais-ClearRefinements {
  button {
    h5 {
      font-size: 14px;
      font-weight: 900;
      color: $primary-color !important; //pour forcer la couleur plugin
      margin-bottom: 0;
      @media (min-width: 572px) {
        font-size: 18px;
      }
    }
  }
}

.check-label {
  padding-left: 20px;
}

.categories-box.radio-box [type='radio']:checked ~ label:before,
.categories-box.radio-box [type='radio']:not(:checked) ~ label:before {
  left: 9px;
  @media (min-width: 992px) {
    left: -5px;
  }
  @media (min-width: 1200px) {
    margin-right: 0;
    left: -7px;
  }
}

.radio-box [type='radio']:checked ~ label:before,
.radio-box [type='radio']:not(:checked) ~ label:before {
  @media (min-width: 1200px) {
    margin-right: -14px;
  }
}

.radio-box [type='radio']:checked ~ label:after,
.radio-box [type='radio']:not(:checked) ~ label:after {
  left: 4px;
  @media (min-width: 992px) {
    left: 5px;
  }
  @media (min-width: 1200px) {
    left: -9px;
  }
}

.radio-box.categories-box [type='radio']:checked ~ label:after,
.radio-box.categories-box [type='radio']:not(:checked) ~ label:after {
  left: 13px;
  @media (min-width: 992px) {
    left: 0;
  }
  @media (min-width: 1200px) {
    left: -1px;
  }
}

.check-box {
  label {
    &:before {
      margin-right: -6px !important;
      @media (max-width: 770px) {
        margin-right: 12px !important;
      }
    }
  }
}

.accordion {
  margin-top: 40px;

  .bloc-more-filter {
    @media (max-width: 992px) {
      margin-top: 0;
      padding-bottom: 20px;
    }

    p {
      @media (max-width: 992px) {
        margin-bottom: 0 !important;
      }
    }

    .radio-box {
      margin-bottom: 35px;
    }
  }
}

.under-construction {
  border-bottom: 1px solid #d1d1d1;
  border-top: 1px solid #d1d1d1;
  padding: 25px 0px;
}

.stick-to-right {
  padding-left: 40%;
}

.check-box {
  label {
    &:before {
      margin-right: 10px;
    }
  }
}

.check-box input:checked + label:after {
  right: 2px;
  @media (max-width: 960px) {
    height: 10px;
  }
  @media (max-width: 768px) {
    right: 20px;
  }
}

.pad-top {
  padding-top: 6px;
}

.pad-top-10 {
  padding-top: 10px;
}

.pad-top-4 {
  padding-top: 4px;
}

.marg-top-16 {
  margin-top: 16px;
}

.pad-left {
  padding-left: 20px;
}

.card-body {
  flex: 1 1 auto;
  padding: 0rem 2rem 0rem 1rem;
}

.t-header-large {
  @media (min-width: 768px) {
    font-size: 18px;
  }
}

.DefaultProgressBar_progressBar {
  background-color: $yellow;
}

.DefaultHandle_handle {
  width: 25px;
  height: 25px;
  border-radius: 100%;
}

.advanced-filter {
  .react-daterange-picker__calendar {
    margin-top: 65px;
    width: 350px;
  }

  .max-w-image {
    max-width: 155px;
    margin-bottom: 20px;
  }
}

.margin-bottom {
  margin-bottom: 20px;
}

.margin-top-45 {
  margin-top: 45px;
}

.hb:hover {
  color: black;
  cursor: pointer !important;
  text-decoration: underline;
}

.hb-underline {
  text-decoration: underline;
}

.company-hb {
  &:hover {
    color: $yellow;
  }
}

.filters-more-filters {
  font-weight: bold;
  font-size: initial;
}

.accordion {
  .radio-box li + li {
    margin-top: 25px;
  }
}

.search-result {
  @media (max-width: 992px) {
    margin-top: 0;
  }
}

.tag-equip-standard {
  border: 2px solid #9a9a9a;
  border-radius: 20px;
  margin-top: 20px;
  font-size: 14px;
  padding: 5px 20px !important;
  color: black;
  display: none;
}

.rent-details-btn {
  margin-bottom: 20px;
}

.rd-top-30 {
  border-radius: 30px 30px 0px 0px !important;
}

.rd-bottom-30 {
  border-radius: 0px 0px 30px 30px;
}

.mg-30 {
  margin: 5px 30px;
}

.fa-external-link {
  font-size: 14px;
  padding: 2px;
  color: $yellow;
}

.tag {
  border: 1px solid #e5ecf6;
  width: max-content;
  border-radius: 7px;
  padding: 10px 20px;
  display: flex;
  button {
    background: transparent;
  }
  &:before {
    content: url('../style/assets/img/Icons/Calendar.svg');
    height: 23px;
    padding-left: 8px;
  }
  &.btn {
    border-radius: 6px;
    border: 1px solid #e5ecf6;
    background: #fff;
    box-shadow: 0px 1px 5.7px -1px rgba(8, 29, 60, 0.1);
    padding: 12px 17px;
    gap: 10px;
    @media (max-width: 992px) {
      width: 100%;
      justify-content: center;
    }
  }
}
