import React, { useEffect, useState, Suspense } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import PersonalInfo from '../../components/equipper_management_portal/personal_info/Personal_info';
import BreadCrumb from '../../shared/components/Bread_crumb';
import ScrollToTop from '../../shared/components/Scroll_to_top';
import Tab from '../../shared/components/tab/Tab';
import { optionsEquipperTab } from '../../shared/helpers/Requests';
import '../../style/equipper/equiper_management_portal.scss';
import ToloIsLoading from '../../shared/components/cards/Tolo_is_loading';
import { useEquipper } from '../../shared/context/Equipper_context';
import SharedProvider from '../../shared/context/Shared_provider';
import { removeCookies, setCookies } from '../../shared/helpers/Cookies';
import { setSessionStorage } from '../../shared/helpers/Session_storage_helper';

export default function EquipperManagementPortal({ t }) {
  const navigate = useNavigate();
  const { GetEquipperPersonalInfo } = useEquipper();
  const [loadedInfo, setLoadedInfo] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const history = useLocation();
  const selectedTab = !history.pathname.includes('underConstruction')
    ? t(
        optionsEquipperTab.find((item) => item.router === history.pathname)
          ?.label
      )
    : t('Under_construction_tab');

  useEffect(() => {
    (async () => {
      setIsLoading(true);
      const res = await GetEquipperPersonalInfo();
      if (res.data === '') {
        window.location.reload();
        removeCookies('token');
        removeCookies('role');
        navigate('/');
      } else {
        setCookies('imageLink', res?.data?.photo_url);
        setCookies('hasRequests', res?.data?.has_requests);
        setSessionStorage('companyName', res?.data?.company);
        setSessionStorage('equipper', res?.data);
        setLoadedInfo(res?.data);
      }
      setIsLoading(false);
    })();
  }, []);

  return (
    <Suspense fallback={<ToloIsLoading />}>
      <div className="management">
        <ScrollToTop>
          <BreadCrumb
            t={t}
            items={[
              {
                label: selectedTab,
                show: true
              }
            ]}
          />
          <SharedProvider>
            <PersonalInfo loadedInfo={loadedInfo} isLoading={isLoading} t={t} />
            <div className="container tabulation">
              <Tab optionsTab={optionsEquipperTab} t={t} />
              <Outlet />
            </div>
          </SharedProvider>
        </ScrollToTop>
      </div>
    </Suspense>
  );
}
