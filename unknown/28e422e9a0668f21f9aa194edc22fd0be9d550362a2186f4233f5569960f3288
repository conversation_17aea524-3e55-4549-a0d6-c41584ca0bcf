.sticky-top {
  height: 68px;
  background: $white !important;
}

.navbar {
  @media(min-width: 992px) {
    margin-bottom: 30px;
  }

  .disapear-on-collapse {
    .react-daterange-picker__inputGroup {
      padding-top: 4px;
    }
  }
}

@media(max-width: 992px) {
  .nav-open {
    .navbar-translate {
      transform: translateX(0);
    }

    .navbar {
      .navbar-toggler {
        position: relative;

        .navbar-toggler-bar {
          -webkit-transform: rotate(0deg);
          -moz-transform: rotate(0deg);
          -o-transform: rotate(0deg);
          transform: rotate(0deg);
          -webkit-transition: .25s ease-in-out;
          -moz-transition: .25s ease-in-out;
          -o-transition: .25s ease-in-out;
          transition: .25s ease-in-out;
          position: absolute;

          &.bar1 {
            -webkit-transform: rotate(45deg);
            -moz-transform: rotate(45deg);
            -o-transform: rotate(45deg);
            transform: rotate(45deg);
            top: 18px;
            left: -3px;
          }

          &.bar2 {
            opacity: 0;
          }

          &.bar3 {
            -webkit-transform: rotate(-45deg);
            -moz-transform: rotate(-45deg);
            -o-transform: rotate(-45deg);
            transform: rotate(-45deg);
            top: 14px;
            left: -3px;
          }
        }
      }
    }
  }

  .navbar-collapse {
    width: 100%;
    background: $white;
    transform: translateX(100%);
    padding-left: 0;
  }

  .navbar {
    margin-bottom: 0 !important;

    .navbar-collapse {
      &::after {
        background: $white;
      }
    }

    .navbar-toggler {
      padding-right: 0;

      &:active,
      &:focus {
        box-shadow: none;
      }
    }
  }

  .navbar-toggler,
  .navbar-translate {
    position: relative;
    z-index: 1033;
    padding-top: 10px;
  }

  .mobile-menu {
    z-index: 1000;
    font-size: 1rem;
    color: #212529;
    text-align: left;
    list-style: none;
    margin-top: 50px;
    padding: 0 15px;

    span {
      width: 28px;
      height: 28px;
      border: 2px solid #D1D1D1;
      border-radius: 28px;
      margin-right: 10px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      position: relative;
      top: 3px;

      img {
        &.small-icon {
          width: 15px;
          height: 15px;
        }
      }
    }

    .black {
      span {
        border-color: #302F2F;
      }
    }


    li {
      text-align: left;
      padding: 5px 10px;
      display: flex;
      align-items: center;
    }

    &__top {
      border-top: 1px solid #E2E2E2;
      border-bottom: 1px solid #E2E2E2;
      padding-left: 0;
      padding-top: 15px;
      padding-bottom: 15px;

      a {
        font-size: 16px;
        text-decoration: none;
      }
    }

    .mobile-lang-menu {
      padding-left: 0;

      a {
        text-decoration: none;
      }
    }

    .round-button {
      position: relative;
      z-index: 3;
      min-width: 260px;
      margin: 50px auto 0;
    }
  }
}
