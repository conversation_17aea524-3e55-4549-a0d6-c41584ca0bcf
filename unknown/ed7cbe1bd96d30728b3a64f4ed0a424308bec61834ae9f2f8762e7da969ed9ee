package models

// ToolerBidzEquipment represents the equipment for bidz.
type ToolerBidzEquipment struct {
	ID            string   `json:"id" firestore:"id"`
	NameEN        string   `json:"name_en" firestore:"name_en"`
	NameFR        string   `json:"name_fr" firestore:"name_fr"`
	DescriptionEN string   `json:"description_en" firestore:"description_en"`
	DescriptionFR string   `json:"description_fr" firestore:"description_fr"`
	Category      []string `json:"category" firestore:"category"`
	SubCategory   []string `json:"sub_category" firestore:"sub_category"`
	<PERSON><PERSON>as    `json:"alias" firestore:"alias"`
	ImageLink     string   `json:"image_link" firestore:"image_link"`
}

// <PERSON><PERSON> is the alias for the equipments.
type Alias struct {
	En []string `json:"en" firestore:"en"`
	Fr []string `json:"fr" firestore:"fr"`
}
