import { isEmpty } from 'lodash';
import React, { useState, useEffect } from 'react';
import { connectInfiniteHits, connectStats } from 'react-instantsearch-dom';
import ViewMore from '../../shared/components/buttons/View_more';
import ToloIsLoading from '../../shared/components/cards/Tolo_is_loading';
import EquipmentsInventoryItem from '../../shared/components/equipment/Equipment_inventory_item';
import RenderIf from '../../shared/components/Render_if';
import NoResults from './No_results';
import RecommendationsSection from '../company_spotlight/Recommendations_section';
import { getSessionStorage } from '../../shared/helpers/Session_storage_helper';
import SignInModal from '../../shared/components/modals/Sign_in_modal';
import BookingModal from '../../shared/components/modals/Booking_modal';
import { getCookies } from '../../shared/helpers/Cookies';
import DateLocationModal from '../../shared/components/modals/Date_location_modal';
import { firstLetterUpperCase } from '../../shared/helpers/String_helps';
import { useLodger } from '../../shared/context/Lodger_context';
import { CustomSpotlightMenu } from './Custom_equipper_spotlight_menu';
import Swipe from '../../shared/components/swipe/Swipe';
import { usePromotions } from '../../shared/context/Promotion_context';
import EquipmentEmptyState from '../../style/assets/img/empty_state/Add_equipment_first.svg';

const InfiniteHits = ({
  hits,
  hasMore,
  refineNext,
  detectLanguage,
  setShowFPModal,
  currency,
  showFPModal,
  objectIDs,
  isExploreMore,
  signIn,
  isEquipperExploreMore,
  t,
  isEquipper,
  isEquipperSpotlight,
  isHomeInstantSearchByCategory
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [showSignIn, setShowSignIn] = useState(false);
  const [show, setShow] = useState(false);
  const [selectedHit, setSelectedHit] = useState();
  const [itemsLength, setItemsLength] = useState(0);
  const [selectedEquipment, setSelectedEquipment] = useState({});
  const [promotion, setPromotion] = useState(0);

  const { GetLodgerPersonalInfo } = useLodger();
  const isLodger =
    getCookies('role')?.includes('lodger') && getCookies('token');
  const [lodger, setLodger] = useState({});
  const category = getSessionStorage('category');
  const noResultsMessage = category
    ? `${t('Equipment_not_found_in')} ${t(
        firstLetterUpperCase(category?.replaceAll(' ', '_'))
      )}`
    : t('Equipment_not_found');
  const handleShow = () => {
    setShow(!show);
  };

  useEffect(() => {
    async function getLodger() {
      if (isLodger && isEquipper) {
        const { status, data } = await GetLodgerPersonalInfo();
        if (status === 200) {
          setLodger(data);
        }
      }
    }

    getLodger();
    if (!isEmpty(hits)) {
      setIsLoading(false);
    }
  }, [hits]);

  const details = [
    {
      show: true,
      title: 'Subcategories',
      selected: getSessionStorage('sub_category'),
      attribute: 'sub_category'
    }
  ];
  const uniqueNames = new Set();
  const filteredHits = hits.filter((hit) => {
    if (!uniqueNames.has(hit.name)) {
      uniqueNames.add(hit.name);
      return true;
    }
    return false;
  });

  const hitsToMap = isExploreMore ? filteredHits.slice(0, 4) : hits;

  const Stats = ({ nbHits, t }) => (
    <h3 className="t-subheading-2 c-fake-black  m-2 p-2">
      {nbHits} {t('Equipment_found')}{' '}
    </h3>
  );
  const role = getCookies('role');
  const { GetPromotionByEquipperAndLodgerID } = usePromotions();
  useEffect(async () => {
    if (role === 'lodger' && hits[0]?.equipper_id) {
      const res = await GetPromotionByEquipperAndLodgerID(hits[0]?.equipper_id);
      if (res.status === 200 && res.data) {
        setPromotion(res.data);
      }
    }
  }, [hits[0]?.equipper_id, role]);
  const CustomStats = connectStats(Stats);

  if (isLoading && isEmpty(hits)) {
    return <ToloIsLoading />;
  }
  return (
    <>
      <RenderIf
        condition={category && !isHomeInstantSearchByCategory && !isExploreMore}
      >
        <div className="row d-flex justify-content-center  hide-scroll-bar margin-lr-0">
          <h3 className="t-subheading-1 c-fake-black mb-4">
            {t(firstLetterUpperCase(category?.replaceAll(' ', '_')))}
          </h3>

          <RenderIf condition={hits.length > 0}>
            <Swipe showArrow={itemsLength > 6} id="scrollmenu-equipments">
              {details.map(
                ({ selected, attribute, show }) =>
                  show && (
                    <div className="d-flex sub-category__items">
                      <CustomSpotlightMenu
                        attribute={attribute}
                        setItemsLength={setItemsLength}
                        selected={selected}
                        t={t}
                      />
                    </div>
                  )
              )}
            </Swipe>
          </RenderIf>
        </div>
        <CustomStats t={t} />
      </RenderIf>
      <div
        className={`row d-flex justify-content-center overflow_categories_row hide-scroll-bar margin-lr-0
      ${
        isHomeInstantSearchByCategory || isExploreMore
          ? 'flex-nowrap'
          : 'flex-wrap mt-4'
      }`}
      >
        {hitsToMap?.map((item, key) => {
          return (
            <EquipmentsInventoryItem
              detectLanguage={detectLanguage}
              hit={item}
              key={key}
              setSelectedHit={setSelectedHit}
              handleShow={handleShow}
              promotion={promotion}
              t={t}
              show={show}
              setShow={setShow}
              setSelectedEquipment={setSelectedEquipment}
              setShowSignIn={setShowSignIn}
              isEquipper={isEquipper}
            />
          );
        })}
      </div>
      <RenderIf condition={isEquipper && show && getCookies('token')}>
        <BookingModal
          handleClose={handleShow}
          isEquipperSpotlight={isEquipperSpotlight}
          isOpen={show}
          data={{
            ...lodger,
            description_lodger: lodger?.description,
            ...selectedEquipment,
            currency: currency || 'usd'
          }}
          detectLanguage={detectLanguage}
          t={t}
        />
      </RenderIf>
      <RenderIf condition={!isEquipper && show}>
        <DateLocationModal
          show={show}
          onClose={handleShow}
          detectLanguage={detectLanguage}
          hit={selectedHit}
          isEquipperExploreMore={isEquipperExploreMore}
          id={selectedHit?.objectID}
          t={t}
        />
      </RenderIf>
      <SignInModal
        setShowFPModal={setShowFPModal}
        showFPModal={showFPModal}
        setShow={setShowSignIn}
        show={showSignIn}
        signIn={signIn}
        t={t}
      />
      <RenderIf
        condition={hasMore && !isHomeInstantSearchByCategory && !isExploreMore}
      >
        <ViewMore onClick={refineNext} />
      </RenderIf>
      <RenderIf condition={isEmpty(hits)}>
        <NoResults message={noResultsMessage} image={EquipmentEmptyState} />
        {objectIDs[0] && (
          <div className="company-spotlight--similarProduct mt-5">
            <h2 className="t-header-h3 bold c-fake-black title-search">
              {t('Discover_other_products_may_interset')}
            </h2>
            <RecommendationsSection objectID={objectIDs[0]} t={t} />
          </div>
        )}
      </RenderIf>
    </>
  );
};

export const CustomInventoryInfiniteHits = connectInfiniteHits(InfiniteHits);

CustomInventoryInfiniteHits.defaultProps = {
  objectIDs: []
};
