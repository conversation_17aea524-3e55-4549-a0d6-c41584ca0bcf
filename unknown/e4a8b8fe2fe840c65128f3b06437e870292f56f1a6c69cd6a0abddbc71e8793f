import React, { useState } from 'react';
import BidzDetails from '../../../modals/Bidz_details';
import Popup from '../../../modals/Popup';
import SuccessPopUp from '../../../modals/Success_pop_up';
import BidzCancelModal from '../../../modals/Bidz_cancel_modal';
import RenderIf from '../../../Render_if';
import BidzRequestItem from './Bidz_request_item';

export default function BidzRequestCard({
  data,
  detectLanguage,
  t,
  refresh,
  acceptRequest,
  declineRequest,
  hasInventory,
  show,
  setShow,
  showError,
  setShowError,
  setError,
  error,
  role,
  member,
  isLoading
}) {
  const [selectedBidz, setSelectedBidz] = useState({});
  const [bidzModal, setBidzModal] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [buttonText, setButtonText] = useState('');

  const declineConfirmationModal = (data) => {
    setSelectedBidz(data);
    setShowConfirmation(true);
    setButtonText(t('Decline'));
  };

  const onCloseSuccess = () => {
    setShow(false);
    setShowConfirmation(false);
    refresh();
  };

  const onClose = () => {
    setShowError(false);
    refresh();
    setShowConfirmation(false);
    setError(null);
  };

  return (
    <>
      {Object.keys(data)?.map(
        (item, key) =>
          item !== 'dataLength' && (
            <div className="body-content" key={key}>
              <div className="body-content__top">
                <div className="d-flex align-items-center">
                  <h3 className="t-subheading-2">
                    <span>
                      {detectLanguage === 'fr'
                        ? data[item] && data[item][0].equipment_name_fr
                        : item}
                    </span>
                  </h3>
                </div>
              </div>
              {data[item] &&
                data[item]?.map((item, key) => (
                  <BidzRequestItem
                    role={role}
                    hasInventory={hasInventory}
                    t={t}
                    key={key}
                    item={item}
                    member={member}
                    setSelectedBidz={setSelectedBidz}
                    setBidzModal={setBidzModal}
                    declineConfirmationModal={declineConfirmationModal}
                  />
                ))}
            </div>
          )
      )}
      <RenderIf condition={hasInventory === 'false'}>
        <BidzCancelModal
          show={showConfirmation}
          action={declineRequest}
          onClose={() => setShowConfirmation(false)}
          buttonText={buttonText}
          message={`${t('Are_you_sure_you_want_to_declined_this_request')}?`}
          isRequest
          isLoading={isLoading}
          item={selectedBidz}
          t={t}
        />
        <SuccessPopUp show={show} onClose={onCloseSuccess} />
        <Popup show={showError} response={error} onClose={onClose} t={t} />
      </RenderIf>

      <BidzDetails
        bidzModal={bidzModal}
        setBidzModal={setBidzModal}
        detectLanguage={detectLanguage}
        data={selectedBidz}
        role={role}
        hasInventory={hasInventory}
        acceptRequest={acceptRequest}
        t={t}
      />
    </>
  );
}
