import * as Yup from 'yup';

const insuranceSchema = (t) =>
  Yup.object().shape({
    insurance_coverage: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .required(t('Insurance_coverage_required')),
    expiry_date_of_insurance: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .required(t('Expiry_date_of_insurance_required'))
      .nullable(),
    insurance_company: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .required(t('Insurance_company_required')),
    insurance_policy_number: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .required(t('Insurance_policy_number_required'))
  });

const deliveryAddressSchema = (t) =>
  Yup.object().shape({
    address: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .required(t('This_field_is_required')),
    city: Yup.object().required(t('This_field_is_required')).nullable(),
    state: Yup.object().required(t('This_field_is_required')).nullable(),
    country: Yup.object().required(t('This_field_is_required')).nullable(),
    zip_code: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .required(t('This_field_is_required'))
  });

const billingAddressSchema = (t) =>
  Yup.object().shape({
    address: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .required(t('This_field_is_required')),
    city: Yup.object().required(t('This_field_is_required')).nullable(),
    state: Yup.object().required(t('This_field_is_required')).nullable(),
    country: Yup.object().required(t('This_field_is_required')).nullable(),
    zip_code: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .required(t('This_field_is_required'))
  });

export { insuranceSchema, deliveryAddressSchema, billingAddressSchema };
