import React from 'react';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import * as Yup from 'yup';
import { useTranslation } from 'react-i18next';
import { isValid } from '../../shared/helpers/Data_helper';

export default function NewPasswordModal(props) {
  const { t } = useTranslation();

  const validate = Yup.object({
    password: Yup.string()
      .required(t('Please_enter_your_password'))
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#/?$%^&*])(?=.{8,})/,
        t('Password_must_contain')
      ),
    confirm_password: Yup.string()
      .oneOf([Yup.ref('password'), null], t('SignUp_password_match'))
      .required(t('Required'))
  });

  return (
    <>
      <button className="close-button" onClick={props.onClose}>
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M19.25 5.25L5.75 18.75"
            stroke="#333333"
            strokeWidth="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M19.25 18.75L5.75 5.25"
            stroke="#333333"
            strokeWidth="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
      <div className="text-center">
        <h3 className="login-title t-header-h6 bold">
          {t('Create_new_password')}
        </h3>
        <Formik
          initialValues={{
            password: '',
            confirm_password: ''
          }}
          onSubmit={props.UpdatePassword}
          validationSchema={validate}
        >
          {(formik) => (
            <>
              <Form>
                <div className="login-container">
                  <div className="container-email form-group">
                    <Field
                      className={`full-width form-control ${isValid(
                        formik,
                        'password'
                      )}`}
                      name="password"
                      placeholder={t('Password')}
                      type="password"
                    />

                    <ErrorMessage
                      name="password"
                      component="span"
                      className="error-message"
                    />
                  </div>
                  <p className="form-group">
                    <Field
                      className={`full-width form-control  ${isValid(
                        formik,
                        'confirm_password'
                      )}`}
                      name="confirm_password"
                      placeholder={t('Confirm_your_password')}
                      type="password"
                    />
                    <ErrorMessage
                      name="confirm_password"
                      component="span"
                      className="error-message"
                    />
                  </p>
                  <div className="fixed-button-modal">
                    <button
                      className="button-signup round-button bold c-primary-color yellow"
                      type="submit"
                    >
                      {t('Save')}
                    </button>
                  </div>
                </div>
              </Form>
            </>
          )}
        </Formik>
      </div>
    </>
  );
}
