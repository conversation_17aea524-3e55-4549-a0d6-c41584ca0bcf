import { onlyFilledItemsFilter } from '../../helpers/Data_helper';
import { isEmptyValue } from '../../helpers/String_helps';
import RenderIf from '../Render_if';
import Collapse from './Collapse';

export default function EquipmentDetailsCard({
  data,
  t,
  showDetails,
  handleShowDetails
}) {
  const LabelandValue = ({ label, value, key }) => (
    <p className="t-subheading-3 c-fake-black " key={key}>
      <span className="bold">{label} </span>
      {isEmptyValue(value)}
    </p>
  );

  const hasFilledItems = data?.some((item) => {
    const filledKeys = onlyFilledItemsFilter(item);
    const arr = filledKeys?.filter(
      (keyName) => item[keyName].value && item[keyName].value
    );

    return arr.length > 0;
  });

  return (
    <RenderIf condition={hasFilledItems}>
      <Collapse
        label={showDetails ? 'View_less' : 'View_more'}
        t={t}
        className="collapse-card"
        showDetails={showDetails}
        handleShowDetails={() => handleShowDetails()}
      >
        {data?.map((item) => {
          const filledKeys = onlyFilledItemsFilter(item);
          return (
            <>
              {filledKeys?.map((keyName, index) => (
                <RenderIf condition={item[keyName].value}>
                  <LabelandValue
                    label={item[keyName].label}
                    key={index}
                    value={item[keyName].value}
                  />
                </RenderIf>
              ))}
            </>
          );
        })}
      </Collapse>
    </RenderIf>
  );
}
