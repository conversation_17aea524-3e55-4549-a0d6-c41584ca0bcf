name: Build and Test Web

on:
  pull_request:
    branches: [dev]

jobs:
  build_test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x]
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}

      - name: Get Yarn cache directory
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - name: Use Yarn cache
        uses: actions/cache@v3
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ matrix.node-version }}-${{ hashFiles('**/yarn.lock') }}

      # `--prefer-offline` gives cache priority
      - name: Install dependencies
        run: yarn install --prefer-offline --frozen-lockfile

      - name: build
        run: yarn run build

      - name: test
        run: yarn run test

      - name: lint
        run: yarn run lint
