import React, { useState } from 'react';
import PropTypes from 'prop-types';
import * as Yup from 'yup';
import {
  CREATE_PROJECT,
  DELETE_PROJECT,
  UPDATE_PROJECT
} from '../../../shared/helpers/Url_prefixes';
import RenderIf from '../../../shared/components/Render_if';
import SuccessPopUp from '../../../shared/components/modals/Success_pop_up';
import ErrorPopUp from '../../../shared/components/modals/Popup';
import CreateProjectModal from './Create_project_modal';
import ProjectDeleteModal from './Delete_project_modal';
import EditProjectModal from './Edit_project_modal';

export default function Crud({
  creditCheckForms,
  equipments,
  members,
  bidz,
  useCreateProject,
  detectedLanguage,
  useDeleteProject,
  selectedProject,
  useEditProject,
  handleClose,
  handleShow,
  show,
  setShow,
  type,
  t
}) {
  const [response, setResponse] = useState({
    errorPrefix: ''
  });

  const validate = Yup.object({
    name: Yup.string()
      .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
      .required(t('Project_name_required_LMP')),
    billing_address: Yup.object().shape({
      address: Yup.string()
        .required(t('This_field_is_required'))
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),
      city: Yup.object().required(t('This_field_is_required')).nullable(),
      state: Yup.object().required(t('This_field_is_required')).nullable(),
      country: Yup.object().required(t('This_field_is_required')).nullable(),
      zip_code: Yup.string()
        .required(t('This_field_is_required'))
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
    }),
    delivery_address: Yup.object().shape({
      address: Yup.string()
        .required(t('This_field_is_required'))
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg')),
      city: Yup.object().required(t('This_field_is_required')).nullable(),
      country : Yup.object().required(t('This_field_is_required')).nullable(),
      state: Yup.object().required(t('This_field_is_required')).nullable(),
      zip_code: Yup.string()
        .required(t('This_field_is_required'))
        .matches(/^(?!\s).*[^\s]$/, t('Space_error_msg'))
    })
  });

  const onCreateProject = async (project) => {
    const res = await useCreateProject(project);
    if (res.status === 200 || res.status === 201) {
      handleShow('Success');
    } else {
      setResponse({ ...res, errorPrefix: CREATE_PROJECT });
      handleShow('Error');
    }
  };

  const onEditProject = async (project) => {
    const res = await useEditProject(project);
    if (res.status === 200) {
      handleShow('Success');
    } else {
      setResponse({ ...res, errorPrefix: UPDATE_PROJECT });
      handleShow('Error');
    }
  };

  const onDeleteProject = async (project) => {
    const res = await useDeleteProject(project);
    if (res.status === 200) {
      handleShow('Success');
    } else {
      setResponse({ ...res, errorPrefix: DELETE_PROJECT });
      handleShow('Error');
    }
  };
  return (
    <>
      <RenderIf condition={show.showCreate}>
        <CreateProjectModal
          show={show.showCreate}
          onClose={() => {
            handleShow('Create');
          }}
          bidzOptions={bidz}
          equipmentOptions={equipments}
          memberOptions={members}
          creditCheckFormOptions={creditCheckForms}
          onCreateClick={onCreateProject}
          validate={validate}
          t={t}
        />
      </RenderIf>

      <RenderIf condition={show.showEdit}>
        <EditProjectModal
          onClose={() => {
            setShow({
              ...show,
              showEdit: false,
              showDetails: false
            });
          }}
          bidzOptions={bidz}
          equipmentOptions={equipments}
          memberOptions={members}
          detectedLanguage={detectedLanguage}
          creditCheckFormOptions={creditCheckForms}
          project={selectedProject}
          onEditClick={onEditProject}
          isForShow={show.showDetails}
          show={show.showEdit}
          validate={validate}
          type={type}
          t={t}
        />
      </RenderIf>

      <RenderIf condition={show.showDelete}>
        <ProjectDeleteModal
          onClose={() => {
            handleShow('Delete');
          }}
          project={selectedProject}
          onDelete={onDeleteProject}
          show={show.showDelete}
          t={t}
        />
      </RenderIf>

      <SuccessPopUp onClose={() => handleClose()} show={show.showSuccess} />
      <ErrorPopUp
        t={t}
        onClose={() => {
          handleClose();
        }}
        prefix={response.errorPrefix}
        show={show.showError}
        response={response}
      />
    </>
  );
}

Crud.propTypes = {
  creditCheckForms: PropTypes.arrayOf(PropTypes.object).isRequired,
  equipments: PropTypes.arrayOf(PropTypes.object).isRequired,
  members: PropTypes.arrayOf(PropTypes.object).isRequired,
  bidz: PropTypes.arrayOf(PropTypes.object).isRequired,
  useCreateProject: PropTypes.func.isRequired,
  useDeleteProject: PropTypes.func.isRequired,
  selectedProject: PropTypes.objectOf(PropTypes.any).isRequired,
  useEditProject: PropTypes.func.isRequired,
  handleClose: PropTypes.func.isRequired,
  handleShow: PropTypes.func.isRequired,
  show: PropTypes.objectOf(PropTypes.any).isRequired,
  type: PropTypes.string.isRequired,
  t: PropTypes.func.isRequired
};

Crud.defaultProps = {
  creditCheckForms: [],
  equipments: [],
  members: [],
  bidz: []
};
