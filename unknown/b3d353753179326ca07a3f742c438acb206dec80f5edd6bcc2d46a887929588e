import React, { useState, useEffect } from 'react';
import CardPopularCategory from '../../../shared/components/cards/Card_popular_category';
import { categories } from '../../../shared/helpers/Categories_helper';
import { Link } from 'react-router-dom';
import InstantSearchAlgolia from '../../search_result/Instant_search_algolia';
import { Configure, Menu } from 'react-instantsearch-dom';
import { CustomInventoryInfiniteHits } from '../../search_result/Custom_inventory_infinite_hits';
import LodgerProvider from '../../../shared/context/Lodger_context';
import { getSessionStorage } from '../../../shared/helpers/Session_storage_helper';

export default function Categories({
  t,
  detectLanguage,
  setShowFPModal,
  showFPModal,
  signIn
}) {
  const [instantCategoryValue, setInstantCategoryValue] = useState(
    getSessionStorage('category')
  );
  const [searchState, setSearchState] = useState({});

  function handleStateSwitch(searchState) {
    setSearchState(searchState);
  }

  useEffect(() => {
    instantCategoryValue &&
      setSearchState({
        ...searchState,
        menu: {
          category: instantCategoryValue || ''
        }
      });
  }, [instantCategoryValue]);

  return (
    <div className="container-categories">
      <div className="container">
        <div className="container-card horizontal-scroll-wrapper squares">
          <div className="title_home">
            <h1 className="t-header-h2 c-fake-black mb-1">
              {t('Explore_home')}
            </h1>
            <p className="t-subheading-2 c-neutrals-gray text-center">
              {t('Text_home')}
            </p>
          </div>
          <div className="d-flex overflow_categories_row hide-scroll-bar justify-content-lg-center">
            <div className="row flex-nowrap justify-content-lg-center justify-content-start categories_row">
              {categories?.map((category, index) => (
                <div className="card_item" key={index}>
                  <CardPopularCategory
                    name={t(category.name)}
                    path={category.path}
                    instantCategoryValue={instantCategoryValue}
                    setInstantCategoryValue={setInstantCategoryValue}
                    iconPath={category.iconPath}
                    value={category.value}
                  />
                </div>
              ))}
            </div>
          </div>
          <div className="d-flex justify-content-lg-center lodger_provider">
            <LodgerProvider>
              <InstantSearchAlgolia
                searchState={searchState}
                onSearchStateChange={(searchState) => {
                  handleStateSwitch(searchState);
                }}
                indexName={import.meta.env.VITE_ALGOLIA_INDEX_NAME_STANDARDS}
              >
                <Configure
                  hitsPerPage={4}
                  filters={
                    instantCategoryValue
                      ? `category:"${instantCategoryValue}"`
                      : 'name_en:excavator OR name_en:backhoe OR name_en:bulldozer OR name_en:loader OR name_en:forklift OR name_en:roller OR name_en:skid'
                  }
                />

                <Menu
                  attribute="category"
                  className="hidden"
                  defaultRefinement={instantCategoryValue}
                />

                <CustomInventoryInfiniteHits
                  detectLanguage={detectLanguage}
                  t={t}
                  setShowFPModal={setShowFPModal}
                  showFPModal={showFPModal}
                  signIn={signIn}
                  isHomeInstantSearchByCategory
                />
              </InstantSearchAlgolia>
            </LodgerProvider>
          </div>

          <div className="text-center request-all w-btn-80">
            <Link to="/availableInventory">
              <button className="round-button yellow hover_black c-black bold mt-lg-5 mb-4 with-arrow-white d-inline-flex align-items-center justify-content-center">
                {t('Explore_derental_catalog')}
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
