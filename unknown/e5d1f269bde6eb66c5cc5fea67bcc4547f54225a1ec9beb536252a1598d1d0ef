footer {
  .footer-link {
    background-color: $primary-color;
    @media (max-width: 992px) {
      padding-bottom: 20px;
    }

    .socials_footer {
      .connect-text {
        position: relative;
        display: flex;
        align-items: center;

        &:before {
          content: '';
          width: 32px;
          height: 1px;
          display: inline-block;
          background: $white;
          margin-right: 12px;
        }
      }

      a {
        border-radius: 5px;
        background: rgba(255, 255, 255, 0.22);
        width: 45px;
        height: 45px;
        padding: 12px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .quick-links {
    font-size: 90%;
    line-height: 40px;
    margin-bottom: 0;
    text-transform: none;
  }

  &.sub-footer {
    margin-top: 25px;
    @media (min-width: 992px) {
      margin-top: 60px;
    }

    .submit-form {
      @media (min-width: 992px) {
        max-width: 420px;
      }

      h2 {
        margin-bottom: 10px;
      }
    }

    .backimage {
      @media (max-width: 992px) {
        padding-bottom: 10px;
      }
    }

    .reach_out {
      padding-bottom: 45px;
      padding-top: 10px;
      margin-top: 50px;


      button {
        margin-top: 16px;
        @media(min-width: 992px) {
          margin-left: 43px;
          margin-top: 0;
        }
      }

    }
  }
}

.row-form {
  justify-content: left;

  .form-group {
    .form-control {
      width: 100%;
    }
  }
}

.button-footer {
  background-color: $yellow;
  border-radius: 50%;
  width: 40px !important;
  height: 40px;
  border: 1px solid;
}

.button-footer:hover {
  background-color: #f9a602;
}

.subTitle {
  font-weight: normal;
  padding-bottom: 30px;
  margin-top: 24px;
  @media (max-width: 992px) {
    padding-bottom: 15px;
  }
}

.footer-a:hover {
  text-decoration: none;
}

.footer::selection {
  background: white;
  text-shadow: none;
}

.backimage {
  max-width: 100%;
  height: auto;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  padding-bottom: 70px;
}

.footer {
  text-align: center;
}

.footer-column:not(:first-child) {
  padding-top: 2rem;
  @media (min-width: 768px) {
    padding-top: 0;
  }
}

.toolaFooter {
  width: 300px;
  @media (max-width: 1000px) {
    width: 250px;
  }
}

.footer-toola {
  text-align: right;
  margin-top: 40px;
}

.footer-column {
  text-align: left;

  .nav-item {
    .nav-link {
      padding: 6px 0;
    }

    &.become-equipper-link {
      a {
        color: $yellow;

        &:hover {
          color: #ccc8c8;
        }
      }
    }

    .nav-link {
      &:hover,
      &:focus {
        color: $yellow;
      }
    }

    .fas {
      margin-right: 0.5rem;
      color: white;
    }
  }

  ul {
    display: inline-block;
    @media (min-width: 768px) {
      text-align: left;
    }
  }

  &.last-column {
    .nav-item {
      padding-top: 10px;

      .nav-link {
        padding: 0;
        line-height: 18px;
      }
    }
  }
}

ul.social-buttons {
  margin-bottom: 0;

  li {
    a {
      border-radius: 50px;
      width: 32px;
      height: 32px;
      display: block;
      -webkit-transition: all 0.3s;
      -moz-transition: all 0.3s;
      transition: all 0.3s;
      margin: 10px 0 0 5px;
      text-decoration: none;
      line-height: 25px;

      &:hover {
        text-decoration: none;
      }
    }
  }
}

.copyright {
  color: white;
}

.fa-ellipsis-h {
  color: black;
  padding: 2rem 0;
}

.fa-list-Link {
  text-align: center;
  padding: 2px;
  border-radius: 50%;
  border: 2px solid whitesmoke;

  &:hover {
    color: $yellow;
  }
}

.list-inline-item {
  &:hover {
    text-decoration: none;
  }
}

.fa-list-button {
  font-size: medium;
  color: black;
  font-weight: bold;
}

.button-submit {
  margin-top: 34px;

  span {
    padding-right: 12px;
  }
}

@media (max-width: 992px) {
  .disapear-on-collapse {
    display: none !important;
  }

  .searchBar {
    background-color: transparent;
    margin: 55px 0;
    border: 0;
    border-radius: 50px;
    height: 100%;
  }
}

.rounded-corners {
  border-radius: 23px;
  height: 50px;
  width: 100%;
  border: 1px solid #d8d8d8;
  padding-left: 10px;
  @media (max-width: 992px) {
    height: 40px;
  }
}

.sticky-bar {
  position: fixed;
  bottom: 0;
  z-index: 800;
  width: 100%;
  background: $white;
  box-shadow: 0 2px 10px 0 rgb(0 0 0 / 30%);
  padding: 10px 0;
  border-top: 1px solid rgb(235, 235, 235);

  a {
    margin-bottom: 0;
    text-decoration: none;
  }
}

.tel-information {
  margin-bottom: 48px;
  @media(min-width: 992px){
    margin-bottom: 70px;
  }

  &__details {
    margin-bottom: 25px;
  }

  &__socials {
    ul {
      padding-left: 0;

      li {
        margin-right: 8px;

        a {
          border-radius: 5px;
          border: 1px solid $fake-black;
          width: 45px;
          height: 45px;
          padding: 12px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
        }

        &:first-child {
          a {
            background: $fake-black;

            svg {
              path {
                fill: $white;
              }
            }
          }
        }
      }
    }
  }

  a {
    text-decoration: none;
  }
}

#tidio-chat-iframe[style*='transform: translateY(50%)'] {
  height: 210px !important;
}

.form_footer {
  padding: 38px 10px;
  border-radius: 14px;
  gap: 34px;
  box-shadow: 0 11px 40px 0 rgba(6, 28, 61, 0.07);
  margin-bottom: 48px;
  margin-top: 48px;
  @media(min-width: 992px) {
    padding: 48px;
    margin-bottom: 0;
    width: 648px;
    height: auto;
    margin-top: 0;
  }

  .form-group {
    .form-control {
      color: $neutrals-gray;

      &::-ms-input-placeholder { /* Edge 12-18 */
        color: $neutrals-gray;
      }

      &::placeholder {
        color: $neutrals-gray;
      }
    }
  }

  label {
    @media(max-width: 992px) {
      font-size: 14px;
      margin-bottom: 6px;
    }
  }

  .error-message {
    margin-top: -2px;
  }
}

.border-bottom {
  border-color: $neutrals-gray !important;
}

.margin-right{
  margin-right: 32px;
}