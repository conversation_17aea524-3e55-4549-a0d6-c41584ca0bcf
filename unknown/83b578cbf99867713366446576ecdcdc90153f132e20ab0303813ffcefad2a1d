import React, { useEffect, useState } from 'react';
import ChevronDown from '../../../style/assets/img/Icons/CaretDown.svg';
import ChevronUp from '../../../style/assets/img/Icons/CaretUp.svg';
import Collapse from 'react-bootstrap/Collapse';

export default function AdvancedFilterTab({
  children,
  title,
  setAddClassName,
  addClassName,
  defaultOpen
}) {
  const [open, setOpen] = useState();

  useEffect(() => {
    setOpen(defaultOpen);
  }, []);

  return (
    <div className="card">
      <div className="card-header" id="headingOne">
        <h2 className="mb-0">
          <button
            className="t-subheading-3 bold c-fake-black d-flex align-items-center justify-content-between btn btn-link collapsed"
            data-toggle="collapse"
            data-target="#collapseOne"
            onClick={() => {
              setOpen(!open);
              addClassName && setAddClassName && setAddClassName(!addClassName);
            }}
            aria-controls="collapseOne"
            aria-expanded={open}
          >
            {title}
            <span className="fa-stack fa-sm">
              <i>
                {!open ? (
                    <img src={ChevronDown} alt="chevron down" />
                ) : (
                    <img src={ChevronUp} alt="chevron up" />
                )}
              </i>
            </span>
          </button>
        </h2>
      </div>
      <Collapse in={open}>
        <div
          id="collapseOne"
          className="collapse"
          aria-labelledby="headingOne"
          data-parent="#accordion"
        >
          <div className="card-body">{children}</div>
        </div>
      </Collapse>
    </div>
  );
}
