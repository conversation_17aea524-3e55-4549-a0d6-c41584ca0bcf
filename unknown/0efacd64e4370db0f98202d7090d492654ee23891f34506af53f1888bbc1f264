import React, { createContext, useContext } from 'react';
import AxiosFactory, {
  METHOD_DELETE,
  METHOD_GET,
  METHOD_POST,
  METHOD_PUT
} from '../helpers/Context_helpers';
import { GET_PROJECT, PROJECT } from '../helpers/Url_constants';

const ProjectsContext = createContext();
export function useProjects() {
  return useContext(ProjectsContext);
}
export default function ProjectProvider({ children }) {
  async function GetProjects() {
    return await AxiosFactory({ url: GET_PROJECT, method: METHOD_GET });
  }
  async function CreateProject(project) {
    return await AxiosFactory({
      url: PROJECT,
      method: METHOD_POST,
      data: project
    });
  }
  async function EditProject(project) {
    return await AxiosFactory({
      url: `${PROJECT}/${project.id}`,
      method: METHOD_PUT,
      data: project
    });
  }
  async function DeleteProject(project) {
    return await AxiosFactory({
      url: `${PROJECT}/${project.id}`,
      method: METHOD_DELETE
    });
  }

  async function GetProject(id) {
    return await AxiosFactory({
      url: `${PROJECT}/${id}`,
      method: METHOD_GET
    });
  }


  const value = {
    GetProjects,
    GetProject,
    CreateProject,
    EditProject,
    DeleteProject
  };

  return (
    <ProjectsContext.Provider value={value}>
      {children}
    </ProjectsContext.Provider>
  );
}
