.center {
  text-align: center;
}

.flex {
  display: flex;
  margin-top: 5%;
}

.bg-equipper-page {
  max-width: 100%;
  height: 700px;
  position: relative;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  padding-bottom: 6%;
  @media(max-width: 992px) {
    background-image: none !important;
    height: auto;
    padding: 0 30px;
  }
}

.second-bg-equipper-page {
  position: relative;
  margin-top: 70px;
  @media(min-width: 992px) {
    margin-top: 100px;
  }
}

.center h1 {
  padding-bottom: 15px;
}

.center h5 {
  padding-bottom: 15px;
}

.center p {
  margin-bottom: 0;
}

.inner-wrap-button {
  position: relative;
  text-align: center;
  @media(min-width: 992px) {
    margin-top: 120px;
  }

  .btn-become-equipper {
    font-weight: 900;
  }
}

.card-equipper-page {
  background-color: white;
  border-radius: 20px;
  padding: 20px;
  margin-bottom: 25px;
  @media(min-width: 992px) {
    padding: 80px 40px;
    margin-bottom: 0;
  }
}

@media(min-width: 1400px) {
  .offset-xl-r-1 {
    margin-right: 8.33333333%;
  }
}

.with-image {
  z-index: 1;
}

.path-img {
  position: absolute;
  z-index: 2;
  width: 150px;
  height: 100px;
  right: 0;
  bottom: -60px;
  @media(min-width: 992px) {
    right: -50px;
  }
}
