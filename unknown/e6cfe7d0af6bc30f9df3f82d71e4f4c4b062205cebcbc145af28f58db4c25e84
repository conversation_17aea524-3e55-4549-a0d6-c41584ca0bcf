import React from 'react';
import { cutString } from '../../helpers/String_helps';
import CustomImage from '../images/Custom_image';
import CompanySpotlightEquipmentItem from './Company_spotlight_equipment_Item';

export default function EquipmentsInventoryItem({
  hit,
  detectLanguage,
  setShowSignIn,
  setSelectedEquipment,
  setSelectedHit,
  setShow,
  lodger,
  handleShow,
  t,
  promotion,
  isEquipper
}) {
  const name =
    detectLanguage === 'fr' ? hit?.name_fr : hit?.name_en || hit?.name;

  return (
    <>
      {isEquipper ? (
        <CompanySpotlightEquipmentItem
          item={hit}
          lodger={lodger}
          promotion={promotion}
          isOpen={setShow}
          setShowSignIn={setShowSignIn}
          selectedEquipment={setSelectedEquipment}
          t={t}
          detectLanguage={detectLanguage}
          isEquipperSpotlight
        />
      ) : (
        <div
          className="col-xxl-3 col-lg-4 card-categorie"
          onClick={() => {
            setSelectedHit(hit);
            handleShow();
          }}
        >
          <div className="similarProduct-box margin-bottom">
            <div className="img-box">
              <CustomImage
                imageUrl={
                  hit.equipper_equipment_picture &&
                  !hit.equipper_equipment_picture.includes(
                    'equipment_library/Empty_state_equipment.png'
                  )
                    ? hit.equipper_equipment_picture
                    : hit.image_link
                }
              />
            </div>
            <div className="content-box text-center">
              <h3
                className="t-subheading-2 c-fake-black"
                onClick={handleShow}
                data-toggle="tooltip"
                data-placement="top"
                title={name}
              >
                {cutString(
                  hit.preferred_equipment_name
                    ? hit.preferred_equipment_name
                    : name || '',
                  30
                )}
              </h3>
              <button
                className="round-button yellow with-arrow-white width-100 c-black d-inline-flex justify-content-center mt-3 align-items-center see_availability bold small mt-2 "
                onClick={() => {
                  setSelectedHit(hit);
                  handleShow();
                }}
              >
                {t('See_availability')}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
