import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import MultiSelect from '../../../../shared/components/multi_select/Multi_select.jsx';
import React, { useEffect, useState } from 'react';
import { FieldArray } from 'formik';
import { attributes } from '../../../../shared/helpers/Data_helper.js';
import { firstLetterUpperCase } from '../../../../shared/helpers/String_helps.js';
import PlusIcon from '../../../../style/assets/img/company_spotlight/Plus.svg';
import Input from '../../../../shared/components/forms/Input.jsx';

export default function Step3({ t, formik, detectLanguage }) {
  const options = [
    ...attributes.map((item) => ({
      label: t(firstLetterUpperCase(item)).replaceAll(':', ''),
      value: item
    }))
  ];
  const [descriptionInputName, setDescriptionInputName] = useState('');
  useEffect(() => {
    setDescriptionInputName(
      detectLanguage === 'fr' ? 'description_fr' : 'description'
    );
    formik.setFieldValue(
      'specifications.0.specification',
      detectLanguage === 'fr' ? 'description_fr' : 'description'
    );
    formik.setFieldValue('specifications.0.value', '');
    formik.setFieldValue('description', '');
    formik.setFieldValue('description_fr', '');
  }, [detectLanguage]);

  useEffect(() => {
    formik?.values?.specifications?.forEach((e) => {
      formik.setFieldValue(e.specification, e.value);
    });
  }, [formik.values.specifications]);

  return (
    <Box className="container col-lg-8">
      <FieldArray
        name="specifications"
        render={(arrayHelpers) => (
          <>
            {formik.values?.specifications?.map((_, index) => (
              <Box
                key={index}
                className="container d-lg-flex justify-content-lg-between
 m-2"
              >
                <div className="form-group col-lg-6">
                  <label className="label-input t-body-regular c-fake-black">
                    {t('Specification')}
                  </label>

                  <MultiSelect
                    options={
                      index === 0
                        ? [
                            {
                              label: t('Description'),
                              value: descriptionInputName
                            }
                          ]
                        : options?.filter(
                            (item) =>
                              !formik.values?.specifications
                                ?.map((item) => item.specification)
                                .includes(item.value)
                          ) || []
                    }
                    disabled={index === 0}
                    t={t}
                    isGeoLocation
                    name={`specifications.${index}.specification`}
                    handleChange={(item) => {
                      formik.setFieldValue(
                        index === 0
                          ? descriptionInputName
                          : `specifications.${index}.specification`,
                        item?.value
                      );
                      _.specification = item?.value;
                    }}
                    defaultValue={
                      index === 0
                        ? {
                            label: t('Description'),
                            value: descriptionInputName
                          }
                        : null
                    }
                    value={
                      index === 0
                        ? {
                            label: t('Description'),
                            value: descriptionInputName
                          }
                        : options?.filter(
                            (item) =>
                              item.value ===
                              formik.values.specifications[index].specification
                          )[0]
                    }
                    placeholder={t('Choose_a_specification')}
                  />
                </div>

                <div className="form-group  col-lg-5 d-flex align-items-center">
                  <div className={`${index === 0 ? 'col-12' : 'col-11'}`}>
                    <Input
                      type="text"
                      label={t('Value')}
                      name={`specifications.${index}.value`}
                      className="form-control"
                      placeholder={t('Value')}
                      value={formik.values.specifications[index].value}
                      isNotRequired={index === 0}
                    />
                  </div>
                  <div className="col-1 m-2">
                    {index > 0 && (
                      <button
                        type="button"
                        className="transparent w-auto mt-4 ml-1 "
                        onClick={() => {
                          formik.setFieldValue(
                            formik.values.specifications[index].specification,
                            ''
                          );
                          arrayHelpers.remove(index);
                        }}
                      >
                        <svg
                          width="15"
                          height="15"
                          viewBox="0 0 25 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M19.25 5.25L5.75 18.75"
                            stroke="#333333"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M19.25 18.75L5.75 5.25"
                            stroke="#333333"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </button>
                    )}
                  </div>
                </div>
              </Box>
            ))}

            <div className="button_content m-2">
              <Button
                style={{
                  fontSize: '17px',
                  fontStyle: 'normal',
                  fontWeight: 700,
                  lineHeight: '30px',
                  textTransform: 'capitalize',
                  background: 'none',
                  marginTop: '16px',
                  marginBottom: '16px',
                  position: 'relative',
                  color: '#ECA869',
                  fontFamily: 'inherit'
                }}
                startIcon={<img src={PlusIcon} alt="plus icon" />}
                className="c-yellow"
                type="button"
                onClick={() => {
                  arrayHelpers.push({
                    specification: '',
                    value: ''
                  });
                }}
              >
                {t('Add_specification')}
              </Button>
            </div>
          </>
        )}
      />
    </Box>
  );
}
