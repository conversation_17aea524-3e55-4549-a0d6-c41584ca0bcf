@use "../../mixin/Transparency.scss" as mix; // current

$none: none;
$font-size-base: 20px;
$white-color: white;
$font-weight-bold: "bold";
$font-size-medium: 300;
$font-size-small: 200;
$navbar-margin-brand: "0%";
$navbar-padding-brand: "0%";
$default-color: "#000000";
$navbar-margin-a: "0%";
$navbar-padding-a: "0%";
$navbar-margin-a-btn: "0%";
$navbar-padding-a-btn: "0%";
$border-radius-extreme: "0%";
$dark-gray: #9a9a9a;
$default-states-color: #403d39 !default;
$bg-nude: #fffcf5 !default;
$line-height-general: 1.5em !default;


.navbar {
  border: none;
  justify-content: space-around;
  font-size: 14px;
  transition: all 0.4s;
  -webkit-transition: all 0.4s;
  padding: 0;
  background: $white-color;
  border-bottom:1px solid #F2F2F2;
  box-shadow: 0 4px 20px 0 rgba(173, 173, 173, 0.15);

  .navbar-brand {
    font-weight: $font-weight-bold;
    margin: $navbar-margin-brand;
    padding: $navbar-padding-brand;
    font-size: $font-size-base;
    color: black;
  }

  .navbar-toggler:focus {
    outline: none;
  }

  .form-control-feedback {
    padding-left: 0px;
  }

  &:not([class*="bg"]) {
    .navbar-toggler {
      .navbar-toggler-bar {
        background: black !important;
      }
    }
  }

  .navbar-nav {
    .nav-item .nav-link {
      line-height: 1.6;
      margin: $navbar-margin-a;
      padding: $navbar-padding-a;
      opacity: 0.8;
      font-size: $font-size-small;
      text-transform: uppercase;
      font-weight: 600;
      color: $default-color;

      p {
        margin: 0px 0px;
        text-transform: uppercase;
        font-weight: 600;
        font-size: 12px;
        line-height: 1.5em;
        padding: 15px 0;
      }
    }

    .nav-item .nav-link.btn {
      margin: $navbar-margin-a-btn;
      padding: 9px;
    }

    .nav-item .nav-link [class^="fa"] {
      font-size: 17px;
      position: relative;
      right: 5px;
    }

    .dropdown-menu {
      border-radius: $border-radius-extreme;
      margin-top: 1px;
    }

    .nav-item {
      .btn {
        i {
          color: $white-color;
        }
      }
    }
  }

  .navbar-collapse {
    justify-content: flex-end;

    & .nav-item {
      & .nav-link {
        p {
          display: inline;
        }
      }

      & .dropdown-item {
        i {
          margin: 0 10px;
          margin: 0 10px 0px 5px;
          font-size: 18px;
          position: relative;
          top: 3px;
        }
      }
    }

    &.show {
      & .navbar-nav {
        & .nav-item {
          padding-right: 10px;
        }
      }
    }

    &:after {
      background-color: #fffcf5;
    }
  }

  #navbarSupportedContent {
    .nav-item {
      position: relative;
    }
  }

  .notification-bubble {
    padding: 0.4em 0.6em;
    position: absolute;
    top: 10px;
    right: -2px;
  }

  .btn {
    margin: 0 8px;
    width: auto !important;
    line-height: normal;
    height: 100% !important;
    vertical-align: super;

    i {
      font-size: 14px;
      position: relative;
      top: 2px;
    }
  }

  .btn-simple {
    font-size: $font-size-medium;
  }



  &.navbar-transparent {
    background: transparent !important;
    padding-top: 25px;
    box-shadow: none;

    .navbar-brand {
      color: $white-color;
    }

    .navbar-nav {
      .nav-item .nav-link {
        color: $white-color;
      }
    }

    .navbar-toggler {
      .navbar-toggler-bar {
        background: $white-color !important;
      }
    }
  }

  .logo-container {
    margin-top: 5px;

    .logo {
      overflow: hidden;
      border-radius: 50%;
      border: 1px solid #333333;
      width: 50px;
      float: left;

      img {
        width: 100%;
      }
    }

    .brand {
      font-size: 18px;
      color: #ffffff;
      line-height: 20px;
      float: left;
      margin-left: 10px;
      margin-top: 5px;
      width: 75px;
      height: 50px;
    }
  }

  .navbar-toggler .navbar-toggler-bar + .navbar-toggler-bar,
  .navbar-toggler .navbar-toggler-icon + .navbar-toggler-icon {
    margin-top: 4px;
  }

  .navbar-toggler {
    .navbar-toggler-bar {
      background: $white-color !important;
      display: block;
      position: relative;
      width: 24px;
      height: 2px;
      border-radius: 1px;
      margin: 0 auto;
    }
  }
}

.navbar-transparent,
[class*="bg"] {
  .navbar-brand {
    color: $white-color;
    filter: #{alpha(opacity=90)};

    &:focus,
    &:hover {
      background-color: transparent;
      filter: #{alpha(opacity=100)};
      color: $white-color;
    }
  }

  .navbar-nav {
    .nav-item .nav-link:not(.btn) {
      color: $white-color;
      border-color: $white-color;
    }

    .active .nav-link .active .nav-link:hover,
    .active .nav-link:focus,
    .nav-item .nav-link:hover,
    .nav-item .nav-link:focus {
      background-color: transparent;
      color: $white-color;
      filter: #{alpha(opacity=100)};
    }

    .nav .nav-item a.btn:hover {
      background-color: transparent;
    }

    .dropdown .nav-link .caret,
    .dropdown .nav-link:hover .caret,
    .dropdown .nav-link:focus .caret {
      border-bottom-color: $white-color;
      border-top-color: $white-color;
    }

    .open .nav-link,
    .open .nav-link:hover,
    .open .nav-link:focus {
      background-color: transparent;
      color: $default-color;
      filter: #{alpha(opacity=100)};
    }
  }

  .btn-default.btn-fill {
    color: $dark-gray;
    background-color: $white-color;
    filter: #{alpha(opacity=100)};
  }

  .btn-default.btn-fill:hover,
  .btn-default.btn-fill:focus,
  .btn-default.btn-fill:active,
  .btn-default.btn-fill.active,
  .open .dropdown-toggle.btn-fill.btn-default {
    border-color: $white-color;
    filter: #{alpha(opacity=100)};
  }
}

.navbar-absolute {
  position: absolute;
  width: 100%;
  padding-top: 10px;
  z-index: 1029;
}

.bd-docs {
  .navigation-example {
    .navbar.navbar-transparent {
      padding-top: 0;
      padding: 20px 0;
      margin-top: 20px;
    }
  }
}

@media screen and (max-width: 991px) {
  .navbar-translate {
    position: relative;
    align-items: center;
    transform: translateX(0);
    transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
  }
  .navbar-collapse {
    position: fixed;
    display: block !important;
    top: 0;
    height: 100%;
    width: 230px;
    right: 0;
    z-index: 1032;
    visibility: visible;
    background-color: #999;
    overflow-y: visible;
    border-top: none;
    text-align: left;
    border-left: 1px solid #ccc5b9;
    padding-right: 0;
    padding-left: 40px;
    padding-top: 15px;

    -webkit-transform: translateX(230px);
    -moz-transform: translateX(230px);
    -o-transform: translateX(230px);
    -ms-transform: translateX(230px);
    transform: translateX(230px);
    -webkit-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -moz-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -o-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -ms-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);

    ul {
      position: relative;
      z-index: 3;
      height: 95%;

      &.mobile-menu__top {
        padding: 16px 0 !important;
      }
    }

    .navbar-nav > .nav-item {
      &:last-child {
        border-bottom: 0;
      }

      & > .nav-link {
        margin: 0px 0px;
        color: $dark-gray !important;
        text-transform: uppercase;
        font-weight: 600;
        font-size: $font-size-small;
        line-height: $line-height-general;
        padding: 15px 0;

        &:hover,
        &:active {
          color: $default-states-color !important;
        }
      }
    }

    &::after {
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      position: absolute;
      background-color: $bg-nude;
      background-image: linear-gradient(
                      to bottom,
                      rgba(0, 0, 0, 0) 0%,
                      rgba(112, 112, 112, 0) 60%,
                      rgba(186, 186, 186, 0.15) 100%
      );
      display: block;
      content: "";
      z-index: 1;
    }

    &.has-image::after {
      @include mix.black-filter(0.8);
    }
  }

  .nav-open {
    & .navbar-collapse {
      -webkit-transform: translateX(0px);
      -moz-transform: translateX(0px);
      -o-transform: translateX(0px);
      -ms-transform: translateX(0px);
      transform: translateX(0px);
    }

    & .wrapper {
      left: 0;

      -webkit-transform: translateX(-230px);
      -moz-transform: translateX(-230px);
      -o-transform: translateX(-230px);
      -ms-transform: translateX(-230px);
      transform: translateX(-230px);
    }

    & .navbar-translate {
      -webkit-transform: translateX(-230px);
      -moz-transform: translateX(-230px);
      -o-transform: translateX(-230px);
      -ms-transform: translateX(-230px);
      transform: translateX(-230px);
    }
  }
  .wrapper .navbar-collapse {
    display: none;
  }
}
