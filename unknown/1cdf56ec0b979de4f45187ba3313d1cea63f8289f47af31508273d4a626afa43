import { groupByEquipmentName } from './Array_helpers';

export const optionsEquipperTab = [
  {
    label: 'Requests_management_title',
    tab: 'REQUEST_MANAGEMENT',
    router: '/equipperManagementPortal',
    show: true
  },

  {
    label: 'Equipments_management_title',
    tab: 'EQUIPMENTS_MANAGEMENT',
    router: '/equipperManagementPortal/equipmentManagement',
    subRouter:['/addEquipment','/addSingleEquipment','/updateEquipment','/equipmentDetails'],
    show: true
  },
  {
    label: 'Pricing_marketing_title',
    tab: 'PRICING_AND_MARKETING',
    router: '/equipperManagementPortal/pricingAndMarketing',
    show: true
  },
  {
    label: 'Team_management_details_title',
    tab: 'MANAGE_MEMBERS',
    router: '/equipperManagementPortal/teamManagement',
    show: true
  },

  {
    label: 'Statistics_title',
    tab: 'STATISTICS',
    router: '/equipperManagementPortal/analytics',
    show: true
  },

  {
    label: 'Comments_rating_title',
    tab: 'COMMENTS_AND_RATINGS',
    router: '/equipperManagementPortal/commentsAndRatings',
    show: true
  },
  {
    label: 'Bidz_management_title',
    tab: 'BIDZ_MANAGEMENT',
    router: '/equipperManagementPortal/bidzManagement',
    show: true
  }
];

export const optionsBidzManagementTab = [
  {
    label: 'Bidz_requests',
    id: 'all',
    selectedTab: 'BIDZ_REQUESTS',
    defaultChecked: true
  },
  {
    label: 'Bidz_offer',
    id: 'offer',
    selectedTab: 'BIDZ_OFFERS',
    defaultChecked: false
  }
];
export const ALL_REQUESTS = 'ALL_REQUESTS';
export const PENDING = 'PENDING';
export const ACCEPTED = 'ACCEPTED';
export const DECLINED = 'DECLINED';
export const CANCELED = 'CANCELED';
export const REQUEST_BY_STATUS = 'REQUEST_BY_STATUS';

export const optionsRequestTab = [
  {
    id: 'all',
    value: 'all_requests',
    label: 'All_requests_text',
    lazy: ALL_REQUESTS,
    selectedTab: ALL_REQUESTS,
    defaultChecked: true
  },
  {
    id: 'pending',
    value: 'pending',
    label: 'Pending_text',
    selectedTab: PENDING,
    lazy: REQUEST_BY_STATUS,
    defaultChecked: false
  },
  {
    id: 'accepted',
    value: 'accepted',
    label: 'Accepted_text',
    selectedTab: ACCEPTED,
    lazy: REQUEST_BY_STATUS,
    defaultChecked: false
  },
  {
    id: 'declined',
    value: 'declined',
    label: 'Declined_text',
    selectedTab: DECLINED,
    lazy: REQUEST_BY_STATUS,
    defaultChecked: false
  },
  {
    id: 'canceled',
    value: 'canceled',
    label: 'Canceled_text',
    selectedTab: CANCELED,
    lazy: REQUEST_BY_STATUS,
    defaultChecked: false
  }
];

export const switchRequest = (
  status,
  res,
  pending,
  accepted,
  rejected,
  canceled,
  setRequest,
  request,
  setCanceled,
  setAccepted,
  setPending,
  setRejected
) => {
  switch (status) {
    case 'Pending':
      if (res.data?.length === pending.dataLength) {
        setRequest({
          ...request,
          isEmptyPendingList: true
        });
      }
      setPending(groupByEquipmentName(res.data));
      break;
    case 'Accepted':
      if (res.data?.length === accepted.dataLength) {
        setRequest({
          ...request,
          isEmptyAcceptedList: true
        });
      }
      setAccepted(groupByEquipmentName(res.data));
      break;
    case 'Rejected':
      if (res.data?.length === rejected.dataLength) {
        setRequest({
          ...request,
          isEmptyRejectedList: true
        });
      }
      setRejected(groupByEquipmentName(res.data));
      break;
    case 'Canceled':
      if (res.data?.length === canceled.dataLength) {
        setRequest({
          ...request,
          isEmptyCanceledList: true
        });
      }
      setCanceled(groupByEquipmentName(res.data));
      break;

    default:
      break;
  }
};
export const requestStatus = ['Pending', 'Accepted', 'Rejected', 'Canceled'];

export const getSentRequest = async (
  getRequest,
  request,
  setRequest,
  sent,
  setSent
) => {
  const res = await getRequest(request.itemsPerSent, 'sent');
  if (res.status === 200 && res.data !== null) {
    if (res.data?.length === sent.dataLength) {
      setRequest({
        ...request,
        isEmptySentList: true
      });
    }
    setSent(groupByEquipmentName(res.data));
  }
};
