.credit-check-form-section {
  h2 {
    margin-bottom: 55px;
  }

  .credit-check-form-box {
    display: flex;
    padding: 17px 10px;
    border: 1px solid $check-grey;
    border-radius: 7px;
    position: relative;
    margin-bottom: 20px;

    img {
      margin-right: 15px;
      max-width: 20px;
      @media (min-width: 1200px) {
        margin-right: 50px;
        max-width: 100%;
      }
    }

    .credit-text {
      p {
        margin-bottom: 0;
      }
    }

    .box-right {
      margin-top: 20px;
      margin-left: 20px;
      @media (min-width: 1200px) {
        justify-content: flex-end;
        margin-top: 0;
        margin-left: 0;
      }

      .border-yellow {
        background: none;
        margin: 0 30px 0 13px;
        @media (max-width: 992px) {
          margin: 0 0 0 13px;
        }
      }

      .round-button {
        &.light-yellow {
          @media (max-width: 992px) {
            min-width: 100px;
          }
        }
      }

      .points-btn {
        color: $check-grey;
        font-size: 40px;
        position: absolute;
        right: 15px;
        top: -82px;
        background: none;
        border: none;
        padding-right: 1px;
        box-shadow: none !important;
        @media (min-width: 1200px) {
          font-size: 60px;
          right: -24px;
          top: -42px;
        }
      }
    }
    @media (min-width: 1200px) {
      .menu-dropdown {
        margin: -7px -100px !important;
      }
    }
    .menu-dropdown {
      margin: -7px -35px !important;
    }
    .dropdown-toggle::after {
      display: none;
    }

    .pad-l-mobile {
      @media (max-width: 992px) {
        padding-left: 0 !important;
      }
    }
  }

  .fill-credit {
    border: 1px solid $medium-grey;
    border-radius: 7px;
    max-width: 100%;
    padding: 25px;
    text-align: center;
    margin-bottom: 25px;
    width: 100%;
    @media (min-width: 992px) {
      max-width: 380px;
      margin-bottom: 0;
      float: right;
    }
  }

  @media (max-width: 992px) {
    .padding-l-0 {
      padding-right: 0;
    }
    .container {
      padding: 0 12px;
    }
    .pad-0 {
      padding: 0;
    }
  }
}
