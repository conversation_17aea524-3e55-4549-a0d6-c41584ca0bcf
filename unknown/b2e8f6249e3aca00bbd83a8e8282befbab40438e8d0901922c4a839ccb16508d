import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import AccountSetting from '../../components/settings/Account_setting';
import UnderConstructionModal from '../../shared/components/modals/Under_construction_modal';

export default function Settings() {
  const { t } = useTranslation();
  const [account, setAccount] = useState(false);

  return (
    <>
      <UnderConstructionModal
        t={t}
        show={account}
        onClose={() => setAccount(false)}
      />
      <div className="container settings">
        <h1 className="t-title-extra-large bold c-primary-color">
          {t('Settings')}
        </h1>
        <div className="container tabulation">
          <input type="radio" id="tab1" name="tab" className="tab-input" />
          <label
            htmlFor="tab1"
            className="t-label-medium c-primary-color tab-label"
          >
            {t('Account_settings')}
          </label>
          <input
            type="radio"
            id="tab2"
            name="tab"
            className="tab-input"
            onClick={() => setAccount(true)}
          />
          <label
            htmlFor="tab2"
            className="t-label-medium c-primary-color tab-label"
          >
            {t('Payment_settings')}
          </label>
          <div className="panels">
            <AccountSetting />
          </div>
        </div>
      </div>
    </>
  );
}
