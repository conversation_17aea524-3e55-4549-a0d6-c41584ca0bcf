package firestoredb

import (
	"context"
	"errors"
	"fmt"

	"google.golang.org/api/iterator"

	"github.com/vima-inc/derental/models"
)

const creditCheckFormCollectionName = "credit_check_forms"

// GetCreditCheckFormById returns a credit check form by id.
func (f *firestoredb) GetCreditCheckFormByID(ctx context.Context, id string) (models.CreditCheckForm, error) {
	snapshot, err := f.client.Collection(creditCheckFormCollectionName).Doc(id).Get(ctx)
	if err != nil {
		return models.CreditCheckForm{}, fmt.<PERSON><PERSON><PERSON>("unable to retrieve credit check form  with id: %s error: %w", id, err)
	}

	var creditCheckForm models.CreditCheckForm

	err = snapshot.DataTo(&creditCheckForm)
	if err != nil {
		return models.CreditCheckForm{}, fmt.Errorf("unable to parse credit check form  with id: %s error: %w", id, err)
	}

	return creditCheckForm, nil
}

// AddCreditCheckForm adds a new credit check form.
func (f *firestoredb) AddCreditCheckForm(ctx context.Context, creditCheckForm models.CreditCheckForm) (models.CreditCheckForm, error) {
	newDoc := f.client.Collection(creditCheckFormCollectionName).NewDoc()
	creditCheckForm.ID = newDoc.ID

	_, err := newDoc.Set(ctx, creditCheckForm)
	if err != nil {
		return models.CreditCheckForm{}, fmt.Errorf("unable to set credit check form  with id: %s error: %w", creditCheckForm.ID, err)
	}

	return creditCheckForm, nil
}

// UpdateCreditCheckForm updates an existing credit check form.
func (f *firestoredb) UpdateCreditCheckForm(ctx context.Context, creditCheckForm models.CreditCheckForm) error {
	_, err := f.client.Collection(creditCheckFormCollectionName).Doc(creditCheckForm.ID).Set(ctx, creditCheckForm)
	if err != nil {
		return fmt.Errorf("can not to update credit check form: %w", err)
	}

	return nil
}

// DeleteCreditCheckForm deletes an existing credit check form.
func (f *firestoredb) DeleteCreditCheckForm(ctx context.Context, id string) error {
	_, err := f.client.Collection(creditCheckFormCollectionName).Doc(id).Delete(ctx)
	if err != nil {
		return fmt.Errorf("unable to delete credit check form: %w", err)
	}

	return nil
}

// GetCreditCheckFormByLodgerID returns a credit check form by lodger id.
func (f *firestoredb) GetCreditCheckFormByLodgerID(ctx context.Context, lodgerID string) ([]models.CreditCheckForm, error) {
	var creditCheckForms []models.CreditCheckForm

	iter := f.client.Collection(creditCheckFormCollectionName).
		Where("user", "==", lodgerID).
		Documents(ctx)

	for {
		doc, err := iter.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to iterate over credit check forms: %w", err)
		}

		var creditCheckForm models.CreditCheckForm

		err = doc.DataTo(&creditCheckForm)
		if err != nil {
			return nil, fmt.Errorf("unable to parse credit check form: %w", err)
		}

		creditCheckForms = append(creditCheckForms, creditCheckForm)
	}

	return creditCheckForms, nil
}

// GetProjectsByCCFID returns a list of projects by credit check form id.
func (f *firestoredb) GetProjectsByCCFID(ctx context.Context, ccfID string) ([]models.Project, error) {
	var projects []models.Project

	iter := f.client.Collection(projectCollectionName).
		Where("credit_check_form_id", "==", ccfID).
		Documents(ctx)

	for {
		doc, err := iter.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to iterate over projects: %w", err)
		}

		var project models.Project

		err = doc.DataTo(&project)
		if err != nil {
			return nil, fmt.Errorf("unable to parse project: %w", err)
		}

		projects = append(projects, project)
	}

	return projects, nil
}
