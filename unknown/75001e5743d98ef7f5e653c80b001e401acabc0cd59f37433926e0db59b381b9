package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

func addLead(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var body models.Lead

		err := c.Bind<PERSON>(&body)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.<PERSON>rror()})
			_ = c.Error(err)

			return
		}

		err = svc.AddLead(c.Request.Context(), body)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.<PERSON>rror()})
			_ = c.<PERSON>rror(err)

			return
		}

		c.Status(http.StatusOK)
	}
}
