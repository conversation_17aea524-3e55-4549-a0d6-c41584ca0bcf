/*
  • Global style
  • Helpers
  • Load more and pagination
  ---------- ---------- ---------- ---------- ----------
*/

/*
  • Global style
  ---------- ---------- ---------- ---------- ----------
*/

* {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  box-sizing: border-box;
  vertical-align: baseline;
}

html {
  font-size: 100%;
}

body {
  font-family: $primary-font;
  font-size: 16px;
  line-height: 28px;
  color: $primary-color;
  background-color: $white;
  height: 100vh;
  overflow-x: hidden;
}

.menu-open {
  overflow-y: hidden;
}

/* begin General CSS */

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-family: $primary-font;
}

b,
strong {
  font-weight: 500;
}

ul {
  list-style: none;
}

a {
  color: $primary-color;

  &:hover,
  &:focus,
  &:active,
  &.active {
    color: $primary-color;
    text-decoration: none;
    outline: 0 none;
  }

  & > img {
    border: 0 none;
  }
}

img {
  max-width: 100%;
  height: auto;
}

@media (max-width: 992px) {
  .equipement_rentals img {
    max-width: 60%;
    height: auto;
  }
}

.clear {
  clear: both;
}

.relative {
  position: relative;
}

.hidden {
  visibility: hidden !important;
  display: none !important;
}

/* end General CSS */

main {
  min-height: 40vh;
  background-color: $light-grey;
}

@media (min-width: 768px) {
  .row-md-reverse {
    flex-direction: row-reverse;
  }
}

/*
  • Helpers
  ---------- ---------- ---------- ---------- ----------
*/
.padding-l-desk-0 {
  @media (min-width: 992px) {
    padding-left: 0 !important;
  }
}

.padding-l-0 {
  padding-left: 0 !important;
}

.margin-l-0 {
  margin-left: 0 !important;
}

.padding-r-0 {
  padding-right: 0 !important;
}

.padding-right-10 {
  padding-right: 10px;
}

.margin-top-20 {
  margin-top: 20px;
}

@media (min-width: 992px) {
  .padding-r-fluid {
    padding-right: 40px;
  }
}

@media (max-width: 992px) {
  .padding-lr-mobile {
    padding: 0 10px;
  }
}

.text-right {
  text-align: right;
}

.full-height {
  height: 100%;
}

.bold {
  font-weight: 700;
}

.weight-400 {
  font-weight: 400;
}

.pointer {
  cursor: pointer;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.overflow-x-scroll {
  overflow-x: scroll;
}

.flex {
  display: flex;
}

.hide-scroll-bar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.margin-lr-0 {
  margin-left: 0;
  margin-right: 0;
}

.padding-fluid {
  @media (min-width: 992px) {
    padding-right: 2%;
    padding-left: 0;
  }
}

.ml-1 {
  margin-left: 4px;
}

a,
.nav-link {
  &:hover,
  &:focus {
    color: currentColor;
  }
}

.h-lg-100 {
  height: auto !important;
  @media (min-width: 992px) {
    height: 100% !important;
  }
}

.w-lg-auto {
  @media (min-width: 992px) {
    height: auto !important;
  }
}

@media (max-width: 992px) {
  .border-b-mobile {
    border-bottom: 1px solid $border-grey;
    padding-bottom: 26px;
    margin-bottom: 36px;
  }
}

/*
  • Load more and pagination
  ---------- ---------- ---------- ---------- ----------
*/

.load-more {
  text-align: center;
  margin-top: 50px;
  @media (max-width: 992px) {
    margin-top: 20px;
  }
  .up {
    button {
      &:after {
        content: url('../style/assets/img/Icons/ArrowCircleDown.svg');
        height: 23px;
        padding-left: 8px;
      }
    }
  }
  .down {
    button {
      &:after {
        content: url('../style/assets/img/Icons/ArrowCircleUp.svg');
        height: 23px;
        padding-left: 8px;
      }
    }
  }
}

.pagination {
  text-align: center;
  margin: 50px auto 20px;
  display: block;
  @media (min-width: 992px) {
    margin: 100px auto 20px;
  }

  ul {
    padding-left: 0;

    li {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      width: 35px;
      height: 35px;
      background: $yellow;
      border: 2px solid $yellow;
      border-radius: 50px;
      margin: 0 5px;

      &:hover {
        background: transparent;
      }

      a {
        &:hover {
          color: $black;
          text-decoration: none;
        }
      }

      &.left,
      &.right {
        background: none;
        border: 0;
      }
    }
  }
}

.text-pagination {
  text-align: center;
}

.active {
  color: red;
}

.pad-bottom {
  padding-bottom: 20px;
}

.um-padding-b-0 {
  @media (max-width: 768px) {
    padding-bottom: 0;
  }
}

input[type='text'],
input[type='number'],
input[type='date'],
textarea {
  font-size: 16px !important;
  appearance: none !important;
}

@media (max-width: 992px) {
  .center-mobile {
    text-align: center;

    .round-button {
      font-weight: 500;
    }
  }
}

.height-100 {
  height: 100%;
}

.confirmation {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  line-height: normal;
  border-radius: 7px;
  margin-top: 8px;
  border: 1px solid rgba(39, 174, 96, 0.6);
  background: linear-gradient(
    to right,
    rgba(33, 150, 83, 0.1),
    rgba(39, 174, 96, 0.1)
  );
}

.css-1r7nmah-multiValue {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: $blue-burger;
  background-color: $multiselect_grey !important;
  padding: 6px 16px;
  border-radius: 100px !important;
  align-items: center;

  .css-12jo7m5 {
    font-size: 14px !important;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    color: $blue-burger;
  }

  .css-xb97g8 {
    height: 12px;
  }

  svg {
    fill: $neutrals-gray;
  }
}

@media only screen and (max-width: 1500px) {
  .col-xxl-4 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-xxl-3 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
}

@media (max-width: 1500px) {
  .col-large-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
}

@media (max-width: 992px) {
  .col-large-10 {
    flex: 0 0 auto;
    width: 100%;
  }
}

@media only screen and (max-width: 1200px) {
  .col-xxl-4 {
    flex: 0 0 auto;
    width: 100%;
  }
  .col-xxl-3 {
    flex: 0 0 auto;
    width: 50%;
  }
}

@media only screen and (max-width: 768px) {
  .col-xxl-3 {
    flex: 0 0 auto;
    width: 100%;
  }

  .equipper-spotlight-p-mob {
    padding-left: 0;
    padding-right: 0;
  }
}

div.scrollmenu {
  overflow: hidden;
  white-space: nowrap;

  a {
    display: inline-block;
    color: white;
    text-align: center;
    padding: 14px;
    text-decoration: none;

    &:hover {
      background-color: #777;
    }
  }
}
