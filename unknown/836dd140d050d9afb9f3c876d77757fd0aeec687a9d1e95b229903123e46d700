name: Deploy Backend to Production Environment

on:
  push:
    tags:
      - 'v[0-9].[0-9]+.[0-9]+-backend'

env:
  GCP_REGION: us-central1
  GCP_GCR_HOST: docker.pkg.dev
  MIN_INSTANCES: 0
  MAX_INSTANCES: 30
  CONCURRENCY: 500

jobs:
  build_and_deploy_api:
    runs-on: ubuntu-latest
    env:
      APP_NAME: tooler
      IMAGE_NAME: $GCP_REGION-$GCP_GCR_HOST/${{ secrets.PROD_GCP_PROJECT_ID }}/cloud-run-source-deploy/$APP_NAME

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v0.2.0
        with:
          project_id: ${{ secrets.PROD_GCP_PROJECT_ID }}
          service_account_key: ${{ secrets.PROD_GCP_CREDENTIALS }}

      - name: Configure Docker
        run: gcloud auth configure-docker ${{ env.GCP_REGION }}-${{ env.GCP_GCR_HOST }} --quiet

      - name: Build Docker image
        run: cd backend && docker build -t ${{ env.IMAGE_NAME }}:${{ github.sha }} -f ./cmd/tooler-api/dockerfile .

      - name: Push Docker image
        run: docker push ${{ env.IMAGE_NAME }}:${{ github.sha }}

      - name: Deploy to cloud-run
        run: |
          gcloud run deploy ${{ secrets.GCP_APP_NAME }} \
            --image ${{ env.IMAGE_NAME }}:${{ github.sha }} \
            --region ${{ env.GCP_REGION }} \
            --platform managed \
            --allow-unauthenticated \
            --project ${{ secrets.PROD_GCP_PROJECT_ID }} \
            --min-instances ${{ env.MIN_INSTANCES }} \
            --max-instances ${{ env.MAX_INSTANCES }} \
            --execution-environment gen2 \
            --concurrency ${{ env.CONCURRENCY }} \
            --set-env-vars MODE=production \
            --set-env-vars GOOGLE_CLOUD_PROJECT=${{ secrets.PROD_GCP_PROJECT_ID }} \
            --set-env-vars FIREBASE_API_KEY=${{ secrets.PROD_FIREBASE_API_KEY }} \
            --set-env-vars FIREBASE_STORAGE_BUCKET=${{ secrets.PROD_FIREBASE_STORAGE_BUCKET }} \
            --set-env-vars EQUIPMENT_LIBRARY_URL=${{ secrets.PROD_EQUIPMENT_LIBRARY_URL }} \
            --set-env-vars FRONTEND_URL=${{ secrets.PROD_FRONTEND_URL }} \
            --set-env-vars AIRTABLE_API_KEY=${{ secrets.AIRTABLE_API_KEY }} \
            --set-env-vars SLACK_WEBHOOK_URL=${{ secrets.SLACK_WEBHOOK_URL }} \
            --set-env-vars SENDGRID_API_KEY=${{ secrets.SENDGRID_API_KEY }} \
            --set-env-vars SENDGRID_SENDER=${{ secrets.SENDGRID_SENDER }} \
            --set-env-vars AWS_REGION=${{ secrets.AWS_REGION }} \
            --set-env-vars AWS_ACCESS_KEY=${{ secrets.AWS_ACCESS_KEY }} \
            --set-env-vars AWS_SECRET_KEY=${{ secrets.AWS_SECRET_KEY }} \
            --set-env-vars BOOKING_NOTIFICATION_EMAIL=${{ secrets.PROD_BOOKING_NOTIFICATION_EMAIL }} \
            --set-env-vars STRIPE_API_KEY=${{ secrets.PROD_STRIPE_API_KEY }} \
            --set-env-vars STRIPE_WEBHOOK_SECRET=${{ secrets.PROD_STRIPE_WEBHOOK_SECRET }} \
            --set-env-vars GOOGLE_TRANSLATION_API_KEY=${{ secrets.PROD_GOOGLE_TRANSLATION_API_KEY }} \
            --set-env-vars TEMPLATR_API_KEY=${{ secrets.TEMPLATR_API_KEY }} \
            --set-env-vars CALIFORNIA_TAX_RATE_ID=${{ secrets.PROD_CALIFORNIA_TAX_RATE_ID }} \
            --set-env-vars NEVADA_TAX_RATE_ID=${{ secrets.PROD_NEVADA_TAX_RATE_ID }} \
            --set-env-vars ARIZONA_TAX_RATE_ID=${{ secrets.PROD_ARIZONA_TAX_RATE_ID }} \
            --set-env-vars TEXAS_TAX_RATE_ID=${{ secrets.PROD_TEXAS_TAX_RATE_ID }} \
            --set-env-vars SAR_TAX_RATE_ID=${{ secrets.PROD_SAR_TAX_RATE_ID }}

  build_and_deploy_tooler-equipment-uploader:
    runs-on: ubuntu-latest
    env:
      APP_NAME: tooler-equipment-uploader
      IMAGE_NAME: $GCP_REGION-$GCP_GCR_HOST/${{ secrets.PROD_GCP_PROJECT_ID }}/cloud-run-source-deploy/$APP_NAME
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v0.2.0
        with:
          project_id: ${{ secrets.PROD_GCP_PROJECT_ID }}
          service_account_key: ${{ secrets.PROD_GCP_CREDENTIALS }}

      - name: Configure Docker
        run: gcloud auth configure-docker ${{ env.GCP_REGION }}-${{ env.GCP_GCR_HOST }} --quiet

      - name: Build Docker image
        run: cd backend && docker build -t ${{ env.IMAGE_NAME }}:${{ github.sha }} -f ./cmd/tooler-equipment-uploader/Dockerfile .

      - name: Push Docker image
        run: docker push ${{ env.IMAGE_NAME }}:${{ github.sha }}

      - name: Deploy to cloud-run
        run: |
          gcloud run deploy ${{ env.APP_NAME }} \
            --image ${{ env.IMAGE_NAME }}:${{ github.sha }} \
            --region ${{ env.GCP_REGION }} \
            --platform managed \
            --allow-unauthenticated \
            --project ${{ secrets.PROD_GCP_PROJECT_ID }} \
            --min-instances ${{ env.MIN_INSTANCES }} \
            --max-instances ${{ env.MAX_INSTANCES }} \
            --execution-environment gen2 \
            --concurrency ${{ env.CONCURRENCY }} \
            --set-env-vars MODE=production \
            --set-env-vars GOOGLE_CLOUD_PROJECT=${{ secrets.PROD_GCP_PROJECT_ID }} \
            --set-env-vars FIREBASE_API_KEY=${{ secrets.PROD_FIREBASE_API_KEY }} \
            --set-env-vars FIREBASE_STORAGE_BUCKET=${{ secrets.PROD_FIREBASE_STORAGE_BUCKET }} \
            --set-env-vars EQUIPMENT_LIBRARY_URL=${{ secrets.PROD_EQUIPMENT_LIBRARY_URL }} \
            --set-env-vars FRONTEND_URL=${{ secrets.PROD_FRONTEND_URL }} \
            --set-env-vars AIRTABLE_API_KEY=${{ secrets.AIRTABLE_API_KEY }} \
            --set-env-vars SLACK_WEBHOOK_URL=${{ secrets.SLACK_WEBHOOK_URL }} \
            --set-env-vars SENDGRID_API_KEY=${{ secrets.SENDGRID_API_KEY }} \
            --set-env-vars SENDGRID_SENDER=${{ secrets.SENDGRID_SENDER }} \
            --set-env-vars AWS_REGION=${{ secrets.AWS_REGION }} \
            --set-env-vars AWS_ACCESS_KEY=${{ secrets.AWS_ACCESS_KEY }} \
            --set-env-vars AWS_SECRET_KEY=${{ secrets.AWS_SECRET_KEY }} \
            --set-env-vars BOOKING_NOTIFICATION_EMAIL=${{ secrets.PROD_BOOKING_NOTIFICATION_EMAIL }} \
            --set-env-vars STRIPE_API_KEY=${{ secrets.PROD_STRIPE_API_KEY }} \
            --set-env-vars STRIPE_WEBHOOK_SECRET=${{ secrets.PROD_STRIPE_WEBHOOK_SECRET }} \
            --set-env-vars GOOGLE_TRANSLATION_API_KEY=${{ secrets.PROD_GOOGLE_TRANSLATION_API_KEY }} \
            --set-env-vars TEMPLATR_API_KEY=${{ secrets.TEMPLATR_API_KEY }} \
            --set-env-vars CALIFORNIA_TAX_RATE_ID=${{ secrets.PROD_CALIFORNIA_TAX_RATE_ID }} \
            --set-env-vars NEVADA_TAX_RATE_ID=${{ secrets.PROD_NEVADA_TAX_RATE_ID }} \
            --set-env-vars ARIZONA_TAX_RATE_ID=${{ secrets.PROD_ARIZONA_TAX_RATE_ID }} \
            --set-env-vars TEXAS_TAX_RATE_ID=${{ secrets.PROD_TEXAS_TAX_RATE_ID }} \
            --set-env-vars SAR_TAX_RATE_ID=${{ secrets.PROD_SAR_TAX_RATE_ID }}

  build_and_deploy_deploy_crone:
    runs-on: ubuntu-latest
    env:
      APP_NAME: derental-crone-job
      IMAGE_NAME: $GCP_REGION-$GCP_GCR_HOST/${{ secrets.PROD_GCP_PROJECT_ID }}/cloud-run-source-deploy/$APP_NAME
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v0.2.0
        with:
          project_id: ${{ secrets.PROD_GCP_PROJECT_ID }}
          service_account_key: ${{ secrets.PROD_GCP_CREDENTIALS }}

      - name: Configure Docker
        run: gcloud auth configure-docker ${{ env.GCP_REGION }}-${{ env.GCP_GCR_HOST }} --quiet

      - name: Build Docker image
        run: cd backend && docker build -t ${{ env.IMAGE_NAME }}:${{ github.sha }} -f ./cmd/tooler-cron-jobs/Dockerfile .

      - name: Push Docker image
        run: docker push ${{ env.IMAGE_NAME }}:${{ github.sha }}

      - name: Deploy derental-crone-job
        run: |
          gcloud run deploy ${{ env.APP_NAME }} \
            --image ${{ env.IMAGE_NAME }}:${{ github.sha }} \
            --region ${{ env.GCP_REGION }} \
            --platform managed \
            --allow-unauthenticated \
            --project ${{ secrets.PROD_GCP_PROJECT_ID }} \
            --min-instances ${{ env.MIN_INSTANCES }} \
            --max-instances ${{ env.MAX_INSTANCES }} \
            --execution-environment gen2 \
            --concurrency ${{ env.CONCURRENCY }} \
            --set-env-vars MODE=production \
            --set-env-vars GOOGLE_CLOUD_PROJECT=${{ secrets.PROD_GCP_PROJECT_ID }} \
            --set-env-vars FIREBASE_API_KEY=${{ secrets.PROD_FIREBASE_API_KEY }} \
            --set-env-vars FIREBASE_STORAGE_BUCKET=${{ secrets.PROD_FIREBASE_STORAGE_BUCKET }} \
            --set-env-vars EQUIPMENT_LIBRARY_URL=${{ secrets.PROD_EQUIPMENT_LIBRARY_URL }} \
            --set-env-vars FRONTEND_URL=${{ secrets.PROD_FRONTEND_URL }} \
            --set-env-vars AIRTABLE_API_KEY=${{ secrets.AIRTABLE_API_KEY }} \
            --set-env-vars SLACK_WEBHOOK_URL=${{ secrets.SLACK_WEBHOOK_URL }} \
            --set-env-vars SENDGRID_API_KEY=${{ secrets.SENDGRID_API_KEY }} \
            --set-env-vars SENDGRID_SENDER=${{ secrets.SENDGRID_SENDER }} \
            --set-env-vars AWS_REGION=${{ secrets.AWS_REGION }} \
            --set-env-vars AWS_ACCESS_KEY=${{ secrets.AWS_ACCESS_KEY }} \
            --set-env-vars AWS_SECRET_KEY=${{ secrets.AWS_SECRET_KEY }} \
            --set-env-vars BOOKING_NOTIFICATION_EMAIL=${{ secrets.PROD_BOOKING_NOTIFICATION_EMAIL }} \
            --set-env-vars STRIPE_API_KEY=${{ secrets.PROD_STRIPE_API_KEY }} \
            --set-env-vars STRIPE_WEBHOOK_SECRET=${{ secrets.PROD_STRIPE_WEBHOOK_SECRET }} \
            --set-env-vars GOOGLE_TRANSLATION_API_KEY=${{ secrets.PROD_GOOGLE_TRANSLATION_API_KEY }} \
            --set-env-vars TEMPLATR_API_KEY=${{ secrets.TEMPLATR_API_KEY }} \
            --set-env-vars CALIFORNIA_TAX_RATE_ID=${{ secrets.PROD_CALIFORNIA_TAX_RATE_ID }} \
            --set-env-vars NEVADA_TAX_RATE_ID=${{ secrets.PROD_NEVADA_TAX_RATE_ID }} \
            --set-env-vars ARIZONA_TAX_RATE_ID=${{ secrets.PROD_ARIZONA_TAX_RATE_ID }} \
            --set-env-vars TEXAS_TAX_RATE_ID=${{ secrets.PROD_TEXAS_TAX_RATE_ID }} \
            --set-env-vars SAR_TAX_RATE_ID=${{ secrets.PROD_SAR_TAX_RATE_ID }}
