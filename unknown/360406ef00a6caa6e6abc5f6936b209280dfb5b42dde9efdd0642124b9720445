@media (max-width: 580px) {
  .toggle-style {
    background-color: #ffffff !important;
    border: none;
    border-radius: 29px;
    min-width: 530px !important;
    height: 40px;
    text-align: center;
  }
}

.toggle-style {
  background-color: #ffffff !important;
  border: none;
  border-radius: 29px;
  min-width: 150px !important;
  height: 40px;
  color: black;
  text-align: center;
  box-shadow: 1px 5px 5px lightgray;

  &.form-select {
    min-width: 210px !important;

    &:focus {
      color: black;
    }
  }
}

.btn-primary:hover {
  color: black;
  outline: none !important;
  // border: none!important;
}

.show .btn-primary.dropdown-toggle:active {
  color: black;
  outline: none !important;
  // border: none!important;
}

.show .btn-primary.dropdown-toggle:focus {
  color: black;
  outline: none !important;
  // border: none!important;
}

.show .btn-primary.dropdown-toggle::after {
  color: black;
  outline: none !important;
  // border: none !important;
}

.show .btn-primary.dropdown-toggle::content {
  color: black;
  outline: none !important;
  // border:none!important;
}

.show .btn-primary.dropdown-toggle::selection {
  color: black;
  outline: none !important;
  // border:none!important;
}

.width-sort {
  width: 60%;
  margin-left: 10% !important;
}

filter-style {
  appearance: none;
}

.map-button-container {
  display: flex;
  justify-content: flex-end;
  @media(min-width: 992px) {
    position: relative;
    top: -5px;
  }
}

.button-style {
  &.filter-btn {
    min-width: 105px;
    height: 44px;
  }

  img,
  svg {
    margin-right: 10px;
  }

  &:hover,
  &:active,
  &:focus {
    color: $primary-color;
    border-color: $near-grey;
    background: white;
  }
}

.ml4 {
  margin-left: 40px;

}

.bh40 {
  height: 40px;
}

.space-down {
  padding-bottom: 1% !important;
}

@media (min-width: 750px) and (max-width: 1000px) {
  .space-right {
    margin-right: 8%;
  }
}

@media (max-width: 750px) {
  .space-right {
    margin-bottom: 2%;
  }
}

.sort-container {
  margin-bottom: 2%;
  width: 100%;
}

.space-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 2%;
  width: 100%;
}

.ay-col-1 {
  width: 10%;
}

.ay-col-2 {
  width: 20%;
}

.ay-col-3 {
  width: 30%;
}

.ay-col-4 {
  width: 40%;
}

.ay-col-5 {
  width: 50%;
}

.ay-col-5 {
  width: 50%;
}

.ay-col-6 {
  width: 60%;
}

.ay-col-7 {
  width: 70%;
}

.ay-col-8 {
  width: 80%;
}

.ay-col-9 {
  width: 90%;
}

.ay-col-10 {
  width: 100%;
}
