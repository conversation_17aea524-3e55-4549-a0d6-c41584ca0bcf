import React from 'react';
import { useTranslation } from 'react-i18next';

export default function ViewMore({
  isEmpty,
  isEmptyMessage,
  isLoadingButton,
  text,
  onClick
}) {
  const { t } = useTranslation();
  return (
    <div className="load-more ">
      <div
        className={`text-center request-all ${
          text?.includes('less') ? 'down' : 'up'
        }`}
      >
        {isEmpty ? (
          <span>{isEmptyMessage}</span>
        ) : (
          <>
            {!isLoadingButton ? (
              <button
                className="round-button c-white yellow hover_black d-inline-flex align-items-center"
                onClick={onClick}
              >
                {text || t('View_more_button')}
              </button>
            ) : (
              <p>
                {' '}
                {t('Loading')}
                ...
              </p>
            )}
          </>
        )}
      </div>
    </div>
  );
}
