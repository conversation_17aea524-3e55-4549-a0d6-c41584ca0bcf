import React, { useState } from 'react';

export default function SwitchButton({
  dataContentA,
  dataContentB,
  disableIndividual,
  changeState,
  formik
}) {
  const [state, setstate] = useState(false);
  
  const checkbox = () => {
    setstate(!state);
    changeState(state, formik);
  };

  return (
    <span className="btn btn-1 Sbutton Sbutton-1" onChange={() => checkbox()}>
      <input
        type="checkbox"
        id="switch"
        className=""
        disabled={disableIndividual}
        data-contenta={`${dataContentA}`}
        data-contentb={`${dataContentB}`}
      />
      <label className="justify-content-end"></label>
    </span>
  );
}
