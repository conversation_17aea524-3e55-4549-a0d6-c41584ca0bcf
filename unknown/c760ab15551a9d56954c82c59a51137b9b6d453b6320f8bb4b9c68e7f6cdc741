import React, { useEffect } from 'react';
import DatePickerComponent from 'react-datepicker';
import { firstLetterUpperCase } from '../../shared/helpers/String_helps';
import { ErrorMessage, FastField, FieldArray } from 'formik';
import MultiSelect from '../../shared/components/multi_select/Multi_select';
import {
  getGeoLocationInitialValues,
  getNestedValue
} from '../../shared/helpers/Credit_application';
import GeoLocation from '../../shared/components/inputs/Geonames';
import convertDate from '../../shared/helpers/Date_helper';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExternalLinkAlt } from '@fortawesome/fontawesome-free-solid';
import CustomLabel from '../../shared/components/labels/Custom_label';
import RenderIf from '../../shared/components/Render_if';
import { subDays } from 'rsuite/esm/utils/dateUtils';

function removeSquareBrackets(input) {
  return input.replace(/\[(.*?)\]/g, '');
}

const getDateForField = (individualConfig, field, daysOffset) =>
  individualConfig?.field?.includes(field)
    ? subDays(new Date(), daysOffset)
    : null;

const RecursiveContainer = ({
  config,
  formik,
  isForShow,
  isPrinting,
  t,
  isUS
}) => {
  const builder = (individualConfig, formik) => {
    const arr = individualConfig?.field?.split('.');
    const obj = arr?.[0] || '';
    const key = arr?.[1] || '';

    switch (individualConfig?.type) {
      case 'array':
        return (
          <div className="border-section">
            {individualConfig?.is_extandable || individualConfig?.is_array ? (
              <FieldArray
                name={individualConfig?.field}
                render={(arrayHelpers) => (
                  <div>
                    {formik?.values[individualConfig?.field]?.map(
                      (key, index) => {
                        const filteredData = Object.keys(key)
                          .map((k) => {
                            const matchingChild =
                              individualConfig.children.find((c) =>
                                c.field.includes('[')
                                  ? c.field.split('[')[0] === k
                                  : c.field === k
                              );
                            if (matchingChild) {
                              return {
                                field: `${individualConfig.field}[${index}].${k}`,
                                name: k,
                                key: individualConfig.field,
                                label: matchingChild?.label,
                                type: matchingChild.type,
                                col: matchingChild.col,
                                options: matchingChild?.options,
                                is_required: matchingChild?.is_required
                              };
                            }
                            return null;
                          })
                          .filter(Boolean);
                        return (
                          <div key={index}>
                            <RecursiveContainer
                              config={filteredData}
                              t={t}
                              formik={formik}
                              isUS={isUS}
                              isPrinting={isPrinting}
                              isForShow={isForShow}
                            />
                            {!(
                              isPrinting ||
                              isForShow ||
                              individualConfig?.is_array
                            ) &&
                              index > 0 && (
                                <button
                                  type="button"
                                  className="round-button black shadow small m-2"
                                  onClick={() => arrayHelpers.remove(index)}
                                >
                                  -
                                </button>
                              )}
                          </div>
                        );
                      }
                    )}

                    {!(isPrinting || isForShow || individualConfig?.is_array) &&
                      formik.values[individualConfig?.field]?.length <
                        individualConfig.max && (
                        <div className="d-flex justify-content-end">
                          <button
                            type="button"
                            className="round-button yellow shadow small m-2"
                            onClick={() => {
                              arrayHelpers.push(
                                individualConfig?.children.reduce(
                                  (acc, curr) => ({
                                    ...acc,
                                    [curr?.field]:
                                      curr.type === 'year'
                                        ? new Date().getFullYear().toString()
                                        : ''
                                  }),
                                  {}
                                )
                              );
                            }}
                          >
                            +
                          </button>
                        </div>
                      )}
                  </div>
                )}
              />
            ) : (
              <RecursiveContainer
                config={individualConfig?.children || []}
                formik={formik}
                t={t}
                isUS={isUS}
                isPrinting={isPrinting}
                isForShow={isForShow}
              />
            )}
          </div>
        );
      case 'checkbox':
        return (
          <div className="d-lg-flex align-items-center yes-no-block">
            <button
              disabled={isForShow}
              onClick={() => {
                formik.setFieldValue(individualConfig?.field, true);
              }}
              name={individualConfig?.field}
              type="button"
              className={
                formik.values[obj][key] && formik.values[obj][key] !== 'No'
                  ? 'round-button yellow small'
                  : 'round-button black shadow small'
              }
            >
              {t('Yes_button')}
            </button>
            <button
              type="button"
              onClick={() => {
                formik.setFieldValue(individualConfig?.field, false);
                if (individualConfig?.field.includes('second_applicant')) {
                  formik.setFieldValue('secondary_applicant_info', {
                    applicant_name: '',
                    SSN: '',
                    date_of_birth: subDays(new Date(), 1),
                    year_of_location: new Date().getFullYear().toString(),
                    phone: '',
                    userID: null,
                    expirationID_date: subDays(new Date(), -1),
                    email: '',
                    type: null,
                    role: null,
                    company_name: '',
                    address: {
                      address: '',
                      city: null,
                      zip_code: '',
                      state: null,
                      country: null
                    },
                    telephone: '',
                    state_formed: '',
                    tax_ID: '',
                    signature: '',
                    title: '',
                    signature_date: new Date()
                  });
                }
                formik.setFieldValue(`${individualConfig?.field}_reason`, '');
              }}
              className={
                !formik.values[obj][key] || formik.values[obj][key] === 'No'
                  ? 'round-button yellow small'
                  : 'round-button black shadow small'
              }
              name={individualConfig?.field}
              disabled={isForShow}
            >
              {t('No_button')}
            </button>
            <RenderIf
              condition={
                !isUS &&
                key !== 'required_order_number' &&
                formik.values[obj][key] &&
                formik.values[obj][key] !== 'No'
              }
            >
              <div className="relative mt-lg-0 mt-2 absolute-error">
                <FastField
                  name={
                    !isForShow
                      ? `${individualConfig?.field}_reason`
                      : individualConfig?.field
                  }
                  placeholder={
                    t(individualConfig?.placeholder) ||
                    t(individualConfig?.label)
                  }
                  disabled={isForShow}
                  type="text"
                  formik={formik}
                  className="form-control w-100"
                />
                <ErrorMessage
                  name={`${individualConfig?.field}_reason`}
                  component="span"
                  className="error-message"
                />
              </div>
            </RenderIf>
          </div>
        );

      case 'select':
        return (
          <>
            {isPrinting ? (
              <input
                defaultValue={
                  getNestedValue(formik.values, individualConfig?.field)
                    ?.value ||
                  getNestedValue(formik.values, individualConfig?.field)
                }
                className="form-control w-100"
                name={individualConfig?.field}
              />
            ) : (
              <MultiSelect
                name={individualConfig?.field}
                placeholder={
                  t(individualConfig?.placeholder) || t(individualConfig?.label)
                }
                options={individualConfig?.options}
                isMulti={false}
                handleChange={(value) => {
                  formik.setFieldValue(individualConfig?.field, value);
                }}
                defaultValue={[
                  getNestedValue(formik.values, individualConfig?.field)
                ]}
                disabled={isForShow}
                isDisabled={isForShow}
                t={t}
              />
            )}
            <ErrorMessage
              name={individualConfig?.field}
              component="span"
              className="error-message"
            />
          </>
        );
      case 'geolocation':
        return (
          <>
            {!isPrinting && !isForShow ? (
              <>
                <GeoLocation
                  name={individualConfig?.field}
                  placeholder={
                    t(individualConfig?.placeholder) ||
                    t(individualConfig?.label)
                  }
                  formik={formik}
                  isCCA
                  names={{
                    country: `${individualConfig?.field}.country`,
                    state: `${individualConfig?.field}.state`,
                    city: `${individualConfig?.field}.city`,
                    zip_code: `${individualConfig?.field}.zip_code`,
                    address: `${individualConfig?.field}.address`
                  }}
                  initialValues={getGeoLocationInitialValues(
                    formik,
                    individualConfig
                  )}
                  t={t}
                />
              </>
            ) : (
              <div className="d-flex">
                <div className="col-lg-4 mr-2 mt-0 mb-0">
                  <label className="t-base-medium bold m-2">
                    {t('Country')}{' '}
                    <span className="c-red star-required">*</span>
                  </label>
                  <input
                    name={`${individualConfig?.field}.country`}
                    value={
                      getGeoLocationInitialValues(formik, individualConfig)
                        ?.country?.value ||
                      getGeoLocationInitialValues(formik, individualConfig)
                        ?.country
                    }
                    disabled
                    className="form-control w-100"
                  />
                </div>
                <div className="col-lg-4 mr-2">
                  <label className="t-base-medium bold m-2">
                    {t('State')} <span className="c-red star-required">*</span>
                  </label>
                  <input
                    name={`${individualConfig?.field}.state`}
                    value={
                      getGeoLocationInitialValues(formik, individualConfig)
                        ?.state?.value ||
                      getGeoLocationInitialValues(formik, individualConfig)
                        ?.state
                    }
                    disabled
                    className="form-control w-100"
                  />
                </div>
                <div className="col-lg-4 mr-2">
                  <label className="t-base-medium bold m-2">
                    {t('City')} <span className="c-red star-required">*</span>
                  </label>
                  <input
                    name={`${individualConfig?.field}.city`}
                    value={
                      getGeoLocationInitialValues(formik, individualConfig)
                        ?.city?.value ||
                      getGeoLocationInitialValues(formik, individualConfig)
                        ?.city
                    }
                    disabled
                    className="form-control w-100"
                  />
                </div>
              </div>
            )}
          </>
        );
      case 'date':
        return (
          <div className="date-picker-project form-group">
            {isPrinting || isForShow ? (
              <input
                name={individualConfig?.field}
                value={convertDate(new Date(formik.values[obj][key]))}
                disabled={isForShow}
                className="form-control w-100"
              />
            ) : (
              <>
                <DatePickerComponent
                  label={t(individualConfig?.label)}
                  isForShow={isForShow}
                  onChange={(date) => {
                    formik.setFieldValue(individualConfig?.field, date);
                  }}
                  maxDate={getDateForField(
                    individualConfig,
                    'date_of_birth',
                    1
                  )}
                  minDate={getDateForField(
                    individualConfig,
                    'expirationID_date',
                    -1
                  )}
                  name={individualConfig?.field}
                  selected={
                    formik.values[obj][key]
                      ? new Date(formik.values[obj][key])
                      : individualConfig?.field.includes('expirationID_date')
                      ? subDays(new Date(), -1)
                      : subDays(new Date(), 1)
                  }
                  readOnly={
                    individualConfig.field === 'bond.date' ||
                    isForShow ||
                    key === 'signature_date'
                  }
                  className="form-control w-100"
                />
                <ErrorMessage
                  name={individualConfig?.field}
                  component="span"
                  className="error-message"
                />
              </>
            )}
          </div>
        );
      case 'file':
        return (
          <>
            {isForShow ? (
              <p className="t-base-medium c-primary-color">
                <strong className="t-base-large">
                  {!formik.values[obj][key] ? (
                    <label className="t-base-medium">
                      {t('No_file_uploaded')}
                    </label>
                  ) : (
                    <a
                      href={formik.values[obj][key] || '#'}
                      target={formik.values[obj][key] ? '_blank' : ''}
                      className="t-base-medium c-primary-color"
                      rel="noopener noreferrer"
                    >
                      {t('View_file')}
                      <span className="ml-2">
                        <FontAwesomeIcon
                          icon={faExternalLinkAlt}
                          className="fa-external-link"
                        />
                      </span>
                    </a>
                  )}
                </strong>
              </p>
            ) : isPrinting ? null : (
              <>
                <input
                  type="file"
                  name={individualConfig?.field}
                  onChange={(e) =>
                    formik.setFieldValue(
                      individualConfig?.field,
                      e.target.files[0]
                    )
                  }
                  accept="application/pdf"
                  className="form-control w-100"
                />
                <ErrorMessage
                  name={individualConfig?.field}
                  component="span"
                  className="error-message"
                />
              </>
            )}
          </>
        );
      case 'year':
        return (
          <div className="date-picker-project form-group year ">
            {isPrinting || isForShow ? (
              <input
                name={individualConfig?.field}
                disabled={isForShow}
                value={
                  obj.includes('bank')
                    ? formik.values[removeSquareBrackets(obj)][
                        obj.match(/\[(.*?)\]/)[1]
                      ][key]?.toString()
                    : formik.values[obj][key]?.toString()
                }
                className="form-control w-100"
              />
            ) : (
              <DatePickerComponent
                className="form-control w-100"
                name={individualConfig?.field}
                maxDate={new Date()}
                selected={
                  obj.includes('bank')
                    ? new Date(
                        formik.values[removeSquareBrackets(obj)][
                          obj.match(/\[(.*?)\]/)[1]
                        ][key]?.toString() || null
                      )
                    : new Date(formik.values[obj][key]?.toString() || null)
                }
                readOnly={isForShow}
                showYearPicker
                dateFormat="yyyy"
                onChange={(year) => {
                  formik.setFieldValue(
                    individualConfig?.field,
                    new Date(year).getFullYear().toString()
                  );
                }}
              />
            )}
          </div>
        );
      default:
        return (
          <>
            <FastField
              {...individualConfig}
              formik={formik}
              disabled={isForShow}
              placeholder={
                t(individualConfig?.placeholder) || t(individualConfig?.label)
              }
              className="form-control w-100"
              name={individualConfig?.field}
              type={individualConfig?.type}
            />
            <ErrorMessage
              name={individualConfig?.field}
              component="span"
              className="error-message"
            />
          </>
        );
    }
  };
  const show = (c) => {
    if (
      c.field === 'bankruptcy_equipment_info' &&
      !formik.values.more_info.bankruptcy
    ) {
      return false;
    }
    if (
      c.field === 'secondary_applicant_info' &&
      [false, 'No', '', undefined].includes(
        formik.values.applicant_info.second_applicant
      )
    ) {
      return false;
    }
    if (
      c.field === 'bankruptcy_trade_info' &&
      !formik.values.more_info.bankruptcy
    ) {
      return false;
    }
    if (
      c.field === 'bankruptcy_cashDown_info' &&
      !formik.values.more_info.bankruptcy
    ) {
      return false;
    }
    if (
      (c.field?.includes('attachments') || c.label === 'Attachments') &&
      isPrinting
    ) {
      return false;
    }
    return true;
  };

  useEffect(() => {}, [isPrinting]);

  return (
    <div className="row">
      {config.map((c) => {
        return (
          <>
            {show(c) && (
              <div className={c.col}>
                {c?.label && (
                  <>
                    <label className="t-base-medium bold m-2">
                      {t(firstLetterUpperCase(c?.label))}
                    </label>
                    {c.is_required && <span className="text-danger">*</span>}
                  </>
                )}
                {show(c) && isUS && c?.description && (
                  <label className="t-small mb-2 lh-sm text-justify">
                    {t(c?.description)}
                  </label>
                )}
                {!isUS && c?.description && (
                  <CustomLabel
                    severity="info"
                    text={t(c?.description)}
                    className="error-alert mb-2"
                  />
                )}
                <p className="form-group margin-bottom-20">
                  {builder(c, formik)}
                </p>
              </div>
            )}
          </>
        );
      })}
    </div>
  );
};

export default RecursiveContainer;
