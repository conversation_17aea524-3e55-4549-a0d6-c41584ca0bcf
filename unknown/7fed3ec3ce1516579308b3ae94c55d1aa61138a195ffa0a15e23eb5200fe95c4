package models

// EventType is the type of cloud storage events.
type EventType string

const (
	// FinalizeEvent is the event type when an object is uploaded.
	FinalizeEvent EventType = "OBJECT_FINALIZE"
)

// FileUploadNotification is the message received from PubSub.
type FileUploadNotification struct {
	Message Message `json:"message"`
}

// Message is the message received from PubSub.
type Message struct {
	Attributes Attributes `json:"attributes"`
}

// Attributes is the attributes of the message.
type Attributes struct {
	ObjectID  string `json:"objectId"`
	EventType string `json:"eventType"`
}
