//go:build integration
// +build integration

package ses

import (
	"context"
	"os"
	"testing"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sesv2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	isotrage "github.com/vima-inc/derental/storage"
)

func TestSendFromTemplate(t *testing.T) {
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion("us-east-1"),
	)

	sesClient := sesv2.NewFromConfig(cfg)

	file, err := os.Open("testdata/d-1d4d725b24364979b811487070856681.html")
	assert.NoError(t, err)

	s := &storageMock{}
	s.On("ReadWithMetadata", mock.Anything, "templates", "templates/d-1d4d725b24364979b811487070856681.html").Return(file, isotrage.Metadata{
		"subject": "Test",
	}, nil)

	cli := &client{
		client:    sesClient,
		from:      os.Getenv("DERENTAL_EMAIL"),
		templates: map[string]string{"equipper": "d-1d4d725b24364979b811487070856681"},
		storage:   s,
		bucket:    "templates",
	}

	to := []string{os.Getenv("DERENTAL_EMAIL")}
	attachement, err := os.ReadFile("testdata/attachment.csv")
	assert.NoError(t, err)

	err = cli.SendFromTemplateWithAttachement(context.Background(), "equipper", to,
		map[string]string{
			"billing_address":      "BILLING ADRESS 44",
			"btu":                  "TEST01",
			"category":             "specialized tooling",
			"cfm":                  "TEST04",
			"contact":              "+15143466744",
			"delivery_address":     "DELIVERY ADDRESS 44",
			"delivery_preference":  "delivery",
			"equipment_image_link": "https://firebasestorage.googleapis.com/v0/b/preprod-derental.appspot.com/o/equipment_library%2F03056130_L.jpeg?alt=media&token=5a254993-907c-41ac-bc07-9930297b25e9",
			"equipment_image":      "https://firebasestorage.googleapis.com/v0/b/preprod-derental.appspot.com/o/equipment_library%2F03056130_L.jpeg?alt=media&token=5a254993-907c-41ac-bc07-9930297b25e9",
			"equipment_name":       "Stucco mixer",
			"equipment_utility":    "TEST10",
			"equipper_name":        "Derental’s team",
			"force":                "TEST09",
			"height":               "TEST05",
			"kw":                   "TEST03",
			"length_bidz":          "TEST07",
			"lodger_email":         "<EMAIL>",
			"lodger_id":            "O0w4xxyXSrYxm4SMt8Fw9WYqqvB3",
			"lodger_name":          "felix kjellberg",
			"payment_method":       "credit card",
			"po_number":            "PURSHASE ORDER 44a",
			"return_date":          "2022-07-22T22:59:59.999Z",
			"start_date":           "2022-07-02T23:00:00.000Z",
			"type_of_propulsion":   "Gaz",
			"volt":                 "TEST02",
			"weight":               "TEST08",
			"width":                "TEST06",
			"cancel_comment":       "cancel comment",
		}, map[string][]byte{
			"attachment": attachement,
		})
	assert.NoError(t, err)
}

func TestSendFromTemplateWithObject(t *testing.T) {
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion("us-east-1"),
	)

	sesClient := sesv2.NewFromConfig(cfg)

	file, err := os.Open("testdata/a4fb871d-9fc6-40b0-baf5-5c52710803a0.html")
	assert.NoError(t, err)

	s := &storageMock{}
	s.On("ReadWithMetadata", mock.Anything, "templates", "templates/a4fb871d-9fc6-40b0-baf5-5c52710803a0.html").Return(file, isotrage.Metadata{
		"subject": "Test",
	}, nil)

	cli := &client{
		client:    sesClient,
		from:      os.Getenv("DERENTAL_EMAIL"),
		templates: map[string]string{"equipment-change": "a4fb871d-9fc6-40b0-baf5-5c52710803a0"},
		storage:   s,
		bucket:    "templates",
	}

	to := []string{os.Getenv("DERENTAL_EMAIL")}

	err = cli.SendFromTemplateWithObject(context.Background(), "equipment-change", to,
		map[string]any{
			"equipment_name": "Equipment",
			"equipment_id":   "ID",
			"equipper_name":  "Equipper",
			"fields": map[string]any{
				"price": map[string]any{
					"field":     "price",
					"old_value": "100",
					"new_value": "200",
				},
				"description": map[string]any{
					"field":     "description",
					"old_value": "old description",
					"new_value": "new description",
				},
			},
		})
	assert.NoError(t, err)
}
