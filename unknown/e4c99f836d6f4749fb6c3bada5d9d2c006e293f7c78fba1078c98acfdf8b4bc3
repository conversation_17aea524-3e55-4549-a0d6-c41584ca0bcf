/*
  ---------- ---------- -----summary----- ---------- ---
  • Team managements
  • Table equipments
  • Modal edit member
  ---------- ---------- ---------- ---------- ----------
*/

/*
  • Team managements
  ---------- ---------- ---------- ---------- ----------
*/

.team-managements {
  .empty-state-container {
    margin: 30px 0 0 !important;
    padding: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }

  &__bottom {
    position: relative;

    .buttons-content {
      margin-top: 40px;
      @media (max-width: 992px) {
        text-align: center;
      }

      .round-button {
        min-width: 190px;
        margin-right: 10px;
        @media (max-width: 992px) {
          min-width: 140px;
        }
      }

      .crud-delete {
        @media (max-width: 768px) {
          margin-top: 20px;
        }
      }
    }

    .pagination {
      margin-top: 16px;
    }

    .members {
      text-align: center;
      position: relative;
      @media (min-width: 992px) {
        position: absolute;
        right: 0;
        top: -25px;
      }
    }
  }

  &.special {
    .border {
      padding: 15px 30px;
      align-items: center;
      display: flex;
    }

    .team-managements__bottom {
      .members {
        @media (min-width: 992px) {
          top: -105px;
        }
      }
    }
  }

  h2 {
    @media (max-width: 992px) {
      margin-top: 20px;
      font-size: 20px;
    }
  }
}

/*
  • Table equipments
  ---------- ---------- ---------- ---------- ----------
*/

.table-equipments {
  margin-top: 15px;

  &__head {
    @media (max-width: 992px) {
      display: none;
    }

    label {
      padding-left: 55px;
    }

    .border {
      padding: 15px 22px;
      border: 0 !important; // to force bootstrap class
      @media (min-width: 992px) {
        border: 1px solid $check-grey !important; // to force bootstrap class
      }

      &.special-padding {
        padding: 11px 11px 0 11px;
        border-bottom: 0 !important; // to force bootstrap class border

        > span {
          padding-bottom: 14px;
          display: block;
          text-align: center;
        }
      }
    }
  }

  &__body {
    @media (max-width: 992px) {
      .affect-to {
        .mobile-left {
          width: 35%;
        }

        .mobile-right {
          width: 65%;
          justify-content: space-between;
          overflow: hidden;
          overflow-x: scroll;
        }
      }
    }

    label {
      padding-left: 35px;
      display: initial;
      width: 100%;
      @media (min-width: 992px) {
        padding-left: 55px;
        display: flex;
        width: auto;
      }
    }

    .mobile-right {
      display: flex;
      align-items: center;
      flex: 0 0 auto;
      width: 50%;
      @media (min-width: 992px) {
        width: 100%;
        overflow-wrap: anywhere;
      }

      &.project-tags {
        display: inline-block !important;

        button {
          margin-bottom: 5px;
        }
      }
    }

    .mobile-left {
      flex: 0 0 auto;
      width: 50%;
      @media (min-width: 992px) {
        width: auto;
      }
    }

    @media (max-width: 992px) {
      .mobile-pad {
        padding-left: 35px;

        &.mobile-right {
          padding-left: 20px;
        }
      }
    }

    .border {
      padding: 15px;
      position: relative;
      border: 0 !important; // to force bootstrap class
      @media (min-width: 992px) {
        border: 1px solid $check-grey !important; // to force bootstrap class
        padding: 15px 22px;
      }

      &.special-padding {
        padding: 0 11px;
        border-bottom: 0 !important; // to force bootstrap class
        border-top: 0 !important; // to force bootstrap class
      }
    }

    @media (max-width: 992px) {
      .border-mobile {
        border-bottom: 1px solid $check-grey !important; //to force boostrap class border

        &:last-child {
          border-bottom: 0 !important; //to force boostrap class border
        }
      }
    }

    img {
      width: 35px;
      height: 35px;
      min-width: 35px;
      border-radius: 35px;
      margin-right: 10px;
      border: 1px solid $check-grey;
    }

    span {
      display: block;
      @media (max-width: 992px) {
        padding-left: 0;
      }
    }

    .check-box {
      &.left {
        label {
          &::before {
            @media (max-width: 992px) {
              top: 14px;
            }
          }
        }
      }

      @media (max-width: 992px) {
        display: flex;
        align-items: center;
        width: 100%;
      }

      label {
        &.hidden-box {
          &:before {
            display: none;
          }
        }
      }
    }

    @media (max-width: 992px) {
      .check-box {
        &.no-flex {
          display: block;

          .mobile-right {
            .right-side {
              margin-top: 10px;
            }
          }

          label {
            display: inline-block;

            span.mobile-left {
              margin-top: 13px;
            }
          }

          input:checked + label:after {
            left: -21px !important;
            top: 20px !important;
          }
        }

        &:last-child {
          label {
            margin-bottom: 0;
          }
        }

        &.special-mobileCheck {
          &.left {
            label {
              padding-left: 0;

              &:before {
                left: -30px;
              }
            }
          }
        }
      }
    }

    .btn-client {
      background: $light-blue;
      font-weight: 500;
      border-color: $light-blue;
      padding: 5px 7px;
      margin-right: 5px;
      font-size: 16px;
      @media (max-width: 992px) {
        min-width: max-content;
        width: 100%;
        font-size: 14px;
        padding: 0 7px;
      }
    }

    .affect-rightZone {
      position: absolute;
      right: 30px;
      @media (max-width: 992px) {
        display: none;
      }

      .affect-nb {
        font-size: 17px;
        color: $near-grey;
        width: 36px;
        height: 32px;
        border: 1px solid $check-grey;
        border-radius: 36px;
        display: inline-block;
        text-align: center;
        margin-right: 32px;
      }

      svg {
        color: $check-grey;
      }
    }

    .check-box input:checked + label:after {
      @media (min-width: 992px) {
        top: auto;
      }
    }
  }
}

@media (max-width: 992px) {
  .minBorder-mobile {
    .table-equipments__body {
      .border {
        padding: 10px 5px;
      }
    }
  }
  .mlr-m-0 {
    margin-left: 0;
    margin-right: 0;
  }
  .check-box.left {
    &.no-flex {
      input:checked + label:after {
        left: 9px;
        top: 13px;
      }
    }
  }
}

.add-item-buttom-width {
  width: 65%;
}

.nav-tab {
  width: 50px;
  cursor: pointer;

  svg {
    width: 20px !important;
    height: 20px;
    position: relative;
  }

  &.chevron-right {
    padding-bottom: 0;
  }
}

/*
  • Modal edit member
  ---------- ---------- ---------- ---------- ----------
*/

.no-title-margeTop {
  h2 {
    margin-top: 0;
  }
}

.content-buttons {
  margin-top: 30px;
  text-align: center;

  button {
    min-width: 120px;
    margin: 0 5px;
    @media (min-width: 572px) {
      min-width: 180px;
    }

    &.border-yellow {
      background: transparent;
    }
  }
}

.add-memberModal {
  @media (max-height: 850px) {
    height: auto;
  }

  .add-member {
    display: block;

    svg {
      margin-right: 10px;
    }
  }

  .send-btn {
    margin-top: 45px;
    text-align: center;
    margin-bottom: 45px;
    z-index: 1000;

    button {
      min-width: 320px;
    }
  }

  &__bottom {
    .copy-content {
      position: relative;

      button {
        position: absolute;
        right: 0;
        top: 0;
        background: transparent;
        @media (max-width: 992px) {
          max-height: 36px;
          padding: 2px 15px;
        }
      }
    }
  }
}

.scrollBarModal {
  @media (min-height: 992px) and (min-width: 850px) {
    padding-right: 40px;
  }
  @media (max-height: 1200px) {
    overflow-y: scroll;
    height: calc(90vh - 120px);
    padding-right: 20px;
    overflow-x: hidden;

    &::-webkit-scrollbar-track {
      border-radius: 30px;
      background-color: $light-grey;
    }

    &::-webkit-scrollbar {
      width: 6px;
      background-color: $light-grey;
      border-radius: 30px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 30px;
      background-color: $yellow;
    }
  }
}

.rfs-select-container {
  border: 1px solid $check-grey !important;
  border-radius: 28px !important;
  box-shadow: none !important;
  height: 50px !important;
}

.rfs-control-container {
  box-shadow: none !important;
  border: none !important;
}

.minus-icon {
  margin-top: 45% !important;
  color: $yellow;
}

.disabled {
  pointer-events: none;
}

.status-pending {
  background-color: #ffd500;
  border-radius: 7px;
  height: 40px;
  @media (max-width: 992px) {
    margin-top: 20px;
  }
}

.owner-tag {
  background-color: #eca86933;
  border-radius: 5px;
  height: 70px;
}

.status-accepted {
  background-color: #b0f0bf;
  border-radius: 7px;
  height: 40px;
  @media (max-width: 992px) {
    margin-top: 20px;
  }
}

.status-owner {
  background-color: #b0f0bf;
  border-radius: 7px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  @media (max-width: 992px) {
    margin-top: 20px;
  }
}
