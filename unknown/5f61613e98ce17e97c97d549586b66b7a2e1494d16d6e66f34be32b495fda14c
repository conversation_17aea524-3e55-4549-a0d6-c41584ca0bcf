import React, { useState } from 'react';
import CodeVerification from '../../../features/signin/Code_verification';
import SignIn from '../../../features/signin/Signin';
import AuthProvider from '../../context/Auth_context';
import ForgotPassword from '../../../features/signin/Forget_password';

export default function SignInModal({
  show,
  signIn,
  setShow,
  setShowFPModal,
  t,
  showFPModal
}) {
  const [showCVModal, setShowCVModal] = useState(false);
  return (
    <AuthProvider>
      <SignIn
        showFPM={() => setShowFPModal(!showFPModal)}
        onClose={() => setShow(false)}
        show={show}
        signIn={signIn}
        t={t}
      />
      <ForgotPassword
        onClose={() => setShowFPModal(false)}
        showCVModal={() => setShowCVModal(true)}
        showFPModal={showFPModal}
        t={t}
      />
      <CodeVerification
        onClose={() => setShowCVModal(false)}
        onCloseFPM={() => setShowFPModal(false)}
        showCVModal={showCVModal}
        t={t}
      />
    </AuthProvider>
  );
}
