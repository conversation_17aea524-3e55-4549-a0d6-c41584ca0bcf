import MobileStepper from '@mui/material/MobileStepper';
import Button from '@mui/material/Button';

export default function StepperMobile({
  steps,
  activeStep,
  handleCancel,
  handleSubmit,
  formik,
  nextButtonDisabled,
  disabled,
  setActiveStep,
  t
}) {
  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  return (
    <MobileStepper
      variant="text"
      steps={steps.length}
      position="static"
      activeStep={activeStep}
      nextButton={
        activeStep === steps.length - 1 ? (
          <Button
            size="small"
            type="button"
            disabled={disabled}
            onClick={()=>handleSubmit(formik.values)}
            sx={{
              color: '#ECA869'
            }}
          >
            {t('Submit')}
          </Button>
        ) : (
          <Button
            size="small"
            type="button"
            disabled={nextButtonDisabled}
            sx={{
              color: '#ECA869'
            }}
            onClick={handleNext}
          >
            {t('Next')}
          </Button>
        )
      }
      backButton={
        <Button
          size="small"
          sx={{
            color: '#838E9E'
          }}
          onClick={activeStep === 0 ? () => handleCancel() : () => handleBack()}
        >
          {t('Back_btn')}
        </Button>
      }
    />
  );
}
