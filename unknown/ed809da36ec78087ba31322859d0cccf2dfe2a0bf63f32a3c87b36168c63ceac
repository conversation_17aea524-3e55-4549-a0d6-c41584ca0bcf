import { faArrowLeft, faArrowRight } from '@fortawesome/fontawesome-free-solid';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { scrollLeft, scrollRight } from '../../helpers/Scroll_helper';
import { useState } from 'react';

export default function Swipe({ children, showArrow, id, className }) {
  const [chevronLeft, setChevronLeft] = useState(false);
  const [chevronRight, setChevronRight] = useState(true);
  return (
    <div className="swiper-home d-flex align-items-center">
      {showArrow && chevronLeft && (
        <div className="nav-tab chevron-left">
          <FontAwesomeIcon
            icon={faArrowLeft}
            color="c-primary-color"
            onClick={() => {
              setChevronRight(true);
              scrollRight(id, setChevronLeft);
            }}
          />
        </div>
      )}

      <div className={className} id={id}>
        {children}
      </div>
      {showArrow && chevronRight && (
        <div className="nav-tab chevron-right">
          <FontAwesomeIcon
            icon={faArrowRight}
            color="c-primary-color"
            onClick={() => {
              setChevronLeft(true);
              scrollLeft(id, setChevronRight);
            }}
          />
        </div>
      )}
    </div>
  );
}

Swipe.defaultProps = {
  className: 'scrollmenu'
};
