package middleware

import "github.com/gin-gonic/gin"

// Cors is a middleware that adds CORS headers to the response.
func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON>.Header().Set("Access-Control-Allow-Origin", "*")
		c.<PERSON>.Header().Set("Access-Control-Allow-Credentials", "true")
		c.<PERSON>.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding,  Authorization, accept, origin, Cache-Control")
		c.<PERSON>.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)

			return
		}

		c.Next()
	}
}
