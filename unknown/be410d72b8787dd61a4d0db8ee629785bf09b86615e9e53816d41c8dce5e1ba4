import React from 'react';
import DataTable from 'react-data-table-component';
import { connectInfiniteHits } from 'react-instantsearch-dom';
import CrudOptions from '../../shared/components/buttons/Crud_options';

const InfiniteHits = ({
  hits,
  setShow,
  setAction,
  setSelectedEquipment,
  setShowConfirmationModal,
  columns,
  uploadPhoto,
  onImageChange
}) => (
  <DataTable
    columns={columns}
    data={hits}
    expandableRows
    pagination
    expandableRowsComponent={row => (
      <CrudOptions
        data={row.data}
        setAction={setAction}
        setSelectedItem={setSelectedEquipment}
        showUpdateModal={() => {
          setShow(true);
        }}
        onImageChange={onImageChange}
        uploadPhoto={uploadPhoto}
        showConfirmationModal={() => {
          setShowConfirmationModal(true);
        }}
      />
    )}
  />
);

export const CustomBidzInfiniteHits = connectInfiniteHits(InfiniteHits);
