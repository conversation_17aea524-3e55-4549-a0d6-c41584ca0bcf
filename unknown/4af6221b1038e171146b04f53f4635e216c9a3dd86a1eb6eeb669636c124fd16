import React from 'react';
import { Form, Formik } from 'formik';
import PropTypes from 'prop-types';

const FormikForm = ({
  initialValues,
  onSubmit,
  component: ChildrenComponent, 
  validationSchema
}) => {
  return (
    <Formik
      initialValues={initialValues}
      onSubmit={(values) =>{
        onSubmit(values);
      }}
      validationSchema={validationSchema}
    >
      {(formik) => (
        <Form>
          <ChildrenComponent formik={formik} />
        </Form>
      )}
    </Formik>
  );
};

FormikForm.propTypes = {
  initialValues: PropTypes.object.isRequired,
  onSubmit: PropTypes.func.isRequired,
  validationSchema: PropTypes.object.isRequired,
  component: PropTypes.elementType.isRequired
};

export default FormikForm;
