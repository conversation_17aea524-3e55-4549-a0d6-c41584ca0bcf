import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import UnavailableImg from '../../../style/assets/img/unavailable_img.svg';
import TooloProfile from '../../../style/assets/img/user-profile.svg';

export default function CustomImage({
  className,
  alt,
  isUser,
  onClick,
  imageUrl
}) {
  const [image, setImage] = useState(imageUrl);

  const onErrorImage = (e) => {
    if (isUser) {
      e.target.src = TooloProfile;
    } else {
      e.target.src = UnavailableImg;
    }
  };

  const onLoadImage = (e) => {
    return e.target.src !== UnavailableImg && e.target.src !== TooloProfile;
  };

  useEffect(() => {
    if (imageUrl) {
      setImage(imageUrl);
    } else {
      setImage(isUser ? TooloProfile : UnavailableImg);
    }
  }, [imageUrl, isUser]);

  return (
    <img
      className={className}
      onClick={onClick}
      src={image}
      alt={alt}
      onLoad={onLoadImage}
      onError={onErrorImage}
    />
  );
}

CustomImage.propTypes = {
  className: PropTypes.string,
  alt: PropTypes.string,
  isUser: PropTypes.bool,
  onClick: PropTypes.func,
  imageUrl: PropTypes.string
};

CustomImage.defaultProps = {
  className: '',
  alt: '',
  isUser: false,
  onClick: () => {},
  imageUrl: ''
};
