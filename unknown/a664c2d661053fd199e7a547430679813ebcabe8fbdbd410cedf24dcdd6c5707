import React, { useContext } from 'react';

import axiosFactory, {
  METHOD_POST,
  METHOD_GET
} from '../helpers/Context_helpers';

import {
  BIDZ_REQUEST,
  ADD_BIDZ_REQUEST,
  GET_BIDZ_REQUEST_EQUIPPER,
  GET_BIDZ_REQUEST_LODGER,
  SET_BID_OFFER,
  BIDZ_OFFER,
  GET_BIDZ_OFFER_BY_RENTER_ID,
  GET_BIDZ_OFFER_BY_EQUIPPER_ID
} from '../helpers/Url_constants';

const BidzContext = React.createContext();

export function useBidzContext() {
  return useContext(BidzContext);
}

export default function BidzProvider({ children }) {
  async function SubmitBidzRequest(bidzRequestItem) {
    return await axiosFactory({
      url: ADD_BIDZ_REQUEST,
      method: METHOD_POST,
      data: bidzRequestItem
    });
  }
  async function GetBidzOfferByRenterID(itemsPerPage, status) {
    const url = `${GET_BIDZ_OFFER_BY_RENTER_ID}?limit=${itemsPerPage}&status=${status}`;

    return await axiosFactory({
      url,
      method: METHOD_GET
    });
  }
  async function GetBidzOfferByEquipperID(itemsPerPage, status) {
    return await axiosFactory({
      url: `${GET_BIDZ_OFFER_BY_EQUIPPER_ID}?limit=${itemsPerPage}&status=${status}`,
      method: METHOD_GET
    });
  }
  async function BidzOfferActions(id) {
    return await axiosFactory({
      url: BIDZ_OFFER + id,
      method: METHOD_POST
    });
  }
  async function BidzCanceledOffer(id, bidzOffer) {
    return await axiosFactory({
      url: `${BIDZ_OFFER + id}/cancel`,
      method: METHOD_POST,
      data: bidzOffer
    });
  }
  async function GetBidzRequestByRenterID(itemsPerPage, status) {
    return await axiosFactory({
      url: `${GET_BIDZ_REQUEST_LODGER}?limit=${itemsPerPage}&status=${status}`,
      method: METHOD_GET
    });
  }

  async function GetBidzRequestByEquipperID(itemsPerPage, status) {
    return await axiosFactory({
      url: `${GET_BIDZ_REQUEST_EQUIPPER}?limit=${itemsPerPage}&status=${status}`,
      method: METHOD_GET
    });
  }
  async function AcceptBidzRequest(id) {
    return await axiosFactory({
      url: `${BIDZ_REQUEST}${id}/accept`,
      method: METHOD_POST
    });
  }
  async function DeclineBidzRequest(id, bidzRequest) {
    return await axiosFactory({
      url: `${BIDZ_REQUEST}${id}/reject`,
      method: METHOD_POST,
      data: bidzRequest
    });
  }
  async function SetBidOffer(bidOffer) {
    return await axiosFactory({
      url: SET_BID_OFFER,
      method: METHOD_POST,
      data: bidOffer
    });
  }
  const value = {
    SubmitBidzRequest,
    GetBidzRequestByRenterID,
    GetBidzOfferByRenterID,
    GetBidzRequestByEquipperID,
    GetBidzOfferByEquipperID,
    BidzOfferActions,
    BidzCanceledOffer,
    AcceptBidzRequest,
    SetBidOffer,
    DeclineBidzRequest
  };

  return <BidzContext.Provider value={value}>{children}</BidzContext.Provider>;
}
