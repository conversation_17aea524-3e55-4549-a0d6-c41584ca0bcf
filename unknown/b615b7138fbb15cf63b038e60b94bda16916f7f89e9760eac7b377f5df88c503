package service

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_getPictureURL(t *testing.T) {
	t.Parallel()

	tests := []struct {
		equipmentLibraryURL string
		equipmentName       string
		want                string
	}{
		{
			equipmentName:       "air breaker",
			equipmentLibraryURL: "https://storage.googleapis.com/dev-derental.appspot.com/equipment_library",
			want:                "https://storage.googleapis.com/dev-derental.appspot.com/equipment_library/air%20breaker",
		},
		{
			equipmentName:       "articulating boom lift bucket van",
			equipmentLibraryURL: "https://storage.googleapis.com/prod-derental.appspot.com/equipment_library",
			want:                "https://storage.googleapis.com/prod-derental.appspot.com/equipment_library/articulating%20boom%20lift%20bucket%20van",
		},
		{
			equipmentName:       "chain block(2)",
			equipmentLibraryURL: "https://storage.googleapis.com/dev-derental.appspot.com/equipment_library",
			want:                "https://storage.googleapis.com/dev-derental.appspot.com/equipment_library/chain%20block%282%29",
		},
	}

	for _, tt := range tests {
		tt := tt

		t.Run(tt.equipmentName, func(t *testing.T) {
			t.Parallel()

			got := getPictureURL(tt.equipmentName, tt.equipmentLibraryURL)
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_appendIfNotExist(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name    string
		initial []string
		value   []string
		want    []string
	}{
		{
			name:    "appendIfNotExist",
			initial: []string{"a", "b", "c"},
			value:   []string{"d", "b", "e"},
			want:    []string{"a", "b", "c", "d", "e"},
		},
		{
			"value is empty",
			[]string{"a", "b", "c"},
			[]string{},
			[]string{"a", "b", "c"},
		},
		{
			"initial is empty",
			[]string{},
			[]string{"a", "b", "c"},
			[]string{"a", "b", "c"},
		},
	}

	for _, tt := range tests {
		tt := tt

		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			got := appendIfNotExist(tt.initial, tt.value...)
			assert.Equal(t, tt.want, got)
		})
	}
}
