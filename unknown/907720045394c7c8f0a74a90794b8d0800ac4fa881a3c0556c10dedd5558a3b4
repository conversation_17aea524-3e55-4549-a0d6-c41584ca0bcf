package service

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/dustin/go-humanize"
	"github.com/google/uuid"
	"github.com/vima-inc/derental/mailer"
	"github.com/vima-inc/derental/models"
)

// sendEmailWithTemplate sends an email using a template based on the recipient's language preference.
func (s *Service) sendEmailWithTemplate(ctx context.Context, templateIDFr, templateIDEn, recipient string, data map[string]string, lang models.CommunicationPreferences) error {
	var templateID string
	if lang == models.French {
		templateID = templateIDFr
	} else {
		templateID = templateIDEn
	}

	err := s.mailer.SendFromTemplate(ctx, templateID, []string{recipient}, data)
	if err != nil {
		return fmt.Errorf("unable to send email: %w", err)
	}

	return nil
}
func (s *Service) sendEmailWithTemplateAndAttachment(ctx context.Context, templateIDFr string, templateIDEn string, recipient string, data map[string]string, lang models.CommunicationPreferences, order models.Order, booking models.BookEquipment) interface{} {
	templateID, attachment, err := s.prepareEmailData(ctx, templateIDFr, templateIDEn, lang, order, booking)
	if err != nil {
		log.Printf(": %v", err)
		return err
	}

	err = s.mailer.SendFromTemplateWithAttachement(ctx, templateID, []string{recipient}, data, attachment)
	if err != nil {
		log.Printf("unable to send email with attachment Error: %v", err)
		return fmt.Errorf(" unable to send email Error: %w", err)
	}

	return nil
}

func (s *Service) prepareEmailData(ctx context.Context, templateIDFr string, templateIDEn string, lang models.CommunicationPreferences, order models.Order, booking models.BookEquipment) (string, map[string][]byte, error) {
	var templateID string
	var attachment map[string][]byte
	invoiceDetails := s.pdfDataFromOrder(ctx, order, booking)
	if lang == models.French {
		templateID = templateIDFr

		pdf, err := s.pdfGenerator.GeneratePDF(ctx, templatrInvoiceFR, invoiceDetails)
		if err != nil {
			return "", nil, fmt.Errorf(" unable to generate PDF for email error: %v", err)
		}
		attachment = s.mailDataInvoiceAttachmentFR(pdf)
	} else {
		templateID = templateIDEn
		pdf, err := s.pdfGenerator.GeneratePDF(ctx, templatrInvoiceEN, invoiceDetails)
		if err != nil {
			return "", nil, fmt.Errorf(" unable to generate PDF for email error: %v", err)
		}
		attachment = s.mailDataInvoiceAttachment(pdf)
	}

	return templateID, attachment, nil
}

// sendBookingRequestEmails sends booking request emails to equipper, lodger, and admin.
func (s *Service) sendBookingRequestEmails(ctx context.Context, equipper models.Equipper, lodger models.Lodger, bookingRequest models.BookEquipment) {
	onClickLink := fmt.Sprintf("%s?sign_in=true", s.frontendURL)

	// Send booking request email to equipper
	if err := s.sendEmailWithTemplate(ctx, mailer.SendgridBookingEquipperRequestIDFR, mailer.SendgridBookingEquipperRequestID, bookingRequest.EquipperEmail, mailDataFromBookEquipment(bookingRequest, onClickLink), equipper.CommunicationPreferences); err != nil {
		log.Printf("unable to send booking request email to equipper %s error: %v", bookingRequest.EquipperEmail, err)
	}

	// Send booking request email to lodger
	if err := s.sendEmailWithTemplate(ctx, mailer.SendgridBookingLodgerRequestIDFR, mailer.SendgridBookingLodgerRequestID, bookingRequest.LodgerEmail, mailDataFromBookEquipment(bookingRequest, onClickLink), lodger.CommunicationPreferences); err != nil {
		log.Printf("unable to send booking request email to lodger %s error: %v", bookingRequest.LodgerEmail, err)
	}

	// Notify admin about new booking request
	if err := s.sendEmailWithTemplate(ctx, mailer.SendgridBookingEquipperRequestID, mailer.SendgridBookingEquipperRequestID, s.bookingNotificationEmail, mailDataFromBookEquipment(bookingRequest, onClickLink), models.English); err != nil {
		log.Printf("unable to send booking request email to admins %v error: %v", s.bookingNotificationEmail, err)
	}

	// Notify Equipper's RecipientList about new booking request
	for i := 0; i < len(equipper.RecipientList); i++ {
		if err := s.sendEmailWithTemplate(ctx, mailer.SendgridBookingEquipperRequestIDFR, mailer.SendgridBookingEquipperRequestID, equipper.RecipientList[i], mailDataFromBookEquipment(bookingRequest, onClickLink), equipper.CommunicationPreferences); err != nil {
			log.Printf("unable to send booking request email to admins %v error: %v", s.bookingNotificationEmail, err)
		}
	}

	// Notify Lodger's RecipientList about new booking request
	for i := 0; i < len(lodger.RecipientList); i++ {
		if err := s.sendEmailWithTemplate(ctx, mailer.SendgridBookingLodgerRequestIDFR, mailer.SendgridBookingLodgerRequestID, lodger.RecipientList[i], mailDataFromBookEquipment(bookingRequest, onClickLink), lodger.CommunicationPreferences); err != nil {
			log.Printf("unable to send booking request email to admins %v error: %v", s.bookingNotificationEmail, err)
		}
	}
}

// BookEquipment send request for booking an equipment.
func (s *Service) BookEquipment(ctx context.Context, lodgerID string, input models.BookEquipmentRequest) (string, error) {
	equipment, err := s.db.GetEquipmentByID(ctx, input.EquipmentID)
	if err != nil {
		return "", fmt.Errorf("unable to get equipment: %w", err)
	}

	log.Printf("Start Date: %v, Available From: %v", input.StartDate.UTC(), equipment.AvailableFrom.UTC())
	if input.StartDate.UTC().Before(equipment.AvailableFrom.UTC()) {
		return "", fmt.Errorf("equipment %s is not available", input.EquipmentID)
	}

	lastBooking, err := s.db.GetBookEquipmentByLodgerID(ctx, lodgerID)
	if err != nil {
		return "", fmt.Errorf("unable to get booking for lodger with id: %s error %w", lodgerID, err)
	}

	for _, book := range lastBooking {
		if book.EquipmentID == input.EquipmentID && (book.Status == models.BookingAccepted || book.Status == models.BookingPending) && input.StartDate.Before(book.EndDate) && input.EndDate.After(book.StartDate) {
			return "", fmt.Errorf("you already have a booking for this equipment")
		}
	}

	lodger, err := s.db.GetLodgerByID(ctx, lodgerID)
	if err != nil {
		return "", fmt.Errorf("unable to get lodger: %w", err)
	}

	equipper, err := s.db.GetEquipperByID(ctx, input.EquipperID)
	if err != nil {
		return "", fmt.Errorf("unable to get equipper: %w", err)
	}

	// validate equipment start date and enddate are not in the closed days of the week
	workHours := equipper.WorkHours
	startDatePlusOne := input.StartDate.AddDate(0, 0, 1)
	endDatePlusOne := input.EndDate.AddDate(0, 0, 1)

	for _, workHour := range workHours {
		if workHour.DayOff {
			if startDatePlusOne.Weekday().String() == workHour.DayOfWeek || endDatePlusOne.Weekday().String() == workHour.DayOfWeek {
				return "", fmt.Errorf("equipper is closed during the requested booking dates")
			}
		}
	}

	// calculate the total price of the booking
	duration := input.EndDate.Sub(input.StartDate).Hours()/24 + 1
	days := int(duration) % 7
	week := int(duration) / 7 % 4
	fourWeeks := int(duration) / 28
	subTotal := float64(days)*equipment.Price.Day + float64(week)*equipment.Price.Week + float64(fourWeeks)*equipment.Price.Month
	// serviceFees := subTotal * 0.03
	serviceFees := 0.0

	waiverInsurance := 0.0

	if input.NeedOne {
		waiverInsurance = subTotal * 0.15
	}

	totalAmount := subTotal + serviceFees + waiverInsurance

	adminIDs := []string{}

	if lodger.MemberOf != "" {
		admins, err := s.db.GetAdminsByOwnerID(ctx, lodger.MemberOf)
		if err != nil {
			return "", fmt.Errorf("unable to get admins: %w", err)
		}

		for _, admin := range admins {
			adminIDs = append(adminIDs, admin.ID)
		}
	}

	bookingRequest := models.BookEquipment{
		EquipperID:               input.EquipperID,
		EquipperName:             equipment.EquipperName,
		EquipperEmail:            equipment.EquipperEmail,
		EquipperImageLink:        equipper.PhotoURL,
		EquipmentID:              input.EquipmentID,
		EquipmentName:            equipment.NameEN,
		EquipmentNameFR:          equipment.NameFR,
		EquipmentImageLink:       equipment.ImageLink,
		EquipperEquipmentPicture: equipment.EquipperEquipmentPicture,
		StartDate:                input.StartDate,
		EndDate:                  input.EndDate,
		OwnerID:                  lodger.MemberOf,
		AdminIDs:                 adminIDs,
		LodgerID:                 lodgerID,
		LodgerEmail:              lodger.Email,
		LodgerName:               lodger.FullName,
		LodgerImageLink:          lodger.PhotoURL,
		LodgerPhoneNumber:        lodger.PhoneNumber,
		LodgerAddress:            lodger.Address.City,
		Currency:                 input.Currency,
		Status:                   models.BookingCreated,
		CreatedAt:                time.Now(),
		CreditCheckFormID:        input.CreditCheckForm.ID,
		CreditCheckForm:          input.CreditCheckForm,
		Category:                 equipment.Category,
		SubCategory:              equipment.SubCategory,
		Price:                    equipment.Price,
		SpecialPrice:             input.SpecialPrice,
		Description:              equipment.Description,
		DescriptionFR:            equipment.DescriptionFR,
		DeliveryAddress:          input.DeliveryAddress,
		BillingAddress:           input.BillingAddress,
		DeliveryDetails:          input.DeliveryDetails,
		PaymentMethod:            input.PaymentMethod,
		PoNumber:                 input.PoNumber,
		SubTotal:                 subTotal,
		ServiceFees:              serviceFees,
		TotalAmount:              totalAmount,
		WaiverInsurance:          waiverInsurance,
		ProjectID:                input.ProjectID,
		InsuranceCompany:         input.InsuranceCompany,
		InsurancePolicyNumber:    input.InsurancePolicyNumber,
		InsuranceCoverage:        input.InsuranceCoverage,
		ExpiryDateOfInsurance:    input.ExpiryDateOfInsurance,
		Comment:                  input.Comment,
		NeedOne:                  input.NeedOne,
		DeliveryType:             input.DeliveryType,
		ReceiverInfo:             input.ReceiverInfo,
		CreatedByMember:          false,
		CompanyName:              input.CompanyName,
		// All equipments specific fields
		InternalID:          equipment.InternalID,
		Brand:               equipment.Brand,
		BrandModel:          equipment.BrandModel,
		DriveType:           equipment.DriveType,
		Weight:              equipment.Weight,
		Height:              equipment.Height,
		Width:               equipment.Width,
		Length:              equipment.Length,
		Diameter:            equipment.Diameter,
		CutDiameter:         equipment.CutDiameter,
		Force:               equipment.Force,
		UsageHours:          equipment.UsageHours,
		BTU:                 equipment.BTU,
		Volt:                equipment.Volt,
		Watt:                equipment.Watt,
		CFM:                 equipment.CFM,
		Capacity:            equipment.Capacity,
		Consumption:         equipment.Consumption,
		TypeOfPropulsion:    equipment.TypeOfPropulsion,
		PlatformHeight:      equipment.PlatformHeight,
		WorkingHeight:       equipment.WorkingHeight,
		HorizontalOutreach:  equipment.HorizontalOutreach,
		PlatformCapacity:    equipment.PlatformCapacity,
		PlatformDimension:   equipment.PlatformDimension,
		PlatformExtension:   equipment.PlatformExtension,
		ExtensionCapacity:   equipment.ExtensionCapacity,
		PlatformRotation:    equipment.PlatformRotation,
		MachineRotation:     equipment.MachineRotation,
		MachineWidth:        equipment.MachineWidth,
		MachineLength:       equipment.MachineLength,
		MachineHeight:       equipment.MachineHeight,
		ClosedMachineHeight: equipment.ClosedMachineHeight,
		ClosedMachineLength: equipment.ClosedMachineLength,
		ClosedMachineWidth:  equipment.ClosedMachineWidth,
		BasketCapacity:      equipment.BasketCapacity,
		BasketLength:        equipment.BasketLength,
		BasketWidth:         equipment.BasketWidth,
		LegsLocation:        equipment.LegsLocation,
		FloorHeight:         equipment.FloorHeight,
		CabinHeight:         equipment.CabinHeight,
		Wheelbase:           equipment.Wheelbase,
		WheelSize:           equipment.WheelSize,
		PlateDimension:      equipment.PlateDimension,
		Decibel:             equipment.Decibel,
		RollWidth:           equipment.RollWidth,
		Compaction:          equipment.Compaction,
		Vibrations:          equipment.Vibrations,
		Lumen:               equipment.Lumen,
		Pressure:            equipment.Pressure,
		Frequency:           equipment.Frequency,
		TiltingCapacity:     equipment.TiltingCapacity,
		OperationCapacity:   equipment.OperationCapacity,
		TankCapacity:        equipment.TankCapacity,
		DiggingDepth:        equipment.DiggingDepth,
		DumpingHeight:       equipment.DumpingHeight,
		DiggingRadius:       equipment.DiggingRadius,
		TechnicalDataSheet:  equipment.TechnicalDataSheet,
		EquipmentUsages:     equipment.EquipmentUsages,
	}

	bookingID, err := s.db.AddBookEquipment(ctx, bookingRequest)
	if err != nil {
		return "", fmt.Errorf("unable to add booking: %w", err)
	}

	bookingRequest.ID = bookingID
	equipper.HasRequests = true

	err = s.db.UpdateEquipper(ctx, equipper)
	if err != nil {
		return "", fmt.Errorf("unable to update equipper: %w", err)
	}

	promo, err := s.db.GetPromotionCodeByEquipperIDAndLodgerID(ctx, input.EquipperID, lodgerID)
	if err != nil {
		return "", fmt.Errorf("unable to get promotion code: %w", err)
	}

	switch input.PaymentMethod {
	case models.PaymentMethodCreditCard:
		if lodger.CustomerID == "" {
			customerID, err := s.payment.CreateCustomer(lodger.FullName, lodger.Email, equipper.Address)
			if err != nil {
				return "", fmt.Errorf("unable to create customer: %w", err)
			}
			lodger.CustomerID = customerID
			err = s.db.UpdateLodger(ctx, lodger)
			if err != nil {
				return "", fmt.Errorf("unable to update lodger: %w", err)
			}
		}

		customerShippingAddress := equipper.Address
		customerShippingAddress.Name = equipper.Company

		items := []models.Item{
			{
				ID:        uuid.New().String(),
				Name:      equipment.NameEN,
				Quantity:  1,
				Price:     int64((bookingRequest.SubTotal + bookingRequest.ServiceFees) * 100),
				IsTaxable: true,
				Coupon:    promo.ID,
			},
		}

		if bookingRequest.NeedOne {
			items = append(items, models.Item{
				ID:        uuid.New().String(),
				Name:      "Limited Damage Waiver",
				Quantity:  1,
				Price:     int64(bookingRequest.WaiverInsurance * 100),
				IsTaxable: true,
			})
		}

		checkoutURL, err := s.CreateOrder(ctx, models.Order{
			ID:            uuid.New().String(),
			Items:         items,
			UserID:        lodgerID,
			BookingID:     bookingID,
			CustomerID:    lodger.CustomerID,
			CustomerEmail: lodger.Email,
			Currency:      models.Currency(bookingRequest.Price.Currency),
			Address:       customerShippingAddress,
		}, "/", "/")
		if err != nil {
			return "", fmt.Errorf("unable to create order: %w", err)
		}

		return checkoutURL, nil
	case models.PaymentMethodCreditAccount:
		bookingRequest.Status = models.BookingPending
		bookingRequest.Amount = totalAmount
		bookingRequest.DiscountAmount = (totalAmount * promo.PercentOff) / 100
		bookingRequest.PaidAmount = bookingRequest.Amount - bookingRequest.DiscountAmount
		bookingRequest.DiscountRate = promo.PercentOff / 100

		creditAccount, err := s.db.GetCreditCheckFormByID(ctx, bookingRequest.CreditCheckFormID)
		if err != nil {
			return "", fmt.Errorf(" unable to get Credit CheckForm with id: %s, error: %w", bookingRequest.CreditCheckFormID, err)
		}
		creditAccount.ActiveBooking = true
		err = s.db.UpdateCreditCheckForm(ctx, creditAccount)
		if err != nil {
			return "", fmt.Errorf("unable to update Credit CheckForm with id: %s, error: %w", bookingRequest.CreditCheckFormID, err)
		}
		err = s.db.UpdateBookEquipment(ctx, bookingRequest)

		if err != nil {
			return "", fmt.Errorf("unable to update booking: %w", err)
		}

		s.sendBookingRequestEmails(ctx, equipper, lodger, bookingRequest)

		return "", nil
	}

	return "", nil
}

// AcceptBooking accept a booking.
func (s *Service) AcceptBooking(ctx context.Context, equipperID string, bookingID string, bookEquipment models.BookEquipmentRequest) error {
	booking, err := s.db.GetBookEquipmentByID(ctx, bookingID)
	if err != nil {
		return fmt.Errorf("unable to get booking: %w", err)
	}

	if booking.EquipperID != equipperID {
		return fmt.Errorf("booking is not yours")
	}

	if booking.Status != models.BookingPending {
		return fmt.Errorf("booking is not pending")
	}

	equipment, err := s.db.GetEquipmentByID(ctx, booking.EquipmentID)
	if err != nil {
		return fmt.Errorf("unable to get equipment: %w", err)
	}

	if equipment.Status == models.EquipmentBooked {
		return fmt.Errorf("equipment is unavailable")
	}

	booking.Status = models.BookingAccepted
	booking.SpecialPrice = bookEquipment.SpecialPrice

	if err != nil {
		return fmt.Errorf("unable to update price: %w", err)
	}

	if booking.ProjectID != "" {
		project, err := s.db.GetProjectByID(ctx, booking.ProjectID)
		if err != nil {
			return fmt.Errorf("unable to get project: %w", err)
		}

		project.Equipments = append(project.Equipments, bookingID)

		err = s.db.UpdateProject(ctx, project)
		if err != nil {
			return fmt.Errorf("unable to update project: %w", err)
		}
	}

	err = s.db.UpdateBookEquipment(ctx, booking)
	if err != nil {
		return fmt.Errorf("unable to accept booking with book_id : %s error: %w", bookingID, err)
	}
	// Available from end date  +1 day
	equipment.AvailableFrom = time.Date(booking.EndDate.Year(), booking.EndDate.Month(), booking.EndDate.Day()+1, 0, 0, 0, 0, time.UTC)
	equipment.AvailableTo = nil
	equipment.Status = models.EquipmentBooked

	err = s.db.UpdateEquipment(ctx, equipment)
	if err != nil {
		return fmt.Errorf("unable to update equipment with book_id : %s equipment_id : %s error: %w", bookingID, booking.EquipmentID, err)
	}

	err = s.BookingRequestToDecline(ctx, booking.EquipmentID)
	if err != nil {
		return fmt.Errorf("unable to decline booking equipment  error: %w", err)
	}

	lodger, err := s.db.GetLodgerByID(ctx, booking.LodgerID)
	if err != nil {
		return fmt.Errorf("unable to get lodger: %w", err)
	}

	onClickLink := fmt.Sprintf("%s?sign_in=true", s.frontendURL)

	if booking.PaymentMethod == models.PaymentMethodCreditAccount {
		if err := s.sendEmailWithTemplate(ctx, mailer.SendgridBookingAcceptTemplateIDFR, mailer.BookingAcceptTemplateID, booking.LodgerEmail, mailDataFromBookEquipment(booking, onClickLink), lodger.CommunicationPreferences); err != nil {
			log.Printf("unable to send booking request email to lodger %s error: %v", booking.LodgerEmail, err)
		}

		// Notify Lodger's RecipientList about accepted request
		for i := 0; i < len(lodger.RecipientList); i++ {
			if err := s.sendEmailWithTemplate(ctx, mailer.SendgridBookingAcceptTemplateIDFR, mailer.BookingAcceptTemplateID, lodger.RecipientList[i], mailDataFromBookEquipment(booking, onClickLink), lodger.CommunicationPreferences); err != nil {
				log.Printf("unable to send booking request email to admins %v error: %v", s.bookingNotificationEmail, err)
			}
		}
	}

	if booking.PaymentMethod == models.PaymentMethodCreditCard {
		order, err := s.db.GetOrderByBookingID(ctx, bookingID)
		if err != nil {
			return fmt.Errorf("unable to get payment intent id: %w", err)
		}

		err = s.CaptureOrder(ctx, order)
		if err != nil {
			return fmt.Errorf("unable to capture order: %w", err)
		}

		if err := s.sendEmailWithTemplateAndAttachment(ctx, mailer.SendgridBookingAcceptTemplateIDFR, mailer.BookingAcceptTemplateID, booking.LodgerEmail, mailDataFromBookEquipment(booking, onClickLink), lodger.CommunicationPreferences, order, booking); err != nil {
			log.Printf("unable to send booking request email to lodger %s error: %v", booking.LodgerEmail, err)
		}

		// Notify Lodger's RecipientList about accepted request
		for i := 0; i < len(lodger.RecipientList); i++ {
			if err := s.sendEmailWithTemplateAndAttachment(ctx, mailer.SendgridBookingAcceptTemplateIDFR, mailer.BookingAcceptTemplateID, lodger.RecipientList[i], mailDataFromBookEquipment(booking, onClickLink), lodger.CommunicationPreferences, order, booking); err != nil {
				log.Printf("unable to send booking request email to admins %v error: %v", s.bookingNotificationEmail, err)
			}
		}
	}

	return nil
}

// RejectBooking reject a booking.
func (s *Service) RejectBooking(ctx context.Context, equipperID string, bookingID string) error {
	booking, err := s.db.GetBookEquipmentByID(ctx, bookingID)
	if err != nil {
		return fmt.Errorf("unable to get booking: %w", err)
	}

	if booking.EquipperID != equipperID {
		return fmt.Errorf("booking is not yours")
	}

	if booking.Status != models.BookingPending {
		return fmt.Errorf("booking is not pending")
	}

	booking.Status = models.BookingRejected
	if booking.CreditCheckFormID != "" {
		creditAccount, err := s.db.GetCreditCheckFormByID(ctx, booking.CreditCheckFormID)
		if err != nil {
			return fmt.Errorf(" unable to get Credit CheckForm with id: %s, error: %w", booking.CreditCheckFormID, err)
		}
		creditAccount.ActiveBooking = false
		err = s.db.UpdateCreditCheckForm(ctx, creditAccount)
		if err != nil {
			return fmt.Errorf("unable to update Credit CheckForm with id: %s, error: %w", booking.CreditCheckFormID, err)
		}
	}

	err = s.db.UpdateBookEquipment(ctx, booking)
	if err != nil {
		return fmt.Errorf("unable to accept booking with book_id : %s error: %w", bookingID, err)
	}

	lodger, err := s.db.GetLodgerByID(ctx, booking.LodgerID)
	if err != nil {
		return fmt.Errorf("unable to get lodger: %w", err)
	}

	onClickLink := fmt.Sprintf("%s?sign_in=true", s.frontendURL)

	if err := s.sendEmailWithTemplate(ctx, mailer.SendgridBookingDeclineTemplateIDFR, mailer.BookingDeclineTemplateID, booking.LodgerEmail, mailDataFromBookEquipment(booking, onClickLink), lodger.CommunicationPreferences); err != nil {
		log.Printf("unable to send booking request email to lodger %s error: %v", booking.LodgerEmail, err)
	}

	// Notify Lodger's RecipientList about new booking request
	for i := 0; i < len(lodger.RecipientList); i++ {
		if err := s.sendEmailWithTemplate(ctx, mailer.SendgridBookingLodgerRequestIDFR, mailer.SendgridBookingLodgerRequestID, lodger.RecipientList[i], mailDataFromBookEquipment(booking, onClickLink), lodger.CommunicationPreferences); err != nil {
			log.Printf("unable to send booking request email to admins %v error: %v", s.bookingNotificationEmail, err)
		}
	}

	if booking.PaymentMethod == models.PaymentMethodCreditCard {
		order, err := s.db.GetOrderByBookingID(ctx, bookingID)
		if err != nil {
			return fmt.Errorf("unable to get payment intent id: %w", err)
		}

		err = s.CancelOrder(ctx, order)
		if err != nil {
			return fmt.Errorf("unable to cancel order: %w", err)
		}
	}

	return nil
}

// CancelBooking cancel a booking.
func (s *Service) CancelBooking(ctx context.Context, userID string, bookingID string, input models.BookEquipment) error {
	booking, err := s.db.GetBookEquipmentByID(ctx, bookingID)
	if err != nil {
		return fmt.Errorf("unable to get booking: %w", err)
	}

	if booking.LodgerID != userID && booking.EquipperID != userID {
		return fmt.Errorf("booking is not yours")
	}

	booking.Status = models.BookingCanceled
	booking.CancelComment = input.CancelComment
	if booking.CreditCheckFormID != "" {
		creditAccount, err := s.db.GetCreditCheckFormByID(ctx, booking.CreditCheckFormID)
		if err != nil {
			return fmt.Errorf(" unable to get Credit CheckForm with id: %s, error: %w", booking.CreditCheckFormID, err)
		}
		creditAccount.ActiveBooking = false
		err = s.db.UpdateCreditCheckForm(ctx, creditAccount)
		if err != nil {
			return fmt.Errorf("unable to update Credit CheckForm with id: %s, error: %w", booking.CreditCheckFormID, err)
		}
	}
	err = s.db.UpdateBookEquipment(ctx, booking)
	if err != nil {
		return fmt.Errorf("unable to cancel booking with book_id : %s error: %w", bookingID, err)
	}

	equipment, err := s.db.GetEquipmentByID(ctx, booking.EquipmentID)
	if err != nil {
		return fmt.Errorf("unable to get equipment: %w", err)
	}

	equipment.Status = models.EquipmentAvailable
	equipment.AvailableFrom = time.Now().UTC().Truncate(24 * time.Hour)

	err = s.db.UpdateEquipment(ctx, equipment)
	if err != nil {
		return fmt.Errorf("unable to update equipment with book_id : %s equipment_id : %s error: %w", bookingID, booking.EquipmentID, err)
	}

	projects, err := s.db.GetProjectsByEquipmentID(ctx, equipment.ID)
	if err != nil {
		return fmt.Errorf("unable to get projects: %w", err)
	}

	updatedProjects := make([]models.Project, 0)

	for _, project := range projects {
		for _, equipmentID := range project.Equipments {
			if equipment.ID != equipmentID {
				project.Equipments = append(project.Equipments, equipmentID)
			}
		}

		updatedProjects = append(updatedProjects, project)
	}

	err = s.db.BatchUpdateProjects(ctx, updatedProjects)
	if err != nil {
		return fmt.Errorf("unable to update projects with book_id: %s error: %w", bookingID, err)
	}

	lodger, err := s.db.GetLodgerByID(ctx, booking.LodgerID)
	if err != nil {
		return fmt.Errorf("unable to get lodger: %w", err)
	}

	if err := s.sendEmailWithTemplate(ctx, mailer.SendgridCancelBookingTemplateIDFR, mailer.SendgridCancelBookingTemplateID, booking.LodgerEmail, mailDataFromBookEquipment(booking, s.frontendURL), lodger.CommunicationPreferences); err != nil {
		log.Printf("unable to send booking request email to lodger %s error: %v", booking.LodgerEmail, err)
	}

	// Notify Lodger's RecipientList about new booking request
	for i := 0; i < len(lodger.RecipientList); i++ {
		if err := s.sendEmailWithTemplate(ctx, mailer.SendgridBookingLodgerRequestIDFR, mailer.SendgridBookingLodgerRequestID, lodger.RecipientList[i], mailDataFromBookEquipment(booking, s.frontendURL), lodger.CommunicationPreferences); err != nil {
			log.Printf("unable to send booking request email to admins %v error: %v", s.bookingNotificationEmail, err)
		}
	}

	return nil
}

// GetBookingByEquipperID returns a booking request by equipper id.
func (s *Service) GetBookingByEquipperID(ctx context.Context, equipperID string) (models.BookingByStatus, error) {
	adminBooking := make([]models.BookEquipment, 0)
	ownerBooking := make([]models.BookEquipment, 0)

	booking, err := s.db.GetBookEquipmentByEquipperID(ctx, equipperID)
	if err != nil {
		return nil, fmt.Errorf("unable to get booking for equipper with id: %s error %w", equipperID, err)
	}

	return mapBookingToBookingStatus(booking, adminBooking, ownerBooking), nil
}

// GetBookedByStatusAndEquipperID returns book request by status and equipper id.
func (s *Service) GetBookedByStatusAndEquipperID(ctx context.Context, equipperID string, status models.BookingStatus, limit int, lastID string) ([]models.BookEquipment, error) {
	return s.db.GetBookedByStatusAndEquipperID(ctx, equipperID, status, limit, lastID)
}

// GetBookingByLodgerID returns a booking request by lodger id.
func (s *Service) GetBookingByLodgerID(ctx context.Context, lodgerID string) (models.BookingByStatus, error) {
	lodger, err := s.db.GetLodgerByID(ctx, lodgerID)
	if err != nil {
		return nil, fmt.Errorf("unable to get lodger with id: %s error %w", lodgerID, err)
	}

	adminBooking := make([]models.BookEquipment, 0)
	ownerBooking := make([]models.BookEquipment, 0)

	if lodger.MemberOf != "" {
		adminBooking, ownerBooking, err = s.GetTeamBookingEquipments(ctx, lodger, adminBooking, lodgerID, ownerBooking)
		if err != nil {
			return nil, err
		}
	}

	booking, err := s.db.GetBookEquipmentByLodgerID(ctx, lodgerID)
	if err != nil {
		return nil, fmt.Errorf("unable to get booking for lodger with id: %s error %w", lodgerID, err)
	}

	return mapBookingToBookingStatus(booking, ownerBooking, adminBooking), nil
}

func (s *Service) GetTeamBookingEquipments(ctx context.Context, lodger models.Lodger, adminBooking []models.BookEquipment, lodgerID string, ownerBooking []models.BookEquipment) ([]models.BookEquipment, []models.BookEquipment, error) {
	member, err := s.db.GetMemberByID(ctx, lodger.MemberID, lodger.MemberOf)
	if err != nil {
		return nil, nil, fmt.Errorf("unable to get member with id: %s error %w", lodger.MemberID, err)
	}

	if member.Type == "admin" && member.MembershipStatus == models.MemberAccepted {
		adminBooking, err = s.db.GetBookEquipmentByAdminIDs(ctx, member.ID, adminBooking[0].Status)
		if err != nil {
			return nil, nil, fmt.Errorf("unable to get booking for admin with id: %s error %w", lodgerID, err)
		}
	} else if member.Type == "owner" && member.MembershipStatus == models.MemberOwner {
		ownerBooking, err = s.db.GetBookEquipmentByOwnerID(ctx, lodgerID, ownerBooking[0].Status)
		if err != nil {
			return nil, nil, fmt.Errorf("unable to get booking for owner with id: %s error %w", lodgerID, err)
		}
	}

	return adminBooking, ownerBooking, nil
}

// GetBookedEquipmentByStatusAndLodgerID returns a booking request by lodger id and status.
func (s *Service) GetBookedEquipmentByStatusAndLodgerID(ctx context.Context, lodgerID string, status models.BookingStatus, limit int, lastID string) ([]models.BookEquipment, error) {
	booking, err := s.db.GetBookedEquipmentByStatusAndLodgerID(ctx, lodgerID, status, limit, lastID)
	if err != nil {
		return nil, fmt.Errorf("unable to get booking for lodger with id: %s error %w", lodgerID, err)
	}

	lodger, err := s.db.GetLodgerByID(ctx, lodgerID)
	if err != nil {
		return nil, fmt.Errorf("unable to get lodger with id: %s error %w", lodgerID, err)
	}

	if lodger.MemberOf != "" {
		booking, err = s.GetTeamMemberBookingEquipments(ctx, lodger, lodgerID, booking, status)
		if err != nil {
			return nil, err
		}
	}

	return booking, nil
}

func (s *Service) GetTeamMemberBookingEquipments(ctx context.Context, lodger models.Lodger, lodgerID string, booking []models.BookEquipment, status models.BookingStatus) ([]models.BookEquipment, error) {
	member, err := s.db.GetMemberByID(ctx, lodger.MemberID, lodger.MemberOf)
	if err != nil {
		return nil, fmt.Errorf("unable to get member with id: %s error %w", lodger.MemberID, err)
	}

	if member.Type == models.Admin && member.MembershipStatus == models.MemberAccepted {
		adminBooking, err := s.AdminBooking(ctx, member, lodgerID, booking, status)
		if err != nil {
			return nil, err
		}

		booking = adminBooking
	} else if member.MembershipStatus == models.MemberOwner {
		ownerBooking, err := s.OwnerBooking(ctx, lodgerID, booking, status)
		if err != nil {
			return nil, err
		}

		booking = ownerBooking
	}

	return booking, nil
}

func (s *Service) OwnerBooking(ctx context.Context, lodgerID string, booking []models.BookEquipment, status models.BookingStatus) ([]models.BookEquipment, error) {
	ownerBooking, err := s.db.GetBookEquipmentByOwnerID(ctx, lodgerID, status)
	if err != nil {
		return nil, fmt.Errorf("unable to get booking for owner with id: %s error %w", lodgerID, err)
	}

	if len(ownerBooking) > 0 {
		for _, book := range ownerBooking {
			book.CreatedByMember = true
			booking = append(booking, book)
		}

		booking = s.RemoveDuplicatesBooking(booking)
	}

	return booking, nil
}

// RemoveDuplicatesBooking Remove duplicates from booking requests.
func (s *Service) RemoveDuplicatesBooking(booking []models.BookEquipment) []models.BookEquipment {
	keys := make(map[string]bool)
	list := []models.BookEquipment{}

	for _, entry := range booking {
		if _, value := keys[entry.ID]; !value {
			keys[entry.ID] = true

			list = append(list, entry)
		}
	}

	return list
}

func (s *Service) AdminBooking(ctx context.Context, member models.Member, lodgerID string, booking []models.BookEquipment, status models.BookingStatus) ([]models.BookEquipment, error) {
	adminBooking, err := s.db.GetBookEquipmentByAdminIDs(ctx, member.ID, status)
	if err != nil {
		return nil, fmt.Errorf("unable to get booking for admin with id: %s error %w", lodgerID, err)
	}

	if len(adminBooking) > 0 {
		for _, book := range adminBooking {
			book.CreatedByMember = true
			booking = append(booking, book)
		}

		booking = s.RemoveDuplicatesBooking(booking)
	}

	return booking, nil
}

func mapBookingToBookingStatus(bookings []models.BookEquipment, ownerBooking []models.BookEquipment, adminBooking []models.BookEquipment) models.BookingByStatus {
	bookingMap := make(map[models.BookingStatus]map[models.BookingEquipment][]models.BookEquipment)

	if len(ownerBooking) > 0 {
		for _, book := range ownerBooking {
			book.CreatedByMember = true
			bookings = append(bookings, book)
		}
	}

	if len(adminBooking) > 0 {
		for _, book := range adminBooking {
			book.CreatedByMember = true
			bookings = append(bookings, book)
		}
	}

	for _, book := range bookings {
		if _, ok := bookingMap[book.Status]; !ok {
			bookingMap[book.Status] = map[models.BookingEquipment][]models.BookEquipment{}
		}

		bookingEquipment := models.BookingEquipment{
			EquipmentID:   book.EquipmentID,
			EquipmentName: book.EquipmentName,
		}

		bookingMap[book.Status][bookingEquipment] = append(bookingMap[book.Status][bookingEquipment], book)
	}

	result := models.BookingByStatus{}

	for status, book := range bookingMap {
		for bookingEquipment, b := range book {
			result[status] = append(result[status], models.RequestBookingEquipment{
				EquipmentName: bookingEquipment.EquipmentName,
				Bookings:      b,
			})
		}
	}

	return result
}

// BookingRequestToDecline decline a BookingRequest with equipmentID.
func (s *Service) BookingRequestToDecline(ctx context.Context, equipmentID string) error {
	bookings, err := s.db.GetBookEquipmentByEquipmentID(ctx, equipmentID)
	if err != nil {
		return fmt.Errorf("unable to get booking for equipment with id: %s error %w", equipmentID, err)
	}

	for _, booking := range bookings {
		if booking.Status == models.BookingAccepted {
			continue
		}

		booking.Status = models.BookingRejected
		if booking.CreditCheckFormID != "" {
			creditAccount, err := s.db.GetCreditCheckFormByID(ctx, booking.CreditCheckFormID)
			if err != nil {
				return fmt.Errorf(" unable to get Credit CheckForm with id: %s, error: %w", booking.CreditCheckFormID, err)
			}
			creditAccount.ActiveBooking = false
			err = s.db.UpdateCreditCheckForm(ctx, creditAccount)
			if err != nil {
				return fmt.Errorf("unable to update Credit CheckForm with id: %s, error: %w", booking.CreditCheckFormID, err)
			}
		}
		err = s.db.UpdateBookEquipment(ctx, booking)
		if err != nil {
			return fmt.Errorf("unable to update booking with id: %s error %w", booking.ID, err)
		}
	}

	return nil
}

// UpdateBookingStatusAfterReturnEquipment to returned after return equipment.
func (s *Service) UpdateBookingStatusAfterReturnEquipment(ctx context.Context, userID string, equipmentID string) error {
	booking, err := s.db.GetAcceptedBookEquipmentByEquipmentID(ctx, equipmentID)
	if err != nil {
		return fmt.Errorf("unable to get booking with id: %s error %w", equipmentID, err)
	}

	if booking.EquipmentID == "" {
		return fmt.Errorf("booking not found")
	}

	if userID != booking.EquipperID {
		return fmt.Errorf(" equipper id is not valid")
	}

	booking.Status = models.BookingReturned
	if booking.CreditCheckFormID != "" {
		creditAccount, err := s.db.GetCreditCheckFormByID(ctx, booking.CreditCheckFormID)
		if err != nil {
			return fmt.Errorf(" unable to get Credit CheckForm with id: %s, error: %w", booking.CreditCheckFormID, err)
		}
		creditAccount.ActiveBooking = false
		err = s.db.UpdateCreditCheckForm(ctx, creditAccount)
		if err != nil {
			return fmt.Errorf("unable to update Credit CheckForm with id: %s, error: %w", booking.CreditCheckFormID, err)
		}
	}
	booking.EndDate = time.Now()

	err = s.db.UpdateBookEquipment(ctx, booking)
	if err != nil {
		return fmt.Errorf("unable to update equipment with id: %s error %w", equipmentID, err)
	}

	equipment, err := s.db.GetEquipmentByID(ctx, booking.EquipmentID)
	if err != nil {
		return fmt.Errorf("unable to get equipment with id: %s error %w", booking.EquipmentID, err)
	}

	equipment.Status = models.EquipmentAvailable

	err = s.db.UpdateEquipment(ctx, equipment)
	if err != nil {
		return fmt.Errorf("unable to update equipment with id: %s error %w", equipment.ID, err)
	}

	return nil
}

//pdfDataFromOrder data for invoice pdf generation

func (s *Service) pdfDataFromOrder(ctx context.Context, order models.Order, booking models.BookEquipment) models.InvoiceDetails {
	var invoice models.InvoiceDetails
	lodger, _ := s.GetLodgerByID(ctx, booking.LodgerID)
	invoice.PaymentDate = time.Now().Format("2006-01-02")
	invoice.Currency = strings.ToUpper(string(order.Currency))
	invoice.TaxValue = humanize.CommafWithDigits(float64(order.Payment.Tax/100), 2)
	invoice.TotalAmount = humanize.CommafWithDigits(booking.PaidAmount, 2)
	invoice.InvoiceNumber = rand.Int63n(9000000) + 1000000
	invoice.ReceiptNumber = rand.Int63n(9000000) + 1000000
	invoice.ClientEmail = order.CustomerEmail
	invoice.ClientName = lodger.FullName
	invoice.PaymentMethod = booking.PaymentMethod
	invoice.Country = lodger.Address.Country
	invoice.ZipCode = lodger.Address.ZipCode
	invoice.DiscountValue = humanize.CommafWithDigits(float64(booking.DiscountAmount), 2)
	invoice.TotalEquipmentsPrice = humanize.CommafWithDigits((float64(order.Payment.Amount) / 100), 2)
	invoice.SubTotalHT = humanize.CommafWithDigits((float64(order.Payment.Amount)/100)-(float64(order.Payment.Discount)/100), 2)
	if booking.DiscountRate < 1 {
		booking.DiscountRate *= 100
	}
	invoice.DiscountPercentage = humanize.CommafWithDigits(booking.DiscountRate, 2)
	taxableAmount := (float64(order.Payment.Amount) / 100) - (float64(order.Payment.Discount) / 100)
	if taxableAmount != 0 {
		taxPercentage := (float64(order.Payment.Tax) / 100) / taxableAmount * 100
		invoice.TaxPercentage = humanize.CommafWithDigits(taxPercentage, 2)
	} else {
		invoice.TaxPercentage = "0"
	}
	for _, item := range order.Items {
		name := booking.EquipmentName
		if lodger.CommunicationPreferences == models.French {
			name = booking.EquipmentNameFR
		}
		unitPrice := booking.SubTotal
		totalAmount := float64(item.Quantity) * unitPrice
		product := models.Product{
			Name:        name,
			Qty:         item.Quantity,
			UnitPrice:   humanize.CommafWithDigits(booking.SubTotal, 2),
			Currency:    strings.ToUpper(string(order.Currency)),
			TotalAmount: humanize.CommafWithDigits(totalAmount, 2),
		}

		invoice.Products = append(invoice.Products, product)

	}

	return invoice
}

// mailDataFromBookEquipment data for booking request & if string is empty will pass Not specified.
func mailDataFromBookEquipment(booking models.BookEquipment, frontendURL string) map[string]string {
	newBooking := ReplaceEmptyString(booking)
	amount := humanize.CommafWithDigits(newBooking.Amount, 2)
	return map[string]string{
		"lodger_name":                newBooking.LodgerName,
		"lodger_contact":             newBooking.LodgerPhoneNumber,
		"equipper_name":              newBooking.EquipperName,
		"equipment_name":             newBooking.EquipmentName,
		"start_date":                 newBooking.StartDate.Format("2006-01-02"),
		"end_date":                   newBooking.EndDate.Format("2006-01-02"),
		"amount":                     amount,
		"currency":                   newBooking.Currency,
		"equipment_image":            newBooking.EquipmentImageLink,
		"equipper_image":             newBooking.EquipperImageLink,
		"weight":                     newBooking.Weight,
		"height":                     newBooking.Height,
		"btu":                        newBooking.BTU,
		"volt":                       newBooking.Volt,
		"type_of_propulsion":         strings.Join(newBooking.TypeOfPropulsion, ", "),
		"price_per_day":              strconv.FormatInt(int64(newBooking.Price.Day), 10),
		"price_per_week":             strconv.FormatInt(int64(newBooking.Price.Week), 10),
		"price_per_month":            strconv.FormatInt(int64(newBooking.Price.Month), 10),
		"price_per_day_special":      strconv.FormatInt(int64(newBooking.SpecialPrice.Day), 10),
		"price_per_week_special":     strconv.FormatInt(int64(newBooking.SpecialPrice.Week), 10),
		"price_per_month_special":    strconv.FormatInt(int64(newBooking.SpecialPrice.Month), 10),
		"delivery_cost":              strconv.FormatInt(int64(booking.SpecialPrice.DeliveryPickupCost+booking.SpecialPrice.DeliveryDropCost), 10),
		"description":                newBooking.Description,
		"brand":                      newBooking.Brand,
		"width":                      newBooking.Width,
		"length":                     newBooking.Length,
		"force":                      newBooking.Force,
		"usage_hours":                newBooking.UsageHours,
		"delivery_address_address":   newBooking.DeliveryAddress.Address,
		"delivery_address_City":      newBooking.DeliveryAddress.City,
		"delivery_address_country":   newBooking.DeliveryAddress.Country,
		"delivery_address_zipCode":   newBooking.DeliveryAddress.ZipCode,
		"delivery_address_State":     newBooking.DeliveryAddress.State,
		"billing_address_address":    newBooking.BillingAddress.Address,
		"billing_address_City":       newBooking.BillingAddress.City,
		"billing_address_country":    newBooking.BillingAddress.Country,
		"billing_address_zipCode":    newBooking.BillingAddress.ZipCode,
		"billing_address_State":      newBooking.BillingAddress.State,
		"payment_method":             string(newBooking.PaymentMethod),
		"delivery_preference":        newBooking.DeliveryDetails.DeliveryPreference,
		"credit_check_form":          newBooking.CreditCheckForm.CreditCheckFormPath,
		"po_number":                  newBooking.PoNumber,
		"capacity":                   newBooking.Capacity,
		"consumption":                newBooking.Consumption,
		"sub_total":                  humanize.CommafWithDigits(newBooking.SubTotal, 2),
		"total_amount":               humanize.CommafWithDigits(newBooking.PaidAmount, 2),
		"tvq":                        humanize.CommafWithDigits(newBooking.TVQ, 2),
		"tps":                        humanize.CommafWithDigits(newBooking.TPS, 2),
		"_fees":                      humanize.CommafWithDigits(newBooking.ServiceFees, 2),
		"cancel_comment":             newBooking.CancelComment,
		"on_click":                   frontendURL,
		"company_name":               newBooking.CompanyName,
		"receiver_info_name":         newBooking.ReceiverInfo.Name,
		"receiver_info_phone_number": newBooking.ReceiverInfo.Phone,
		"internal_id":                newBooking.InternalID,
		"equipper_equipment_picture": newBooking.EquipperEquipmentPicture,
		"brand_model":                newBooking.BrandModel,
		"drive_type":                 strings.Join(newBooking.DriveType, ", "),
		"diameter":                   newBooking.Diameter,
		"cut_diameter":               newBooking.CutDiameter,
		"watt":                       newBooking.Watt,
		"cfm":                        newBooking.CFM,
		"platform_height":            newBooking.PlatformHeight,
		"working_height":             newBooking.WorkingHeight,
		"horizontal_outreach":        newBooking.HorizontalOutreach,
		"platform_capacity":          newBooking.PlatformCapacity,
		"platform_dimension":         newBooking.PlatformDimension,
		"platform_extension":         newBooking.PlatformExtension,
		"extension_capacity":         newBooking.ExtensionCapacity,
		"platform_rotation":          newBooking.PlatformRotation,
		"machine_rotation":           newBooking.MachineRotation,
		"machine_width":              newBooking.MachineWidth,
		"machine_length":             newBooking.MachineLength,
		"machine_height":             newBooking.MachineHeight,
		"closed_machine_height":      newBooking.ClosedMachineHeight,
		"closed_machine_length":      newBooking.ClosedMachineLength,
		"closed_machine_width":       newBooking.ClosedMachineWidth,
		"basket_capacity":            newBooking.BasketCapacity,
		"basket_length":              newBooking.BasketLength,
		"basket_width":               newBooking.BasketWidth,
		"legs_location":              newBooking.LegsLocation,
		"floor_height":               newBooking.FloorHeight,
		"cabin_height":               newBooking.CabinHeight,
		"wheelbase":                  newBooking.Wheelbase,
		"wheel_size":                 newBooking.WheelSize,
		"plate_dimension":            newBooking.PlateDimension,
		"decibel":                    newBooking.Decibel,
		"roll_width":                 newBooking.RollWidth,
		"compaction":                 newBooking.Compaction,
		"vibrations":                 newBooking.Vibrations,
		"lumen":                      newBooking.Lumen,
		"pressure":                   newBooking.Pressure,
		"frequency":                  newBooking.Frequency,
		"tilting_capacity":           newBooking.TiltingCapacity,
		"operation_capacity":         newBooking.OperationCapacity,
		"tank_capacity":              newBooking.TankCapacity,
		"digging_depth":              newBooking.DiggingDepth,
		"dumping_height":             newBooking.DumpingHeight,
		"digging_radius":             newBooking.DiggingRadius,
		"technical_data_sheet":       newBooking.TechnicalDataSheet,
		"equipment_usages":           newBooking.EquipmentUsages,
	}
}

// mailDataFromBookEquipment data for booking request in French version & if string is empty will pass Not specified.
func mailDataFromBookEquipmentFR(booking models.BookEquipment, frontendURL string) map[string]string {
	newBooking := ReplaceEmptyString(booking)

	return map[string]string{
		"lodger_name":                newBooking.LodgerName,
		"lodger_contact":             newBooking.LodgerPhoneNumber,
		"equipper_name":              newBooking.EquipperName,
		"equipment_name":             newBooking.EquipmentNameFR,
		"start_date":                 newBooking.StartDate.Format("2006-01-02"),
		"end_date":                   newBooking.EndDate.Format("2006-01-02"),
		"amount":                     humanize.CommafWithDigits(newBooking.Amount, 2),
		"currency":                   newBooking.Currency,
		"equipment_image":            newBooking.EquipmentImageLink,
		"equipper_image":             newBooking.EquipperImageLink,
		"weight":                     newBooking.Weight,
		"height":                     newBooking.Height,
		"btu":                        newBooking.BTU,
		"volt":                       newBooking.Volt,
		"type_of_propulsion":         strings.Join(newBooking.TypeOfPropulsion, ", "),
		"price_per_day":              humanize.CommafWithDigits(newBooking.Price.Day, 2),
		"price_per_week":             humanize.CommafWithDigits(newBooking.Price.Week, 2),
		"price_per_month":            humanize.CommafWithDigits(newBooking.Price.Month, 2),
		"price_per_day_special":      humanize.CommafWithDigits(newBooking.SpecialPrice.Day, 2),
		"price_per_week_special":     humanize.CommafWithDigits(newBooking.SpecialPrice.Week, 2),
		"price_per_month_special":    humanize.CommafWithDigits(newBooking.SpecialPrice.Month, 2),
		"delivery_cost":              humanize.CommafWithDigits(booking.SpecialPrice.DeliveryPickupCost+booking.SpecialPrice.DeliveryDropCost, 2),
		"description":                newBooking.Description,
		"brand":                      newBooking.Brand,
		"width":                      newBooking.Width,
		"length":                     newBooking.Length,
		"force":                      newBooking.Force,
		"usage_hours":                newBooking.UsageHours,
		"delivery_address_address":   newBooking.DeliveryAddress.Address,
		"delivery_address_City":      newBooking.DeliveryAddress.City,
		"delivery_address_country":   newBooking.DeliveryAddress.Country,
		"delivery_address_zipCode":   newBooking.DeliveryAddress.ZipCode,
		"delivery_address_State":     newBooking.DeliveryAddress.State,
		"billing_address_address":    newBooking.BillingAddress.Address,
		"billing_address_City":       newBooking.BillingAddress.City,
		"billing_address_country":    newBooking.BillingAddress.Country,
		"billing_address_zipCode":    newBooking.BillingAddress.ZipCode,
		"billing_address_State":      newBooking.BillingAddress.State,
		"payment_method":             string(newBooking.PaymentMethod),
		"delivery_preference":        newBooking.DeliveryDetails.DeliveryPreference,
		"credit_check_form":          newBooking.CreditCheckForm.CreditCheckFormPath,
		"po_number":                  newBooking.PoNumber,
		"capacity":                   newBooking.Capacity,
		"consumption":                newBooking.Consumption,
		"sub_total":                  humanize.CommafWithDigits(newBooking.SubTotal, 2),
		"total_amount":               humanize.CommafWithDigits(newBooking.PaidAmount, 2),
		"tvq":                        humanize.CommafWithDigits(newBooking.TVQ, 2),
		"tps":                        humanize.CommafWithDigits(newBooking.TPS, 2),
		"_fees":                      humanize.CommafWithDigits(newBooking.ServiceFees, 2),
		"cancel_comment":             newBooking.CancelComment,
		"on_click":                   frontendURL,
		"company_name":               newBooking.CompanyName,
		"receiver_info_name":         newBooking.ReceiverInfo.Name,
		"receiver_info_phone_number": newBooking.ReceiverInfo.Phone,
		"internal_id":                newBooking.InternalID,
		"equipper_equipment_picture": newBooking.EquipperEquipmentPicture,
		"brand_model":                newBooking.BrandModel,
		"drive_type":                 strings.Join(newBooking.DriveType, ", "),
		"diameter":                   newBooking.Diameter,
		"cut_diameter":               newBooking.CutDiameter,
		"watt":                       newBooking.Watt,
		"cfm":                        newBooking.CFM,
		"platform_height":            newBooking.PlatformHeight,
		"working_height":             newBooking.WorkingHeight,
		"horizontal_outreach":        newBooking.HorizontalOutreach,
		"platform_capacity":          newBooking.PlatformCapacity,
		"platform_dimension":         newBooking.PlatformDimension,
		"platform_extension":         newBooking.PlatformExtension,
		"extension_capacity":         newBooking.ExtensionCapacity,
		"platform_rotation":          newBooking.PlatformRotation,
		"machine_rotation":           newBooking.MachineRotation,
		"machine_width":              newBooking.MachineWidth,
		"machine_length":             newBooking.MachineLength,
		"machine_height":             newBooking.MachineHeight,
		"closed_machine_height":      newBooking.ClosedMachineHeight,
		"closed_machine_length":      newBooking.ClosedMachineLength,
		"closed_machine_width":       newBooking.ClosedMachineWidth,
		"basket_capacity":            newBooking.BasketCapacity,
		"basket_length":              newBooking.BasketLength,
		"basket_width":               newBooking.BasketWidth,
		"legs_location":              newBooking.LegsLocation,
		"floor_height":               newBooking.FloorHeight,
		"cabin_height":               newBooking.CabinHeight,
		"wheelbase":                  newBooking.Wheelbase,
		"wheel_size":                 newBooking.WheelSize,
		"plate_dimension":            newBooking.PlateDimension,
		"decibel":                    newBooking.Decibel,
		"roll_width":                 newBooking.RollWidth,
		"compaction":                 newBooking.Compaction,
		"vibrations":                 newBooking.Vibrations,
		"lumen":                      newBooking.Lumen,
		"pressure":                   newBooking.Pressure,
		"frequency":                  newBooking.Frequency,
		"tilting_capacity":           newBooking.TiltingCapacity,
		"operation_capacity":         newBooking.OperationCapacity,
		"tank_capacity":              newBooking.TankCapacity,
		"digging_depth":              newBooking.DiggingDepth,
		"dumping_height":             newBooking.DumpingHeight,
		"digging_radius":             newBooking.DiggingRadius,
		"technical_data_sheet":       newBooking.TechnicalDataSheet,
		"equipment_usages":           newBooking.EquipmentUsages,
	}
}

func ReplaceEmptyString(booking models.BookEquipment) models.BookEquipment {
	paymentMethod := string(booking.PaymentMethod)
	typeOfProp := strings.Join(booking.TypeOfPropulsion, ", ")
	attributes := []*string{
		&booking.LodgerName,
		&booking.LodgerPhoneNumber,
		&booking.EquipperName,
		&booking.EquipmentName,
		&booking.EquipmentImageLink,
		&booking.EquipperImageLink,
		&booking.Weight,
		&booking.Height,
		&booking.BTU,
		&booking.Volt,
		&typeOfProp,
		&booking.Description,
		&booking.Brand,
		&booking.Width,
		&booking.Length,
		&booking.Force,
		&booking.UsageHours,
		&booking.DeliveryAddress.Address,
		&booking.DeliveryAddress.City,
		&booking.DeliveryAddress.Country,
		&booking.DeliveryAddress.ZipCode,
		&booking.DeliveryAddress.State,
		&booking.BillingAddress.Address,
		&booking.BillingAddress.City,
		&booking.BillingAddress.Country,
		&booking.BillingAddress.ZipCode,
		&booking.BillingAddress.State,
		&paymentMethod,
		&booking.DeliveryDetails.DeliveryPreference,
		&booking.CreditCheckForm.CreditCheckFormPath,
		&booking.PoNumber,
		&booking.Capacity,
		&booking.Consumption,
		&booking.BrandModel,
		&booking.Diameter,
		&booking.CutDiameter,
		&booking.Watt,
		&booking.CFM,
		&booking.PlatformHeight,
		&booking.WorkingHeight,
		&booking.HorizontalOutreach,
		&booking.PlatformCapacity,
		&booking.PlatformDimension,
		&booking.PlatformExtension,
		&booking.ExtensionCapacity,
		&booking.PlatformRotation,
		&booking.MachineRotation,
		&booking.MachineWidth,
		&booking.MachineLength,
		&booking.MachineHeight,
		&booking.ClosedMachineHeight,
		&booking.ClosedMachineLength,
		&booking.ClosedMachineWidth,
		&booking.BasketCapacity,
		&booking.BasketLength,
		&booking.BasketWidth,
		&booking.LegsLocation,
		&booking.FloorHeight,
		&booking.CabinHeight,
		&booking.Wheelbase,
		&booking.WheelSize,
		&booking.PlateDimension,
		&booking.Decibel,
		&booking.RollWidth,
		&booking.Compaction,
		&booking.Vibrations,
		&booking.Lumen,
		&booking.Pressure,
		&booking.Frequency,
		&booking.TiltingCapacity,
		&booking.OperationCapacity,
		&booking.TankCapacity,
		&booking.DiggingDepth,
		&booking.DumpingHeight,
		&booking.DiggingRadius,
		&booking.TechnicalDataSheet,
		&booking.EquipmentUsages,
	}

	for _, attribute := range attributes {
		if *attribute == "" {
			*attribute = NotSpecified
		}
	}

	return booking
}
func (s *Service) mailDataInvoiceAttachmentFR(pdf []byte) map[string][]byte {

	return map[string][]byte{

		"Facture.pdf": pdf,
	}
}
func (s *Service) mailDataInvoiceAttachment(pdf []byte) map[string][]byte {

	return map[string][]byte{

		"Invoice.pdf": pdf,
	}
}
