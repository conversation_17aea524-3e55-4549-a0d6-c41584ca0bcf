import React, { useState } from 'react';
import { FaPlus } from 'react-icons/fa';
import { Configure, SearchBox } from 'react-instantsearch-dom';
import { CustomBidzInfiniteHits } from '../../components/algolia/Bidz_infinite_hits';
import InstantSearchAlgolia from '../../components/search_result/Instant_search_algolia';
import FloatingActionButton from '../../shared/components/buttons/Floating_action_button';
import AddBidzInventoryModal from '../../shared/components/modals/Add_bidz_inventory_modal';
import ConfirmationModal from '../../shared/components/modals/Confirmation_modal';
import Popup from '../../shared/components/modals/Popup';
import SuccessPopUp from '../../shared/components/modals/Success_pop_up';
import { useEquipment } from '../../shared/context/Equipment_context';
import { useInventoryContext } from '../../shared/context/Tooler_bidz_inventory_management_context';

const columns = [
  {
    name: 'Name En',
    selector: (row) => row.name_en,
    sortable: true
  },
  {
    name: 'Name Fr',
    selector: (row) => row.name_en,
    sortable: true
  },
  {
    name: 'Aliases EN',
    selector: (row) => row['alias.en']
  },
  {
    name: 'Aliases FR',
    selector: (row) => row['alias.fr']
  },

  {
    name: 'Category ',
    selector: (row) => row.category,
    sortable: true
  },
  {
    name: 'Sub Category',
    selector: (row) => row.sub_category,
    sortable: true
  }
];
export default function ToolerBidzInventoryManagement({ t }) {
  const { addInventory, editInventory, deleteInventory, uploadEquipmentPhoto } =
    useInventoryContext();
  const { AirTableSynchronization } = useEquipment();
  const [selectedEquipment, setSelectedEquipment] = useState(null);
  const [show, setShow] = useState(false);
  const [onAction, setOnAction] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showFailurePopUp, setShowFailurePopUp] = useState(false);
  const [failureResponse, setFailureResponse] = useState(null);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [action, setAction] = useState('');
  const [searchState, setSearchState] = useState({
    query: ''
  });

  function handleStateSwitch(searchState) {
    setSearchState(searchState);
  }

  async function initEquipmentStandard() {
    setOnAction(true);
    const response = await AirTableSynchronization();
    if (response.status === 201 || response.status === 200) {
      setShowSuccess(true);
    } else {
      setShowFailurePopUp(true);
      setFailureResponse(response);
    }
    setOnAction(false);
  }

  const uploadPhoto = async (selectedFile, id) => {
    const data = new FormData();
    data?.set('file', selectedFile);
    const res = await uploadEquipmentPhoto(data, id);
    if (res.status === 200) {
      setShowSuccess(true);
    } else {
      setFailureResponse(res);
      setShowFailurePopUp(true);
    }
  };

  const closeAllAndRefresh = () => {
    setShow(false);
    setShowSuccess(false);
    setShowFailurePopUp(false);
    setShowConfirmationModal(false);
  };

  async function addToToolerBidzInventory(values) {
    const response = await addInventory(values);
    if (response.status === 201 || response.status === 200) {
      setSelectedEquipment(null);
      setShowSuccess(true);
    } else {
      setShowFailurePopUp(true);
      setFailureResponse(response);
    }
  }

  async function updateToolerBidzInventory(values) {
    const response = await editInventory(values);
    setOnAction(true);
    if (response.status === 201 || response.status === 200) {
      setShowSuccess(true);
      setSelectedEquipment(null);
    } else {
      setShowFailurePopUp(true);
      setFailureResponse(response);
    }
    setOnAction(false);
  }

  const onImageChange = (event, equipment) => {
    setSelectedEquipment(equipment);
    if (event.target.files && event.target.files[0]) {
      uploadPhoto(
        event.target.files[0],
        equipment.path?.slice(
          'tooler_bidz_equipment_inventory/'.length,
          equipment.path.length
        )
      );
    }
  };

  async function deleteToolerBidzInventory(values) {
    const response = await deleteInventory(values);
    setOnAction(true);
    if (response.status === 201 || response.status === 200) {
      setShowSuccess(true);
      setSelectedEquipment(null);
    } else {
      setShowFailurePopUp(true);
      setFailureResponse(response);
    }
    setOnAction(false);
  }

  const getConfirmationAction = async () => {
    if (action === 'delete') {
      await deleteToolerBidzInventory(
        selectedEquipment.path?.slice(
          'tooler_bidz_equipment_inventory/'.length,
          selectedEquipment.path.length
        )
      );
    } else if (action === 'Delete all') {
      setOnAction(true);
      deleteToolerBidzInventory('all').then(() => {
        setOnAction(false);
      });
    } else if (action === 'Initialize') {
      await initEquipmentStandard();
    }
  };

  const getMessage = () => {
    switch (action) {
      case 'delete':
        return 'Are you sure you want to delete this equipment?';
      case 'delete all':
        return 'Are you sure you want to delete all equipment?';
      case 'initialize':
        return 'Are you sure you want to initialize the equipment list?';
      default:
        return '';
    }
  };

  const setActionHandler = (action) => {
    setAction(action);
    setShowConfirmationModal(true);
  };

  return (
    <InstantSearchAlgolia
      searchState={searchState}
      onSearchStateChange={(searchState) => {
        handleStateSwitch(searchState);
      }}
      indexName={import.meta.env.VITE_ALGOLIA_INDEX_NAME_STANDARDS}
    >
      <Configure hitsPerPage={200} />
      <AddBidzInventoryModal
        show={show}
        initialValues={selectedEquipment}
        isEdit={selectedEquipment}
        onClose={() => {
          setShow(false);
        }}
        onSubmit={(data) => {
          selectedEquipment
            ? updateToolerBidzInventory(data)
            : addToToolerBidzInventory(data);
        }}
        t={t}
        onImageChange={onImageChange}
      />
      <ConfirmationModal
        show={showConfirmationModal}
        buttonText={action}
        isLoading={onAction}
        action={() => {
          getConfirmationAction();
          getMessage();
        }}
        message={getMessage()}
        onClose={() => setShowConfirmationModal(false)}
        t={t}
      />
      <SuccessPopUp
        show={showSuccess}
        onClose={() => {
          closeAllAndRefresh();
          window.location.reload();
        }}
      />
      <Popup
        show={showFailurePopUp}
        response={failureResponse}
        onClose={() => {
          closeAllAndRefresh();
        }}
        t={t}
      />
      <div className="send-btn fixed-button-modal">
        <button
          className="round-button yellow bold fwb-700 bold red"
          onClick={() => setActionHandler('Initialize')}
        >
          {t('Upload_inventory')}
        </button>
        <button
          className="round-button red bold fwb-700 bold "
          onClick={() => setActionHandler('Delete all')}
        >
          {t('Delete_all')}
        </button>
      </div>

      <SearchBox />
      <CustomBidzInfiniteHits
        columns={columns}
        action={action}
        setAction={setAction}
        setSelectedEquipment={setSelectedEquipment}
        onImageChange={onImageChange}
        setShow={setShow}
        setShowConfirmationModal={setShowConfirmationModal}
      />
      <FloatingActionButton
        backgroundColor="#ffc107"
        icon={<FaPlus />}
        color="black"
        onClick={() => {
          setShow(true);
        }}
      />
    </InstantSearchAlgolia>
  );
}
