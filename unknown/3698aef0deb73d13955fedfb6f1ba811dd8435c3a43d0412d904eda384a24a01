import React, { useEffect } from 'react';
import { SearchBox } from 'react-instantsearch-dom';
import { CustomSpotlightMenu } from '../search_result/Custom_equipper_spotlight_menu';
import {
  getSessionStorage,
  setSessionStorage
} from '../../shared/helpers/Session_storage_helper';
import { index } from '../../shared/helpers/Algolia_helper';

export default function FilterByCategorySubcategory({
  t,
  isAvailableInventory,
  isEquipmentManagement,
  isEquipperSpotlight,
  setObjectIDs
}) {
  const url = window.location.search;
  const Params = new URLSearchParams(url);
  const booked = Params.get('booked');

  useEffect(() => {
    if (booked === 'true') {
      setSessionStorage('status', 'booked');
    }
  }, [booked]);

  const details = [
    {
      show: isEquipmentManagement,
      title: 'Status',
      selected: getSessionStorage('status'),
      attribute: 'status'
    },
    {
      show: true,
      title: 'Categories',
      selected: getSessionStorage('category'),
      attribute: 'category'
    }
  ];

  const handleSearch = async (event) => {
    event.preventDefault();
    const data = await index.search(event.currentTarget.value);
    setObjectIDs(data?.hits?.map((item) => item.objectID));
  };

  return (
    <div
      className={`col-lg-3 padding-r-0 width-filter-category ${
        isEquipperSpotlight ? '' : 'p-0'
      }`}
    >
      <div
        className={
          isAvailableInventory
            ? 'company-spotlight--search row form-group mt-lg-0 mt-4'
            : ''
        }
      >
        <div className="search-inventory">
          {isAvailableInventory ? (
            <SearchBox
              translations={{
                placeholder: t('Search_placeholder')
              }}
              onChange={handleSearch}
            />
          ) : (
            <SearchBox
              translations={{
                placeholder: t('Search_placeholder')
              }}
            />
          )}
        </div>
      </div>
      {details?.map(
        ({ selected, attribute, show }) =>
          show && (
            <div className="filter-category">
              <div className="accordionDrop">
                <CustomSpotlightMenu
                  attribute={attribute}
                  isEquipperSpotlight={isEquipperSpotlight}
                  isEquipmentManagement={isEquipmentManagement}
                  selected={selected}
                  t={t}
                  isHomeInstantSearchByCategory
                />
              </div>
            </div>
          )
      )}
    </div>
  );
}
