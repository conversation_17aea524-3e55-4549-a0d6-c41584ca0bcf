import React, { useEffect, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { CustomClearRefinements } from '../../../components/search_result/Custom_clear_refinements';
import { CustomRadioBoxMenu } from '../../../components/search_result/Radio_box_menu';
import { CustomRefinementList } from '../../../components/search_result/RefinementList';
import SearchInputAutoCompleteEquipment from '../../../features/search_result/Search_input_auto_complete_equipment';
import { isDisabled } from '../../helpers/Date_helper';
import AdvancedFilterTab from './Advanced_filter_tab';
import DatePicker from '../date_picker/Date_picker';
import { setSessionStorage } from '../../helpers/Session_storage_helper';
import CustomButton from '../buttons/Custom_button';
import { connectInfiniteHits } from 'react-instantsearch-dom';
import { attributes } from '../../helpers/Data_helper';
import { navigateToSearchResult } from '../../helpers/Algolia_helper';

const AdvancedFilter = ({
  t,
  setAddClassName,
  detectLanguage,
  addClassName,
  setChangedSearchState,
  hits
}) => {
  const { city, start_date, end_date, name_fr, name_en, id } = useParams();
  const navigate = useNavigate();
  const [scrollBarModal, setScrollBarModal] = useState(false);
  const [startDate, setStartDate] = useState(new Date(parseInt(start_date)));
  const [endDate, setEndDate] = useState(new Date(parseInt(end_date)));
  const [location, setLocation] = useState({
    value: city,
    isSelected: false
  });
  const currentTime = new Date();

  const handleStartDateChange = (date) => {
    date.setHours(
      currentTime.getHours(),
      currentTime.getMinutes(),
      currentTime.getSeconds(),
      currentTime.getMilliseconds()
    );
    setStartDate(date);
    setSessionStorage('start_date', date.getTime());
  };

  const handleEndDateChange = (date) => {
    date.setHours(
      currentTime.getHours(),
      currentTime.getMinutes(),
      currentTime.getSeconds(),
      currentTime.getMilliseconds()
    );
    setEndDate(date);
    setSessionStorage('end_date', date.getTime());
  };

  const handleDateChange = async () => {
    await navigateToSearchResult(
      {
        name_fr,
        name_en
      },
      location,
      startDate,
      endDate,
      id,
      navigate
    );
    setSessionStorage('currentRefinements', []);
    setSessionStorage('start_date', startDate.getTime());
    setSessionStorage('end_date', endDate.getTime());
  };

  async function onLocationChange(currentValue) {
    setLocation({ value: currentValue, isSelected: true });
    setSessionStorage('currentRefinements', []);
    setSessionStorage('location', currentValue);
    await navigateToSearchResult(
      {
        name_fr,
        name_en
      },
      { value: currentValue },
      startDate,
      endDate,
      id,
      navigate
    );
  }

  useEffect(() => {
    setStartDate(new Date(parseInt(start_date)));
    setEndDate(new Date(parseInt(end_date)));
    setLocation({ value: city, isSelected: false });
  }, [end_date, start_date, city]);
  const equipmentsWithNonEmptyAttributes = hits.filter((equipment) => {
    return attributes.some((attr) => equipment[attr] !== '');
  });

  const componentRef = useRef(null);
  useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.contentRect.height > 500) {
          setScrollBarModal(true);
        } else {
          setScrollBarModal(false);
        }
      }
    });

    if (componentRef.current) {
      resizeObserver.observe(componentRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [componentRef]);

  return (
    <div id="accordion" className="accordion accordion-advancedFilter">
      <div className={scrollBarModal ? 'scrollBarModal' : ''}>
        <div className="head row">
          <h2 className="t-subheading-2 c-fake-black bold col-7">
            {t('Advanced_filters')}
          </h2>
          <p className="t-body-regular c-neutrals-gray col-5 text-end">
            <CustomClearRefinements
              setChangedSearchState={setChangedSearchState}
              t={t}
              clearsQuery
            />
          </p>
        </div>

        <div className="form-group location">
          <SearchInputAutoCompleteEquipment
            attribute="location"
            placeholder={t('Set_your_location')}
            indexName={import.meta.env.VITE_ALGOLIA_INDEX_COVERAGE_AREAS_NAME}
            className="search-input-auto-complete form-control"
            value={location.value}
            onLocationChange={onLocationChange}
            onChange={setLocation}
            t={t}
            isAdvancedFilter
          />
        </div>
        <div className="form-group advanced_filter_date">
          <div className="search-result-datepicker advanced-filter form-group">
            <DatePicker
              handleStartDateChange={handleStartDateChange}
              handleEndDateChange={handleEndDateChange}
              startDate={startDate}
              endDate={endDate}
              startDateClassName="form-control w-100 "
              endDateClassName="form-control w-100"
            />
          </div>
          <CustomButton
            data-toggle="tooltip"
            onClick={handleDateChange}
            textPopper={
              isDisabled(startDate, endDate) ? t('Date_error_msg') : ''
            }
            textButton={t('Apply_new_rental_dates')}
            className="round-button yellow w-100"
            disabled={isDisabled(startDate, endDate)}
          />
        </div>
        <div ref={componentRef}>
          <AdvancedFilterTab title={t('Categories')}>
            <CustomRadioBoxMenu attribute="category" t={t} searchable />
          </AdvancedFilterTab>
          <AdvancedFilterTab title={t('Sub_category')}>
            <CustomRadioBoxMenu attribute="sub_category" t={t} searchable />
          </AdvancedFilterTab>
          <div className={detectLanguage === 'fr' ? 'hidden' : ''}>
            <AdvancedFilterTab
              title={t('Equipment_name_description')}
              defaultOpen
            >
              <CustomRadioBoxMenu
                attribute="description"
                showMore
                searchable
                t={t}
              />
            </AdvancedFilterTab>
          </div>

          <div className={detectLanguage === 'fr' ? '' : 'hidden'}>
            <AdvancedFilterTab
              title={t('Equipment_name_description')}
              defaultOpen
            >
              <CustomRadioBoxMenu
                attribute="description_fr"
                showMore
                searchable
                t={t}
              />
            </AdvancedFilterTab>
          </div>
          {equipmentsWithNonEmptyAttributes.length > 0 ? (
            <AdvancedFilterTab
              title={t('More_filters')}
              setAddClassName={setAddClassName}
              addClassName={addClassName}
              defaultOpen
            >
              <>
                {attributes?.map((attribute, key) => (
                  <CustomRefinementList
                    attribute={attribute}
                    key={key}
                    t={t}
                    searchable
                    isMoreFilters
                  />
                ))}
              </>
            </AdvancedFilterTab>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export const CustomAdvancedFilters = connectInfiniteHits(AdvancedFilter);
