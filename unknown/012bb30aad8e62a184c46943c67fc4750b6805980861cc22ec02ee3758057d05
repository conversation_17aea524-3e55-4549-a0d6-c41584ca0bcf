import { isEmpty } from 'lodash';
import React from 'react';
import CustomImage from '../../../images/Custom_image';
import RenderIf from '../../../Render_if';

export default function BidzRequestItem({
  item,
  t,
  role,
  member,
  setBidzModal,
  hasInventory,
  setSelectedBidz,
  declineConfirmationModal
}) {
  const noRole = role === 'equipper' ? 'lodger' : 'equipper';

  return (
    <div className="body-content__bottom">
      <div className="row">
        <div className="col-xl-10 offset-xl-2 bg-content">
          <div className="row align-items-center">
            <div className="col-xl-3 col-8 order-1">
              <div className="d-flex align-items-center">
                <CustomImage
                  imageUrl={item[`${noRole}_image_link`]}
                  alt="profile"
                  isUser
                />
                <p className="t-body-regular">{item[`${noRole}_name`]}</p>
              </div>
            </div>
            <RenderIf
              condition={
                role === 'lodger' &&
                member &&
                member.type === 'admin' &&
                !isEmpty(sessionStorage.getItem('member_of'))
              }
            >
              <div className="col-xl-3 col-8 order-1">
                <div className="d-flex align-items-center">
                  <CustomImage
                    imageUrl={item[`${role}_image_link`]}
                    alt="profile"
                    isUser
                  />

                  <p className="t-body-regular">{item[`${role}_name`]}</p>
                </div>
              </div>
            </RenderIf>
            <div className="col-xl-3 order-xl-3 order-2">
              <p className="t-body-regular text-left mobile-ml">
                {`${item.start_date?.slice(0, 10)} -
                                       ${item.return_date?.slice(0, 10)}`}
              </p>
            </div>
            <div
              className={
                role === 'equipper' && hasInventory === 'false'
                  ? 'col-xl-5  order-4'
                  : member && member.type === 'admin'
                  ? 'col-xl-3  order-4'
                  : 'col-xl-6  order-4'
              }
            >
              <div className="actions bidz-actions">
                <RenderIf
                  condition={
                    role === 'equipper' &&
                    hasInventory === 'false' &&
                    item.status === 'pending'
                  }
                >
                  <>
                    <button
                      className="round-button yellow c-black"
                      onClick={() => {
                        setSelectedBidz(item);
                        setBidzModal(true);
                      }}
                    >
                      {t('Make_bid')}
                    </button>
                    <button
                      className="round-button black shadow c-black"
                      onClick={() => declineConfirmationModal(item)}
                    >
                      {t('Decline')}
                    </button>
                  </>
                </RenderIf>
                <RenderIf
                  condition={
                    role === 'equipper' &&
                    (item.status === 'accepted' ||
                      item.status === 'rejected' ||
                      (hasInventory === 'true' && item.status === 'sent'))
                  }
                >
                  <button
                    className="round-button black bold shadow"
                    onClick={() => {
                      setSelectedBidz(item);
                      setBidzModal(true);
                    }}
                  >
                    {t('View_details')}
                  </button>
                </RenderIf>
                <RenderIf
                  condition={
                    role === 'lodger' &&
                    (item.status === 'pending' ||
                      item.status === 'accepted' ||
                      item.status === 'rejected')
                  }
                >
                  <button
                    className="round-button black bold shadow"
                    onClick={() => {
                      setSelectedBidz(item);
                      setBidzModal(true);
                    }}
                  >
                    {t('View_details')}
                  </button>
                </RenderIf>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
