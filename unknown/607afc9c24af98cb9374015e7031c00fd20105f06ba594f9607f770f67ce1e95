import React, { useState } from 'react';
import ProjectForm from './Project_form';
import { address } from '../../../shared/helpers/Address_helper';
import RenderIf from '../../../shared/components/Render_if';

export default function CreateProjectModal({
  t,
  show,
  onCreateClick,
  bidzOptions,
  equipmentOptions,
  memberOptions,
  creditCheckFormOptions,
  onClose,
  validate
}) {
  const [selectedEquipments, setSelectedEquipments] = useState([]);
  const [selectedLodgers, setSelectedLodgers] = useState([]);
  const [selectedBidz, setSelectedBidz] = useState([]);
  const [selectedCreditCheckForm, setSelectedCreditCheckForm] = useState([]);

  const initialValues = {
    ...address,
    start_date: new Date(),
    end_date: new Date(),
    name: ''
  };

  const createProject = async (event) => {
    const project = {
      ...event,
      equipments: selectedEquipments,
      credit_check_form_id: selectedCreditCheckForm?.value?.id,
      members: selectedLodgers,
      credit_check_form: { ...selectedCreditCheckForm?.value },
      bidz_equipment: selectedBidz
    };
    onCreateClick(project);
  };

  return (
    <RenderIf condition={show}>
      <div className="modal create-project-modal">
        <div className="modal-content no-title-margeTop add-memberModal">
          <button className="close-button" onClick={onClose}>
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19.25 5.25L5.75 18.75"
                stroke="#333333"
                strokeWidth="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M19.25 18.75L5.75 5.25"
                stroke="#333333"
                strokeWidth="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
          <div className="row mt-4">
            <div className="col-lg-11 mx-auto">
              <div className="scrollBarModal">
                <h2 className="t-header-h5 c-fake-black credit-title">
                  {t('Create_project_LMP')}
                </h2>
                <ProjectForm
                  validate={validate}
                  onSubmitFn={createProject}
                  initialValues={initialValues}
                  selectedEquipments={selectedEquipments}
                  setSelectedEquipments={setSelectedEquipments}
                  selectedLodgers={selectedLodgers}
                  setSelectedLodgers={setSelectedLodgers}
                  selectedBidz={selectedBidz}
                  bidzOptions={bidzOptions}
                  equipmentOptions={equipmentOptions}
                  memberOptions={memberOptions}
                  creditCheckFormOptions={creditCheckFormOptions}
                  setSelectedBidz={setSelectedBidz}
                  selectedCreditCheckForm={selectedCreditCheckForm}
                  setSelectedCreditCheckForm={setSelectedCreditCheckForm}
                  t={t}
                  onClose={onClose}
                  buttonText="Create_project_LMP"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </RenderIf>
  );
}
