package sendgridmailer

import (
	"context"
	"encoding/base64"
	"fmt"
	"net/http"

	"github.com/gabriel-vasile/mimetype"
	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"

	"github.com/vima-inc/derental/mailer"
)

type client struct {
	sendgridClient *sendgrid.Client
	from           *mail.Email
	templates      map[string]string
}

// New returns a new SendGrid client.
func New(apiKey string, sender string) mailer.Mailer {
	return &client{
		sendgridClient: sendgrid.NewSendClient(apiKey),
		from:           mail.NewEmail("", sender),
	}
}

// SetTemplate sets the template for the given name.
func (c *client) SetTemplateIDs(templateIDs map[string]string) {
	c.templates = templateIDs
}

// Send sends an email.
func (c *client) SendFromTemplate(ctx context.Context, templateName string, to []string, params map[string]string) error {
	m := mail.NewV3Mail()

	m.SetFrom(c.from)

	templateID, exist := c.templates[templateName]
	if !exist {
		return fmt.Errorf("template %s not found", templateName)
	}

	m.SetTemplateID(templateID)

	p := mail.NewPersonalization()
	tos := []*mail.Email{}

	for _, to := range to {
		tos = append(tos, mail.NewEmail("", to))
	}

	p.AddTos(tos...)

	for k, v := range params {
		p.SetDynamicTemplateData(k, v)
	}

	m.AddPersonalizations(p)

	request := c.sendgridClient.Request
	request.Body = mail.GetRequestBody(m)

	response, err := sendgrid.MakeRequestWithContext(ctx, request)
	if err != nil || response.StatusCode != http.StatusAccepted {
		return fmt.Errorf("error sending email: %w", err)
	}

	return nil
}
func (c *client) SendFromTemplateWithObject(ctx context.Context, templateName string, to []string, params map[string]any) error {
	m := mail.NewV3Mail()

	m.SetFrom(c.from)

	templateID, exist := c.templates[templateName]
	if !exist {
		return fmt.Errorf("template %s not found", templateName)
	}

	m.SetTemplateID(templateID)

	p := mail.NewPersonalization()
	tos := []*mail.Email{}

	for _, to := range to {
		tos = append(tos, mail.NewEmail("", to))
	}

	p.AddTos(tos...)

	for k, v := range params {
		p.SetDynamicTemplateData(k, v)
	}

	m.AddPersonalizations(p)

	request := c.sendgridClient.Request
	request.Body = mail.GetRequestBody(m)

	response, err := sendgrid.MakeRequestWithContext(ctx, request)
	if err != nil || response.StatusCode != http.StatusAccepted {
		return fmt.Errorf("error sending email: %w", err)
	}

	return nil
}

func (c *client) SendFromTemplateWithAttachement(ctx context.Context, templateName string, to []string, params map[string]string, attachment map[string][]byte) error {
	m := mail.NewV3Mail()

	m.SetFrom(c.from)

	templateID, exist := c.templates[templateName]
	if !exist {
		return fmt.Errorf("template %s not found", templateName)
	}

	m.SetTemplateID(templateID)

	p := mail.NewPersonalization()
	tos := []*mail.Email{}

	for _, to := range to {
		tos = append(tos, mail.NewEmail("", to))
	}

	p.AddTos(tos...)

	for k, v := range params {
		p.SetDynamicTemplateData(k, v)
	}

	m.AddPersonalizations(p)

	request := c.sendgridClient.Request
	request.Body = mail.GetRequestBody(m)
	for name, data := range attachment {
		encoded := base64.StdEncoding.EncodeToString(data)
		mimeType := mimetype.Detect(data).String()
		a := mail.NewAttachment()
		a.SetContent(encoded)
		a.SetType(mimeType)
		a.SetFilename(name)
		a.SetDisposition("attachment")
		m.AddAttachment(a)
	}

	response, err := sendgrid.MakeRequestWithContext(ctx, request)
	if err != nil || response.StatusCode != http.StatusAccepted {
		return fmt.Errorf("error sending email: %w", err)
	}

	return nil
}
