package service

import (
	"context"
	"fmt"

	"github.com/vima-inc/derental/models"
)

// CreatePromotionCode creates a promotion code
func (s *Service) CreatePromotionCode(ctx context.Context, promo models.Promotion) error {
	id, err := s.payment.CreateCoupon(promo.Code, promo.PercentOff, promo.Currency, promo.Duration)
	if err != nil {
		return fmt.Errorf("unable to create coupon: %w", err)
	}

	promo.ID = id

	err = s.db.CreatePromotionCode(ctx, promo)
	if err != nil {
		return fmt.Errorf("unable to create promotion code: %w", err)
	}

	return nil
}

// DeletePromotionCode deletes a promotion code
func (s *Service) DeletePromotionCode(ctx context.Context, equipperID string, code string) error {
	err := s.payment.DeleteCoupon(code)
	if err != nil {
		return fmt.Errorf("unable to delete coupon from payment: %w", err)
	}

	err = s.db.DeletePromotionCode(ctx, equipperID, code)
	if err != nil {
		return fmt.Errorf("unable to delete promotion code from db: %w", err)
	}

	return nil
}

// GetAllPromotionCodeByEquipperID gets all promotion codes for an equipper.
func (s *Service) GetAllPromotionCodeByEquipperID(ctx context.Context, equipperID string) ([]models.Promotion, error) {
	promotions, err := s.db.GetAllPromotionCodeByEquipperID(ctx, equipperID)
	if err != nil {
		return nil, fmt.Errorf("unable to get promotion for equipper %s: %w", equipperID, err)
	}

	return promotions, nil
}

func (s *Service) GetPromotionCodeByEquipperIDAndLodgerID(ctx context.Context, equipperID string, lodgerID string) (models.Promotion, error) {
	promo, err := s.db.GetPromotionCodeByEquipperIDAndLodgerID(ctx, equipperID, lodgerID)
	if err != nil {
		return models.Promotion{}, fmt.Errorf("unable to get promotion code by equipperID and lodgerID: %w", err)
	}

	return promo, nil
}
