$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 1000px,
  xl: 1180px,
  xxl: 1340px
);
.container {
  max-width: 100%;
}

@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 1000px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

/*@media (min-width: 1400px) {
  .container {
    max-width: 1140px;
  }
}

@media (min-width: 1600px) {
  .container {
    max-width: 1424px;
  }
}

@media (min-width: 1500px) {
  .container {
    max-width: 1475px;
  }
}*/

$primary-color: #061C3D;
$near-black: #262525;
$near-black2: #302f2f;
$fake-black:#333333;
$white: #ffffff;
$yellow: #ECA869;
$light-grey: #f1f1f1;
$near-grey: #9a9a9a;
$medium-grey: #707070;
$grey: #00000029;
$black: #141414;
$grey-titles: #515151;
$check-grey: #d1d1d1;
$green: #b0f0bf;
$rose: #ffc1c1;
$light-yellow: #ECA869;
$red: #FF313A;
$light-blue: #e0f8fc;
$border-grey: #E5ECF6;
$blue-grey: #42526B;
$blue-burger:#061C3D;
$gray-white:#F2F2F2;
$new-green:#0DA317;
$neutrals-lightest-gray:#C8C9CB;
$light-gray: #E8ECF2;
$neutrals-gray:#838E9E;
$ui-black :#171515;
$titles_color:#081D3C;
$multiselect_grey : #EEF2F6;
$blue:#1DA1F2;

//Fonts

$primary-font: 'airbnb-cereal-medium', sans-serif;
$secondary-font: 'airbnb-cereal-bold', sans-serif;
