module.exports = {
  env: {
    browser: true,
    es2021: true
  },
  extends: ['airbnb', 'plugin:prettier/recommended'],
  overrides: [],
  parserOptions: {
    ecmaFeatures: {
      jsx: true
    },
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  plugins: ['react', 'prettier'],
  rules: {
    'no-unsafe-optional-chaining': 'off',
    'jsx-no-useless-fragment': 'off',
    'no-undef': 'off',
    'prettier/prettier': 'off',
    'react/function-component-definition': 'off',
    'react/no-unstable-nested-components': 'off',
    'jsx-a11y/control-has-associated-label': 'off',
    'react/jsx-no-useless-fragment': 'off',
    'react/jsx-props-no-spreading': 'off',
    'react/jsx-fragments': 'off',
    'no-console': 'warn',
    'react/jsx-no-constructed-context-values': 'off',
    quotes: [
      'error',
      'single',
      {
        avoidEscape: true
      }
    ],
    'import/no-extraneous-dependencies': [
      'error',
      {
        devDependencies: false,
        optionalDependencies: false,
        peerDependencies: false
      }
    ],
    'comma-dangle': [
      'error',
      {
        arrays: 'never',
        objects: 'never',
        functions: 'never'
      }
    ],
    'react/prop-types': 0,
    'jsx-a11y/no-static-element-interactions': [
      'off',
      {
        handlers: [
          'onClick',
          'onMouseDown',
          'onMouseUp',
          'onKeyPress',
          'onKeyDown',
          'onKeyUp'
        ],
        allowExpressionValues: true
      }
    ],
    'jsx-a11y/click-events-have-key-events': [
      'off',
      {
        handlers: [
          'onClick',
          'onMouseDown',
          'onMouseUp',
          'onKeyPress',
          'onKeyDown',
          'onKeyUp'
        ],
        allowExpressionValues: true
      }
    ],
    'react/no-array-index-key': 0,
    'jsx-a11y/label-has-for': 0,
    'no-shadow': [
      'off',
      {
        builtinGlobals: false,
        hoist: 'functions',
        allow: [],
        ignoreOnInitialization: false
      }
    ],
    'jsx-a11y/label-has-associated-control': [
      'off',
      {
        labelComponents: [],
        labelAttributes: [],
        controlComponents: [],
        assert: 'both',
        depth: 3
      }
    ],
    camelcase: [
      'off',
      {
        properties: 'never'
      }
    ],
    'react/destructuring-assignment': 0,
    'no-return-await.js ': 0,
    'no-return-await': 0,
    'no-return-assign': 0,
    'no-param-reassign': 0,
    'no-unused-expressions': 0,
    'no-plusplus': 0,
    radix: 0,
    'no-new': 0,
    'no-nested-ternary': 0,
    'no-unsafe-finally': 0,
    'no-underscore-dangle': 0,
    'react/no-unknown-property': 0,
    'consistent-return': 0,
    'no-await-in-loop': 0,
    'import/extensions': 0,
    'import/no-unresolved': 0,
    'no-use-before-define': 0,
    'jsx-a11y/aria-role': 0,
    'import/order': 0,
    'jsx-a11y/no-noninteractive-element-interactions': 0,
    'react/button-has-type': 0,
    'new-cap': 0,
    'react-hooks/exhaustive-deps': 'off',
    'react/default-props-match-prop-types': [
      'off',
      {
        allowRequiredDefaults: false
      }
    ],
    'react/boolean-prop-naming': [
      'off',
      {
        propTypeNames: ['bool', 'mutuallyExclusiveTrueProps'],
        rule: '^(is|has)[A-Z]([A-Za-z0-9]?)+',
        message: '',
        validateNested: false
      }
    ],
    'react/forbid-prop-types': [
      'off',
      {
        forbid: ['any', 'array', 'object'],
        checkContextTypes: true,
        checkChildContextTypes: true
      }
    ],
    'react/jsx-boolean-value': ['off'],
    'react/jsx-closing-bracket-location': ['off'],
    'react/jsx-closing-tag-location': ['off'],
    'react/jsx-curly-brace-presence': ['off'],
    'react/jsx-curly-spacing': ['off'],
    'react/jsx-equals-spacing': ['off', 'never'],
    'react/jsx-filename-extension': [
      'off',
      {
        allow: null,
        extensions: ['.jsx']
      }
    ],
    'react/jsx-first-prop-new-line': ['off', 'multiline-multiprop'],
    'react/jsx-indent-props': ['off'],
    'react/jsx-indent': ['off'],
    'react/jsx-max-props-per-line': ['off'],
    'react/jsx-no-bind': [
      'off',
      {
        ignoreRefs: true,
        allowArrowFunctions: true,
        allowFunctions: false,
        allowBind: false,
        ignoreDOMComponents: true
      }
    ],
    'react/jsx-no-comment-textnodes': ['off'],
    'react/void-dom-elements-no-children': ['off'],
    'react/style-prop-object': [
      'off',
      {
        allow: null
      }
    ],
    'react/sort-comp': [
      'off',
      {
        order: [
          'static-methods',
          'instance-variables',
          'lifecycle',
          '/^on.+$/',
          'getters',
          'setters',
          '/^(get|set)(?!(InitialState$|DefaultProps$|ChildContext$)).+$/',
          'instance-methods',
          'everything-else',
          'rendering'
        ],
        groups: {
          lifecycle: [
            'displayName',
            'propTypes',
            'contextTypes',
            'childContextTypes',
            'mixins',
            'statics',
            'defaultProps',
            'constructor',
            'getDefaultProps',
            'getInitialState',
            'state',
            'getChildContext',
            'componentWillMount',
            'componentDidMount',
            'componentWillReceiveProps',
            'shouldComponentUpdate',
            'componentWillUpdate',
            'componentDidUpdate',
            'componentWillUnmount'
          ],
          rendering: ['/^render.+$/', 'render']
        }
      }
    ],
    'react/self-closing-comp': ['off', null],
    'react/require-render-return': ['off'],
    'react/require-default-props': ['off'],
    'react/react-in-jsx-scope': ['off'],
    'react/prefer-stateless-function': [
      'off',
      {
        ignorePureComponents: true
      }
    ],
    'react/prefer-es6-class': ['off', 'always'],
    'react/no-will-update-set-state': ['off', null],
    'react/no-unused-state': ['off'],
    'array-bracket-spacing': ['off', 'never', null],
    'array-callback-return': [
      'off',
      {
        allowImplicit: true
      }
    ],
    'no-unused-expression': [
      'off',
      {
        allowShortCircuit: true,
        allowTernary: true,
        allowTaggedTemplates: true
      }
    ],
    'import/prefer-default-export': 'off',
    'import/named': 'off',
    'object-shorthand': ['off', 'always', { avoidQuotes: true }],
    'import/no-named-as-default-member': 'off',
    'import/no-named-as-default': 'off',
    'no-restricted-syntax': 'off'
  },

  root: true
};
