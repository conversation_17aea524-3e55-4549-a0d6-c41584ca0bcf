import React, { useState } from 'react';
import PersonalInfo from '../../../shared/components/management_portal/Personal_info';
import { useLodger } from '../../../shared/context/Lodger_context';
import EditPersonalInfo from '../../../shared/components/modals/Edit_personal_info';
import Popup from '../../../shared/components/modals/Popup';
import SuccessPopUp from '../../../shared/components/modals/Success_pop_up';
import RenderIf from '../../../shared/components/Render_if';
import ToloIsLoading from '../../../shared/components/cards/Tolo_is_loading';
import { getCookies } from '../../../shared/helpers/Cookies';
import { UPLOAD_PROFILE_PHOTO_LODGER } from '../../../shared/helpers/Url_constants';
import axios from 'axios';

export default function LodgerPersonalInfo({ loadedInfo, isLoading, t }) {
  const { UpdateLodgerPersonalInfo } = useLodger();
  const [show, setShow] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [response, setResponse] = useState({ message: '' });
  const [showPopup, setShowPopup] = useState(false);

  const updatePersonalInfo = async (data) => {
    const res = await UpdateLodgerPersonalInfo(data);
    if (res.status === 200) {
      setResponse(res);
      setShowSuccess(true);
    } else {
      setResponse(res);

      setShowPopup(true);
    }
  };
  const uploadImage = async (selectedFile) => {
    const formatData = new FormData();

    formatData.set('file', selectedFile);

    const header = {
      'Content-Type': 'multipart/form-data'
    };
    header.Authorization = `Bearer ${getCookies('token')}`;

    const res = await axios.post(
      import.meta.env.VITE_REACT_APP_BASE_URL + UPLOAD_PROFILE_PHOTO_LODGER,
      formatData,
      { headers: header }
    );
    if (res.status === 200) {
      if (loadedInfo.photo_url !== '') {
        setResponse({
          ...res,
          message: t('Upload_img_msg')
        });
      }
      setShowSuccess(true);
    } else {
      setResponse(res);
      setShowPopup(true);
    }
  };
  if (!loadedInfo) {
    return <ToloIsLoading />;
  }
  return (
    <RenderIf condition={loadedInfo}>
      <PersonalInfo
        isLoading={isLoading}
        loadedInfo={loadedInfo}
        showModal={() => setShow(true)}
        t={t}
      />
      <RenderIf condition={show && loadedInfo}>
        <EditPersonalInfo
          t={t}
          uploadImage={uploadImage}
          onClose={() => setShow(false)}
          loadedInfo={loadedInfo}
          show={show}
          updatePersonalInfo={updatePersonalInfo}
          role="lodger"
        />
      </RenderIf>
      <Popup
        t={t}
        show={showPopup}
        onClose={() => {
          setShowPopup(false);
          window.location.reload();
        }}
        response={response}
      />
      <SuccessPopUp
        onClose={() => {
          setShowSuccess(false);
          window.location.reload();
        }}
        message={response.message}
        show={showSuccess}
      />
    </RenderIf>
  );
}
