package ses

import (
	"bytes"
	"context"
	"io"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	isotrage "github.com/vima-inc/derental/storage"
)

type storageMock struct {
	mock.Mock
}

func (s *storageMock) ReadWithMetadata(ctx context.Context, bucket string, path string) (io.ReadCloser, isotrage.Metadata, error) {
	args := s.Called(ctx, bucket, path)
	return args.Get(0).(io.ReadCloser), args.Get(1).(isotrage.Metadata), args.Error(2)
}

func TestLoadTemplate(t *testing.T) {
	s := &storageMock{}
	s.On("ReadWithMetadata", mock.Anything, "templates", "templates/d-0020ba781d394997bb5512583b0f729a.html").Return(
		io.NopCloser(bytes.NewBufferString("Hello {{.equipment_name}} from {{.start_date}} to {{.end_date}}")), isotrage.Metadata{
			"subject": "Hello",
		}, nil)

	client := &client{
		storage: s,
		bucket:  "templates",
	}

	got, subject, err := client.loadTemplate(context.Background(), "d-0020ba781d394997bb5512583b0f729a",
		map[string]string{
			"equipment_name": "Velo",
			"start_date":     "2021-01-01",
			"end_date":       "2021-01-02",
		})
	assert.NoError(t, err)
	assert.Contains(t, got, "Velo")
	assert.Contains(t, got, "2021-01-01")
	assert.Contains(t, got, "2021-01-02")

	assert.Equal(t, "Hello", subject)
}
