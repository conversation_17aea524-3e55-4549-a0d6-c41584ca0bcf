import React from 'react';
import Tooltip from '@mui/material/Tooltip';

export default function CustomTooltip({ children, text, placement }) {
  return (
    <div className="App z-index-1">
      <Tooltip title={text} placement={placement || BOTTOM}>
        {children}
      </Tooltip>
    </div>
  );
}
export const TOP = 'top';
export const BOTTOM = 'bottom';
export const LEFT = 'left';
export const RIGHT = 'right';
