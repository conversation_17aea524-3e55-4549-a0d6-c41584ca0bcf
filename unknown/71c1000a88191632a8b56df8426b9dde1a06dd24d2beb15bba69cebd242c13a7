import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import {
  firstLetterUpperCase,
  formatPhoneNumber,
  isEmptyValue
} from '../../helpers/String_helps';
import RenderIf from '../Render_if';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExternalLinkAlt } from '@fortawesome/fontawesome-free-solid';
import { useProjects } from '../../context/Project_context';

function RequestDetail({
  data,
  role,
  t,
  isRequest,
  isBidz,
  priceDetails,
  isEquipper,
  handleChange,
  isUS,
  makeBidzOffer
}) {
  const { GetProject } = useProjects();
  const [project, setProject] = useState('');

  useEffect(() => {
    const fetchProject = async () => {
      const res = await GetProject(data.project_id);
      if (res.status === 200) {
        setProject(res.data.name);
      }
    };

    fetchProject();
  }, [data.project_id]);
  return (
    <>
      <RenderIf condition={data}>
        <RenderIf condition={data?.cancel_comment}>
          <div className="row align-items-center justify-content-between row-infos form-group">
            <div className="col-lg-12 padding-r-0 padding-l-0">
              <div className="padding-l-0 margin-top-20">
                <p className="t-body-regular">
                  <span className="t-body-regular bold c-near-grey">
                    {t('Cancelation_reason_text')}:
                  </span>{' '}
                  {isEmptyValue(data?.cancel_comment, t)}
                </p>
              </div>
            </div>
          </div>
        </RenderIf>

        <div className="row align-items-center justify-content-between row-infos">
          <div className="col-md-6 padding-l-0 margin-top-20">
            <p className="t-body-regular">
              <span className="t-body-regular bold c-near-grey">
                {t('Request_initiatior_name')} :
              </span>{' '}
              {data?.lodger_name}
            </p>
          </div>
          <div className="col-md-6 padding-l-0 margin-top-20">
            <p className="t-body-regular">
              <span className="t-body-regular bold c-near-grey">
                {t('Request_initiatior_phone')} :
              </span>{' '}
              {data?.phone_number}
            </p>
          </div>
        </div>
        <div className="row align-items-center justify-content-between row-infos">
          <div className="col-md-6 padding-l-0 margin-top-20">
            <p className="t-body-regular">
              <span className="t-body-regular bold c-near-grey">
                {t('Receiver_information_name')} :
              </span>{' '}
              {isEmptyValue(data?.receiver_info?.name)}
            </p>
          </div>
          <div className="col-md-6 padding-l-0 margin-top-20">
            <p className="t-body-regular">
              <span className="t-body-regular bold c-near-grey">
                {t('Receiver_information_phone')} :
              </span>{' '}
              {isEmptyValue(data?.receiver_info?.phone)}
            </p>
          </div>
        </div>
        <div className="row align-items-center justify-content-between row-infos">
          <RenderIf condition={data?.credit_check_form?.credit_check_form_path}>
            <div className="col-md-6 padding-l-0 margin-top-20">
              <p className="t-body-regular">
                <span className="t-body-regular bold c-near-grey">
                  {t('Credit_check_form')} :
                </span>{' '}
                <a
                  href={data?.credit_check_form?.credit_check_form_path}
                  target="_blank"
                  className="t-base-medium c-primary-color"
                  rel="noopener noreferrer"
                >
                  {t('View_details')}
                  <span className="ml-2">
                    <FontAwesomeIcon
                      icon={faExternalLinkAlt}
                      className="fa-external-link"
                    />
                  </span>
                </a>
              </p>
            </div>
          </RenderIf>
          <RenderIf condition={!isEquipper}>
            <div className="col-md-6 padding-l-0 margin-top-20">
              <p className="t-body-regular">
                <span className="t-body-regular bold c-near-grey">
                  {t('Project_name_LMP')} :
                </span>{' '}
                {isEmptyValue(project)}
              </p>
            </div>
          </RenderIf>
        </div>

        <RenderIf
          condition={
            data?.credit_check_form?.attachments?.insurance ||
            data?.credit_check_form?.attachments?.user_id
          }
        >
          <div className="row align-items-center justify-content-between row-infos">
            <RenderIf
              condition={
                data?.credit_check_form?.attachments?.partnership_agreement
              }
            >
              <div
                className={
                  data?.credit_check_form?.attachments?.user_id
                    ? 'col-md-6 padding-l-0 margin-top-20'
                    : 'col-md-7 padding-l-0'
                }
              >
                <p className="t-body-regular c-fake-black">
                  <strong className="t-body-regular">
                    <span className="c-near-grey t-body-regular bold">
                      {t('Partnership_agreement')}:{' '}
                    </span>
                    <a
                      href={
                        data?.credit_check_form?.attachments
                          ?.partnership_agreement
                      }
                      target="_blank"
                      className="t-body-regular c-fake-black"
                      rel="noopener noreferrer"
                    >
                      {t('View_details')}
                      <span className="ml-2">
                        <FontAwesomeIcon
                          icon={faExternalLinkAlt}
                          className="fa-external-link"
                        />
                      </span>
                    </a>
                  </strong>
                </p>
              </div>
            </RenderIf>

            <RenderIf condition={data?.credit_check_form?.attachments?.user_id}>
              <div className="col-md-6 padding-l-0 margin-top-20">
                <p className="t-body-regular c-primary-color">
                  <strong className="t-body-regular">
                    <span className="c-near-grey t-body-regular bold">
                      {t('User_ID')}:{' '}
                    </span>
                    <a
                      href={data?.credit_check_form?.attachments?.user_id}
                      target="_blank"
                      className="t-body-regular c-fake-black"
                      rel="noopener noreferrer"
                    >
                      {t('View_details')}
                      <span className="ml-2">
                        <FontAwesomeIcon
                          icon={faExternalLinkAlt}
                          className="fa-external-link"
                        />
                      </span>
                    </a>
                  </strong>
                </p>
              </div>
            </RenderIf>
          </div>
        </RenderIf>
        <RenderIf
          condition={
            data?.credit_check_form?.attachments?.insurance ||
            data?.credit_check_form?.attachments?.second_applicant_id
          }
        >
          <div className="row align-items-center justify-content-between row-infos">
            <RenderIf
              condition={data?.credit_check_form?.attachments?.insurance}
            >
              <div className="col-md-6 padding-l-0 margin-top-20">
                <p className="t-body-regular c-fake-black">
                  <strong className="t-body-regular">
                    <span className="c-near-grey t-body-regular bold">
                      {t('Insurance')}:{' '}
                    </span>
                    <a
                      href={data?.credit_check_form?.attachments?.insurance}
                      target="_blank"
                      className="t-body-regular c-fake-black"
                      rel="noopener noreferrer"
                    >
                      {t('View_details')}
                      <span className="ml-2">
                        <FontAwesomeIcon
                          icon={faExternalLinkAlt}
                          className="fa-external-link"
                        />
                      </span>
                    </a>
                  </strong>
                </p>
              </div>
            </RenderIf>
            <RenderIf
              condition={
                data?.credit_check_form?.attachments?.second_applicant_id
              }
            >
              <div className="col-md-6 padding-l-0 margin-top-20">
                <p className="t-body-regular c-fake-black">
                  <strong className="t-body-regular">
                    <span className="c-near-grey t-body-regular bold">
                      {t('Secondary_applicant_userID')}:{' '}
                    </span>
                    <a
                      href={
                        data?.credit_check_form?.attachments
                          ?.second_applicant_id
                      }
                      target="_blank"
                      className="t-body-regular c-fake-black"
                      rel="noopener noreferrer"
                    >
                      {t('View_details')}
                      <span className="ml-2">
                        <FontAwesomeIcon
                          icon={faExternalLinkAlt}
                          className="fa-external-link"
                        />
                      </span>
                    </a>
                  </strong>
                </p>
              </div>
            </RenderIf>
          </div>
        </RenderIf>

        <div className="row align-items-center justify-content-between row-infos">
          <div className="col-md-6 padding-l-0 margin-top-20">
            <p className="t-body-regular">
              <span className="t-body-regular bold c-near-grey">
                {t('Delivery_preference')} :
              </span>{' '}
              {t(
                `${firstLetterUpperCase(
                  data?.delivery_details.delivery_preference?.replaceAll(
                    '-',
                    '_'
                  )
                )}${isUS ? '_name_us' : '_name'}`
              )}
            </p>
          </div>
          <div className="col-md-6 padding-l-0 margin-top-20">
            <p className="t-body-regular">
              <span className="t-body-regular bold c-near-grey">
                {t('Payment_method')} :
              </span>{' '}
              {t(
                firstLetterUpperCase(data?.payment_method?.replaceAll(' ', '_'))
              )}
            </p>
          </div>
        </div>
        <RenderIf condition={data?.delivery_type}>
          <div className="row align-items-center justify-content-between row-infos">
            <div className="col-md-6 padding-l-0 margin-top-20">
              <p className="t-body-regular">
                <span className="t-body-regular bold c-near-grey">
                  {t('Delivery_type')} :
                </span>{' '}
                {isEmptyValue(data?.delivery_type, t)}
              </p>
            </div>
          </div>
        </RenderIf>

        <RenderIf condition={role === 'equipper' && data?.status !== 'sent'}>
          <div className="row align-items-center justify-content-between row-infos">
            <div className="col-md-6 padding-l-0 margin-top-20">
              <p className="t-body-regular">
                <span className="t-body-regular bold c-near-grey">
                  {t('PO_number')} :
                </span>{' '}
                {isEmptyValue(data?.po_number, t)}
              </p>
            </div>
            <div className="col-md-6 padding-l-0 margin-top-20">
              <p className="t-body-regular">
                <span className="t-body-regular bold c-near-grey">
                  {t('Contact')} :
                </span>{' '}
                {formatPhoneNumber(data ? data.phone_number : 'N/A')}
              </p>
            </div>
          </div>
        </RenderIf>

        <div className="row align-items-center justify-content-between row-infos">
          <div className="col-md-6 padding-l-0 margin-top-20">
            <p className="t-body-regular">
              <span className="t-body-regular bold c-near-grey">
                {t('Start_date')} :
              </span>{' '}
              {data?.start_date?.slice(0, 10)}
            </p>
          </div>

          <div className="col-md-6 padding-l-0 margin-top-20">
            <p className="t-body-regular">
              <span className="t-body-regular bold c-near-grey">
                {t('Return_date')} :
              </span>{' '}
              {data
                ? data[`${isBidz ? 'return_date' : 'end_date'}`]?.slice(0, 10)
                : ''}
            </p>
          </div>
        </div>

        <div className="row align-items-center justify-content-between row-infos">
          <div className="col-md-6 padding-l-0 margin-top-20">
            <p className="t-body-regular">
              <span className="t-body-regular bold c-near-grey">
                {t('Pickup_hour')} :
              </span>{' '}
              {data?.delivery_details.pickup}
            </p>
          </div>
          <div className="col-md-6 padding-l-0 margin-top-20">
            <p className="t-body-regular">
              <span className="t-body-regular bold c-near-grey">
                {t('Drop_hour')} :
              </span>{' '}
              {data?.delivery_details.drop}
            </p>
          </div>
        </div>

        <RenderIf condition={!data?.need_one}>
          <div className="row align-items-center justify-content-between row-infos form-group">
            <div className="col-lg-12 padding-r-0 padding-l-0">
              <div className="padding-l-0 margin-top-20">
                <p className="t-body-regular">
                  <span className="t-body-regular bold c-near-grey">
                    {isUS
                      ? t('Assurance_renonciation_dommage_us')
                      : t('Assurance_renonciation_dommage')}{' '}
                    :
                  </span>{' '}
                  {` ${data?.insurance_company}, ${
                    data?.insurance_policy_number
                  }, ${
                    data?.insurance_coverage
                  }, ${data?.expiry_date_of_insurance?.slice(0, 10)}`}
                </p>
              </div>
            </div>
          </div>
        </RenderIf>

        <RenderIf condition={data?.billing_address.address}>
          <div className="row align-items-center justify-content-between row-infos form-group">
            <div className="col-lg-12 padding-r-0 padding-l-0">
              <div className="padding-l-0 margin-top-20">
                <p className="t-body-regular">
                  <span className="t-body-regular bold c-near-grey">
                    {t('Billing_address_LMP')}:
                  </span>{' '}
                  {` ${data?.billing_address.address}, ${data?.billing_address.city}, ${data?.billing_address.state}, ${data?.billing_address.zip_code}`}
                </p>
              </div>
            </div>
          </div>
        </RenderIf>

        <div className="row align-items-center justify-content-between row-infos form-group">
          <RenderIf condition={data?.delivery_address.address}>
            <div className="col-lg-12 padding-r-0 padding-l-0">
              <div className="padding-l-0 margin-top-20">
                <p className="t-body-regular">
                  <span className="t-body-regular bold c-near-grey">
                    {t('Delivery_address_LMP')}:
                  </span>{' '}
                  {` ${data?.delivery_address.address}, ${data?.delivery_address.city}, ${data?.delivery_address.state}, ${data?.delivery_address.zip_code}`}
                </p>
              </div>
            </div>
          </RenderIf>
        </div>
        {isRequest ||
          (!isBidz && data?.status && data?.price_per_day && (
            <div className="result-box with-border">
              {priceDetails?.map((item, key) => (
                <div
                  className="row align-items-center justify-content-between comission-price-details "
                  key={key}
                >
                  <div
                    className={
                      item.name === 'total_amount'
                        ? 'col-sm-12 total-amount-price-details'
                        : 'col-md-12 padding-l-0'
                    }
                  >
                    <p className="t-body-regular c-fake-black">
                      <strong className="t-body-regular">
                        <span className="c-near-grey t-body-regular bold">
                          {item.label}
                        </span>
                        {item.value}
                      </strong>
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ))}
        <RenderIf condition={makeBidzOffer || (isBidz && data?.price_per_day)}>
          <div className="row align-items-center justify-content-between row-infos form-group">
            <div className="padding-l-0 mt-4">
              <div className="padding-l-0">
                <div className="opinion--title">
                  <h3 className="t-body-regular bold c-near-grey  pl-0">
                    {t('Price_offer')}
                  </h3>{' '}
                </div>
                <div className="row">
                  <div className="col-lg-3 padding-r-0 ">
                    <input
                      required
                      type={!makeBidzOffer ? 'text' : 'number'}
                      className="form-control  w-75"
                      name="day"
                      value={
                        !makeBidzOffer
                          ? `${data?.price_per_day} ${data?.currency}`
                          : null
                      }
                      disabled={!makeBidzOffer}
                      placeholder="Day"
                      onChange={handleChange}
                    />
                  </div>
                  <div className="col-lg-3 padding-r-0 ">
                    <input
                      required
                      type={!makeBidzOffer ? 'text' : 'number'}
                      name="week"
                      disabled={!makeBidzOffer}
                      value={
                        !makeBidzOffer
                          ? `${data?.price_per_week} ${data?.currency}`
                          : null
                      }
                      className="form-control w-75 "
                      placeholder="Week"
                      onChange={handleChange}
                    />
                  </div>
                  <div className="col-lg-3 padding-r-0 col-sm-12">
                    <input
                      required
                      type={!makeBidzOffer ? 'text' : 'number'}
                      name="month"
                      className="form-control w-75"
                      disabled={!makeBidzOffer}
                      value={
                        !makeBidzOffer
                          ? `${data?.price_per_month} ${data?.currency}`
                          : null
                      }
                      placeholder="4 weeks"
                      onChange={handleChange}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <RenderIf condition={data?.comment}>
            <div className="row align-items-center justify-content-between row-infos form-group">
              <div className="col-lg-12 padding-r-0 padding-l-0">
                <div className="padding-l-0 margin-top-20">
                  <p className="t-body-regular">
                    <span className="t-body-regular bold c-near-grey">
                      {t('Special_note')}:
                    </span>{' '}
                    {isEmptyValue(data?.comment, t)}
                  </p>
                </div>
              </div>
            </div>
          </RenderIf>
        </RenderIf>
      </RenderIf>
    </>
  );
}

export default RequestDetail;

RequestDetail.propTypes = {
  data: PropTypes.object,
  role: PropTypes.string,
  t: PropTypes.func
};

RequestDetail.defaultProps = {
  data: {},
  role: '',
  equipments: [],
  t: () => {},
  isRequest: false,
  pricesData: []
};
