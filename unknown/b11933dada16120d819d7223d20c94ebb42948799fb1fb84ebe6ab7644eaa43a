import React from 'react';
import DatePickerComponent from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { subDays } from 'rsuite/esm/utils/dateUtils';

export default function DatePicker({
  label,
  isForShow,
  handleStartDateChange,
  handleEndDateChange,
  startDate,
  endDate,
  endDateClassName,
  startDateClassName,
  placeholder,
  filterDate
}) {
  return (
    <>
      {label !== '' && label !== undefined && (
        <label htmlFor="input-field" className="font-selecor fwb">
          {label}
        </label>
      )}
      <div className="adjustPicker new-datepicker">
        <div className="start_date">
          <DatePickerComponent
            selected={startDate}
            onChange={handleStartDateChange}
            readOnly={isForShow}
            minDate={subDays(new Date(), 0)}
            filterDate={filterDate} // Disable specific dates
            className={startDateClassName}
            placeholderText={placeholder}
          />
        </div>
        <div className="end_date">
          <DatePickerComponent
            selected={endDate}
            onChange={handleEndDateChange}
            readOnly={isForShow}
            minDate={startDate}
            filterDate={filterDate} // Disable specific dates
            className={endDateClassName}
            placeholderText={placeholder}
          />
        </div>
      </div>
    </>
  );
}
