import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

export default function IconButton({
  onClick,
  className,
  classNameButton,
  type,
  title,
  disabled,
  icon
}) {
  return (
    <div className={className}>
      <button
        className={classNameButton}
        onClick={onClick}
        data-toggle="tooltip"
        data-placement="top"
        title={title}
        type={type}
        disabled={disabled}
      >
        <FontAwesomeIcon icon={icon} />
      </button>
    </div>
  );
}
