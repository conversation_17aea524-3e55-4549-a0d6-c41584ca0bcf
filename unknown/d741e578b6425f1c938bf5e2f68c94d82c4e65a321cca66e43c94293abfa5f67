/*
  ---------- ---------- -----summary----- ---------- ---
  • statistics intro
  • statistics rented blocs
  • statistics ratings
  ---------- ---------- ---------- ---------- ----------
*/

/*
  • statistics intro
  ---------- ---------- ---------- ---------- ----------
*/

.statistics-month {
  .toggle-style {
    font-weight: 900;
  }
}

.statistics-intro {
  padding-left: 15px;
  padding-right: 15px;
  padding-bottom: 20px;
  margin-bottom: 70px;
  @media (min-width: 992px) {
    padding-left: 95px;
    padding-right: 95px;
    padding-bottom: 50px;
  }

  .yellow-content {
    padding: 30px 50px;
    background: $light-yellow;
    border-radius: 30px;
    margin-top: 20px;
    @media (min-width: 992px) {
      margin-top: 38px;
    }

    .t-header-large-30 {
      @media(min-width: 768px) {
        font-size: 27px;
      }
    }
  }
}

/*
  • statistics rented blocs
  ---------- ---------- ---------- ---------- ----------
*/

.rented-blocs {
  margin-top: 40px;
  margin-bottom: 45px;

  .white-bg {
    @media (max-width: 992px) {
      padding-left: 15px;
      padding-right: 15px;
      padding-bottom: 20px;
    }
  }

  .mobile-mr-b {
    @media (max-width: 992px) {
      margin-bottom: 45px;
    }
  }

  &__head {
    h2 {
      @media (max-width: 992px) {
        margin-top: 25px;
        font-size: 20px;
      }
    }
  }
}

/*
  • statistics ratings
  ---------- ---------- ---------- ---------- ----------
*/

.gu-raitings {
  justify-content: flex-end;

  .rated-stars-s {
    .star-ratings-css {
      font-size: 1rem;
    }
  }

  .rated-stars-m {
    .star-ratings-css {
      font-size: 2rem;
    }
  }

  .rated-stars-l {
    .star-ratings-css {
      font-size: 2.5rem;
    }
  }

  .rated-stars {
    width: 70px;
  }

  .star-ratings-css {
    position: relative;
  }

  .star-ratings-css::before {
    content: url("../style/assets/img/Icons/note_empty.svg");
    opacity: 0.7;
  }

  $ratevalues: 0, 1, 2, 3, 4, 5;
  @each $ratevalue in $ratevalues {
    [data-rate="#{$ratevalue}"]::after {
      width: calc(#{$ratevalue} * 20%);
    }
  }

  .star-ratings-css::after {
    content: url("./assets/img/Icons/note_full.svg");
    position: absolute;
    z-index: 1;
    display: block;
    left: 0;
    top: 0;
    width: attr(rating);
    overflow: hidden;
  }
}
