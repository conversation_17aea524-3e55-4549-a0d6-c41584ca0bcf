import React, { useEffect, useState } from 'react';
import { Dropdown } from 'react-bootstrap';
import signUpIcon from '../../../style/assets/img/Icons/sign-up-icon.svg';
import { getCookies } from '../../helpers/Cookies';
import CustomImage from '../images/Custom_image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import menuIcon from '../../../style/assets/img/Icons/menu_icon.svg';

export default function Menu({ t, token }) {
  const role = getCookies('role');
  const img = getCookies('imageLink');
  const [image, setImage] = useState('');

  useEffect(() => {
    if (img) {
      setImage(img);
    }
  }, [img, token]);

  const clearSessionStorage = () => {
    sessionStorage.clear();
  };

  return (
    <>
      <Dropdown>
        <Dropdown.Toggle className="menuButton">
          <img src={menuIcon} alt="icon menu" />
        </Dropdown.Toggle>
        <Dropdown.Menu className="custom-dropdown-menu custom-dropdown-menu-position">
          {token && (
            <>
              <Dropdown.Item
                href={
                  role === 'equipper'
                    ? '/equipperManagementPortal'
                    : '/renterManagementPortal'
                }
                onClick={() => clearSessionStorage()}
                className="t-base-medium c-black text-left"
              >
                <span className="img">
                  <CustomImage
                    imageUrl={image || null}
                    alt="avatar"
                    isUser
                    className="profile-icon"
                  />
                </span>
                <span>
                  {role === 'equipper'
                    ? t('Equipper_management_portal')
                    : t('Renter_management_portal')}
                </span>
              </Dropdown.Item>
            </>
          )}
          {!token && (
            <Dropdown.Item
              href="/SignUpAsLodger"
              className="t-base-medium c-black text-left black"
            >
              <span className="img">
                <img
                  src={signUpIcon}
                  alt="icon settings"
                  className="small-icon"
                />
              </span>
              <span>{t('SignUp')}</span>
            </Dropdown.Item>
          )}
          <Dropdown.Item
            href="/ourStory"
            className="t-base-medium c-black text-left black"
          >
            <span className="img">
              <FontAwesomeIcon icon="info-circle" className="small-icon" />
            </span>
            <span>{t('Our_story')}</span>
          </Dropdown.Item>
          <Dropdown.Item
            href="/Help"
            className="t-base-medium c-black text-left black"
          >
            <span className="img">
              <FontAwesomeIcon icon="question-circle" className="small-icon" />
            </span>
            <span>{t('Contact_us')}</span>
          </Dropdown.Item>
        </Dropdown.Menu>
      </Dropdown>
    </>
  );
}
