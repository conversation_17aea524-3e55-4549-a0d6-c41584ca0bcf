import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

function getWindowDimensions() {
  const { innerWidth: width, innerHeight: height } = window;
  return {
    width,
    height
  };
}
export default function useWindowDimensions({ functions, dimension }) {
  const [windowDimensions, setWindowDimensions] = useState(
    getWindowDimensions()
  );
  const location = useLocation();
  useEffect(() => {
    function handleResize() {
      setWindowDimensions(getWindowDimensions());
      if (window.innerWidth < dimension) {
        functions(true);
      }
      if (window.innerWidth > dimension) {
        functions(false);
      }
    }
    handleResize();
    if (
      !location.pathname.includes('/searchResult') &&
      windowDimensions.width > dimension
    ) {
      window.addEventListener('resize', handleResize);
    }
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  return windowDimensions;
}
