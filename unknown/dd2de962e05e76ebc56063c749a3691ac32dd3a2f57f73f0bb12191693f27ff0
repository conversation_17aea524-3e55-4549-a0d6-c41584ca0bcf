package firestoredb

import (
	"context"
	"fmt"

	"github.com/vima-inc/derental/models"
)

const leadCollectionName = "leads"

// AddLead adds a new lead.
func (f *firestoredb) AddLead(ctx context.Context, lead models.Lead) error {
	_, err := f.client.Collection(leadCollectionName).NewDoc().Set(ctx, lead)
	if err != nil {
		return fmt.Errorf("unable to set lead with id: %s error: %w", lead.ID, err)
	}

	return nil
}
