export const billingAddress = [
  {
    name: 'billing_address?.address',
    placeholder: 'Billing_address_LMP'
  },
  {
    name: 'billing_address?.zip_code',
    placeholder: 'Zip_code'
  },
  {
    name: 'billing_address?.country',
    placeholder: 'Country'
  },
  {
    name: 'billing_address?.state',
    placeholder: 'State'
  },
  {
    name: 'billing_address?.city',
    placeholder: 'City'
  }
];

export const deliveryAddress = [
  {
    name: 'delivery_address?.address',
    placeholder: 'Delivery_address_LMP'
  },
  {
    name: 'delivery_address?.zip_code',
    placeholder: 'Zip_code'
  },
  {
    name: 'delivery_address?.country',
    placeholder: 'Country'
  },
  {
    name: 'delivery_address?.state',
    placeholder: 'State'
  },
  {
    name: 'delivery_address?.city',
    placeholder: 'City'
  }
];

export const deliveryPreference = (isUS) => [
  {
    defaultChecked: true,
    id: 'pick-up',
    name: 'radio-group',
    label: isUS ? 'WillCall_name' : 'Pick_up_name'
  },
  {
    id: 'delivery',
    name: 'radio-group',
    label: 'Delivery_name'
  }
];

export const deliveryType = [
  {
    id: 'pickup',
    name: 'delivery-type',
    defaultChecked: true,
    label: 'Go'
  },
  {
    id: 'drop',
    name: 'delivery-type',
    label: 'Back'
  },
  {
    id: 'drop_and_pickup',
    name: 'delivery-type',
    label: 'Go_back'
  }
];

export const address = {
  billing_address: {
    address: '',
    state: '',
    zip_code: '',
    city: '',
    country: ''
  },
  delivery_address: {
    address: '',
    state: '',
    zip_code: '',
    city: '',
    country: ''
  }
};

export const requestDetailsAddresses = (data) => [
  {
    data: deliveryAddress,
    address: 'Delivery_address_LMP',
    name: 'delivery_address',
    value: [
      data?.delivery_address?.address,
      data?.delivery_address?.state,
      data?.delivery_address?.zip_code,
      data?.delivery_address?.city,
      data?.delivery_address?.country
    ],
    addressValue: data?.delivery_address?.address,
    show: data?.delivery_preference === 'delivery'
  },
  {
    data: billingAddress,
    address: 'Billing_address_LMP',
    name: 'billing_address',
    value: [
      data?.billing_address?.address,
      data?.billing_address?.state,
      data?.billing_address?.zip_code,
      data?.billing_address?.city,
      data?.billing_address?.country
    ],
    addressValue: data?.billing_address?.address,
    show: isEmpty(data?.billing_address)
  }
];

export const isEmpty = (obj) => {
  for (const prop in obj) {
    if (obj[prop]) return true;
  }
  return false;
};

export const showDeliveryAddress = (formik) => {
  formik.setFieldValue('isDelivery', true);
  if (formik.values.project) {
    formik.setFieldValue('delivery_address', {
      address: formik.values.project?.delivery_address?.address,
      city: {
        label: formik.values.project?.delivery_address?.city,
        value: formik.values.project?.delivery_address?.city
      },
      state: {
        label: formik.values.project?.delivery_address?.state,
        value: formik.values.project?.delivery_address?.state
      },
      zip_code: formik.values.project?.delivery_address?.zip_code,
      country: {
        label: formik.values.project?.delivery_address?.country,
        value: formik.values.project?.delivery_address?.country
      }
    });
  } else {
    formik.setFieldValue('delivery_address', {
      address: '',
      state: '',
      zip_code: '',
      city: '',
      country: ''
    });
  }
};

export const hideDeliveryAddress = (formik) => {
  formik.setFieldValue('isDelivery', false);
  formik.setFieldValue('delivery_address', {
    address: '',
    zip_code: '',
    country: '',
    state: '',
    city: ''
  });
  formik.setFieldError('delivery_address', {});
  formik.setFieldTouched('delivery_address', false);
  formik.isValid = true;
};
