
.comments-ratings {
  .comments-box {
    background: $white;
    padding: 22px 30px;
    border-radius: 30px;
    margin-bottom: 10px;
  }


  img {
    border-radius: 30px;
    max-height: 235px;
    width: 100%;
  }

  .flex-elem {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
  }

  .comments {
    padding: 10px 25px;
    border-radius: 30px;
    background: $light-grey;
  }

  .rental-grey-box {
    margin-top: 22px;
    @media(max-width: 992px) {
      padding-bottom: 0;
      margin-top: 12px;
    }

    .mobileDisplay-box {
      @media(max-width: 992px) {
        align-items: center;
        p {
          margin-right: 20px;
          margin-bottom: 0;
        }
      }
    }
  }

  .lodger-box {
    background: $white;
    padding: 20px;
    border-radius: 30px;
    @media(max-width: 992px) {
      margin-bottom: 30px;
    }

    &__top {
      text-align: center;
      border-bottom: 1px solid $check-grey;
      padding-bottom: 10px;

      img {
        width: 145px;
        height: 145px;
        border-radius: 145px;
        object-fit: cover;
      }

      h3 {
        margin-bottom: 15px;
        margin-top: 10px;
      }
    }

    &__bottom {
      margin-top: 10px;

      .address-content {
        margin-bottom: 15px;
      }

      span {
        display: block;
      }
    }
  }
}

.react-stars-wrapper {
  display: flex;
  justify-content: center;
}

.w100 {
  width: 100px;
}