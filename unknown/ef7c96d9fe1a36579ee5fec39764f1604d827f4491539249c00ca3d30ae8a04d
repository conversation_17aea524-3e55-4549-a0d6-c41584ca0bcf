import React, { useEffect, useState } from 'react';
import TooloIsLoading from '../../../shared/components/cards/Tolo_is_loading';
import { useBidzContext } from '../../../shared/context/Bidz_context';
import { useCreditCheckFormContext } from '../../../shared/context/Credit_check_form_context';
import { useEquipment } from '../../../shared/context/Equipment_context';
import { useTeamContext } from '../../../shared/context/Team_context';
import { useProjects } from '../../../shared/context/Project_context';
import ProjectDataTable from './Project_data_table';
import { userType } from '../../../shared/helpers/Team_helper';
import RenderIf from '../../../shared/components/Render_if';
import Crud from './Crud';
import { getCookies } from '../../../shared/helpers/Cookies';

export default function ProjectManagement({ t, detectLanguage }) {
  const { GetProjects, CreateProject, EditProject, DeleteProject } =
    useProjects();
  const { GetBidzOfferByRenterID } = useBidzContext();
  const { GetMyCreditCheckForm } = useCreditCheckFormContext();
  const { GetRentalInSummary } = useEquipment();
  const { GetTeamLodger } = useTeamContext();

  const memberId = sessionStorage.getItem('member_id');
  const memberOf = sessionStorage.getItem('member_of');

  const [creditCheckForms, setCreditCheckForms] = useState([]);
  const [equipments, setEquipments] = useState([]);
  const [members, setMembers] = useState([]);
  const [bidz, setBidz] = useState([]);
  const [projects, setProjects] = useState();
  const [isInitialized, setIsInitialized] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [type, setType] = useState('');
  const [show, setShow] = useState({
    showSuccess: false,
    showError: false,
    showEdit: false,
    showDelete: false,
    showCreate: false,
    showDetails: false
  });

  const selectProject = (project) => {
    setSelectedProject(project);
  };

  const refreshProjects = () => {
    setIsInitialized(false);
    selectProject(null);
  };

  const handleShow = (action) => {
    setShow({
      ...show,
      [`show${action}`]: !show[`show${action}`]
    });
  };

  const handleClose = () => {
    setShow({
      showSuccess: false,
      showEdit: false,
      showDelete: false,
      showCreate: false,
      showDetails: false,
      showError: false
    });
    refreshProjects();
  };

  useEffect(() => {
    setType(getCookies('member').type);
    if (isInitialized === false) {
      setIsInitialized(true);
      (async () => {
        setIsLoading(true);
        const response = await GetProjects();
        if (response.status === 200 && response.data) {
          setProjects(response.data);
        }
        setIsLoading(false);
      })();
    }
    return () => {
      setProjects(null);
    };
  }, [isInitialized]);

  useEffect(() => {
    if (show.showCreate || show.showEdit || show.showDetails) {
      (async () => {
        try {
          const bidzRes = await GetBidzOfferByRenterID(100, 'accepted');
          if (bidzRes.status === 200 && bidzRes.data) {
            setBidz(
              bidzRes.data?.map((bidz) => ({
                value: bidz.id,
                label:
                  bidz[`equipment_name${detectLanguage === 'fr' ? '_fr' : ''}`]
              }))
            );
          }

          const ccfRes = await GetMyCreditCheckForm();
          if (ccfRes.status === 200 && ccfRes.data) {
            setCreditCheckForms(
              ccfRes.data?.map((data) => ({
                value: data,
                label: data?.name
              }))
            );
          }

          if (memberId) {
            const {
              status,
              data: { admins, power_collaborators, collaborators }
            } = await GetTeamLodger();

            if (status === 200) {
              const team = [
                ...(admins?.filter(
                  (member) =>
                    member.membership_status !== 'owner' &&
                    member.membership_status !== 'pending'
                ) || []),
                ...(power_collaborators?.filter(
                  (member) => member.membership_status !== 'pending'
                ) || []),
                ...(collaborators?.filter(
                  (member) => member.membership_status !== 'pending'
                ) || [])
              ];
              setType(userType(team));
              setMembers(
                team
                  .filter((member) => member.email !== getCookies('email'))
                  ?.map((member) => ({
                    value: member.id,
                    label: `${member.email} (${member.type})`
                  }))
              );
            }
          }

          const res = await GetRentalInSummary(100);
          if (res.status === 200 && res.data) {
            setEquipments(
              res.data?.map((equipment) => ({
                value: equipment.id,
                label:
                  equipment[
                    `equipment_name${detectLanguage === 'fr' ? '_fr' : ''}`
                  ]
              }))
            );
          }
        } catch (error) {
          console.error('Error in useEffect:', error);
        }
      })();
    }
  }, [show, memberId, memberOf, detectLanguage]);

  if (isLoading) {
    return <TooloIsLoading />;
  }

  return (
    <div className="panel" id="p5">
      <div className="white-bg">
        <RenderIf
          condition={
            show.showCreate ||
            show.showEdit ||
            show.showDelete ||
            show.showDetails
          }
        >
          <Crud
            t={t}
            handleShow={handleShow}
            show={show}
            setShow={setShow}
            selectedProject={selectedProject}
            useCreateProject={CreateProject}
            creditCheckForms={creditCheckForms}
            detectedLanguage={detectLanguage}
            equipments={equipments}
            members={members}
            bidz={bidz}
            useEditProject={EditProject}
            useDeleteProject={DeleteProject}
            handleClose={handleClose}
            type={type}
          />
        </RenderIf>
        <ProjectDataTable
          handleShowDetails={() => {
            setShow({
              ...show,
              showDetails: true,
              showEdit: true
            });
          }}
          handleShowCreateProjectModal={() => {
            handleShow('Create');
          }}
          handleShowEditProjectModal={() => {
            handleShow('Edit');
          }}
          handleShowDeleteProjectModal={() => {
            handleShow('Delete');
          }}
          selectedProject={selectedProject}
          selectProject={selectProject}
          projects={projects}
          type={type}
          t={t}
        />
      </div>
    </div>
  );
}
