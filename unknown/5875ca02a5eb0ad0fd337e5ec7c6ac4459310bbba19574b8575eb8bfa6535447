@media (min-width: 992px) {
  .bg-equipper {
    max-width: 100%;
    height: 800px;
    position: relative;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
    padding-bottom: 6%;
  }
  .equipper-content {
    position: relative;

    .widget-blanc {
      &:nth-child(2) {
        float: right;
      }
    }

    &:nth-child(1) {
      top: 95px;
    }

    &:nth-child(2) {
      top: 145px;
    }

    &:nth-child(3) {
      top: 100px;

      .widget-blanc {
        margin-bottom: 60px;

        &:nth-child(1) {
          float: right;
        }

        &:nth-child(2) {
          float: left;
        }
      }
    }
  }

  .fade-in1 {
    animation: fadeIn 5s;
    opacity: 1;
  }

  .fade-in2 {
    animation: fadeIn 6s;
    animation-delay: 3s;
    opacity: 1;
  }

  .fade-in3 {
    animation: fadeIn 7s;
    animation-delay: 5s;
    opacity: 1;
  }

  .fade-in4 {
    animation: fadeIn 8s;
    animation-delay: 7s;
    opacity: 1;
  }

  .fade-in5 {
    animation: fadeIn 9s;
    animation-delay: 9s;
    opacity: 1;
  }

  .fade-in6 {
    animation: fadeIn 10;
    animation-delay: 11s;
    opacity: 1;
  }

  .fade-in7 {
    animation: fadeIn 11s;
    animation-delay: 13s;
    opacity: 1;
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}

@media (max-width: 992px) {
  .bg-equipper {
    background-image: none !important;
  }
}

.padding-equipper {
  @media (min-width: 992px) {
    padding: 3% 0 0 0;
  }

  h1 {
    padding-bottom: 20px;
  }

  p {
    font-size: 20px;
    line-height: normal;
    @media (max-width: 992px) {
      font-size: 16px;
    }
  }
}

.subTitle {
  font-weight: normal;
  padding-bottom: 4%;
}

.widget-blanc {
  background-color: $white;
  border-radius: 20px;
  width: 100%;
  margin-bottom: 20px;
  padding: 10px 25px;
  font-size: small;
  @media (min-width: 992px) {
    position: relative;
    width: fit-content;
    margin-bottom: 80px;
  }
  @media (min-width: 1400px) {
    margin-bottom: 100px;
  }

  .anim-text {
    font-size: 14px;
    line-height: normal;
    @media (min-width: 1400px) {
      font-size: 16px;
    }
  }

  &.sec1 {
    @media (min-width: 992px) {
      position: relative;
      top: 40px;
    }
    @media (min-width: 1400px) {
      left: calc(100% - 450px);
    }
  }
}

@media (min-width: 1400px) {
  .equipper-content {
    position: relative;

    .widget-blanc {
      &:nth-child(2) {
        float: right;
      }
    }

    &:nth-child(1) {
      top: 65px;
    }

    &:nth-child(2) {
      top: 120px;
    }

    &:nth-child(3) {
      top: 100px;

      .widget-blanc {
        margin-bottom: 60px;

        &:nth-child(1) {
          float: right;
        }

        &:nth-child(2) {
          float: left;
        }
      }
    }
  }
}

.become-equipper {
  &__head {
    p {
      margin-top: 0;
    }
  }
}

.become-equipper-button-icon {
  width: 70px;
}

.btn-become-equipper {
  padding: 15px 30px;
  margin-top: 20px;
  @media (max-width: 992px) {
    display: block;
    margin-left: auto;
    margin-right: auto;
    margin-top: 0;
  }
}

.label-become-equipper {
  background-color: rgba(180, 179, 179, 0.349);
  border-radius: 20px;
  padding: 10px 25px;
  font-size: 16px;
  font-weight: bolder;
  margin-top: 0;
  margin-bottom: 30px;
  border: none;
  display: block;
  @media (min-width: 992px) {
    display: inline-block;
    font-size: 18px;
  }
}

.inner-wrap {
  position: relative;
  text-align: center;
  margin-top: 30px;
  @media (min-width: 992px) {
    margin-top: 180px;
    margin-left: 30%;
  }
}

.content-become-equipper {
  @media (max-width: 992px) {
    margin-bottom: 30px;
    margin-top: 30px;
  }
}
