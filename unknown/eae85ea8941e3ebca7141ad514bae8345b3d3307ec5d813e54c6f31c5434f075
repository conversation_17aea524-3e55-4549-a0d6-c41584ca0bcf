.no-padding-row {
  padding: 0;
}

.divider-right {
  padding-left: 20% !important;
  margin-top: 5% !important;
}

.divider-left {
  padding-right: 20% !important;
  margin-top: 5% !important;
}

.signup-title {
  padding-bottom: 45px;
}

.register-container {
  margin-top: 16px;
  @media (min-width: 992px) {
    margin-top: 38px;
  }
}

.signup-title h1 {
  padding-bottom: 15px;
}

.typeofuser-container {
  background-color: white;
  border-radius: 20px;
  padding: 0;
  @media (min-width: 992px) {
    margin-bottom: 18px;
  }
}

.typeofuser-container > h6 {
  padding: 3%;
  text-align: center;
}

.btn-container {
  display: inline-block;
  margin: 2% 0;
  margin-right: 1%;
  padding: 0 1%;
  font-size: 0;
  border-radius: 20px;
  border: 1px solid #000;
  width: 74% !important;
}

.are-you-title {
  width: 100%;
}

@media (max-width: 1000px) {
  .are-you-title {
    width: 100%;
    text-align: center;
    padding: 0;
  }
}

label.label-signup {
  position: relative;
  cursor: pointer;
  font-size: 12px;
  display: inline-block;
  text-transform: uppercase;
  letter-spacing: 5px;
  width: 50%;
  padding: 3% 3%;
  transition: all 0.3s ease;
}

input#professional + label.label-signup:before {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 100%;
  z-index: -1;
  background-color: #000;
  transition: all 0.3s ease;
}

input#professional:checked + label.label-signup:before {
  left: 0;
}

input#professional:checked + label.label-signup,
input#individual:checked + label.label-signup {
  color: #000;
}

.Input-form {
  display: flex;
  border-radius: 28px;
  width: 98%;
  padding: 1.5%;
  padding-left: 2%;
  border: 1px solid rgb(187, 183, 183);
  height: 50px;
}

.btn-sighup-content {
  margin-top: 23px;

  @media (max-width: 786px) {
    text-align: center;
  }

  .button-signup {
    margin-top: 5px;
    min-width: 100%;
    @media (min-width: 572px) {
      min-width: 375px;
    }
  }
}

.check-signup {
  margin-top: 30px;
  @media (min-width: 992px) {
    margin-top: 65px;
  }
}

.button-add-equipment {
  margin-top: 20px;

  &.round-button.grey {
    &:disabled {
      color: #00000029;
    }

    &:hover {
      background: #d1d1d1;
      color: #ffffff;
      border: 1px solid #d1d1d1;

      &:disabled {
        color: #00000029;
      }
    }
  }
}

.InputError {
  border-radius: 28px;
  margin-bottom: 3%;
  width: 98%;
  padding: 1.5%;
  padding-left: 2%;
  border: 1px solid rgb(235, 6, 6);
  height: 50px;
}

.register-form {
  background-color: white;
  border-radius: 28px;
}

.credit-content {
  margin-bottom: 1%;
  background-color: white;
  border-radius: 28px;
  height: fit-content;
  padding: 30px 40px;
  margin-top: 20px;
  @media (min-width: 992px) {
    margin-top: 0;
  }
}

.credit-title {
  text-align: center;
  margin: 35px 30px;
}

.credit-check-container {
  border: 1px solid $light-gray;
  border-radius: 12px;
  height: 250px;
  padding: 10px;
}

.credit-check-title {
  text-align: center;
  margin-top: 40px;
}

.btn-check-form {
  margin-top: 35px;
}

.credit-button {
  justify-content: center;
  width: 100%;
}

.or-check {
  text-align: center;
  font-weight: bold;
  margin-top: 25%;
  color: gray;
}

.Row-pad {
  padding: 25px 0;
  flex-wrap: nowrap;
  @media (min-width: 992px) {
    padding: 20px 50px;
  }

  &__left {
    width: auto;
    padding-right: 0 !important;
  }

  &__right {
    margin: 0 auto;
    @media (min-width: 572px) {
      width: 83%;
    }
  }
}

.switch-button {
  -webkit-box-shadow: 1px 1px 1px 1px rgba(100, 100, 100, 0.9);
  box-shadow: 1px 1px 1px 1px rgba(211, 208, 208, 0.9);
  background: rgba(189, 189, 189, 0.354);
  border-radius: 30px;
  overflow: hidden;
  width: 40% !important;
  text-align: start;
  font-weight: bold;
  font-size: 18px;
  letter-spacing: 1px;
  color: rgb(0, 0, 0);
  position: relative;
  padding-right: 20% !important;
}

.switcher {
  padding-bottom: 2%;
}

@media (max-width: 1000px) {
  .switcher {
    text-align: center;
  }
}

.PhoneInputInput {
  border: none;
}

.PhoneInputInput:active,
.PhoneInputInput:focus {
  border: none;
  box-shadow: none;
}

.PhoneInputCountryIcon--border {
  border: none;
  background-color: transparent !important;
  box-shadow: none !important;

  svg {
    position: relative;
    top: -6px;
  }
}

.container-email {
  position: relative;
  margin-bottom: 10px;
  @media (min-width: 992px) {
    margin-bottom: 25px;
  }

  &:last-child {
    margin-bottom: 0;
  }

  &.form-group {
    .form-control {
      width: 100%;
    }
  }

  .css-1okebmr-indicatorSeparator {
    display: none;
  }
}

.error-icon {
  position: absolute;
  top: 22px;
  right: 38px;
  color: #ff0000;
}

.success-icon {
  position: absolute;
  top: 22px;
  right: 38px;
  color: #00ff00;
  visibility: hidden;
}

.Input-form.success {
  border: 1px solid #00ff00;

  .success-icon {
    visibility: visible;
  }
}

input.Input-form,
input.Input-form:focus {
  background: #f0f0ff;
}

.error-message {
  padding-left: 25px;
  display: flex;
  align-items: center;
  line-height: normal;
  margin-top: 5px;
  position: relative;
  color: $red;
  font-size: 12px;

  &:before {
    content: url('../style/assets/img/Icons/Warning.svg');
    position: absolute;
    left: 0;
  }
}

.login-error-message {
  text-align: center;
}

.error-message-container {
  position: absolute;
  width: 500px;
  z-index: 1000;
}

.error-message-password {
  background-color: #000;
  color: #f0f0ff;
  padding: 1.5% 1.5%;
  margin-top: 0%;
  font-size: small;
  font-weight: 500;
  margin-right: 45%;
  width: 55%;
  border-start-start-radius: 20px;
  border-start-end-radius: 20px;
  z-index: 100;
}

.conditions-status {
  background-color: #fff;
  color: #3b3b3b;
  padding: 1.5% 1.5%;
  font-size: small;
  font-weight: 500;
  width: 55%;
  border-end-end-radius: 20px;
  border-end-start-radius: 20px;
  z-index: 100;
}

.zip-description {
  color: gray;
  font-size: small;
  margin-left: 6%;
}

.error-alert {
  border-radius: 20px;
}

.condition {
  display: flex;
}

.check-icon {
  color: #05a805;
  margin-top: 1%;
  margin-right: 2%;
}

.margin-top-100 {
  margin-top: 100px;
}

.margin-top-10 {
  margin-top: 10px;
}

.register-form {
  .label-check {
    input {
      padding: 0;
      height: initial;
      width: initial;
      margin-bottom: 0;
      display: none;
      cursor: pointer;
    }

    label {
      position: relative;
      cursor: pointer;

      &:before {
        content: '';
        -webkit-appearance: none;
        background-color: transparent;
        border: 2px solid $check-grey;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05),
          inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
        padding: 6px;
        display: inline-block;
        position: relative;
        vertical-align: middle;
        cursor: pointer;
        margin-right: 5px;
        border-radius: 5px;
        width: 20px;
        height: 20px;
      }
    }
  }

  .label-check input:checked + label:after {
    content: '';
    display: block;
    position: absolute;
    top: 4px;
    left: 7px;
    width: 6px;
    height: 13px;
    border: solid $white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }

  .label-check input:checked + label:before {
    background: $yellow;
    border-color: $yellow;
  }
}

.siac {
  list-style-type: none;
}

.css-junnbe-control {
  border-radius: 5px !important;

  .css-14el2xx-placeholder,
  .css-ujecln-Input2 {
    font-size: 16px;
    color: $fake-black;
  }
}

.MuiGrid-item {
  padding-top: 10px !important;

  &:first-child {
    padding-top: 0 !important;
  }

  .css-1okebmr-indicatorSeparator {
    display: none;
  }
}

// .css-11u53oe-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input {
//   padding: 0 !important;
//   font-family: $primary-font !important;
// }

// .css-2m9kme-MuiFormControl-root {
//   margin: 0 !important;
// }

.css-12wnr2w-MuiButtonBase-root-MuiCheckbox-root {
  border-radius: 5px !important;
  color: #e1e2eb !important;
  padding: 0 !important;
  margin-right: 8px !important;

  .css-i4bv87-MuiSvgIcon-root {
    width: 35px !important;
    height: 35px !important;
  }

  &:hover {
    background: none !important;
    color: $yellow !important;
  }
}

.css-kk1bwy-MuiButtonBase-root-MuiMenuItem-root.Mui-selected {
  background: #f3f6f9 !important;

  &:hover {
    background: #f3f6f9 !important;
  }
}

.css-12wnr2w-MuiButtonBase-root-MuiCheckbox-root.Mui-checked {
  color: $yellow !important;
}

.css-glpkwh-control {
  min-height: 48px !important;
}

// .css-9ddj71-MuiInputBase-root-MuiOutlinedInput-root {
//   color: hsl(0, 0%, 80%) !important;

//   path {
//     d: path(
//       'M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z'
//     ) !important;
//   }
//   .css-11u53oe-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input {
//     color: hsl(0, 0%, 80%) !important;
//     path {
//       d: path('M19.25 18.75L5.75 5.25 M5.75 18.75L19.25 5.25') !important;
//     }
//     &:hover {
//       border-color: hsl(0, 0%, 70%) !important;
//     }
//   }
// }

.css-w66kx-MuiChip-root {
  font-family: $primary-font !important;
  background-color: $multiselect_grey !important;
  margin-right: 8px !important;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 20px !important;
  color: $blue-burger !important;
}

.form-group {
  .MuiGrid-container {
    width: 100% !important;
  }

  .pl-0 {
    padding-left: 0 !important;
  }

  .pad-r {
    padding-right: 10px !important;
  }
}

.ml {
  margin-left: 34px !important;
}

.css-6od3lo-MuiChip-label {
  padding: 0px 5px !important;
  font-family: $primary-font !important;
  font-weight: normal !important;
  font-size: 12px !important;
  color: $fake-black !important;
}

.css-1nsayvs-MuiInputBase-root-MuiInput-root:after {
  border-bottom: 2px solid #eca869 !important;
}

.css-1dtnjt5 {
  display: flex;
  flex-wrap: nowrap !important;
}
