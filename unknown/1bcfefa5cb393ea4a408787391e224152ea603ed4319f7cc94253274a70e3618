import React, { useEffect, useState } from 'react';
import CustomImage from '../images/Custom_image';
import { getSessionStorage } from '../../helpers/Session_storage_helper';

export const EquipmentCardSearchResults = ({
  img,
  t,
  detectLanguage,
  nameEn,
  nameFr
}) => {
  const [name, setName] = useState('');
  const [equipmentImg, setEquipmentImg] = useState('');
  const requestedName = getSessionStorage('nameEn');
  useEffect(() => {
    setName(requestedName || (detectLanguage === 'fr' ? nameFr : nameEn));
    setEquipmentImg(img);
  }, [requestedName, nameEn, img]);

  return (
    <div className="result-box with-border">
      <div className="full-container">
        <div className="row">
          <div className="col-8">
            <div className="result-box__top">
              <div className="result-box__top-left">
                <span className="t-subheading-2 c-fake-black bold">
                  Equipment
                </span>
                <span className="d-block t-body-large equipper-box-text c-neutrals-gray">
                  {name}
                </span>
              </div>
            </div>
          </div>
          <div className="padding-l-0  col-4">
            <div className="result-box__image">
              <CustomImage imageUrl={equipmentImg} alt={t('Cant_load_image')} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
