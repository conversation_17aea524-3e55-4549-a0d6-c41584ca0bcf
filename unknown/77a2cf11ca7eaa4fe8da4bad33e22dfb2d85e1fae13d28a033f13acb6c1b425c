.settings {
  h1 {
    margin-left: 10px;
  }

  .white-bg {
    @media (max-width: 992px) {
      margin-bottom: 20px;
    }
  }

  .form-group {
    .form-control {
      width: 100%;

      &.no-marge {
        margin-bottom: 5px;
      }
    }

    &.bigHeight {
      position: relative;

      .form-control {
        height: 85px;
        padding-left: 65px;
        font-size: 14px;
        @media (min-width: 992px) {
          padding-left: 140px;
          font-size: 16px;
        }
      }

      span {
        position: absolute;
        top: 13px;
        left: 65px;
        @media (min-width: 992px) {
          left: 140px;
        }
      }

      img {
        position: absolute;
        top: 20px;
        left: 20px;
        width: 30px;
        @media (min-width: 992px) {
          width: auto;
          top: 35px;
          left: 30px;
        }
      }

      .default-visa {
        width: 76px;
        min-width: 76px;
        position: absolute;
        right: 12px;
        top: 35px;
        font-size: 12px;
        padding: 5px 10px;
        @media (min-width: 992px) {
          width: 138px;
          min-width: 138px;
          font-size: 16px;
          padding: 10px 15px;
          right: 100px;
          top: 17px;
        }
      }

      .points-btn {
        color: $check-grey;
        font-size: 40px;
        position: absolute;
        right: 20px;
        top: -5px;
        background: none;
        @media (min-width: 992px) {
          right: 30px;
          top: 12px;
          font-size: 60px;
        }
      }
    }

    .small-text {
      padding-left: 10px;
    }
  }

  .border-drop {
    width: 100%;
    min-width: 100% !important;
    border: 1px solid $check-grey;
    box-shadow: none;
    text-align: left;
    margin-bottom: 20px;
    height: 36px;
    color: $near-grey;
    @media (min-width: 992px) {
      height: 50px;
    }

    &::after {
      right: 12px;
      position: absolute;
      top: 16px;
      @media (min-width: 992px) {
        top: 23px;
      }
    }
  }

  .panels {
    .panel {
      padding: 0;

      h2 {
        margin-bottom: 25px;
        padding-top: 15px;
        border-top: 1px solid $check-grey;
        @media (min-width: 992px) {
          margin-top: 25px;
        }

        &.no-border {
          border: 0;
        }
      }
    }
  }

  .close-account {
    margin-bottom: 25px;
    margin-top: 25px;
    @media (min-width: 992px) {
      margin-bottom: 60px;
      margin-top: 35px;
    }

    span {
      display: block;
      margin-top: 10px;
      @media (min-width: 992px) {
        margin-left: 35px;
        display: inline-block;
        margin-top: 0;
      }
    }
  }

  .round-button {
    min-width: 220px;
  }

  .add-card {
    margin-top: 15px;
    max-width: 230px;
    @media (min-width: 992px) {
      margin-top: 35px;
    }
  }

  .tabulation {
    @media (max-width: 992px) {
      padding: 0;
    }

    .panels {
      margin-top: 35px;
    }

    &.settings-tabulation {
      .tab-label {
        width: auto;
      }
    }
  }
}
