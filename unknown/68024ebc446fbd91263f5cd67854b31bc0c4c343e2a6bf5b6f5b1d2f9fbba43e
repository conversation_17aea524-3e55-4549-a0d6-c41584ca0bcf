import CircularProgress from '@mui/material/CircularProgress';
import React from 'react';
import { useTranslation } from 'react-i18next';

export default function LoadingModal(props) {
  const { t } = useTranslation();
  if (!props.show) return null;
  return (
    <div className="modal">
      <div className="modal-content no-title-margeTop ">
        <div className="center t-header-30 bold">
          <h3>{t('Please_wait')}</h3>
        </div>
        <div className="d-flex justify-content-center">
          <CircularProgress
            sx={{
              color: '#ECA869'
            }}
          />
        </div>
      </div>
    </div>
  );
}
