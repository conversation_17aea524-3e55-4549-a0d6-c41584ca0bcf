import React from 'react';
import { connectCurrentRefinements } from 'react-instantsearch-dom';
import { firstLetterUpperCase } from '../../shared/helpers/String_helps';

const CurrentRefinements = ({ items, t }) => {
  const filterResult = items?.map((item) => {
    if (
      ['available_from', 'status', 'coverage_area', 'name', 'name_fr','description','description_fr'].includes(
        item.attribute
      )
    ) {
      return null;
    }

    return (
      <p className="t-body-small c-blue equipment_specification margin-left-10">
        <strong className="t-body-small c-blue-grey bold">
          {t(firstLetterUpperCase(item.attribute?.replaceAll('.', '_')))}
        </strong>{' '}
        <strong className="t-body-small c-blue-grey bold">{item?.currentRefinement||[]?.map((item) => item).join(', ')}</strong>
        {/* <span className="margin-left-10"><img
            src={cross} alt="cross"/></span> */}
      </p>
    );
  });

  return (
    <div className="current-refinement-container result-left-bottom">

      {filterResult}
    </div>
  );
};

export const CustomCurrentRefinements =
  connectCurrentRefinements(CurrentRefinements);
