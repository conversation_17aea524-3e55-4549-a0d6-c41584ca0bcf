import React from 'react';
import RenderIf from '../Render_if';
import Modal from './Modal';
import NoResult from '../../../style/assets/img/no_results.svg';

export default function UnderConstructionModal({ show, onClose, t }) {
  return (
    <RenderIf condition={show}>
      <Modal
        onClose={onClose}
        noScrollModal
        className="under-construction-modal text-center t-header-h5 c-grey-titles"
        t={t}
      >
        <img src={NoResult} alt="NoResult" />
        <h2 className="t-header-h6 bold bigger">
          {t('Under_construction_modal_title')}
        </h2>
        <p className="t-body-regular c-medium-grey">
          {t('Under_construction_modal_text')}
        </p>
        <button className="round-button yellow c-black" onClick={onClose}>
          {t('Under_construction_modal_button_text')}
        </button>
      </Modal>
    </RenderIf>
  );
}
