const EMAIL_NOT_FOUND = 'unable to signin the user: EMAIL_NOT_FOUND';
const INVALID_PASSWORD = 'unable to signin the user: INVALID_PASSWORD';

export { EMAIL_NOT_FOUND, INVALID_PASSWORD };

export function getError<PERSON>ey(message, prefix) {
    const msg = message.substring(
        0,
        message.indexOf(',') > 0 ? message.indexOf(',') : (message.indexOf(':') > 0 ? message.indexOf(':') : message.length)
    )
        .trim()
        .replaceAll(/ /g, '_');

    return prefix + msg;
}

