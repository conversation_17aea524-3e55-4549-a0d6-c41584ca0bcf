import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useTranslation } from 'react-i18next';
import BgHelp from '../../style/assets/img/help/bg_help.png';
import TooloHelpLeft from '../../style/assets/img/help/toolo_help_left.png';
import <PERSON>laHelpRight from '../../style/assets/img/help/toola_help_right.png';
import Equip from '../../style/assets/img/help/equip.png';
import EquipGrey from '../../style/assets/img/help/equip_grey.png';
import TooloPoints from '../../style/assets/img/help/toolo_points.png';
import TooloPointsMob from '../../style/assets/img/help/toolo_points_mob.png';

export default function Help() {
  const { t } = useTranslation();
  return (
    <div className="help">
      <div className="container">
        <div className="help__topSection">
          <h1 className="t-title-large c-primary-color text-center bold">
            {t('Page_help_title1')}
          </h1>
          <div className="form-group text-center">
            <input
              type="search"
              className="form-control"
              placeholder={t('Page_help_search_placeholder')}
            />
            <button className="button-searchHelp">
              <FontAwesomeIcon icon="search" />
            </button>
          </div>
        </div>
      </div>
      <div
        className="help__messageBlock"
        style={{
          backgroundImage: `url(${BgHelp})`,
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center'
        }}
      >
        <div className="equip">
          <img src={Equip} alt="notif zzz" />
        </div>
        <div className="equipGrey">
          <img src={EquipGrey} alt="notif zzz" />
        </div>
        <div className="container-message">
          <div className="container">
            <div className="row">
              <div className="col-lg-5">
                <div className="left-side">
                  <div className="left-side--image">
                    <img
                      src={TooloHelpLeft}
                      alt="notif zzz"
                    />
                  </div>
                  <p className="t-title-med-lage bold">
                    {t('Page_help_toolo_bull1')}
                  </p>
                </div>
              </div>
              <div className="col-lg-7">
                <div className="right-side">
                  <div className="right-side--image">
                    <img
                      src={ToolaHelpRight}
                      alt="notif zzz"
                    />
                  </div>
                  <p className="t-title-med-lage bold">
                    {t('Page_help_toolo_bull2')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="container">
        <div className="help__popular-questions">
          <div className="row align-items-center">
            <div className="col-lg-8">
              <h2 className="t-title-large c-primary-color bold">
                {t('Page_help_title2')}
              </h2>
            </div>
            <div className="col-lg-4">
              <p className="t-header-h6 c-near-grey bold text-uppercase">
                {t('Get_Answers')}
              </p>
            </div>
          </div>
          <div className="row questions">
            <div className="col-lg-4">
              <p className="t-title-med-lage bold">
                {t('Page_help_question1')}
              </p>
              <p className="t-title-med-lage bold">
                {t('Page_help_question2')}
              </p>
            </div>
            <div className="col-lg-4">
              <img
                src={TooloPoints}
                alt="toolo"
                className="d-lg-block d-none"
              />
              <img
                src={TooloPointsMob}
                alt="toolo"
                className="d-lg-none d-block mobile-image"
              />
            </div>
            <div className="col-lg-4">
              <p className="t-title-med-lage bold">
                {t('Page_help_question3')}
              </p>
              <p className="t-title-med-lage bold">
                {t('Page_help_question4')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
