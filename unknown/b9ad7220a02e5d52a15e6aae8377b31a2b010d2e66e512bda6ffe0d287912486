import React from 'react';
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import PropTypes from 'prop-types';
import flags from 'react-phone-number-input/flags';
import { useTranslation } from 'react-i18next';

PhoneInputField.propTypes = {
  className: PropTypes.string,
  form: PropTypes.any.isRequired,
  field: PropTypes.any.isRequired,
  onChange: PropTypes.func,
  disabled: PropTypes.bool
};
PhoneInputField.defaultProps = {
  className: '',
  onChange: null,
  name: '',
  value: '',
  setFieldValue: null,
  placeholder: 'Phone_number',
  disabled: false
};
export function PhoneInputField({
  field: { name, value },
  form: { setFieldValue },
  onChange,
  className,
  disabled,
  placeholder
}) {
  const { t } = useTranslation();

  const onValueChange = (phoneNumber) => {
    setFieldValue(name, phoneNumber);
    if (onChange) {
      onChange(phoneNumber);
    }
  };

  return (
    <PhoneInput
      placeholder={t(placeholder)}
      name={name}
      value={value}
      onChange={onValueChange}
      className={className}
      disabled={disabled}
      flags={flags}
    />
  );
}
