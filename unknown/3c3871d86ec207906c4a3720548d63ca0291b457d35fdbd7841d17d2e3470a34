import { Button, Container } from 'react-floating-action-button';
import React from 'react';

export default function FloatingActionButton(props) {
  return (
    <Container>
      <Button
        tooltip={props.placeHolder}
        icon="fas fa-plus"
        styles={{
          backgroundColor: props.backgroundColor,
          color: props.color,
          marginBottom: props.marginBottom
        }}
        onClick={props.onClick}
        key={props.key}
        rotate
      >
        {props.icon}
      </Button>
    </Container>
  );
}
