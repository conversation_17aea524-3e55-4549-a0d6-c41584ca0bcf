import React, { useEffect, useState } from 'react';
import BreadCrumb from '../../shared/components/Bread_crumb';
import bidzImage from '../../style/assets/img/company_spotlight/Marketing-cuate.png';
import { useParams } from 'react-router-dom';
import ToloIsLoading from '../../shared/components/cards/Tolo_is_loading';
import BidzModal from '../../shared/components/modals/Bidz_modal';
import RenderIf from '../../shared/components/Render_if';
import { useLodger } from '../../shared/context/Lodger_context';
import { useInventoryContext } from '../../shared/context/Tooler_bidz_inventory_management_context';
import { getCookies } from '../../shared/helpers/Cookies';
import SignInModal from '../../shared/components/modals/Sign_in_modal';
import MultiSelect from '../../shared/components/multi_select/Multi_select';
import FormikForm from '../../shared/components/forms/Formik';
import Input from '../../shared/components/forms/Input';
import { FieldArray } from 'formik';
import useResponsive from '../../shared/helpers/Responsive';
import { bidzAttributes } from '../../shared/helpers/Data_helper';
import { firstLetterUpperCase } from '../../shared/helpers/String_helps';
import InputForm from '../../shared/components/inputs/Input_form';
import { handleKeyDown } from '../../shared/helpers/Textarea';

const BidzForm = ({
  formik,
  t,
  setBidzRequestDetails,
  bidzRequestDetails,
  token,
  togglePopup,
  setShowSignIn
}) => {
  const role = getCookies('role');
  const options = bidzAttributes.map((item) => ({
    label: t(firstLetterUpperCase(item)).replaceAll(':', ''),
    value: item
  }));

  return (
    <>
      <FieldArray
        name="specifications"
        render={(arrayHelpers) => (
          <div>
            {formik.values?.specifications?.map((_, index) => (
              <div key={index}>
                <div className="row mt-4 d-flex align-items-center">
                  <div className="col-lg-6">
                    <div className="form-group">
                      <label className="label-input t-body-regular c-fake-black">
                        {t('Specification')}
                      </label>
                      <MultiSelect
                        options={
                          options?.filter(
                            (item) =>
                              !formik.values?.specifications
                                ?.map((item) => item.specification)
                                .includes(item.value)
                          ) || []
                        }
                        t={t}
                        isGeoLocation
                        name={`specifications.${index}.specification`}
                        handleChange={(item) => {
                          formik.setFieldValue(
                            `specifications[${index}]specification`,
                            item?.value
                          );
                        }}
                        defaultValue={
                          formik.values?.specifications[index]?.specification
                        }
                        placeholder={t('Choose_a_specification')}
                      />
                    </div>
                  </div>
                  <div className={index > 0 ? 'col-modified' : 'col-lg-6'}>
                    <div className="form-group">
                      <Input
                        type="text"
                        label={t('Value')}
                        name={`specifications.${index}.value`}
                        className="form-control"
                        placeholder={t('Value')}
                      />
                    </div>
                    {index > 0 && (
                      <button
                        type="button"
                        className="transparent position-absolute w-auto"
                        onClick={() => arrayHelpers.remove(index)}
                      >
                        <svg
                          width="25"
                          height="24"
                          viewBox="0 0 25 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M19.25 5.25L5.75 18.75"
                            stroke="#333333"
                            strokeWidth="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <path
                            d="M19.25 18.75L5.75 5.25"
                            stroke="#333333"
                            strokeWidth="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}

            <div className="button_content">
              <button
                className="c-yellow"
                type="button"
                onClick={() => {
                  arrayHelpers.push({
                    specification: '',
                    value: ''
                  });
                }}
              >
                {t('Add_specification')}
              </button>
            </div>
          </div>
        )}
      />
      <div className="specification_equipment__content">
        <h3 className="t-body-large c-fake-black mb-2 bold">
          {t('Type_of_propulsion')}
        </h3>
        <p className="t-body-small c-neutrals-gray">
          {t('Specify_the_propulsion_method_used_by_your_equipment')}
        </p>
        <div className="row mt-4">
          <div className="col-lg-6">
            <div className="form-group">
              <label className="label-input t-body-regular c-fake-black">
                {t('Type_of_propulsion')}
              </label>
              <MultiSelect
                options={[
                  { label: 'Electric', value: 'Electric' },
                  { label: 'Diesel', value: 'Diesel' },
                  { label: 'Propane', value: 'Propane' },
                  { label: 'Hybrid', value: 'Hybrid' },
                  { label: 'Manual', value: 'Manual' },
                  { label: 'Air', value: 'Air' },
                  { label: 'Hydraulic', value: 'Hydraulic' },
                  { label: 'Natural_gas', value: 'Natural gas' },
                  { label: 'Gas', value: 'Gas' },
                  { label: 'Battery', value: 'Battery' }
                ]}
                handleChange={(item) => {
                  formik.setFieldValue('type_of_propulsion', item?.value);
                }}
                t={t}
                name="type_of_propulsion"
                placeholder={t('Choose_a_propulsion')}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="specification_equipment__content">
        <h3 className="t-body-large c-fake-black mb-2 bold">
          {t('Equipment_purpose_use')}
        </h3>
        <p className="t-body-small c-neutrals-gray">
          {t('This_will_help_us_find_the_right_match_for_your_needs')}
        </p>
        <div className="row mt-4">
          <div className="col-lg-12">
            <div className="form-group">
              <InputForm
                className="form-control "
                as="textarea"
                name="equipment_utility"
                isFormik
                placeholder="Optional"
                onKeyDown={(e) => {
                  handleKeyDown(e);
                }}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="text-center">
        <button
          className="round-button yellow c-black"
          type="button"
          disabled={role === 'equipper'}
          onClick={() => {
            setBidzRequestDetails({
              ...bidzRequestDetails,
              ...formik.values,
              ...formik.values.specifications.reduce((result, item) => {
                result[item.specification] = item.value;
                return result;
              }, {})
            });

            token ? togglePopup() : setShowSignIn(true);
          }}
        >
          {t('Request_a_quote')}
        </button>
      </div>
    </>
  );
};

export default function BidzCompanySpotlight({
  t,
  setShowFPModal,
  showFPModal,
  signIn
}) {
  const { getEquipmentById } = useInventoryContext();
  const { GetLodgerPersonalInfo } = useLodger();
  const { id, city } = useParams();
  const token = getCookies('token');
  const isLodger = getCookies('role') === 'lodger';

  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [bidzRequestDetails, setBidzRequestDetails] = useState({});
  const [bidzEquipment, setBidzEquipment] = useState({});
  const [showSignIn, setShowSignIn] = useState(false);
  const [viewDetails, setViewDetails] = useState(false);
  const { isMobile } = useResponsive();

  const togglePopup = () => {
    setIsOpen(!isOpen);
  };

  const toggleViewDetails = () => {
    setViewDetails(!viewDetails);
  };

  const show = () => {
    if (isMobile) {
      return viewDetails;
    }
    return true;
  };

  useEffect(() => {
    setIsLoading(true);
    if (isLodger && token) {
      (async () => {
        const res = await GetLodgerPersonalInfo();
        if (res.status === 200) {
          setBidzRequestDetails({
            ...res.data,
            equipper_name: "Derental's team",
            lodger_id: res.data?.id,
            lodger_name: res.data?.full_name,
            lodger_email: res.data?.email,
            lodger_image_link: res.data?.photo_url
          });
        }
      })();
    }
    setIsLoading(false);
  }, [token, isLodger]);

  useEffect(() => {
    setIsLoading(true);
    (async () => {
      const { status, data } = await getEquipmentById(id);
      if (status === 200) {
        setBidzEquipment({
          alias: data?.alias,
          category: data?.category?.map((item) => item.toLowerCase()),
          equipment_image_link: data?.image_link,
          requested_name: sessionStorage.getItem('name'),
          equipment_name_fr: data?.name_fr,
          equipment_id: data?.id,
          equipment_name: data?.name_en,
          coverage_area: [city]
        });
      }
    })();
    setIsLoading(false);
  }, [id, city]);

  if (isLoading || !bidzEquipment || !bidzRequestDetails) {
    return <ToloIsLoading />;
  }
  return (
    <div className="bidz_company_spotlihght">
      <div className="container-fluid">
        <div className="d-lg-none d-block">
          <BreadCrumb
            t={t}
            items={[
              {
                label: `${t('Company_spotlight')} : Derental  `
              }
            ]}
          />
        </div>
        <div className="row">
          <div className="col-lg-4">
            <div className="text-center bidz_company_spotlihght__left">
              <h1 className="t-header-h6 c-yellow">Derental.bidz</h1>
              <img src={bidzImage} alt="bidz" />
              <div className="content_bidz">
                <h2 className="t-subheading-1 c-fake-black mb-32">
                  {t('Let_us_find_you_exactly_what_you_are_looking_for')}
                </h2>
                <p className="t-body-large c-blue-grey">
                  {t(
                    'Derentalbidz_allows_you_to_rent_any_equipment_any_tool_any_time'
                  )}
                </p>
                <p className="t-body-large c-blue-grey">
                  {t('Tell_us_what_you_need_and_well_take_care_of_the_rest')}
                </p>
                <p className="t-body-large c-blue-grey">
                  {t(
                    'No_more_wasting_hours_on_the_phone_or_traveling_to_check_if_the_equipment_you_are_looking_for_is_available'
                  )}
                </p>
                <p className="t-body-large c-blue-grey mb-32">
                  {t('Follow_these_steps_to_get_started')}
                </p>
                <p className="t-body-large c-blue-grey mb-32 bold">
                  1. {t('Create_your_account_on')}
                  derentalequipment.com{' '}
                </p>

                {show() && (
                  <>
                    <p className="t-body-large c-blue-grey mb-32 bold">
                      2.{' '}
                      {t(
                        'Fill_in_the_criteria_to_identify_the_equipment_you_need_and_submit_your_request'
                      )}
                    </p>
                    <p className="t-body-large c-blue-grey mb-32 bold">
                      3.{' '}
                      {t(
                        'You_will_receive_a_quote_from_the_rental_centers_near_your_jobsite'
                      )}
                    </p>
                    <p className="t-body-large c-yellow mb-32 bold">
                      {t('It_that_simple_fast_and_efficient')}
                    </p>
                    <p className="t-body-small c-blue-grey mb-32 bold">
                      {t(
                        'Our_automated_bidding_system_DerentalBidz_will_request'
                      )}
                    </p>
                  </>
                )}
                {isMobile && (
                  <button
                    className="round-button yellow c-black"
                    type="button"
                    onClick={() => toggleViewDetails()}
                  >
                    {viewDetails ? t('Hide_details') : t('View_details')}
                  </button>
                )}
              </div>
            </div>
          </div>
          <div className="col-lg-8">
            <div className="bidz_company_spotlihght__right">
              <div className="d-lg-block d-none">
                <BreadCrumb
                  t={t}
                  items={[
                    {
                      label: `${t('Company_spotlight')} : Derental  `
                    }
                  ]}
                />
              </div>

              <h2 className="t-subheading-1 c-fake-black mb-2">
                {t('Let_us_find_your_equipment')}
              </h2>
              <p className="t-body-large c-blue-grey">
                {t('Bidz_allow_you_to_rent_any_equipment_any_tool_any_time')}
              </p>
              <div className="specification_equipment">
                <div className="specification_equipment__head">
                  <div className="d-flex justify-content-between align-items-center">
                    <div className="specification_equipment__head_left">
                      <h2 className="t-subheading-1 c-fake-black">
                        {t('Equipment')}
                      </h2>
                      <h3 className="t-subheading-2 c-neutrals-gray">
                        {bidzEquipment?.equipment_name}
                      </h3>
                    </div>
                    <img
                      src={bidzEquipment?.equipment_image_link}
                      alt="forklift"
                    />
                  </div>
                </div>
                <div className="specification_equipment__content">
                  <h3 className="t-body-large c-fake-black mb-2 bold">
                    {t('Specifications')}
                  </h3>
                  <p className="t-body-small c-neutrals-gray">
                    {t('Specify_the_specifications_of_the_equipment')}
                  </p>
                  <FormikForm
                    t={t}
                    initialValues={{
                      specifications: [
                        {
                          specification: '',
                          value: ''
                        },
                        {
                          specification: '',
                          value: ''
                        }
                      ]
                    }}
                    onSubmit={() => {}}
                    component={({ formik }) => (
                      <BidzForm
                        t={t}
                        formik={formik}
                        bidzRequestDetails={bidzRequestDetails}
                        setBidzRequestDetails={setBidzRequestDetails}
                        token={token}
                        togglePopup={togglePopup}
                        setShowSignIn={setShowSignIn}
                      />
                    )}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <RenderIf condition={isOpen}>
        <BidzModal
          t={t}
          isOpen={isOpen}
          handleClose={togglePopup}
          bidzRequest={{
            ...bidzRequestDetails,
            ...bidzEquipment
          }}
        />
      </RenderIf>
      <RenderIf condition={showSignIn}>
        <SignInModal
          t={t}
          signIn={signIn}
          show={showSignIn}
          setShow={setShowSignIn}
          setShowFPModal={setShowFPModal}
          showFPModal={showFPModal}
        />
      </RenderIf>
    </div>
  );
}
