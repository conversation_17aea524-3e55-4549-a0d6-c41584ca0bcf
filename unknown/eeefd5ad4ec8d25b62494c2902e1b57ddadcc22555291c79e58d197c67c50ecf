import React, { useEffect, useState } from 'react';
import {
  getSessionStorage,
  setSessionStorage
} from '../../helpers/Session_storage_helper';

export default function CardPopularCategory({
  name,
  iconPath,
  value,
  setInstantCategoryValue
}) {
  const [selected, setSelected] = useState(false);

  const searchResult = () => {
    setInstantCategoryValue(value);
    setSessionStorage('category', value);
    setSessionStorage('sub_category', '');
  };
  useEffect(
    () => {
      if (getSessionStorage('category') === value) {
        setSelected(true);
      } else {
        setSelected(false);
      }
    },
    [getSessionStorage('category')],
    value
  );

  return (
    <div
      className={`card content_card ${selected ? 'is-selected' : ''}`}
      onClick={searchResult}
    >
      <div className="content-details">
        <img src={iconPath} alt="" />
      </div>
      <div className="c-black d-flex flex-column justify-content-end align-items-center text-center">
        <h3
          className={
            selected
              ? 'card-title t-caption-small c-blue-grey selected'
              : 'card-title t-caption-small c-blue-grey'
          }
        >
          {name}
        </h3>
      </div>
    </div>
  );
}
