import Autocomplete from '@mui/material/Autocomplete';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import CustomButton from '../../../../shared/components/buttons/Custom_button.jsx';
import SearchInputAutoCompleteEquipment from '../../../../features/search_result/Search_input_auto_complete_equipment.jsx';
import Input from '../../../../shared/components/forms/Input.jsx';
import RenderIf from '../../../../shared/components/Render_if.jsx';
import { useInventoryContext } from '../../../../shared/context/Tooler_bidz_inventory_management_context.jsx';
import makeStyles from '@mui/styles/makeStyles';
import { useEffect, useRef, useState } from 'react';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import BlankImage from '../../../../style/assets/img/blank_equipment.svg';
import CustomTooltip from '../../../../shared/components/tooltips/Tooltip.jsx';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle } from '@fortawesome/fontawesome-free-solid';

const useStyles = makeStyles({
  root: {
    backgroundColor: '#e9ecef',
    minHeight: '48px',
    borderRadius: '5px',
    borderColor: '#e9ecef',
    '& .MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline': {
      borderColor: '#e9ecef !important',
      minHeight: '48px !important'
    },
    '& .MuiChip-label': {
      color: 'black !important',
      fontFamily: 'inherit'
    }
  }
});

const AutocompleteField = ({ name, value }) => {
  const classes = useStyles();

  return (
    <Autocomplete
      variant="standard"
      name={name}
      clearIcon={false}
      options={[]}
      value={value || []}
      freeSolo
      className={classes.root}
      readOnly
      multiple
      renderTags={(value, getTagProps) =>
        value.map((option, index) => (
          <Chip label={option} {...getTagProps({ index })} key={index} />
        ))
      }
      renderInput={(params) => <TextField placeholder="" {...params} />}
    />
  );
};

const Step1 = ({
  t,
  detectLanguage,
  smallDevice,
  setNextButtonDisabled,
  formik,
  setAliasEn,
  equipmentName,
  setEquipmentName
}) => {
  const [equipmentID, setEquipmentID] = useState('');
  const { getEquipmentById } = useInventoryContext();
  const hiddenFileInput = useRef(null);

  useEffect(() => {
    const isNextButtonDisabled =
      formik.values.category.length === 0 ||
      formik.values.internal_id === '' ||
      formik.errors?.minimum_rental_period ||
      formik.errors?.preferred_equipment_name;
    setNextButtonDisabled(isNextButtonDisabled);
  }, [formik, equipmentID]);

  const handleClick = (event) => {
    event.preventDefault();
    event.stopPropagation();
    hiddenFileInput.current.click();
  };
  const [image, setImage] = useState(BlankImage);

  const onImageChange = (event) => {
    if (event.target.files && event.target.files[0]) {
      const imageUrl = URL.createObjectURL(event.target.files[0]);
      setImage(imageUrl);
      formik.setFieldValue('equipper_equipment_picture', imageUrl);
    }
  };

  useEffect(() => {
    if (equipmentID) {
      (async () => {
        const { status, data } = await getEquipmentById(equipmentID);
        if (status === 200) {
          formik.setFieldValue('name', data?.name_en);
          formik.setFieldValue('name_fr', data?.name_fr);
          formik.setFieldValue('image_link', data?.image_link);
          formik.setFieldValue('category', data?.category);
          formik.setFieldValue('sub_category', data?.sub_category);
          formik.setFieldValue('alias.fr', data?.alias.fr);
          formik.setFieldValue('alias.en', data?.alias.en);
          setAliasEn(data?.alias?.en || []);
        }
      })();
    }
  }, [equipmentID]);
  const theme = createTheme({
    palette: {
      primary: { main: '#ECA869' }
    }
  });
  return (
    <ThemeProvider theme={theme}>
      <Box className="container d-lg-flex w-100 d-flex flex-lg-row flex-column flex-column-reverse col-lg-12 justify-content-around">
        <Box
          className="d-lg-flex flex-lg-column d-flex flex-row-reverse col-lg-6 justify-sm-content-around align-sm-items-center"
          gap="20px"
          width={smallDevice ? '100%' : '300px'}
          marginBottom="20px"
          marginTop="20px"
        >
          <Box>
            <Typography fontWeight="bold" variant="h6">
              {t('Equipment_picture')}
            </Typography>

            <Typography variant="p" hidden={!formik.values.image_link}>
              {t('Image_equipment_recommended')}
            </Typography>
            <CustomButton
              textButton={t('Upload_image')}
              type="file"
              variant="contained"
              className="round-button yellow c-black d-lg-none"
              color="primary"
              onClick={handleClick}
            />
          </Box>

          <Box
            component="img"
            className={smallDevice ? 'col-5' : 'col-12'}
            height={smallDevice ? '166px' : 'auto'}
            alt="Equipment image"
            src={
              formik.values.equipper_equipment_picture &&
              !formik.values.equipper_equipment_picture.includes(
                'equipment_library/Empty_state_equipment.png'
              )
                ? formik.values.equipper_equipment_picture
                : formik.values.image_link
                ? formik.values.image_link
                : image
            }
          />
          <input
            type="text"
            value="equipper_equipment_picture"
            name="equipper_equipment_picture"
            className="d-none"
          />

          <CustomButton
            textButton={t('Upload_image')}
            type="file"
            textPopper={t('Choose_your_equipment_first')}
            variant="contained"
            className="round-button yellow c-black d-lg-block d-none mt-2"
            color="primary"
            disabled={!formik.values.image_link}
            onClick={handleClick}
          />
          <input
            type="file"
            onChange={onImageChange}
            className="max-w-image "
            ref={hiddenFileInput}
            accept=".png, .jpg, .jpeg, .bmp, .tiff"
            style={{ display: 'none' }}
          />
        </Box>
        <Box className=" mt-4 col-lg-6">
          <div className="form-group">
            <label className="label-input t-body-regular c-fake-black">
              {t('Derental_equipment_name')}{' '}
              <span className="c-red star-required">*</span>
            </label>
            <Box
              className="form-group"
              style={{
                width: '100%',
                '& .autocomplete-options-menu': { position: 'absolute' }
              }}
            >
              <SearchInputAutoCompleteEquipment
                attribute="equipment"
                placeholder={t('Select_equipment')}
                value={equipmentName.requestedName || ''}
                onChange={setEquipmentName}
                detectLanguage="fr"
                indexName={import.meta.env.VITE_ALGOLIA_INDEX_NAME_STANDARDS}
                setEquipmentID={setEquipmentID}
                t={t}
                isEquipment
                isAddEquipment
                className="form-control w-100"
              />
            </Box>
          </div>
          <RenderIf condition={formik.values.name || formik.values.name_fr}>
            <div className="form-group">
              <RenderIf
                condition={
                  (formik.values.alias.en &&
                    (detectLanguage === 'en' || detectLanguage === 'ar')) ||
                  (formik.values.alias.fr && detectLanguage === 'fr')
                }
              >
                <label className="label-input d-lg-block">
                  {t('Alternative_name')}
                  <CustomTooltip text="Alternative names commonly used in the industry for an equipment.">
                    <span className="c-near-grey">
                      <FontAwesomeIcon
                        icon={faInfoCircle}
                        className="c-near-grey"
                      />
                    </span>
                  </CustomTooltip>
                </label>
              </RenderIf>
              <RenderIf
                condition={
                  (detectLanguage === 'en' || detectLanguage === 'ar') &&
                  formik.values.alias.en
                }
              >
                <AutocompleteField
                  name="alias.en"
                  value={formik.values.alias.en || []} // Ensure alias is an array
                />
              </RenderIf>
              <RenderIf
                condition={detectLanguage === 'fr' && formik.values.alias.fr}
              >
                <AutocompleteField
                  name="alias.fr"
                  value={formik.values.alias.fr}
                />
              </RenderIf>
            </div>
          </RenderIf>
          <br />
          <div className="form-group">
            <Input
              type="text"
              label={t('Preferred_equipment_name')}
              name="preferred_equipment_name"
              className="form-control"
              isNotRequired
            />
          </div>
          <br />
          <div className="form-group">
            <Input
              type="text"
              label={t('Equipment_id')}
              name="internal_id"
              className="form-control"
            />
          </div>
          <br />
          <div className="form-group">
            <Input
              type="number"
              label={t('Minimum_rental_period')}
              name="minimum_rental_period"
              className="form-control"
              isNotRequired
            />
          </div>
          <br />
          <RenderIf condition={formik.values.name || formik.values.name_fr}>
            <div className="form-group">
              <label className="label-input d-lg-block">
                {t('Category')} <span className="c-red star-required">*</span>
              </label>
              <AutocompleteField
                name="category"
                value={formik.values.category}
              />
            </div>
            <br />
            <div className="form-group">
              <label className="label-input d-lg-block">
                {t('Sub_category')}{' '}
                <span className="c-red star-required">*</span>
              </label>
              <AutocompleteField
                name="sub_category"
                value={formik.values.sub_category}
              />
            </div>
            <br />
          </RenderIf>
        </Box>
      </Box>
    </ThemeProvider>
  );
};

export default Step1;
