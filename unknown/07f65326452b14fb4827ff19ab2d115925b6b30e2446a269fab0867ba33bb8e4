/*.backBlack {
  background-color: rgba(0, 0, 0, 0.925);
  padding: 2% 0;
  text-align: center;
}*/

.date-range-picker {
  margin-top: 4%;
}

.shadow-bt {
  box-shadow: 0 5px 7px -3px grey;
}

.backImage {
  padding-bottom: 30px;
  position: relative;

  @media (min-width: 992px) {
    min-height: 80%;
    background: none !important;
    padding-bottom: 100px;
  }
  @media (min-width: 1200px) {
    padding-bottom: 150px;
  }
}

.label-padding {
  padding-top: 2px;
  padding-left: 10px;
  @media (min-width: 992px) {
    padding-left: 0;
  }
}

.searchContainer {
  max-width: 100%;
  height: auto;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: contain;
  @media (max-width: 992px) {
    margin-top: 20px;
  }
}

.title1 {
  text-align: center;
  font-family: $secondary-font;
  font-weight: 700;
  @media (max-width: 992px) {
    font-size: 20px;
  }
}

.searchBar {
  margin: 40px auto 0;
  border-radius: 18px;
  width: 100%;
  height: 63px;
  font-size: 15px;
  @media (min-width: 992px) {
    border: 2px solid $yellow;
    background-color: white;
    height: 100px;
    padding: 20px 30px;
    font-size: 18px;
  }
  @media (min-width: 1200px) {
    width: 1030px;
  }

  .form-group {
    padding-left: 10px;
  }

  .button-search {
    @media (min-width: 992px) {
      right: -18px;
    }

    svg {
      color: #ffff;
    }
  }

  .button-search {
    transition-duration: 0.4s;
    -moz-transition-duration: 0.4s;
    -webkit-transition-duration: 0.4s;
    -o-transition-duration: 0.4s;
    display: flex;
    align-items: center;
    justify-content: center;

    &.disabled {
      opacity: 0.8;
    }
  }

  .search-text {
    display: none;
    transition: all ease 0.1s;
  }

  .padding-lr-0 {
    @media (max-width: 992px) {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }

  form {
    @media (max-width: 992px) {
      padding-bottom: 30px;
    }
  }

  @media (max-width: 992px) {
    .css-vnkopk-MuiStepLabel-iconContainer {
      padding-right: 0;
    }
    .css-1bw0nnu-MuiStep-root {
      padding: 0;
    }
    .css-ubi7u8-MuiSvgIcon-root-MuiStepIcon-root {
      color: transparent;
      border: 2px solid $yellow;
      border-radius: 100%;
      width: 32px;
      height: 32px;

      .css-opt7yd-MuiStepIcon-text {
        fill: $yellow;
      }

      &.Mui-active {
        .css-opt7yd-MuiStepIcon-text {
          fill: $white;
        }
      }
    }
    .css-z7uhs0-MuiStepConnector-line {
      border-color: $yellow;
    }
  }
}

.adjustPicker {
  padding: 0;
  @media (max-width: 980px) {
    .react-daterange-picker__wrapper {
      display: flex;
      flex-grow: 1;
      flex-shrink: 0;
      align-items: center;
      border: 0;
      flex-direction: column;
    }
    .react-daterange-picker__inputGroup {
      min-width: calc(13px + 4.32em + 0.434em);
      height: 100%;
      flex-grow: 1;
      box-sizing: content-box;
      padding-left: 22px;
      padding-bottom: 35px;
    }
  }
}

.border-gradient-far-right-adjustPicker {
  border: 0;
  @media (max-width: 980px) {
    padding: 5px !important;
    background: white;
    margin-bottom: 5px;
    border: 0.5px solid $yellow !important;
    border-radius: 15px !important;
    height: 57px;
  }
}

.searchInput {
  background-color: transparent;
  border: 0;
  box-shadow: none;

  &:focus {
    outline: none;
    box-shadow: none;
  }
}

.align-items {
  p {
    color: $yellow;
  }
}

.searchInput::selection {
  background: $light-grey; /* WebKit/Blink Browsers */
}

.searchDate {
  background-color: transparent;
  height: 40px;
  width: 100% !important;
  border: 0;
  box-shadow: none !important;

  &:focus {
    outline: none;
    box-shadow: none;
  }
}

.searchDate::selection {
  background: $light-grey; /* WebKit/Blink Browsers */
}

.button-search {
  background-color: $yellow;
  border-radius: 7px;
  border: 0 solid;
  width: 52px;
  height: 52px;
  position: relative;
  font-size: x-large;
}

@media (max-width: 992px) {
  .css-m5vj9m-MuiStepper-root {
    padding: 0 30px;
  }
  .disapear-on-collapse {
    display: none !important;
  }

  .searchBar {
    background-color: transparent;
    margin: 55px 0;
    border: 0;
    border-radius: 50px;
  }

  .button-search {
    background-color: $yellow;
    border-radius: 50px;
    border: 0px solid;
    width: 100% !important;
    height: 50px;
    margin-top: 7px;
    right: 0;
  }
  .center-mobile {
    margin: 0 auto;
  }
}

.border-none {
  border: none;
}

.search-image {
  margin-top: 0;
  position: absolute;
  bottom: 0;
  z-index: -1;
  left: 0;
  right: 0;
  @media (max-width: 992px) {
    left: -10px;
    right: -10px;
  }

  img {
    width: 100%;
    margin: 0 auto;
  }
}

.search-content {
  position: relative;
  margin-top: 30px;
  @media (min-width: 992px) {
    margin-top: 120px;
  }
}

.foxy {
  .css-1s2u09g-control {
    align-items: flex-start;
    border: none;
    background-color: hsla(0, 0%, 100%, 0);
    cursor: default;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    min-height: 30px;
    outline: 0 !important;
    position: relative;
    -webkit-transition: all 100ms;
    transition: all 100ms;
    box-sizing: border-box;
    margin: -6px 0px 0px 0px;
    text-transform: capitalize;
  }

  .css-1pahdxg-control {
    -webkit-align-items: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border: none;
    background-color: hsla(0, 0%, 100%, 0);
    cursor: default;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    box-shadow: none;
    -webkit-box-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    min-height: 30px;
    outline: 0 !important;
    position: relative;
    -webkit-transition: all 100ms;
    transition: all 100ms;
    box-sizing: border-box;
    margin: -6px 0px 0px 0px;
    text-transform: capitalize;
  }
}

.select-wrapper-container {
  position: relative;

  .select-wrapper {
    position: relative;
    z-index: 1;
  }

  .input-required,
  .input-required:focus,
  .input-required:hover,
  .input-required:active {
    position: absolute;
    border: 0;
    background-color: transparent;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
  }
}

.IndicatorIconsWrapper-sc-1jozl2i-0 {
  opacity: 0;
}

.border-gradient-yellow {
  @media (min-width: 992px) {
    padding: 0 13px;
  }

  .rfs-select-container {
    border: none !important;
    margin-top: -2.5%;
    box-shadow: none !important;
    box-shadow: none !important;
  }

  label {
    font-weight: 600;
  }
}

.autocomplete-options-menu {
  background-color: $white;
  padding: 14px 14px 14px 0;
  border-radius: 14px;
  margin-top: 7px;
  box-shadow: 0 2px 40px 0 #55555577;
  max-height: 205px;

  .search-mobile & {
    @media (max-width: 992px) {
      position: absolute;
      width: 100%;
      z-index: 2;
    }
  }

  li {
    padding: 6px 10px 6px 10px;
    text-align: center;

    span {
      @media (min-width: 992px) {
        font-size: 14px;
      }
    }
  }
}

.siac {
  background-color: transparent !important;
  border: none;
  border-radius: 0;
  padding: 0 !important;

  .container-wrapper-equip-name {
    @media (min-width: 1200px) {
      position: relative;
      top: 20px;
    }

    ul {
      width: 100%;
      padding: 0;
      @media (min-width: 1200px) {
        width: 200%;
        .navbar & {
          width: 225%;
        }
      }

      .scroll-bar-options {
        overflow-x: hidden;
      }
    }
  }

  ul {
    padding: 0;
  }
}

.navbar {
  .set_location {
    .wrapper {
      ul {
        @media (min-width: 1200px) {
          width: 188%;
        }
      }
    }
  }
}

.searchBar {
  .set_location {
    .wrapper {
      ul {
        @media (min-width: 1200px) {
          width: 225%;
        }
      }
    }
  }
}

.search-input-auto-complete {
  background-color: transparent !important;
  font-weight: 300;

  &:focus,
  &:active {
    outline: none;
    box-shadow: none;
  }

  &::placeholder {
    /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: $neutrals-gray;
    font-weight: 300;
  }

  &:-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: $neutrals-gray;
    font-weight: 300;
  }

  &::-ms-input-placeholder {
    /* Microsoft Edge */
    color: $neutrals-gray;
    font-weight: 300;
  }
}

.autocomplete-label {
  &:hover {
    background: var(--accent-50, rgba(236, 168, 105, 0.2));
    color: #000000 !important;
    border-radius: 10px;
  }
}

/* clears the ‘X’ from Internet Explorer */
input[type='search']::-ms-clear {
  display: none;
  width: 0;
  height: 0;
}

input[type='search']::-ms-reveal {
  display: none;
  width: 0;
  height: 0;
}

/* clears the ‘X’ from Chrome */
input[type='search']::-webkit-search-decoration,
input[type='search']::-webkit-search-cancel-button,
input[type='search']::-webkit-search-results-button,
input[type='search']::-webkit-search-results-decoration {
  display: none;
}

.scroll-bar-options {
  overflow-y: scroll;
  max-height: 175px;
}

.scroll-bar-options::-webkit-scrollbar {
  width: 6px;
}

.scroll-bar-options::-webkit-scrollbar-track {
  border-radius: 20px;
  background-color: rgb(235, 231, 231);
}

.scroll-bar-options::-webkit-scrollbar-thumb {
  border-radius: 20px;
  background-color: $yellow;
}

.explore-equipments-header {
  text-decoration-line: underline;
}

.title-top-padding {
  padding-top: 40px;
}

:focus-visible {
  outline: none;
}

input.react-daterange-picker__inputGroup__input {
  &:focus-visible {
    outline: none;
  }
}

.width-100 {
  width: 100%;
}

.rs-steps-item-status-finish {
  .rs-steps-item-title:after {
    border-color: $yellow;
  }
}

.siac {
  .container {
    padding: 0 !important;
  }
}

.margin-horizontal-15 {
  margin: 0 15px auto;
}

.searchBar {
  ul {
    padding: 0 !important;
  }

  .first_column {
    padding-left: 0;
  }
}

.hight-100 {
  height: 500px;
}

.adjustPicker {
  &.new-datepicker {
    display: flex;
    @media (max-width: 992px) {
      width: 100%;
      border: 2px solid $yellow;
      border-radius: 20px;
      padding: 5px;
      background: $white;
      height: 42px;
    }

    .react-datepicker-wrapper {
      min-width: 140px;
      position: relative;
      top: -9px;
      @media (max-width: 992px) {
        top: 6px;
      }

      &:first-child {
        .react-datepicker__input-container {
          @media (max-width: 992px) {
            border-right: 2px solid $yellow;
            padding-left: 20px;
          }
        }
      }

      .react-datepicker__input-container {
        @media (max-width: 992px) {
          padding-left: 10px;
        }

        .searchInput-navbar {
          @media (max-width: 992px) {
            height: 28px;
            padding-bottom: 0;
            padding-right: 30px;
            font-size: 12px;
            color: $near-grey;
            position: relative;
            top: 2px;
          }
        }
      }
    }
  }
}

.show-search-bar {
  .adjustPicker {
    &.new-datepicker {
      .react-datepicker-wrapper {
        top: 8px;
        min-width: 132px;

        .searchInput-z {
          background: transparent;
        }
      }
    }
  }
}

.react-datepicker {
  border-radius: 7px;
  background: $white;

  filter: drop-shadow(0px 11px 40px rgba(6, 28, 61, 0.07));
  border: 0;
  font-family: $primary-font;
  padding: 16px 24px;

  &__day {
    font-size: 14px;
    color: $fake-black;

    &[aria-disabled='true'] {
      background: $light-gray;
      border-radius: 0.3rem;

      &:hover {
        background: $light-gray;
        color: $fake-black;
      }
    }

    &-names {
      margin: 0;
      font-size: 12px;
    }

    &--selected {
      color: $white;

      &:hover {
        color: $white;
      }
    }

    &--keyboard {
      &-selected {
        color: $white;

        &:hover {
          color: $white;
        }
      }
    }

    &:hover {
      color: $white;
      background: $yellow;
    }
  }

  &__header {
    border-top-left-radius: 7px;
    background: transparent;
    border-bottom: 0;
    padding: 0;
  }

  &__navigation {
    &--previous {
      left: auto;
      right: 50px;
    }

    &-icon {
      &:before {
        border-color: $fake-black;
      }
    }

    &--previous,
    &--next {
      top: 16px;
    }
  }

  &__current {
    &-month {
      text-align: left;
      margin-bottom: 13px;
    }
  }
}

.react-datepicker-popper[data-placement^='bottom'] {
  padding-top: 20px;

  .react-datepicker__triangle::before,
  .react-datepicker__triangle::after {
    display: none;
  }
}

.react-datepicker__current-month,
.react-datepicker-time__header,
.react-datepicker-year-header {
  font-size: 16px;
}

.year {
  .react-datepicker__navigation--previous {
    margin-right: 116px !important;
  }

  .react-datepicker__navigation--next {
    margin-right: -6px !important;
  }
}

.react-datepicker__header:not(.react-datepicker__header--has-time-select) {
  border-top-right-radius: 7px;
}

.react-datepicker__day--selected,
.react-datepicker__day--in-selecting-range,
.react-datepicker__day--in-range,
.react-datepicker__month-text--selected,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__month-text--in-range,
.react-datepicker__quarter-text--selected,
.react-datepicker__quarter-text--in-selecting-range,
.react-datepicker__quarter-text--in-range,
.react-datepicker__year-text--selected,
.react-datepicker__year-text--in-selecting-range,
.react-datepicker__year-text--in-range {
  background-color: $yellow;
}

.react-datepicker__day--selected {
  &:hover {
    background-color: $yellow;
  }
}

.react-datepicker__day--keyboard-selected,
.react-datepicker__month-text--keyboard-selected,
.react-datepicker__quarter-text--keyboard-selected,
.react-datepicker__year-text--keyboard-selected,
.react-datepicker__day--keyboard-selected:hover,
.react-datepicker__month-text--keyboard-selected:hover,
.react-datepicker__quarter-text--keyboard-selected:hover,
.react-datepicker__year-text--keyboard-selected:hover {
  background-color: $yellow;
}

.react-datepicker__day--keyboard-selected:hover,
.react-datepicker__month-text--keyboard-selected:hover,
.react-datepicker__quarter-text--keyboard-selected:hover,
.react-datepicker__year-text--keyboard-selected:hover {
  color: $white;
}

.adjustPicker {
  display: flex;

  .react-datepicker-wrapper {
    &:nth-child(2) {
      .react-datepicker__input-container {
        margin-left: 10px;
      }
    }
  }
}

.no-result-img {
  max-width: 40px;
}

.react-datepicker__year-wrapper {
  display: 'block';
}
