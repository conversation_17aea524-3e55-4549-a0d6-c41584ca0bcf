import { getCookies } from './Cookies';

export function splitTeamList(teamList, type) {
  let admins = [];
  teamList?.forEach((member) => {
    if (member.type === type) {
      if (admins.includes(member)) {
        const index = admins.indexOf(member);
        admins.splice(index, 1);
        admins = [...admins];
      } else {
        admins = [...admins, member];
      }
    }
  });
  return admins;
}
export function checkIfMemberExists(list, id) {
  let result = -1;
  list.forEach((member, index) => {
    if (member.id === id) {
      result = index;
    }
  });
  return result;
}

export function extractFromList(listToDeleteFrom, listToDelete) {
  const result = [];
  listToDeleteFrom.forEach((member) => {
    if (checkIfMemberExists(listToDelete, member.id) === -1) {
      result.push(member);
    }
  });
  return result;
}
export function concatList(listToAddTo, ListToAdd) {
  const result = [];
  listToAddTo.forEach((member) => {
    if (checkIfMemberExists(result, member.id) === -1) result.push(member);
  });
  ListToAdd.forEach((member) => {
    if (checkIfMemberExists(result, member.id) === -1) result.push(member);
  });
  return result;
}

export const userType = (members) => {
  const member =
    members !== undefined
      ? members?.find((member) => member.email === getCookies('email'))
      : null;
  return member && member.type;
};

export const getConnectedUser = (team) => {
  let member = { privilege_level: 10 };
  Object.keys(team).forEach((key) => {
    team[key].forEach((temp) => {
      if (temp.email === getCookies('email')) {
        member = temp;
      }
    });
  });
  return member;
};
