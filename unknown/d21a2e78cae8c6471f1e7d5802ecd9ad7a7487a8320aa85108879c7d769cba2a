import React, { useEffect, useState } from 'react';
import { BsPlusCircle } from 'react-icons/bs';
import { Field, FieldArray, Form, Formik } from 'formik';
import { AiOutlineMinusCircle } from 'react-icons/ai';
import RenderIf from '../Render_if';
import { firstLetterUpperCase } from '../../helpers/String_helps';
import ToloIsLoading from '../cards/Tolo_is_loading';
import Modal from './Modal';
import { itemsBidzToReturn } from '../../helpers/Data_helper';
import { isUndefined } from 'lodash';

export default function AddBidzInventoryModal({
  show,
  t,
  isEdit,
  onClose,
  initialValues,
  onSubmit,
  onImageChange,
  isForShow
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState([]);
  // const [selectedSubCategory, setSelectedSubCategory] = useState([]);
  const formikInitialValues = initialValues
    ? {
        ...initialValues,
        alias: {
          en: initialValues['alias.en'],
          fr: initialValues['alias.fr']
        }
      }
    : {
        name_en: '',
        name_fr: '',
        description: '',
        alias: {
          en: [''],
          fr: ['']
        },
        image_link: ''
      };
  useEffect(() => {
    function getCategories() {
      setIsLoading(true);
      const templist = [];
      initialValues.category?.forEach((currentCategory, index) => {
        templist[index] = {
          value: currentCategory,
          label: firstLetterUpperCase(currentCategory)
        };
      });
      setSelectedCategory(templist);
      setIsLoading(false);
    }

    function getSubCategories() {
      setIsLoading(true);
      const templist = [];
      initialValues.sub_category?.forEach((currentSubCategory, index) => {
        templist[index] = {
          value: currentSubCategory,
          label: currentSubCategory
        };
      });
      // setSelectedSubCategory(templist);
      setIsLoading(false);
    }

    initialValues && getSubCategories();
    initialValues && getCategories();
  }, [initialValues]);

  if (isLoading) {
    return <ToloIsLoading />;
  }
  return (
    <RenderIf condition={show}>
      <Modal
        isCenter
        image={initialValues && initialValues.image_link}
        className="text-center t-header-h5 c-grey-titles"
        t={t}
        onClose={onClose}
      >
        <div className="scrollbar">
          <Formik
            initialValues={formikInitialValues}
            onSubmit={(values) => {
              values.category = selectedCategory?.map(
                (category) => category.value
              );
              values.id = isEdit
                ? formikInitialValues.path?.slice(
                    'tooler_bidz_equipment_inventory/'.length,
                    formikInitialValues.path.length
                  )
                : '';
              onSubmit(values);
            }}
          >
            {(formik) => (
              <Form className="form-group m-b-0">
                {Object.keys(formikInitialValues)?.map((key) => (
                  <>
                    <RenderIf
                      condition={
                        itemsBidzToReturn(key) &&
                        typeof formikInitialValues[key] !== 'object'
                      }
                    >
                      <h5 className="fwb margin-top-10">
                        {key.replaceAll('_', ' ')}
                      </h5>
                      <Field
                        key={key}
                        name={key}
                        type="text"
                        className="form-control w-100 margin-top-10"
                        placeholder={key.replaceAll('_', ' ')}
                        value={formik?.values[key]}
                      />
                    </RenderIf>
                    <RenderIf condition={key === 'category'}>
                      <h5 className="fwb margin-top-10">{t('Categories')}</h5>
                      {/* <CategoriesMultiSelect
                        isMulti
                        setSelectedCategories={setSelectedCategory}
                        defaultValue={selectedCategory}
                        allowSelectAll
                        placeholder="Categories"
                        t={t}
                        value={selectedCategory}
                      /> */}
                    </RenderIf>
                    <RenderIf condition={key === 'sub_category'}>
                      <h5 className="fwb margin-top-10">
                        {t('Sub_categories')}
                      </h5>
                      {/* <CategoriesMultiSelect
                        setSelectedCategories={setSelectedSubCategory}
                        defaultValue={selectedSubCategory}
                        placeholder="Sub-Categories"
                        value={selectedSubCategory}
                        t={t}
                        isMulti
                        allowSelectAll
                        isSubCategory
                      /> */}
                    </RenderIf>
                    <RenderIf condition={key === 'alias'}>
                      <section className="credit-check-modal__section ">
                        {Object.keys(formik.values.alias)?.map((lang) => (
                          <>
                            <p classNmae="fwb margin-top-10">Alias {lang}</p>
                            <FieldArray
                              name={`alias.${lang}`}
                              render={(arrayHelpers) => (
                                <>
                                  {!isUndefined(formik.values.alias[lang]) &&
                                    formik.values.alias[lang]?.map(
                                      (_, index) => (
                                        <div key={index}>
                                          <Field
                                            disabled={isForShow}
                                            name={`alias.${lang}[${index}]`}
                                            value={
                                              formik.values.alias[lang][index]
                                            }
                                            autoComplete="off"
                                            className="form-control col-12"
                                          />
                                          {index !== 1 && (
                                            <div className="col-lg-1 ">
                                              <AiOutlineMinusCircle
                                                size={30}
                                                style={{
                                                  marginTop: '15%'
                                                }}
                                                color="#ECA869"
                                                onClick={() => {
                                                  arrayHelpers.remove(index);
                                                }}
                                              />
                                            </div>
                                          )}
                                        </div>
                                      )
                                    )}

                                  <BsPlusCircle
                                    size={30}
                                    color="#ECA869"
                                    onClick={() => {
                                      arrayHelpers.push('');
                                    }}
                                  />
                                </>
                              )}
                            />
                          </>
                        ))}
                      </section>
                    </RenderIf>
                  </>
                ))}
                <div className="fixed-button-modal">
                  <button
                    className="button-signup round-button yellow bold c-primary-color"
                    onClick={formik.handleSubmit}
                  >
                    {isEdit ? 'Update' : 'Add'}
                  </button>
                </div>
                {isEdit && (
                  <label
                    htmlFor="formId"
                    className="cbutton-signup round-button yellow bold c-primary-color"
                  >
                    <input
                      type="file"
                      onChange={(event) => {
                        onImageChange(event, formikInitialValues);
                      }}
                      name=""
                      accept=".png, .jpg, .jpeg, .bmp, .tiff"
                      id="formId"
                      hidden
                    />
                    {t('Upload_image')}
                  </label>
                )}
              </Form>
            )}
          </Formik>
        </div>
      </Modal>
    </RenderIf>
  );
}
