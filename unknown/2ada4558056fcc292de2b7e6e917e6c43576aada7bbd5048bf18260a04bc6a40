/*
  • notification
  • no notification
  • notification background
  ---------- ---------- ---------- ---------- ----------
*/


/*
  • notification
  ---------- ---------- ---------- ---------- ----------
*/

.notification-content {
  &__sections {
    background: $white;
    padding: 25px;
    border-radius: 30px;
    margin-top: 70px;

    .border-content {
      position: relative;

      &::after {
        content: "";
        display: inline-block;
        background: $light-grey;
        width: calc(100% - 115px);
        height: 1px;
        position: absolute;
        top: 11px;
        right: 0;
      }
    }

    .view-all {
      text-align: center;
      padding-top: 40px;
      @media(min-width: 1200px) {
        padding-top: 160px;
      }
    }
  }

  &__section:not(:first-child) {
    margin-top: 35px;
  }
}

.notification-box {
  padding: 10px 22px;
  background: $light-yellow;
  margin-top: 20px;
  border-radius: 32px;
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  @media(min-width: 1200px) {
    flex-wrap: nowrap;
    align-items: center;
  }

  p {
    margin-bottom: 0;
  }

  &__image {
    width: 70px;

    img {
      width: 46px;
      height: 46px;
      border: 2px solid $primary-color;
      border-radius: 50px;
    }
  }

  &__content {
    width: calc(100% - 90px);
    padding-left: 15px;
    @media(min-width: 1200px) {
      width: 100%;
      padding-left: 0;
    }

    p {
      margin-bottom: 0;
      color: $primary-color;
      @media(min-width: 1200px) {
        margin-left: 35px;
      }
    }
  }

  &__right {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
    flex-direction: row-reverse;
    margin-top: 20px;
    @media(min-width: 1200px) {
      justify-content: end;
      width: 35%;
      flex-direction: initial;
      margin-top: 0;
    }

    button {
      margin-left: 50px;
      @media(min-width: 1200px) {
        margin-left: 40px;
      }
    }
  }

  &.state {
    background: $light-grey;

    .notification-box__image {
      img {
        border-color: $check-grey;
      }
    }
  }
}

/*
  • no notification
  ---------- ---------- ---------- ---------- ----------
*/

.no-notification {
  &__image {
    text-align: center;
    margin-top: 95px;

    img {
      max-width: 415px;
    }
  }

  &__content {
    background: $white;
    border-radius: 30px;
    padding: 60px 30px;
    max-width: 670px;
    margin: 30px auto 0;

    p {
      margin-bottom: 0;
      text-align: center;
    }
  }
}


/*
  • notification background
  ---------- ---------- ---------- ---------- ----------
*/

.notification-container {
  position: relative;

  .notification-bg {
    position: absolute;
    bottom: -100px;
    z-index: -1;
  }
}
