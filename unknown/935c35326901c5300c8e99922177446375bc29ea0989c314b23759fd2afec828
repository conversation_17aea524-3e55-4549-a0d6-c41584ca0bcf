import React, { useContext, useEffect, useState } from 'react';
import AxiosFactory, {
  METHOD_POST,
  METHOD_GET,
  METHOD_DELETE,
  METHOD_PUT
} from '../helpers/Context_helpers';
import {
  GET_EQUIPMENT_BY_ID,
  GET_ALL_EQUIPMENTS,
  ADD_EQUIPMENT,
  GET_RENTALS_IN_PROGRESS,
  GET_RENTAL_IN_SUMMARY,
  DELETE_ALL_EQUIPMENTS,
  EQUIPMENT,
  AIRTABLE_SYNC,
  UPDATE_BOOKING_STATUS
} from '../helpers/Url_constants';
import axios from 'axios';
import { getCookies } from '../helpers/Cookies';
import { index } from '../helpers/Algolia_helper';
import { isEmpty } from 'lodash';

const EquipmentContext = React.createContext();

export function useEquipment() {
  return useContext(EquipmentContext);
}

export function useFetchUniqueNames() {
  const [equipments, setEquipments] = useState(null);
  const language = getCookies('lang') ? getCookies('lang') : 'en';
  useEffect(() => {
    const fetchUniqueNames = async () => {
      const filter = `equipper_id:${getCookies('userId')} AND is_active:true`;
      const hitsPerPage = 100;
      const uniqueNames = new Set();
      const nameMap = {};
      let page = 0;
      let hasMore = true;

      while (hasMore) {
        const results = await index.search('', {
          filters: filter,
          hitsPerPage: hitsPerPage,
          page: page
        });

        results?.hits?.forEach((hit) => {
          if (hit.name) {
            uniqueNames.add(hit.name);
            nameMap[hit.name] = hit.name_fr;
          }
        });

        page++;
        hasMore = page < results.nbPages;
      }

      const equipmentOptions = Array.from(uniqueNames).map((name) => {
        const label = language === 'fr' ? nameMap[name] : name;
        return {
          label: label || name,
          value: name
        };
      });

      if (!isEmpty(equipmentOptions)) {
        setEquipments(equipmentOptions);
      }
    };

    fetchUniqueNames();
  }, [language]);

  return equipments;
}

export default function EquipmentProvider({ children }) {
  async function GetEquipmentByID(id) {
    return await AxiosFactory({
      url: GET_EQUIPMENT_BY_ID + id,
      method: METHOD_GET
    });
  }
  async function GetAllEquipments(itemsPerPage, lastId) {
    const url = lastId
      ? `${GET_ALL_EQUIPMENTS}limit=${itemsPerPage}&last_id=${lastId}`
      : `${GET_ALL_EQUIPMENTS}limit=${itemsPerPage}`;
    return await AxiosFactory({
      url,
      method: METHOD_GET
    });
  }
  async function UpdateEquipmentStatus(id, status) {
    return await AxiosFactory({
      url: `${EQUIPMENT}/${id}`,
      method: METHOD_POST,
      data: { status: status }
    });
  }
  async function UpdateBatchOfEquipmentByName(data) {
    return await AxiosFactory({
      url: `${EQUIPMENT}`,
      method: METHOD_PUT,
      data: data
    });
  }
  async function AddEquipments(equipments) {
    const header = {
      'Content-Type': 'multipart/form-data'
    };
    header.Authorization = `Bearer ${getCookies('token')}`;

    const res = await axios.post(
      import.meta.env.VITE_REACT_APP_BASE_URL + ADD_EQUIPMENT,
      equipments,
      { headers: header }
    );
    return res;
  }
  async function AddSingleEquipment(equipment) {
    return await AxiosFactory({
      url: EQUIPMENT,
      method: METHOD_POST,
      data: equipment
    });
  }
  async function UpdateEquipment(equipment) {
    return await AxiosFactory({
      url: `${EQUIPMENT}/${equipment.id}`,
      method: METHOD_PUT,
      data: equipment
    });
  }
  async function AirTableSynchronization(currency) {
    return await AxiosFactory({
      url: AIRTABLE_SYNC,
      method: METHOD_POST,
      data: {
        currency: currency
      }
    });
  }
  async function DeleteEquipment(id) {
    return await AxiosFactory({
      url: `${EQUIPMENT}/${id}`,
      method: METHOD_DELETE
    });
  }
  async function DeleteAllEquipments() {
    return await AxiosFactory({
      url: DELETE_ALL_EQUIPMENTS,
      method: METHOD_DELETE
    });
  }
  async function UpdateBookingStatusAfterReturnEquipment(id) {
    return await AxiosFactory({
      url: `${UPDATE_BOOKING_STATUS}/${id}/return`,
      method: METHOD_POST
    });
  }
  async function GetRentalsInProgress(itemsPerPage, lastId) {
    const url = lastId
      ? `${GET_RENTALS_IN_PROGRESS}?limit=${itemsPerPage}&status=accepted&last_id=${lastId}`
      : `${GET_RENTALS_IN_PROGRESS}?limit=${itemsPerPage}&status=accepted`;
    return await AxiosFactory({
      url,
      method: METHOD_GET
    });
  }
  async function GetRentalInSummary(itemsPerPage, lastId) {
    const url = lastId
      ? `${GET_RENTAL_IN_SUMMARY}?limit=${itemsPerPage}&status=accepted&last_id=${lastId}`
      : `${GET_RENTAL_IN_SUMMARY}?limit=${itemsPerPage}&status=accepted`;
    return await AxiosFactory({
      url,
      method: METHOD_GET
    });
  }

  const value = {
    GetEquipmentByID,
    AddEquipments,
    GetAllEquipments,
    GetRentalsInProgress,
    DeleteAllEquipments,
    UpdateBatchOfEquipmentByName,
    GetRentalInSummary,
    DeleteEquipment,
    AirTableSynchronization,
    UpdateBookingStatusAfterReturnEquipment,
    UpdateEquipmentStatus,
    AddSingleEquipment,
    UpdateEquipment
  };

  return (
    <EquipmentContext.Provider value={value}>
      {children}
    </EquipmentContext.Provider>
  );
}
