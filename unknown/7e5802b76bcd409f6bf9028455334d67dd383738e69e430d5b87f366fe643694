.modal {
  overflow-y: hidden;

  .modal-content {
    &.credit-check-modal {
      height: 90%;
      min-width: 95%;
      background-color: $white;
      border-radius: 8px;
      padding: 30px 17px 30px 17px;
      @media (min-width: 992px) {
        padding: 60px 20px 30px 40px;
      }
      @media (min-width: 1300px) {
        min-width: 80%;
      }

      .credit-check-modal__section {
        margin-top: 15px;
        @media (min-width: 992px) {
          margin-top: 20px;
        }

        &:first-child {
          margin-top: 0;
        }

        h2 {
          margin: 10px 0px;
          line-height: normal;
        }
      }

      .scrollbar {
        overflow-y: scroll;
        padding-right: 20px;
        @media (min-width: 992px) {
          padding-right: 40px;
        }

        &::-webkit-scrollbar-track {
          border-radius: 30px;
          background-color: $light-grey;
        }

        &::-webkit-scrollbar {
          width: 6px;
          background-color: $light-grey;
          border-radius: 30px;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 30px;
          background-color: $yellow;
        }
      }

      .m-b-0 {
        margin-bottom: 0;
      }

      .form-group {
        .pt-0 {
          padding-left: calc(var(--bs-gutter-x) * .5) !important;
          padding-right: calc(var(--bs-gutter-x) * .5) !important;
        }

        .MuiGrid-container {
          margin-top: 0 !important;
          margin-left: 0 !important;

          .MuiGrid-root {
            &:first-child {
              padding-left: 0 !important;
            }

            &:last-child {
              padding-right: 0 !important;
            }
          }
        }
      }

      .ml {
        padding-right: 0 !important;
        margin-left: 0 !important;
      }

      .absolute-error {
        .error-message {
          position: absolute;
        }
      }

      .round-button {
        transition: none;
      }
    }
  }

  .close-button {
    width: 25px;
    height: 25px;
    position: absolute;
    top: 10px;
    right: 30px;
    background: none;
    @media (min-width: 992px) {
      top: 15px;
      right: 15px;
    }
  }
}

.credit-check-modal-top {
  border-radius: 30px;
  background-color: #fff;
  height: 400px;
  box-shadow: 0px 2px 10px rgb(199, 199, 199);
  padding: 3%;
}

.credit-check-modal-inputs {
  border-radius: 3px;
  box-shadow: 0px 2px 3px 0px 3px rgb(199, 199, 199);
  padding: 3%;
}

.border-section {
  border: 1px solid $check-grey;
  border-radius: 7px;
  padding: 15px;

  h3 {
    margin-bottom: 20px;
    text-align: left;
  }

  .form-group {
    .form-control {
      width: 100%;
    }
  }

  button {
    padding: 5px 10px;
    min-width: 80px;
    margin-right: 20px;
  }

  .yes-no-block {
    margin-bottom: 20px;
    line-height: 28px;

    p {
      margin-right: 20px;
      @media (min-width: 992px) {
        margin-bottom: 0 !important;
      }
    }

    .form-group {
      margin-top: 20px !important;
      margin-right: 0;
      @media (min-width: 992px) {
        margin-top: 0;
      }

      .form-control {
        margin-bottom: 0;
      }
    }

    button {
      @media (max-width: 992px) {
        min-width: 60px;
        padding: 2px 10px;
      }
    }
  }

  .minus {
    svg {
      margin-top: 30px;
      padding: 0;
      @media (min-width: 992px) {
        margin-top: 40px;
      }
    }
  }
}

.credit-check-modal {
  .form-group {


    .pt-0 {
      padding-top: 0 !important;
    }


  }
}
