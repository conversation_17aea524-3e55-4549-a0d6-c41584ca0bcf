import React from 'react';
import { Navbar<PERSON>rand } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLinkedinIn, faFacebookF } from '@fortawesome/free-brands-svg-icons';
import {
  faQuestion,
  faUser,
  faEnvelopeOpen,
  faCopyright
} from '@fortawesome/fontawesome-free-solid';
import { useNavigate } from 'react-router-dom';
import NeedInformation from '../../components/home/<USER>';
import SignInModal from '../../shared/components/modals/Sign_in_modal';
import { getCookies, clearCookies } from '../../shared/helpers/Cookies';
import { clearSessionStorage } from '../../shared/helpers/Session_storage_helper';

export default function Footer({
  t,
  detectLanguage,
  setShowFPModal,
  showFPModal,
  signIn,
  show,
  setShow
}) {
  const navigate = useNavigate();
  const token = getCookies('token');
  const icons = [
    {
      icon: faLinkedinIn,
      path: 'https://www.linkedin.com/company/derental-inc'
    },
    {
      icon: faFacebookF,
      path: 'https://facebook.com/DerentalEquipment'
    }
  ];
  const logout = () => {
    navigate('/');
    clearCookies();
    clearSessionStorage();
  };
  const handleExploreClick = () => {
    // Navigate to the first step of the MobileSearch form
    const mobileSearchElement = document.getElementById('mobile-search'); // Assuming you will add an id to the MobileSearch component
    if (mobileSearchElement) {
      mobileSearchElement.scrollIntoView({ behavior: 'smooth' });
    } else {
      navigate('/#mobile-search'); // Fallback to home if the element is not found
      setTimeout(() => {
        const mobileSearchElement = document.getElementById('mobile-search');
        if (mobileSearchElement) {
          mobileSearchElement.scrollIntoView({ behavior: 'smooth' });
        }
      }, 200); // Delay the scroll to allow time for the page to load
    }
  };
  return (
    <>
      <SignInModal
        t={t}
        detectLanguage={detectLanguage}
        setShowFPModal={setShowFPModal}
        showFPModal={showFPModal}
        signIn={signIn}
        show={show}
        setShow={setShow}
      />
      <footer className="sub-footer">
        <div className="container">
          {window.location.pathname === '/' && <NeedInformation t={t} />}
        </div>
        <div className="container-fluid">
          <div className="row">
            <div className="footer-link">
              <div className="container mt-5">
                <div className="row mt-2">
                  <div className="col-md-4 col-lg-4 col-xl-3 mb-4">
                    <div className="navbar-translate">
                      <NavbarBrand
                        data-placement="bottom"
                        style={{
                          fontWeight: 600,
                          color: 'white',
                          fontSize: 27
                        }}
                        href="/"
                        onClick={() => clearSessionStorage()}
                      >
                        <span>De</span>
                        rental
                      </NavbarBrand>
                      <p className="t-body-regular c-white mt-3">
                        {t('Text_footer_bottom')}
                      </p>
                      <div className="socials_footer">
                        <p className="t-body-regular c-white mt-3 mb-2 connect-text">
                          {t('Need_more_info_text_social')}
                        </p>
                        <div>
                          {icons.map((item, key) => (
                            <a
                              href={item.path}
                              className="me-2 text-reset"
                              key={key}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <FontAwesomeIcon icon={item.icon} color="white" />
                            </a>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-2 col-lg-2 col-xl-2 mx-auto mb-4 ">
                    <h6 className="mb-3 nav-Link footer-a t-subheading-2 text-start c-white">
                      {t('Company')}
                    </h6>
                    <div className="align-items-center">
                      <div className="footer-column">
                        <ul className="nav flex-column">
                          <li className="nav-item">
                            <a
                              className="nav-link footer-a t-body-regular c-white"
                              href="/ourStory"
                            >
                              {t('Our_story')}
                            </a>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-2 col-lg-2 col-xl-2 mx-auto mb-4 ">
                    <h6 className="mb-3 nav-Link footer-a t-subheading-2 text-start c-white">
                      {t('Legal')}
                    </h6>
                    <div className="align-items-center">
                      <div className="footer-column">
                        <ul className="nav flex-column">
                          <li className="nav-item">
                            <a
                              className="nav-link footer-a t-body-regular c-white"
                              href="/terms&conditions"
                            >
                              {t('Terms_condition')}
                            </a>
                          </li>
                          <li className="nav-item ">
                            <a
                              className="nav-link footer-a t-body-regular c-white"
                              href="/privacyPolicy"
                            >
                              {t('Privacy_notice')}
                            </a>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-2 col-lg-2 col-xl-2 mx-auto mb-4 ">
                    <h6 className="mb-3 nav-Link footer-a t-subheading-2 text-start c-white">
                      {t('Links')}
                    </h6>
                    <div className="align-items-center">
                      <div className="footer-column">
                        <ul className="nav flex-column">
                          <li className="nav-item">
                            <a
                              className="nav-link footer-a t-body-regular c-yellow"
                              href="/SignUpAsLodger"
                            >
                              {t('Become_lodger')}
                            </a>
                          </li>
                          <li className="nav-item">
                            <a
                              className="nav-link footer-a t-body-regular c-white"
                              href="/availableInventory"
                            >
                              {t('Explore_equipment')}
                            </a>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-2 col-lg-3 col-xl-3 mx-auto mb-4 ">
                    <h6 className="mb-3 nav-Link footer-a t-subheading-2 text-start c-white">
                      {t('Contact')}
                    </h6>
                    <div className="align-items-center">
                      <div className="footer-column last-column">
                        <ul className="nav flex-column">
                          <li className="nav-item">
                            <a
                              className="nav-link footer-a t-body-regular c-white"
                              href="tel:************"
                            >
                              {'  '}USA: (+1) ************
                            </a>
                          </li>
                          <li className="nav-item">
                            <a
                              className="nav-link footer-a t-body-regular c-white"
                              href="tel:55-172-2746"
                            >
                              {'  '} SAUDI: (+966) 59 980 8901
                            </a>
                          </li>
                          <li className="nav-item">
                            <a
                              className="nav-link footer-a t-body-regular c-white"
                              href="tel:54-995-5400"
                            >
                              {'  '}CANADA: (+1) ************
                            </a>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="footer-column d-flex justify-content-start justify-content-lg-end pt-4 pb-4 mb-4 border-bottom">
                    <div className="white nav-Link footer-a">
                      <ul className="d-md-flex align-items-center justify-content-end padding-l-0">
                        <li className="nav-item me-4">
                          <a
                            className="nav-link footer-a t-body-regular c-white"
                            href="mailto:<EMAIL>"
                          >
                            <FontAwesomeIcon
                              icon={faEnvelopeOpen}
                              className="c-white me-1"
                            />
                            {'  '}
                            <EMAIL>
                          </a>
                        </li>
                        <li className="nav-item">
                          <span className="nav-link footer-a t-body-regular c-white">
                            <FontAwesomeIcon
                              icon={faCopyright}
                              className="c-white me-1"
                            />{' '}
                            Derental {new Date().getFullYear()}
                          </span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
      <div className="sticky-bar d-lg-none d-block">
        <div className="container">
          <div className="row align-items-center justify-content-center">
            <div className="col-8 mx-auto">
              <div className="row">
                <div className="col-4">
                  <a
                    href="#explore"
                    className="d-block text-center c-near-grey"
                    onClick={handleExploreClick}
                  >
                    <FontAwesomeIcon icon="search" />
                    <span className="t-base-large c-near-grey d-block">
                      {t('Explore_footer')}
                    </span>
                  </a>
                </div>
                <div className="col-4">
                  <a href="/Help" className="d-block text-center c-near-grey">
                    <FontAwesomeIcon icon={faQuestion} color="c-near-grey" />
                    <span className="t-base-large c-near-grey d-block">
                      {t('Help')}
                    </span>
                  </a>
                </div>

                <div className="col-4">
                  <a
                    href="#logIn"
                    className="d-block text-center c-near-grey"
                    onClick={() => (token ? logout() : setShow(true))}
                  >
                    <FontAwesomeIcon icon={faUser} color="c-near-grey" />
                    <span className="t-base-large c-near-grey d-block">
                      {token ? t('Logout_noun') : t('Login')}
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
