import React from 'react';
import { connectHighlight } from 'react-instantsearch-dom';
import { cutString } from '../../shared/helpers/String_helps';

const Highlight = ({
  highlight, attribute, hit, className
}) => {
  const parsedHit = highlight({
    highlightProperty: '_highlightResult',
    attribute,
    hit
  });

  return (
    <span>
      {parsedHit?.map(
        (part, index) => (part.isHighlighted ? (
          <mark
            className={className}
            data-toggle="tooltip"
            data-placement="right"
            title={part.value}
            key={index}
          >
            {cutString(part.value, 23)}
          </mark>
        ) : (
          <span
            className={className}
            tooltip={part.value}
            data-toggle="tooltip"
            data-placement="right"
            title={part.value}
            key={index}
          >
            {cutString(part.value, 23)}
          </span>
        ))
      )}
    </span>
  );
};

export const CustomHighlight = connectHighlight(Highlight);
