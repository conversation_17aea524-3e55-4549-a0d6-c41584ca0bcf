package firestoredb

import (
	"context"
	"errors"
	"fmt"

	"google.golang.org/api/iterator"

	"github.com/vima-inc/derental/models"
)

const memberCollectionName = "members"

// GetMembers returns lodger members.
func (f *firestoredb) GetMembers(ctx context.Context, uid string) (models.Members, error) {
	path := fmt.Sprintf("%s/%s/members", memberCollectionName, uid)
	rows := f.client.Collection(path).Documents(ctx)

	var members models.Members

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return models.Members{}, fmt.Errorf("unable to retrieve member with uid: %s error: %w", uid, err)
		}

		var member models.Member

		err = doc.DataTo(&member)
		if err != nil {
			return models.Members{}, fmt.<PERSON><PERSON>rf("unable to parse member with uid: %s error: %w", uid, err)
		}

		switch member.Type {
		case models.Admin:
			if members.Admins == nil {
				members.Admins = []models.Member{}
			}

			members.Admins = append(members.Admins, member)
		case models.PowerCollaborator:
			if members.PowerCollaborators == nil {
				members.PowerCollaborators = []models.Member{}
			}

			members.PowerCollaborators = append(members.PowerCollaborators, member)
		case models.Collaborator:
			if members.Collaborators == nil {
				members.Collaborators = []models.Member{}
			}

			members.Collaborators = append(members.Collaborators, member)
		case models.AccountManager:
			if members.AccountManagers == nil {
				members.AccountManagers = []models.Member{}
			}

			members.AccountManagers = append(members.AccountManagers, member)
		}
	}

	return members, nil
}

// GetMemberByID returns a member by id.
func (f *firestoredb) GetMemberByID(ctx context.Context, memberID string, lodgerID string) (models.Member, error) {
	snapshot, err := f.client.Collection(memberCollectionName).Doc(lodgerID).Collection(memberCollectionName).Doc(memberID).Get(ctx)
	if err != nil {
		return models.Member{}, fmt.Errorf("unable to retrieve member with id: %s error: %w", lodgerID, err)
	}

	var member models.Member

	err = snapshot.DataTo(&member)
	if err != nil {
		return models.Member{}, fmt.Errorf("unable to parse member with id: %s error: %w", lodgerID, err)
	}

	member.ID = snapshot.Ref.ID

	return member, nil
}

// AddMember add a new member.
func (f *firestoredb) AddMember(ctx context.Context, lodgerID string, member models.Member) (models.Member, error) {
	path := fmt.Sprintf("%s/%s/members", memberCollectionName, lodgerID)
	newDoc := f.client.Collection(path).NewDoc()
	member.LodgerID = lodgerID
	member.ID = newDoc.ID

	_, err := newDoc.Set(ctx, member)
	if err != nil {
		return member, fmt.Errorf("unable to add member with uid: %s error: %w", lodgerID, err)
	}

	return member, nil
}

// UpdateMember updates an existing member.
func (f *firestoredb) UpdateMember(ctx context.Context, uid string, member models.Member) error {
	path := fmt.Sprintf("%s/%s/members", memberCollectionName, uid)

	_, err := f.client.Collection(path).Doc(member.ID).Set(ctx, member)
	if err != nil {
		return fmt.Errorf("unable to update member with uid: %s error: %w", uid, err)
	}

	return nil
}

// DeleteMember deletes an existing members by id.
func (f *firestoredb) DeleteMember(ctx context.Context, uid string, memberID ...string) error {
	path := fmt.Sprintf("%s/%s/members", memberCollectionName, uid)

	batch := f.client.BulkWriter(ctx)

	for _, id := range memberID {
		_, err := batch.Delete(f.client.Collection(path).Doc(id))
		if err != nil {
			return fmt.Errorf("unable to delete member with uid: %s error: %w", uid, err)
		}
	}

	batch.End()

	return nil
}

// GetSelectionList returns a selection list of members with privileges less than or equal to the given member.
func (f *firestoredb) GetSelectionList(ctx context.Context, member models.Member) ([]models.Member, error) {
	path := fmt.Sprintf("%s/%s/members", memberCollectionName, member.LodgerID)
	rows := f.client.Collection(path).Where("membership_status", "!=", models.MemberPending).Documents(ctx)

	var members []models.Member

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return members, fmt.Errorf("unable to retrieve members with teamID: %s error: %w", member.LodgerID, err)
		}

		var memberTmp models.Member

		err = doc.DataTo(&memberTmp)
		if err != nil {
			return members, fmt.Errorf("unable to parse member with TeamID: %s error: %w", memberTmp.LodgerID, err)
		}

		if member.PrivilegeLevel >= memberTmp.PrivilegeLevel {
			members = append(members, memberTmp)
		}
	}

	return members, nil
}

// invitationExists checks if an invitation exists for the given email.
func (f *firestoredb) InvitationExists(ctx context.Context, email string, memberOf string) (bool, error) {
	path := fmt.Sprintf("%s/%s/members", memberCollectionName, memberOf)
	rows := f.client.Collection(path).Where("email", "==", email).Documents(ctx)

	var members []models.Member

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return true, fmt.Errorf("unable to retrieve members with teamID: %s error: %w", memberOf, err)
		}

		var memberTmp models.Member

		err = doc.DataTo(&memberTmp)
		if err != nil {
			return true, fmt.Errorf("unable to parse member with TeamID: %s error: %w", memberTmp.LodgerID, err)
		}

		members = append(members, memberTmp)
	}

	return len(members) == 0, nil
}

// GetAdminsByOwnerID returns a list of admins by owner id.
func (f *firestoredb) GetAdminsByOwnerID(ctx context.Context, ownerID string) ([]models.Member, error) {
	path := fmt.Sprintf("%s/%s/members", memberCollectionName, ownerID)
	rows := f.client.Collection(path).Where("type", "==", "admin").Documents(ctx)

	var members []models.Member

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return members, fmt.Errorf("unable to retrieve members with teamID: %s error: %w", ownerID, err)
		}

		var member models.Member

		err = doc.DataTo(&member)
		if err != nil {
			return members, fmt.Errorf("unable to parse member with TeamID: %s error: %w", member.LodgerID, err)
		}

		members = append(members, member)
	}

	return members, nil
}
