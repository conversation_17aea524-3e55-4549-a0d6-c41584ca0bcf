import React from 'react';
import { Button } from 'react-bootstrap';
import { faSlidersH } from '@fortawesome/fontawesome-free-solid';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useTranslation } from 'react-i18next';
import map_icon from '../../../style/assets/img/Icons/map_icon.svg';

export default function SwitchMapsFilter(props) {
  const { t } = useTranslation();

  return (
    <div className="map-button-container mb-2">
      {props.isMap ? (
        <Button
          className="button-style filter-btn isSm t-base-large extraBold c-primary-color"
          onClick={props.showMap}
        >
          <FontAwesomeIcon icon={faSlidersH} color="white" className="mr-3" />
          {t('Filters')}
        </Button>
      ) : (
        <Button
          className="button-style isSM t-base-large extraBold c-primary-color"
          onClick={props.showMap}
        >
          <img src={map_icon} alt="icon map" />
          {t('Show_map_text')}
        </Button>
      )}
    </div>
  );
}
