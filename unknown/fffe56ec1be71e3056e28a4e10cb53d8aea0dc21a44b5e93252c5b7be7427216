.NavBarSearchBar {
  background-color: white;
  border: 1px solid #eca869;
  border-radius: 10px;
  vertical-align: middle;
  padding: 0;

  .border-gradient-right {
    .end_date {
      @media (min-width: 992px) {
        position: relative;
        left: -60px;
      }
    }
  }
}

.searchInput-navbar {
  background-color: transparent;
  height: 40px;
  width: 100%;
  border: 0;
  box-shadow: none;
  padding-top: 4px;
}

.searchInput-navbar::placeholder {
  color: #000;
}

.labelless-date-picker {
  background-color: transparent;
  height: 40px;
  width: 100%;
  font-weight: bold;
  border: 0;
  box-shadow: none;
}

.labelless-date-picker::placeholder {
  color: #000;
}

.labelless-date-picker::selection {
  background: #f1f1f1; /* WebKit/Blink Browsers */
}

.searchInput-navbar {
  &:focus {
    outline: none;
    box-shadow: none;
  }
}

.searchDates {
  background-color: transparent;
  height: 40px;
  width: 100%;
  border: 0;
  box-shadow: none;

  &:focus {
    outline: none;
    box-shadow: none;
  }
}

.searchDates::selection {
  background: #f1f1f1; /* WebKit/Blink Browsers */
}

.labeless {
  border-right: 3px solid #eca869;
}

.navbarMargin {
  margin-left: 100px;
}

.button-search-navbar {
  background-color: #eca869;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  margin-top: 7px;
  align-self: end;
  margin-right: -12px;
  position: relative;
  z-index: 3;

  svg {
    color: #ffff;
  }

  &.disabled {
    opacity: 0.8;
  }
}

.border-gradient-right {
  border-right: 0px solid;
  border-left: 0px solid;
  border-top: 0px solid;
  border-bottom: 0px solid;
  border-image-slice: 0 1 0 0;
  border-width: 1px;
  max-height: 70px;

  .end_date {
    padding-left: 20px;
  }
}

.searchInput-z {
  padding-bottom: 0;
}

.border-gradient-yellow {
  border-image-source: linear-gradient(
    to bottom,
    white 20%,
    #e5ecf6 20%,
    #e5ecf6 80%,
    #ffffff 80%,
    white 100%
  );
}

.border-gradient-yellow-navbar {
  border-image-source: linear-gradient(
    to bottom,
    white 20%,
    #f2f2f2 20%,
    #f2f2f2 80%,
    #ffffff 80%,
    white 100%
  );
  height: 45px;
  padding-left: 15px;
}

.disapear-on-collapse {
  padding: 1% 0;

  .react-daterange-picker__inputGroup {
    height: 100%;
    flex-grow: 1;
    padding: 4% 30% 0 0;
    box-sizing: content-box;
  }
}

.border-gradient-far-right {
  border: 0 solid;
}

@media (max-width: 992px) {
  .disapear-on-collapse {
    display: none !important;
  }
  .border-gradient-right {
    padding: 5px;
    background: white;
    margin-bottom: 5px !important;
    border: 0.5px solid #eca869;
    border-radius: 15px;
  }
}

.moxy {
  .css-1s2u09g-control {
    -webkit-align-items: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border: none;
    background-color: hsla(0, 0%, 100%, 0);
    cursor: default;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    min-height: 30px;
    outline: 0 !important;
    position: relative;
    -webkit-transition: all 100ms;
    transition: all 100ms;
    box-sizing: border-box;
    margin: 2px 3px 0 0;
    text-transform: capitalize;
  }

  .css-1pahdxg-control {
    -webkit-align-items: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border: none;
    background-color: hsla(0, 0%, 100%, 0);
    cursor: default;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    box-shadow: none;
    -webkit-box-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    min-height: 30px;
    outline: 0 !important;
    position: relative;
    -webkit-transition: all 100ms;
    transition: all 100ms;
    box-sizing: border-box;
    margin: 2px 3px 0 0;
    text-transform: capitalize;
  }
}

.css-319lph-ValueContainer {
  padding-left: 0 !important;
}

.NavBarSearchBar {
  .siac {
    width: 100%;
    padding-top: 4% !important;
    margin: 0 !important;
  }
}

.NavBarSearchBar {
  .search-input-auto-complete {
    background-color: transparent !important;
    width: 100%;
  }
}
