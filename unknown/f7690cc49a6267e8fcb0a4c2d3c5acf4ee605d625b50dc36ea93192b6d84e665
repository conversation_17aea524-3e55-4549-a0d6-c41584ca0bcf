
.bidz_company_spotlihght {
  .mb-32 {
    margin-bottom: 32px;
  }

  .col-lg-8 {
    @media(min-width: 992px) {
      width: 55%;
    }
  }

  &__left {
    padding: 43px 0 20px 0;
    border-right: 1px solid $border-grey;
    @media(min-width: 992px) {
      padding: 43px 48px;
    }

    img {
      margin: 32px 0;
    }
  }

  &__right {
    .breadcrumb {
      padding: 0px 0px 40px 0px;

      .container {
        padding: 0;
      }
    }

    .specification_equipment {
      padding: 30px;
      border-radius: 14px;
      border: 1px solid $border-grey;
      background: $white;
      box-shadow: 0 4px 20px 0 rgba(173, 173, 173, 0.15);
      margin-top: 45px;

      &__head {
        margin-bottom: 7px;

        img {
          max-width: 170px;
        }
      }

      &__content {
        margin-bottom: 32px;

        .form-group {
          margin-top: 10px;

          .form-control {
            width: 100%;
            height: 50px;

            option[value=""][disabled] {
              color: $neutrals-gray;
            }

            &::-webkit-input-placeholder { /* Chrome/Opera/Safari */
              color: $neutrals-gray;
            }

            &::-moz-placeholder { /* Firefox 19+ */
              color: $neutrals-gray;
            }

            &:-ms-input-placeholder { /* IE 10+ */
              color: $neutrals-gray;
            }

            &:-moz-placeholder { /* Firefox 18- */
              color: $neutrals-gray;
            }

            &.textarea {
              min-height: 125px;
            }
          }
        }

        .button_content {
          button {
            font-size: 17px;
            font-style: normal;
            font-weight: 700;
            line-height: 30px;
            text-transform: capitalize;
            background: none;
            margin-top: 16px;
            margin-bottom: 16px;
            position: relative;
            padding-left: 24px;

            &:before {
              content: url("../style/assets/img/company_spotlight/Plus.svg");
              position: absolute;
              left: 0;
              top: 3px;
            }
          }
        }

        .col-modified {
          width: 98%;
          position: relative;
          @media(min-width: 992px) {
            width: 48%;
          }

          button {
            right: -15px;
            top: 53%;
          }
        }
      }

      .round-button {
        min-width: 225px;
      }
    }
  }
}