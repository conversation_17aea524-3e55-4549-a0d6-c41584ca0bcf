import Modal from '../../../shared/components/modals/Modal';
import RenderIf from '../../../shared/components/Render_if';
import Input from '../../../shared/components/forms/Input';
import { ErrorMessage, Form, Formik } from 'formik';
import * as Yup from 'yup';
import CustomButton from '../../../shared/components/buttons/Custom_button';
import { useEquipment } from '../../../shared/context/Equipment_context';
import MultipleSelectCheckmarks from '../../../shared/components/multi_select/Multi_select_mui';
import { useState } from 'react';

export default function MinimumRentalPeriodModal({
  show,
  t,
  equipments,
  onClose,
  handleShowFailure,
  handleShowSuccess
}) {
  const { UpdateBatchOfEquipmentByName } = useEquipment();
  const [isLoading, setIsLoading] = useState(false);

  const validate = Yup.object({
    minimum_rental_period: Yup.number()
      .min(1, t('Minimum_rental_period_must_be_greater_than_0'))
      .required(t('This_field_is_required')),
    equipment_list: Yup.array()
      .required(t('This_field_is_required'))
      .nullable()
      .min(1, t('This_field_is_required'))
  });

  const updateBatchOfEquipmentByName = async (data) => {
    setIsLoading(true);
    const { status } = await UpdateBatchOfEquipmentByName({
      equipment_names: data?.equipment_list,
      minimum_rental_period: data?.minimum_rental_period
    });
    setIsLoading(false);

    if (status === 200) {
      onClose();
      handleShowSuccess();
    } else {
      handleShowFailure();
    }
  };

  const onChange = (value, name, formik) => {
    formik.setFieldValue(name, value);
  };

  return (
    <RenderIf condition={show}>
      <Modal
        t={t}
        noScrollModal
        onClose={onClose}
        description="Minimum_rental_period"
        className="mrp"
        subTitle="Enter_minimum_rental_duration"
        classNameDescription="t-header-h5 c-grey-titles mt-2 pt-2"
      >
        <Formik
          initialValues={{
            equipment_list: null,
            minimum_rental_period: 0
          }}
          validationSchema={validate}
          onSubmit={(values) => {
            updateBatchOfEquipmentByName(values);
          }}
        >
          {(formik) => {
            return (
              <Form>
                <div className="row mt-2 pt-2">
                  <div className="col-lg-12">
                    <p className="form-group">
                      <Input
                        label={t('Minimum_rental_period')}
                        name="minimum_rental_period"
                        type="number"
                        placeholder={t('Enter_minimum_rental_duration')}
                      />
                    </p>
                    <p className="subTitle-modal">
                      {t('MRP_names_select_info')}
                    </p>
                  </div>
                </div>
                <div className="row mt-1 pt-1">
                  <div className="col-lg-12">
                    <p className="form-group">
                      <label className="label-input d-lg-block">
                        {t('Derental_equipment_name')}
                        <span className="c-red star-required"> * </span>
                      </label>

                      <MultipleSelectCheckmarks
                        options={equipments}
                        t={t}
                        id="equipment_list_id"
                        name="equipment_list"
                        onChange={(value) =>
                          onChange(value, 'equipment_list', formik)
                        }
                        placeholder={t('Select_equipments')}
                      />
                      <ErrorMessage
                        name="equipment_list"
                        component="span"
                        className="error-message"
                      />

                      <p className="subTitle-modal">
                        {t('Equipment_names_select_info')}
                      </p>
                    </p>
                  </div>
                </div>
                <div className="text-center mt-4 fixed-button-modal">
                  <CustomButton
                    className="round-button black transparent bold mb-3"
                    onClick={onClose}
                    textButton={t('Cancel')}
                  />

                  <CustomButton
                    className="round-button yellow bold   d-inline-flex align-items-center justify-content-center "
                    textButton={t('Apply_changes')}
                    isLoading={isLoading}
                    type="submit"
                  />
                </div>
              </Form>
            );
          }}
        </Formik>
      </Modal>
    </RenderIf>
  );
}

MinimumRentalPeriodModal.defaultProps = {
  show: false,
  onClose: () => {}
};
