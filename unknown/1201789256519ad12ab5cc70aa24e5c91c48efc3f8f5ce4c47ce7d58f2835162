import React from 'react';
import { useParams } from 'react-router-dom';
import { faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const BreadCrumb = ({
  items,
  t

}) => {
  const { name } = useParams();
  const equipmentName = name || ' ';
  const category = sessionStorage.getItem('category');

  const renderSeparator = () => (
    <span className="sep">
      <FontAwesomeIcon icon={faChevronRight} color="c-primary-color" />
    </span>
  );

  return (
    <div className="breadcrumb">
      <div className="container">
        <a className="t-body-small c-neutrals-gray no-underline hb" href="/">
          {t('Home')}
        </a>
        {(!items || items.length === 0) && category && (
          <>
            {renderSeparator()}
            <span className="t-body-small c-neutrals-gray ">{category.trim()}</span>
          </>
        )}
        {equipmentName !== ' ' && equipmentName.length !== 0 && (
          <>
            {renderSeparator()}
            <span className="t-body-small c-neutrals-gray ">{equipmentName}</span>
          </>
        )}
        {items?.map((item, index) => (
          <span key={index}>
           {item.label && renderSeparator()}
            <span className="t-body-small c-fake-black" onClick={item.onClick}>
              {item.label}
            </span>
          </span>
        ))}
      </div>
    </div>
  );
};

BreadCrumb.defaultProps = {
  items: []
};

export default BreadCrumb;
