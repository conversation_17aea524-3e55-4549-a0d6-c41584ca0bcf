package api

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

func signin(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var body models.SignInInput

		err := c.BindJSON(&body)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		resp, err := svc.SignIn(c.Request.Context(), body)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func signup(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var signupInput models.SignUpInput

		err := c.Bind<PERSON>(&signupInput)
		if err != nil {
			c.J<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		memberID := c.Query("member_id")
		lodgerID := c.Query("lodger_id")

		err = signupInput.Validate()
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		resp, err := svc.SignUp(c.Request.Context(), signupInput, memberID, lodgerID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func existByEmail(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		email := c.Param("email")

		userID, err := svc.ExistByEmail(c.Request.Context(), email)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, gin.H{
			"email":   email,
			"user_id": userID,
		})
	}
}

func forgotPassword(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		email := c.Param("email")
		if email == "" {
			err := fmt.Errorf("email is required")
			c.JSON(http.StatusBadRequest, gin.H{"error": err})
			_ = c.Error(err)
			return
		}
		err := svc.ForgotPassword(c.Request.Context(), email)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}
		c.Status(http.StatusOK)
	}
}

func resetPassword(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var body models.PasswordResetInput

		err := c.BindJSON(&body)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = svc.ResetPassword(c.Request.Context(), body)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func validateToken(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var body models.PasswordResetInput

		err := c.BindJSON(&body)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = svc.ValidateToken(c.Request.Context(), body)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}
