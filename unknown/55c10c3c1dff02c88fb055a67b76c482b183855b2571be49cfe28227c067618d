import React from 'react';
import { Alert } from 'react-bootstrap';
import ReactCodeInput from 'react-code-input';

export default function VerifyCodeModal({
  onClose,
  t,
  verifyCode,
  handlePinChange,
  error,
  code
}) {
  return (
    <>
      <button className="close-button" onClick={onClose}>
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M19.25 5.25L5.75 18.75"
            stroke="#333333"
            strokeWidth="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M19.25 18.75L5.75 5.25"
            stroke="#333333"
            strokeWidth="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
      <div className="text-center">
        <h3 className="FPmodal-title t-header-h6 bold">
          {t('Verification_code')}
        </h3>
        <p className="t-header-medium CVmodal-pragraph">{t('Enter_code')}</p>
        <div className="container-btn-vc">
          <ReactCodeInput
            className="input-code"
            type="text"
            value={code}
            fields={6}
            onChange={handlePinChange}
          />
        </div>
        {error && (
          <Alert variant="danger" className="mt-4">
            {error}
          </Alert>
        )}
        <div className="fixed-button-modal">
          <button
            className="button-signup fwb-700 round-button yellow bold c-primary-color mt-4"
            onClick={verifyCode}
          >
            {t('Verify')}
          </button>
        </div>
      </div>
    </>
  );
}
