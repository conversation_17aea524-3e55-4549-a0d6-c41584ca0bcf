import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Formik, Field, Form, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { useNavigate, useLocation } from 'react-router-dom';
import { Alert } from 'react-bootstrap';
import { useAuth } from '../../shared/context/Auth_context';
import {
  EMAIL_NOT_FOUND,
  INVALID_PASSWORD
} from '../../shared/helpers/Error_messages';
import RenderIf from '../../shared/components/Render_if';
import { setCookies } from '../../shared/helpers/Cookies';
import { clearSessionStorage } from '../../shared/helpers/Session_storage_helper';

export default function SignIn({ show, onClose, showFPM, t, signIn }) {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const { Login } = useAuth();
  const [error, setError] = useState('');

  const initialValues = {
    email: '',
    password: ''
  };

  const validate = Yup.object({
    email: Yup.string()
      .email(t('Invalid_email'))
      .required(t('This_field_is_required')),
    password: Yup.string().required(t('This_field_is_required'))
  });

  const handleSubmit = async (event) => {
    const { data, status, error } = await Login({
      email: event.email,
      password: event.password
    });

    if (status === 200 || status === 201) {
      setCookies('token', data?.idToken, 7200);
      setCookies('role', data?.userType, 7200);
      setCookies('userId', data?.localId, 7200);
      setCookies('email', data?.email, 7200);
      setCookies('imageLink', data?.imageUrl, 7200);

      data?.userType === 'equipper' &&
        setCookies('has_inventory', data?.has_inventory);

      if (data?.userType === 'equipper') {
        clearSessionStorage();
        sessionStorage.setItem('change', false);
        navigate('/equipperManagementPortal');
      } else if (
        pathname.toString().includes('/SearchResult') ||
        pathname.toString().includes('/EquipperSpotlight') ||
        pathname.toString().includes('/companySpotlight') ||
        pathname.toString().includes('/equipmentDetails') ||
        pathname.toString().includes('/bidzSearchResult')
      ) {
        navigate(pathname);
      } else if (signIn) {
        clearSessionStorage();
        sessionStorage.setItem('change', false);
        navigate('/renterManagementPortal');
      } else {
        navigate('/');
      }
      onClose();
    } else if (error.error === EMAIL_NOT_FOUND) {
      setError(t('Failed_email'));
    } else if (error.error === INVALID_PASSWORD) {
      setError(t('Failed_Password'));
    } else {
      setError(t('Failed_to_login'));
    }
  };

  const redirect = () => {
    navigate('/SignUpAsLodger');
    onClose();
  };

  return (
    <RenderIf condition={show}>
      <div className="modal">
        <div className="modal-content">
          <div className="row">
            <div className="col-lg-11 mx-auto">
              <button className="close-button" onClick={onClose}>
                <svg
                  width="25"
                  height="24"
                  viewBox="0 0 25 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M19.25 5.25L5.75 18.75"
                    stroke="#333333"
                    strokeWidth="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M19.25 18.75L5.75 5.25"
                    stroke="#333333"
                    strokeWidth="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
              <h3 className="t-header-h6 bigger text-center c-fake-black">
                {t('Welcome_back')}
              </h3>
              {error && <Alert variant="danger">{error}</Alert>}
              <Formik
                initialValues={initialValues}
                onSubmit={handleSubmit}
                validationSchema={validate}
              >
                {(formik) => (
                  <Form>
                    <div className="login-container">
                      <div className="form-group">
                        <label className="t-body-regular c-fake-black">
                          {t('Email')}
                        </label>
                        <Field
                          placeholder={t('Email')}
                          type="text"
                          name="email"
                          autoComplete="off"
                          className="Input-form form-control full-width"
                        />
                        <ErrorMessage
                          name="email"
                          component="span"
                          className="error-message"
                        />
                      </div>
                      <div className="form-group marg-top-20">
                        <label className="t-body-regular c-fake-black">
                          {t('Password')}
                        </label>
                        <Field
                          placeholder={t('Password')}
                          type="password"
                          name="password"
                          autoComplete="off"
                          className="Input-form form-control full-width"
                        />
                        <ErrorMessage
                          name="password"
                          component="span"
                          className="error-message"
                        />
                      </div>
                      <div className="text-end mt-3">
                        <h6
                          className="t-subheading-4 c-red pointer"
                          onClick={showFPM}
                        >
                          {t('Forget_your_password')}
                        </h6>
                      </div>
                      <div className="text-center fixed-button-modal">
                        <button
                          disabled={
                            formik.isSubmitting ||
                            !(formik.isValid && formik.dirty)
                          }
                          className={`btn-login round-button bold yellow full-width mt-5 ${
                            formik.isSubmitting ||
                            !(formik.isValid && formik.dirty)
                              ? 'disabled'
                              : 'yellow'
                          }`}
                          type="submit"
                        >
                          {t('Login')}
                        </button>
                      </div>
                    </div>
                  </Form>
                )}
              </Formik>
              <div className="create-account text-center">
                <div className="divider">
                  <hr className="col-2 mx-auto c-blue-grey" />
                </div>
                <div className="text-center">
                  <h6 className="t-subheading-4 c-fake-black weight-400">
                    {t('You_dont_have_an_account')}
                  </h6>
                  <h6 className="t-subheading-4 c-fake-black weight-400">
                    <strong className="bold">{t('Create_an_account')}</strong>{' '}
                    {t('As_account')}{' '}
                    <span onClick={redirect} className="c-yellow bold pointer">
                      {t('Renter_account')}
                    </span>
                  </h6>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </RenderIf>
  );
}

SignIn.propTypes = {
  show: PropTypes.bool,
  onClose: PropTypes.func,
  showFPM: PropTypes.func,
  signIn: PropTypes.bool
};

SignIn.defaultProps = {
  show: false,
  signIn: false
};
