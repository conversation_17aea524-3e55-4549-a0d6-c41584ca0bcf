package service

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

const j1 = `
		{
			"id": "rec04aCoMY6PH8MhZ",
			"createdTime": "2022-10-31T13:39:23.000Z",
			"fields": {
				"Capacity": "1 1/2T",
				"Name description [FR]": "ALUMINIUM 32'   ",
				"Equipment name [FR]": "ÉCHELLE",
				"Price per day": 105,
				"Price per week": 330,
				"Price per month": 948,
				"Status": "Under repair",
				"Picture ID": 175
			}
		}
`

const j2 = `
	{
			"id": "rec04aCoMY6PH8MhZ",
			"createdTime": "2022-10-31T13:39:23.000Z",
			"fields": {
				"Weight (lbs)": "4,4 lbs",
				"Height (ft)": "4,1\"",
				"Width (ft)": "3,1\"",
				"Price per day": 20,
				"Price per week": 45,
				"Price per month": 90,
				"Deposit": 50,
				"Weekend price": 30,
				"Status": "Available",
				"Rental center location": "Varennes",
				"Diameter": "5\"",
				"master.inventory copy": "Angle grinder",
				"Sub Category": [
					"Concrete and masonry"
				],
				"Category": [
					"Specialized tooling"
				],
				"Name description [FR]": [
					"Pour béton"
				],
				"Name description [EN]": [
					"For concrete"
				],
				"Equipment name [FR]": [
					"Meuleuse d'angle"
				],
				"Equipment name [EN]": [
					"Angle grinder"
				]
			}
		}
`

const j3 = `
	{
			"id": "rec04aCoMY6PH8MhZ",
			"createdTime": "2022-10-31T13:39:23.000Z",
			"fields": {
				"Weight (lbs)": "4,4 lbs",
				"Height (ft)": "4,1\"",
				"Width (ft)": "3,1\"",
				"Price per day": 20,
				"Price per week": 45,
				"Price per month": 90,
				"Deposit": 50,
				"Weekend price": 30,
				"Status": "Available",
				"Rental center location": "Varennes",
				"Diameter": "5\"",
				"master.inventory copy": "Angle grinder",
				"Sub Category": [
					"Concrete and masonry"
				],
				"Category": [
					"Specialized tooling"
				],
				"Name description [FR]": [
					"Pour béton"
				],
				"Name description [EN]": [
					"For concrete"
				],
				"Equipment name [EN]": [
					"Angle grinder"
				]
			}
		}`

type equipmentForTest struct {
	ID          string        `json:"id"`
	CreatedTime string        `json:"createdTime"`
	Fields      fieldsForTest `json:"fields"`
}

type fieldsForTest struct {
	Capacity          string `json:"Capacity"`
	NameDescriptionFR any    `json:"Name description [FR]"`
	EquipmentNameFR   any    `json:"Equipment name [FR]"`
	PricePerDay       int64  `json:"Price per day"`
	PricePerWeek      int64  `json:"Price per week"`
	PricePerMonth     int64  `json:"Price per month"`
	Status            string `json:"Status"`
	PictureID         int64  `json:"Picture ID"`
}

func TestFormatName(t *testing.T) {
	t.Parallel()

	expectedName1 := "ÉCHELLE"
	expectedName2 := "Meuleuse d'angle"
	expectedName3 := ""

	var e1, e2, e3 equipmentForTest
	err := json.Unmarshal([]byte(j1), &e1)
	assert.NoError(t, err)

	err = json.Unmarshal([]byte(j2), &e2)
	assert.NoError(t, err)

	err = json.Unmarshal([]byte(j3), &e3)
	assert.NoError(t, err)

	assert.Equal(t, expectedName1, formatName(e1.Fields.EquipmentNameFR))
	assert.Equal(t, expectedName2, formatName(e2.Fields.EquipmentNameFR))
	assert.Equal(t, expectedName3, formatName(e3.Fields.EquipmentNameFR))
}
