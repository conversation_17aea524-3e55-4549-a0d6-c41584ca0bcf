package models

import (
	"time"
)

// EquipmentStatus represents the status of a booking.
type EquipmentStatus string

// EquipmentStatus constants.
const (
	EquipmentAvailable EquipmentStatus = "available"
	EquipmentBooked    EquipmentStatus = "booked"
	EquipmentIdle      EquipmentStatus = "idle"
)

// EquipmentAlias represents the lists of alias french & alias english.
type EquipmentAlias struct {
	FR []string `json:"fr" firestore:"fr"`
	EN []string `json:"en" firestore:"en"`
}

// Equipment is a struct representing a Equipment.
type Equipment struct {
	ID                  string          `json:"id" firestore:"id"`
	NameEN              string          `json:"name" firestore:"name"`
	NameFR              string          `json:"name_fr" firestore:"name_fr"`
	Description         string          `json:"description" firestore:"description"`
	DescriptionFR       string          `json:"description_fr" firestore:"description_fr"`
	SubCategory         []string        `json:"sub_category" firestore:"sub_category"`
	Category            []string        `json:"category" firestore:"category"`
	Address             Address         `json:"address" firestore:"address"`
	AvailableFrom       time.Time       `json:"available_from" firestore:"available_from"`
	AvailableTo         *time.Time      `json:"available_to" firestore:"available_to"`
	AvgRating           float64         `json:"avg_rating" firestore:"avg_rating"`
	TotalComments       int             `json:"total_comments" firestore:"total_comments"`
	Price               Price           `json:"price" firestore:"price"`
	EquipperID          string          `json:"equipper_id" firestore:"equipper_id"`
	EquipperName        string          `json:"equipper_name" firestore:"equipper_name"`
	EquipperEmail       string          `json:"equipper_email" firestore:"equipper_email"`
	Status              EquipmentStatus `json:"status" firestore:"status"`
	CreatedAt           time.Time       `json:"created_at" firestore:"created_at"`
	UpdatedAt           time.Time       `json:"updated_at" firestore:"updated_at"`
	CoverageArea        []string        `json:"coverage_area" firestore:"coverage_area"`
	Alias               EquipmentAlias  `json:"alias" firestore:"alias"`
	AliasID             string          `json:"alias_id" firestore:"alias_id"`
	OwnerID             string          `json:"owner_id" firestore:"owner_id"`
	IsActive            bool            `json:"is_active" firestore:"is_active"`
	MinimumRentalPeriod int             `json:"minimum_rental_period" firestore:"minimum_rental_period"`
	// Old but used equipment specs
	ImageLink                string   `json:"image_link" firestore:"image_link"`
	InternalID               string   `json:"internal_id" firestore:"internal_id"`
	Brand                    string   `json:"brand" firestore:"brand"`
	BrandModel               string   `json:"brand_model" firestore:"brand_model"`
	DriveType                []string `json:"drive_type" firestore:"drive_type"`
	Weight                   string   `json:"weight" firestore:"weight"`
	Height                   string   `json:"height" firestore:"height"`
	Width                    string   `json:"width" firestore:"width"`
	Length                   string   `json:"length" firestore:"length"`
	Diameter                 string   `json:"diameter" firestore:"diameter"`
	CutDiameter              string   `json:"cut_diameter" firestore:"cut_diameter"`
	Force                    string   `json:"force" firestore:"force"`
	UsageHours               string   `json:"usage_hours" firestore:"usage_hours"`
	BTU                      string   `json:"btu" firestore:"btu"`
	Volt                     string   `json:"volt" firestore:"volt"`
	Watt                     string   `json:"watt" firestore:"watt"`
	CFM                      string   `json:"cfm" firestore:"cfm"`
	Capacity                 string   `json:"capacity" firestore:"capacity"`
	Consumption              string   `json:"consumption" firestore:"consumption"`
	TypeOfPropulsion         []string `json:"type_of_propulsion" firestore:"type_of_propulsion"`
	PlatformHeight           string   `json:"platform_height" firestore:"platform_height"`
	WorkingHeight            string   `json:"working_height" firestore:"working_height"`
	EquipperEquipmentPicture string   `json:"equipper_equipment_picture" firestore:"equipper_equipment_picture"`
	HorizontalOutreach       string   `json:"horizontal_outreach" firestore:"horizontal_outreach"`
	PlatformCapacity         string   `json:"platform_capacity" firestore:"platform_capacity"`
	PlatformDimension        string   `json:"platform_dimension" firestore:"platform_dimension"`
	PlatformExtension        string   `json:"platform_extension" firestore:"platform_extension"`
	ExtensionCapacity        string   `json:"extension_capacity" firestore:"extension_capacity"`
	PlatformRotation         string   `json:"platform_rotation" firestore:"platform_rotation"`
	MachineRotation          string   `json:"machine_rotation" firestore:"machine_rotation"`
	MachineWidth             string   `json:"machine_width" firestore:"machine_width"`
	MachineLength            string   `json:"machine_length" firestore:"machine_length"`
	MachineHeight            string   `json:"machine_height" firestore:"machine_height"`
	ClosedMachineHeight      string   `json:"closed_machine_height" firestore:"closed_machine_height"`
	ClosedMachineLength      string   `json:"closed_machine_length" firestore:"closed_machine_length"`
	ClosedMachineWidth       string   `json:"closed_machine_width" firestore:"closed_machine_width"`
	BasketCapacity           string   `json:"basket_capacity" firestore:"basket_capacity"`
	BasketLength             string   `json:"basket_length" firestore:"basket_length"`
	BasketWidth              string   `json:"basket_width" firestore:"basket_width"`
	LegsLocation             string   `json:"legs_location" firestore:"legs_location"`
	FloorHeight              string   `json:"floor_height" firestore:"floor_height"`
	CabinHeight              string   `json:"cabin_height" firestore:"cabin_height"`
	Wheelbase                string   `json:"wheelbase" firestore:"wheelbase"`
	WheelSize                string   `json:"wheel_size" firestore:"wheel_size"`
	PlateDimension           string   `json:"plate_dimension" firestore:"plate_dimension"`
	Decibel                  string   `json:"decibel" firestore:"decibel"`
	RollWidth                string   `json:"roll_width" firestore:"roll_width"`
	Compaction               string   `json:"compaction" firestore:"compaction"`
	Vibrations               string   `json:"vibrations" firestore:"vibrations"`
	Lumen                    string   `json:"lumen" firestore:"lumen"`
	Pressure                 string   `json:"pressure" firestore:"pressure"`
	Frequency                string   `json:"frequency" firestore:"frequency"`
	TiltingCapacity          string   `json:"tilting_capacity" firestore:"tilting_capacity"`
	OperationCapacity        string   `json:"operation_capacity" firestore:"operation_capacity"`
	TankCapacity             string   `json:"tank_capacity" firestore:"tank_capacity"`
	DiggingDepth             string   `json:"digging_depth" firestore:"digging_depth"`
	DumpingHeight            string   `json:"dumping_height" firestore:"dumping_height"`
	DiggingRadius            string   `json:"digging_radius" firestore:"digging_radius"`
	TechnicalDataSheet       string   `json:"technical_data_sheet" firestore:"technical_data_sheet"`
	EquipmentUsages          string   `json:"equipment_usages" firestore:"equipment_usages"`
	PreferredEquipmentName   string   `json:"preferred_equipment_name" firestore:"preferred_equipment_name"`
}

// EquipmentChange represents a change made to an equipment
type EquipmentChange struct {
	ID            string         `json:"id" firestore:"id"`
	EquipmentID   string         `json:"equipment_id" firestore:"equipment_id"`
	EquipmentName string         `json:"equipment_name" firestore:"equipment_name"`
	EquipperName  string         `json:"equipper_name" firestore:"equipper_name"`
	Fields        []ChangeFields `json:"fields" firestore:"fields"`
	ChangedAt     time.Time      `json:"changed_at" firestore:"changed_at"`
}

// ChangeFields represents a change made to an equipment field
type ChangeFields struct {
	Field    string `json:"field" firestore:"field"`
	OldValue string `json:"old_value" firestore:"old_value"`
	NewValue string `json:"new_value" firestore:"new_value"`
}

// Price represents the price of a Equipment in a given time.
type Price struct {
	Day                float64 `json:"day" firestore:"day"`
	Week               float64 `json:"week" firestore:"week"`
	Month              float64 `json:"month" firestore:"month"`
	Currency           string  `json:"currency" firestore:"currency"`
	DeliveryDropCost   float64 `json:"delivery_drop_coast" firestore:"delivery_drop_coast"`
	DeliveryPickupCost float64 `json:"delivery_pickup_cost" firestore:"delivery_pickup_cost"`
	Deposit            float64 `json:"deposit" firestore:"deposit"`
	WeekendPrice       float64 `json:"weekend_price" firestore:"weekend_price"`
}

// MappingFileUploadColumns is a map of the column index to the equipment field name.
func MappingFileUploadColumns() map[int]string {
	return map[int]string{
		0:  "Equipment ID",
		1:  "Preferred Equipment Name",
		2:  "Equipment Name [EN] @Derental",
		3:  "Picture",
		4:  "Name description [EN]",
		5:  "Price per day",
		6:  "Price per week",
		7:  "Price per month",
		8:  "Weekend price",
		9:  "Security Deposit",
		10: "One way delivery",
		11: "Brand",
		12: "Brand model",
		13: "Drive type",
		14: "Usage hours",
		15: "Unused Field 2",
		16: "Weight (lbs)",
		17: "Height (ft)",
		18: "Width (ft)",
		19: "Length (ft)",
		20: "Diameter",
		21: "Cut diameter",
		22: "Force",
		23: "BTU",
		24: "Volt",
		25: "Watt",
		26: "CFM",
		27: "Capacity",
		28: "Consumption",
		29: "Propulsion type",
		30: "Platform height (ft)",
		31: "Working height (ft)",
		32: "Horizontal outreach (ft)",
		33: "Platform capacity (lbs)",
		34: "Platform dimension (ft)",
		35: "Platform extension",
		36: "Extension capacity (lbs)",
		37: "Platform rotation (degree)",
		38: "Machine rotation (degree)",
		39: "Machine width (ft)",
		40: "Machine length (ft)",
		41: "Machine height (ft)",
		42: "Closed machine height (ft)",
		43: "Closed machine length (ft)",
		44: "Closed machine width (ft)",
		45: "Basket capacity",
		46: "Basket length (ft)",
		47: "Basket width (ft)",
		48: "Legs location (ft)",
		49: "Floor height (ft)",
		50: "Cabin height (ft)",
		51: "Wheelbase (ft)",
		52: "Wheel size (in)",
		53: "Plate dimension",
		54: "Decibel",
		55: "Roll width (ft)",
		56: "Compaction",
		57: "Vibrations/min.",
		58: "Lumen",
		59: "Pressure",
		60: "Frequency",
		61: "Tilting capacity",
		62: "Operation capacity,",
		63: "Tank capacity",
		64: "Digging depth",
		65: "Dumping height",
		66: "Digging radius",
		67: "Technical data sheet",
		68: "Usages of equipment",
	}
}

// BookEquipmentRequest is a struct representing a Book equipment.
type BookEquipmentRequest struct {
	EquipperID            string          `json:"equipper_id"`
	EquipmentID           string          `json:"equipment_id"`
	StartDate             time.Time       `json:"start_date"`
	EndDate               time.Time       `json:"end_date"`
	CreditCheckForm       CreditCheckForm `json:"credit_check_form"`
	BillingAddress        Address         `json:"billing_address" firestore:"billing_address"`
	DeliveryAddress       Address         `json:"delivery_address" firestore:"delivery_address"`
	PaymentMethod         PaymentMethod   `json:"payment_method" firestore:"payment_method"`
	DeliveryDetails       DeliveryDetails `json:"delivery_details" firestore:"delivery_details"`
	PoNumber              string          `json:"po_number" firestore:"po_number"`
	SpecialPrice          Price           `json:"special_price" firestore:"special_price"`
	ServiceFees           float64         `json:"service_fees" firestore:"service_fees"`
	SubTotal              float64         `json:"sub_total" firestore:"sub_total"`
	TotalAmount           float64         `json:"total_amount" firestore:"total_amount"`
	TVQ                   float64         `json:"tvq" firestore:"tvq"`
	TPS                   float64         `json:"tvp" firestore:"tps"`
	ProjectID             string          `json:"project_id" firestore:"project_id"`
	NeedOne               bool            `json:"need_one" firestore:"need_one"`
	Comment               string          `json:"comment" firestore:"comment"`
	InsuranceCompany      string          `json:"insurance_company" firestore:"insurance_company"`
	InsurancePolicyNumber string          `json:"insurance_policy_number" firestore:"insurance_policy_number"`
	InsuranceCoverage     string          `json:"insurance_coverage" firestore:"insurance_coverage"`
	ExpiryDateOfInsurance string          `json:"expiry_date_of_insurance" firestore:"expiry_date_of_insurance"`
	DeliveryType          DeliveryType    `json:"delivery_type" firestore:"delivery_type"`
	CompanyName           string          `json:"company_name" firestore:"company_name"`
	ReceiverInfo          ReceiverInfo    `json:"receiver_info" firestore:"receiver_info"`
	WaiverInsurance       float64         `json:"waiver_insurance" firestore:"waiver_insurance"`
	TaxRate               float64         `json:"tax_rate" firestore:"tax_rate"`
	Currency              string          `json:"currency" firestore:"currency"`
}

// BookingStatus represents the status of a booking.
type BookingStatus string

// BookingStatus constants.
const (
	BookingCreated  BookingStatus = "created"
	BookingPending  BookingStatus = "pending"
	BookingAccepted BookingStatus = "accepted"
	BookingRejected BookingStatus = "rejected"
	BookingCanceled BookingStatus = "canceled"
	BookingReturned BookingStatus = "returned"
)

// BookEquipment is a struct representing a Book equipment.
type BookEquipment struct {
	ID                    string          `json:"id" firestore:"id"`
	EquipmentName         string          `json:"equipment_name" firestore:"equipment_name"`
	EquipmentNameFR       string          `json:"equipment_name_fr" firestore:"equipment_name_fr"`
	EquipperID            string          `json:"equipper_id" firestore:"equipper_id"`
	EquipperEmail         string          `json:"equipper_email" firestore:"equipper_email"`
	EquipperName          string          `json:"equipper_name" firestore:"equipper_name"`
	EquipperImageLink     string          `json:"equipper_image_link" firestore:"equipper_image_link"`
	EquipmentID           string          `json:"equipment_id" firestore:"equipment_id"`
	StartDate             time.Time       `json:"start_date" firestore:"start_date"`
	EndDate               time.Time       `json:"end_date" firestore:"end_date"`
	OwnerID               string          `json:"owner_id" firestore:"owner_id"`
	AdminIDs              []string        `json:"admin_ids" firestore:"admin_ids"`
	LodgerID              string          `json:"lodger_id" firestore:"lodger_id"`
	LodgerEmail           string          `json:"lodger_email" firestore:"lodger_email"`
	LodgerName            string          `json:"lodger_name" firestore:"lodger_name"`
	LodgerImageLink       string          `json:"lodger_image_link" firestore:"lodger_image_link"`
	LodgerPhoneNumber     string          `json:"phone_number" firestore:"phone_number"`
	LodgerAddress         string          `json:"address" firestore:"address"`
	Amount                float64         `json:"amount" firestore:"amount"`
	Currency              string          `json:"currency" firestore:"currency"`
	Status                BookingStatus   `json:"status" firestore:"status"`
	CreatedAt             time.Time       `json:"created_at" firestore:"created_at"`
	CreditCheckFormID     string          `json:"credit_check_form_id" firestore:"credit_check_form_id"`
	CreditCheckForm       CreditCheckForm `json:"credit_check_form" firestore:"credit_check_form"`
	Category              []string        `json:"category" firestore:"category"`
	SubCategory           []string        `json:"sub_category" firestore:"sub_category"`
	Price                 Price           `json:"price" firestore:"price"`
	SpecialPrice          Price           `json:"special_price" firestore:"special_price"`
	Description           string          `json:"description" firestore:"description"`
	DescriptionFR         string          `json:"description_fr" firestore:"description_fr"`
	BillingAddress        Address         `json:"billing_address" firestore:"billing_address"`
	DeliveryAddress       Address         `json:"delivery_address" firestore:"delivery_address"`
	PaymentMethod         PaymentMethod   `json:"payment_method" firestore:"payment_method"`
	DeliveryDetails       DeliveryDetails `json:"delivery_details" firestore:"delivery_details"`
	PoNumber              string          `json:"po_number" firestore:"po_number"`
	SubTotal              float64         `json:"sub_total" firestore:"sub_total"`
	ServiceFees           float64         `json:"service_fees" firestore:"service_fees"`
	TPS                   float64         `json:"tps" firestore:"tps"`
	TVQ                   float64         `json:"tvq" firestore:"tvq"`
	TotalAmount           float64         `json:"total_amount" firestore:"total_amount"`
	CancelComment         string          `json:"cancel_comment" firestore:"cancel_comment"`
	ProjectID             string          `json:"project_id" firestore:"project_id"`
	CreatedByMember       bool            `json:"created_by_member" firestore:"created_by_member"`
	NeedOne               bool            `json:"need_one" firestore:"need_one"`
	Comment               string          `json:"comment" firestore:"comment"`
	InsuranceCompany      string          `json:"insurance_company" firestore:"insurance_company"`
	InsurancePolicyNumber string          `json:"insurance_policy_number" firestore:"insurance_policy_number"`
	InsuranceCoverage     string          `json:"insurance_coverage" firestore:"insurance_coverage"`
	ExpiryDateOfInsurance string          `json:"expiry_date_of_insurance" firestore:"expiry_date_of_insurance"`
	WaiverInsurance       float64         `json:"waiver_insurance" firestore:"waiver_insurance"`
	DeliveryType          DeliveryType    `json:"delivery_type" firestore:"delivery_type"`
	UpdatedAt             time.Time       `json:"updated_at" firestore:"updated_at"`
	CompanyName           string          `json:"company_name" firestore:"company_name"`
	ReceiverInfo          ReceiverInfo    `json:"receiver_info" firestore:"receiver_info"`
	PaidAmount            float64         `json:"paid_amount" firestore:"paid_amount"`
	TaxAmount             float64         `json:"tax" firestore:"tax"`
	DiscountAmount        float64         `json:"discount_amount" firestore:"discount_amount"`
	DiscountRate          float64         `json:"discount_rate" firestore:"discount_rate"`
	// Old but used equipment specs
	EquipmentImageLink       string   `json:"equipment_image_link" firestore:"equipment_image_link"`
	InternalID               string   `json:"internal_id" firestore:"internal_id"`
	Brand                    string   `json:"brand" firestore:"brand"`
	BrandModel               string   `json:"brand_model" firestore:"brand_model"`
	DriveType                []string `json:"drive_type" firestore:"drive_type"`
	Weight                   string   `json:"weight" firestore:"weight"`
	Height                   string   `json:"height" firestore:"height"`
	Width                    string   `json:"width" firestore:"width"`
	Length                   string   `json:"length" firestore:"length"`
	Diameter                 string   `json:"diameter" firestore:"diameter"`
	CutDiameter              string   `json:"cut_diameter" firestore:"cut_diameter"`
	Force                    string   `json:"force" firestore:"force"`
	UsageHours               string   `json:"usage_hours" firestore:"usage_hours"`
	BTU                      string   `json:"btu" firestore:"btu"`
	Volt                     string   `json:"volt" firestore:"volt"`
	Watt                     string   `json:"watt" firestore:"watt"`
	CFM                      string   `json:"cfm" firestore:"cfm"`
	Capacity                 string   `json:"capacity" firestore:"capacity"`
	Consumption              string   `json:"consumption" firestore:"consumption"`
	TypeOfPropulsion         []string `json:"type_of_propulsion" firestore:"type_of_propulsion"`
	PlatformHeight           string   `json:"platform_height" firestore:"platform_height"`
	WorkingHeight            string   `json:"working_height" firestore:"working_height"`
	EquipperEquipmentPicture string   `json:"equipper_equipment_picture" firestore:"equipper_equipment_picture"`
	HorizontalOutreach       string   `json:"horizontal_outreach" firestore:"horizontal_outreach"`
	PlatformCapacity         string   `json:"platform_capacity" firestore:"platform_capacity"`
	PlatformDimension        string   `json:"platform_dimension" firestore:"platform_dimension"`
	PlatformExtension        string   `json:"platform_extension" firestore:"platform_extension"`
	ExtensionCapacity        string   `json:"extension_capacity" firestore:"extension_capacity"`
	PlatformRotation         string   `json:"platform_rotation" firestore:"platform_rotation"`
	MachineRotation          string   `json:"machine_rotation" firestore:"machine_rotation"`
	MachineWidth             string   `json:"machine_width" firestore:"machine_width"`
	MachineLength            string   `json:"machine_length" firestore:"machine_length"`
	MachineHeight            string   `json:"machine_height" firestore:"machine_height"`
	ClosedMachineHeight      string   `json:"closed_machine_height" firestore:"closed_machine_height"`
	ClosedMachineLength      string   `json:"closed_machine_length" firestore:"closed_machine_length"`
	ClosedMachineWidth       string   `json:"closed_machine_width" firestore:"closed_machine_width"`
	BasketCapacity           string   `json:"basket_capacity" firestore:"basket_capacity"`
	BasketLength             string   `json:"basket_length" firestore:"basket_length"`
	BasketWidth              string   `json:"basket_width" firestore:"basket_width"`
	LegsLocation             string   `json:"legs_location" firestore:"legs_location"`
	FloorHeight              string   `json:"floor_height" firestore:"floor_height"`
	CabinHeight              string   `json:"cabin_height" firestore:"cabin_height"`
	Wheelbase                string   `json:"wheelbase" firestore:"wheelbase"`
	WheelSize                string   `json:"wheel_size" firestore:"wheel_size"`
	PlateDimension           string   `json:"plate_dimension" firestore:"plate_dimension"`
	Decibel                  string   `json:"decibel" firestore:"decibel"`
	RollWidth                string   `json:"roll_width" firestore:"roll_width"`
	Compaction               string   `json:"compaction" firestore:"compaction"`
	Vibrations               string   `json:"vibrations" firestore:"vibrations"`
	Lumen                    string   `json:"lumen" firestore:"lumen"`
	Pressure                 string   `json:"pressure" firestore:"pressure"`
	Frequency                string   `json:"frequency" firestore:"frequency"`
	TiltingCapacity          string   `json:"tilting_capacity" firestore:"tilting_capacity"`
	OperationCapacity        string   `json:"operation_capacity" firestore:"operation_capacity"`
	TankCapacity             string   `json:"tank_capacity" firestore:"tank_capacity"`
	DiggingDepth             string   `json:"digging_depth" firestore:"digging_depth"`
	DumpingHeight            string   `json:"dumping_height" firestore:"dumping_height"`
	DiggingRadius            string   `json:"digging_radius" firestore:"digging_radius"`
	TechnicalDataSheet       string   `json:"technical_data_sheet" firestore:"technical_data_sheet"`
	EquipmentUsages          string   `json:"equipment_usages" firestore:"equipment_usages"`
	TaxRate                  float64  `json:"tax_rate" firestore:"tax_rate"`
}

// BookingEquipment is a struct representing an Equipment booking.
type BookingEquipment struct {
	EquipmentID   string `json:"equipment_id"`
	EquipmentName string `json:"equipment_name"`
}

type ReceiverInfo struct {
	Name  string `json:"name"`
	Phone string `json:"phone"`
}

type Invoice struct {
	SubTotal    float64 `json:"sub_total" firestore:"sub_total"`
	ServiceFees float64 `json:"service_fees" firestore:"service_fees"`
	TotalAmount float64 `json:"total_amount" firestore:"total_amount"`
}

// RequestBookingEquipment is a struct representing a Request for booking an equipments.
type RequestBookingEquipment struct {
	EquipmentName string          `json:"equipment_name"`
	Bookings      []BookEquipment `json:"bookings"`
}

// BookingByStatus represents the list of bookings by status.
type BookingByStatus map[BookingStatus][]RequestBookingEquipment

// IsValid checks if the booking status is valid.
func (b BookingStatus) IsValid() bool {
	return b == BookingPending || b == BookingAccepted || b == BookingRejected || b == BookingCanceled
}

// BidzOfferEquipment is a struct representing a offer for request Bidz an equipments.
type BidzOfferEquipment struct {
	EquipmentName string      `json:"equipment_name"`
	BidsOffer     []BidzOffer `json:"bids_offers"`
}

// BidsOfferByStatus represents the list of BidsOffer by status.
type BidsOfferByStatus map[BidsOfferStatus][]BidzOfferEquipment

// IsValid returns true if the status is valid.
func (b BidsOfferStatus) IsValid() bool {
	return b == OfferPending || b == OfferAccepted || b == OfferRejected || b == OfferCanceled
}

// BidsOfferStatus represents the status of a bidz offer.
type BidsOfferStatus string

// BidsOfferStatus constants.
const (
	OfferPending  BidsOfferStatus = "pending"
	OfferAccepted BidsOfferStatus = "accepted"
	OfferRejected BidsOfferStatus = "rejected"
	OfferCanceled BidsOfferStatus = "canceled"
)

// PaymentMethod represents the payment method.
type PaymentMethod string

// PaymentMethod constants.
const (
	PaymentMethodCreditCard    PaymentMethod = "credit card"
	PaymentMethodCreditAccount PaymentMethod = "credit account"
)

// BidzOffer is a struct representing an offer for equipment.
type BidzOffer struct {
	ID                     string          `json:"id" firestore:"id"`
	EquipmentName          string          `json:"equipment_name" firestore:"equipment_name"`
	EquipmentNameFR        string          `json:"equipment_name_fr" firestore:"equipment_name_fr"`
	EquipmentID            string          `json:"equipment_id" firestore:"equipment_id"`
	EquipmentImageLink     string          `json:"equipment_image_link" firestore:"equipment_image_link"`
	Description            string          `json:"description" firestore:"description"`
	Category               []string        `json:"category" firestore:"category"`
	EquipperID             string          `json:"equipper_id" firestore:"equipper_id"`
	EquipperEmail          string          `json:"equipper_email" firestore:"equipper_email"`
	EquipperName           string          `json:"equipper_name" firestore:"equipper_name"`
	EquipperImageLink      string          `json:"equipper_image_link" firestore:"equipper_image_link"`
	LodgerID               string          `json:"lodger_id" firestore:"lodger_id"`
	LodgerEmail            string          `json:"lodger_email" firestore:"lodger_email"`
	LodgerName             string          `json:"lodger_name" firestore:"lodger_name"`
	LodgerImageLink        string          `json:"lodger_image_link" firestore:"lodger_image_link"`
	LodgerPhoneNumber      string          `json:"phone_number" firestore:"phone_number"`
	LodgerAddress          string          `json:"address" firestore:"address"`
	OwnerID                string          `json:"owner_id" firestore:"owner_id"`
	AdminIDs               []string        `json:"admin_ids" firestore:"admin_ids"`
	Amount                 float64         `json:"amount" firestore:"amount"`
	RequestID              string          `json:"request_id" firestore:"request_id"`
	Currency               string          `json:"currency" firestore:"currency"`
	Status                 BidsOfferStatus `json:"status" firestore:"status"`
	PoNumber               string          `json:"po_number" firestore:"po_number"`
	DeliveryAddress        Address         `json:"delivery_address" firestore:"delivery_address"`
	ReturnDate             time.Time       `json:"return_date" firestore:"return_date"`
	BillingAddress         Address         `json:"billing_address" firestore:"billing_address"`
	StartDate              time.Time       `json:"start_date" firestore:"start_date"`
	DeliveryPreference     string          `json:"delivery_preference" firestore:"delivery_preference"`
	PaymentMethod          PaymentMethod   `json:"payment_method" firestore:"payment_method"`
	Contact                string          `json:"contact" firestore:"contact"`
	Btu                    string          `json:"btu" firestore:"btu"`
	Force                  string          `json:"force" firestore:"force"`
	UsageHours             string          `json:"usage_hours" firestore:"usage_hours"`
	Volt                   string          `json:"volt" firestore:"volt"`
	Cfm                    string          `json:"cfm" firestore:"cfm"`
	Kw                     string          `json:"kw" firestore:"kw"`
	Height                 string          `json:"height" firestore:"height"`
	Width                  string          `json:"width" firestore:"width"`
	Length                 string          `json:"length" firestore:"length"`
	Weight                 string          `json:"weight" firestore:"weight"`
	Brand                  string          `json:"brand" firestore:"brand"`
	Capacity               string          `json:"capacity" firestore:"capacity"`
	Consumption            string          `json:"consumption" firestore:"consumption"`
	PricePerMonth          float64         `json:"price_per_month" firestore:"price_per_month"`
	PricePerDay            float64         `json:"price_per_day" firestore:"price_per_day"`
	PricePerWeek           float64         `json:"price_per_week" firestore:"price_per_week"`
	DeliveryDropCost       float64         `json:"delivery_drop_coast" firestore:"delivery_drop_coast"`
	DeliveryPickupCost     float64         `json:"delivery_pickup_cost" firestore:"delivery_pickup_cost"`
	TypeOfPropulsion       []string        `json:"type_of_propulsion" firestore:"type_of_propulsion"`
	CreditCheckForm        string          `json:"credit_check_form" firestore:"credit_check_form"`
	EquipmentUtility       string          `json:"equipment_utility" firestore:"equipment_utility"`
	BidsOfferCancelComment string          `json:"bids_offer_cancel_comment" firestore:"bids_offer_cancel_comment"`
	ProjectID              string          `json:"project_id" firestore:"project_id"`
	CreatedAt              time.Time       `json:"created_at" firestore:"created_at"`
	UpdatedAt              time.Time       `json:"updated_at" firestore:"updated_at"`
	NeedOne                bool            `json:"need_one" firestore:"need_one"`
	CreatedByMember        bool            `json:"created_by_member" firestore:"created_by_member"`
	Comment                string          `json:"comment" firestore:"comment"`
	InsuranceCompany       string          `json:"insurance_company" firestore:"insurance_company"`
	InsurancePolicyNumber  string          `json:"insurance_policy_number" firestore:"insurance_policy_number"`
	InsuranceCoverage      string          `json:"insurance_coverage" firestore:"insurance_coverage"`
	ExpiryDateOfInsurance  string          `json:"expiry_date_of_insurance" firestore:"expiry_date_of_insurance"`
	WaiverInsurance        float64         `json:"waiver_insurance" firestore:"waiver_insurance"`
	DeliveryType           DeliveryType    `json:"delivery_type" firestore:"delivery_type"`
	Invoice                Invoice         `json:"invoice" firestore:"invoice"`
	ReceiverInfo           ReceiverInfo    `json:"receiver_info" firestore:"receiver_info"`
}

// BidsRequestStatus represents the status of a bids request.
type BidsRequestStatus string

// BidsRequestStatus constants.
const (
	RequestPending  BidsRequestStatus = "pending"
	RequestAccepted BidsRequestStatus = "accepted"
	RequestRejected BidsRequestStatus = "rejected"
	RequestCanceled BidsRequestStatus = "cancel"
	RequestSent     BidsRequestStatus = "sent"
)

// DeliveryType represents the type of delivery.
type DeliveryType string

// DeliveryType represents the type of delivery.
const (
	Drop          DeliveryType = "drop"
	Pickup        DeliveryType = "pickup"
	DropAndPickup DeliveryType = "drop_and_pickup"
)

// BidzRequestEquipment represents the request for bidz equipment.
type BidzRequestEquipment struct {
	EquipmentName string        `json:"equipment_name"`
	BidsRequests  []BidsRequest `json:"bids_requests"`
}

// BidsRequestByStatus represents the list of BidsRequest by status.
type BidsRequestByStatus map[BidsRequestStatus][]BidzRequestEquipment

// IsValid validates the status of a bidz request.
func (b BidsRequestStatus) IsValid() bool {
	return b == RequestPending || b == RequestAccepted || b == RequestRejected
}

// BidsRequest is a struct representing an request for equipment.
type BidsRequest struct {
	ID                    string            `json:"id" firestore:"id"`
	EquipmentID           string            `json:"equipment_id" firestore:"equipment_id"`
	EquipmentName         string            `json:"equipment_name" firestore:"equipment_name"`
	EquipmentNameFR       string            `json:"equipment_name_fr" firestore:"equipment_name_fr"`
	EquipmentSerialNumber string            `json:"equipment_serial_number" firestore:"equipment_serial_number"`
	EquipmentImageLink    string            `json:"equipment_image_link" firestore:"equipment_image_link"`
	Description           string            `json:"description" firestore:"description"`
	Category              []string          `json:"category" firestore:"category"`
	CoverageArea          []string          `json:"coverage_area" firestore:"coverage_area"`
	EquipperID            string            `json:"equipper_id" firestore:"equipper_id"`
	EquipperEmail         string            `json:"equipper_email" firestore:"equipper_email"`
	EquipperName          string            `json:"equipper_name" firestore:"equipper_name"`
	EquipperImageLink     string            `json:"equipper_image_link" firestore:"equipper_image_link"`
	LodgerID              string            `json:"lodger_id" firestore:"lodger_id"`
	LodgerEmail           string            `json:"lodger_email" firestore:"lodger_email"`
	LodgerName            string            `json:"lodger_name" firestore:"lodger_name"`
	LodgerImageLink       string            `json:"lodger_image_link" firestore:"lodger_image_link"`
	LodgerPhoneNumber     string            `json:"phone_number" firestore:"phone_number"`
	LodgerAddress         string            `json:"address" firestore:"address"`
	OwnerID               string            `json:"owner_id" firestore:"owner_id"`
	AdminIDs              []string          `json:"admin_ids" firestore:"admin_ids"`
	Status                BidsRequestStatus `json:"status" firestore:"status"`
	Btu                   string            `json:"btu" firestore:"btu"`
	Force                 string            `json:"force" firestore:"force"`
	UsageHours            string            `json:"usage_hours" firestore:"usage_hours"`
	Volt                  string            `json:"volt" firestore:"volt"`
	Cfm                   string            `json:"cfm" firestore:"cfm"`
	Kw                    string            `json:"kw" firestore:"kw"`
	Length                string            `json:"length" firestore:"length"`
	Height                string            `json:"height" firestore:"height"`
	Weight                string            `json:"weight" firestore:"weight"`
	Width                 string            `json:"width" firestore:"width"`
	Brand                 string            `json:"brand" firestore:"brand"`
	Capacity              string            `json:"capacity" firestore:"capacity"`
	Consumption           string            `json:"consumption" firestore:"consumption"`
	TypeOfPropulsion      []string          `json:"type_of_propulsion" firestore:"type_of_propulsion"`
	CreditCheckForm       string            `json:"credit_check_form" firestore:"credit_check_form"`
	Contact               string            `json:"contact" firestore:"contact"`
	CreatedAt             time.Time         `json:"created_at" firestore:"created_at"`
	UpdatedAt             time.Time         `json:"updated_at" firestore:"updated_at"`
	StartDate             time.Time         `json:"start_date" firestore:"start_date"`
	ReturnDate            time.Time         `json:"return_date" firestore:"return_date"`
	DeliveryAddress       Address           `json:"delivery_address" firestore:"delivery_address"`
	DeliveryPreference    string            `json:"delivery_preference" firestore:"delivery_preference"`
	PaymentMethod         PaymentMethod     `json:"payment_method" firestore:"payment_method"`
	BillingAddress        Address           `json:"billing_address" firestore:"billing_address"`
	PoNumber              string            `json:"po_number" firestore:"po_number"`
	EquipmentUtility      string            `json:"equipment_utility" firestore:"equipment_utility"`
	RejectionComment      string            `json:"rejection_comment" firestore:"rejection_comment"`
	ProjectID             string            `json:"project_id" firestore:"project_id"`
	NeedOne               bool              `json:"need_one" firestore:"need_one"`
	CreatedByMember       bool              `json:"created_by_member" firestore:"created_by_member"`
	Comment               string            `json:"comment" firestore:"comment"`
	InsuranceCompany      string            `json:"insurance_company" firestore:"insurance_company"`
	InsurancePolicyNumber string            `json:"insurance_policy_number" firestore:"insurance_policy_number"`
	InsuranceCoverage     string            `json:"insurance_coverage" firestore:"insurance_coverage"`
	ExpiryDateOfInsurance string            `json:"expiry_date_of_insurance" firestore:"expiry_date_of_insurance"`
	WaiverInsurance       float64           `json:"waiver_insurance" firestore:"waiver_insurance"`
	DeliveryType          DeliveryType      `json:"delivery_type" firestore:"delivery_type"`
	TaxRate               float64           `json:"tax_rate" firestore:"tax_rate"`
	Currency              string            `json:"currency" firestore:"currency"`
	ReceiverInfo          ReceiverInfo      `json:"receiver_info" firestore:"receiver_info"`
}
type DeliveryDetails struct {
	DeliveryPreference string `json:"delivery_preference" firestore:"delivery_preference"`
	Drop               string `json:"drop" firestore:"drop"`
	Pickup             string `json:"pickup" firestore:"pickup"`
}
