import Box from '@mui/material/Box';
import MultiSelect from '../../../../shared/components/multi_select/Multi_select.jsx';
import Input from '../../../../shared/components/forms/Input.jsx';
import React, { useEffect } from 'react';

export default function Step2({ t, formik, setNextButtonDisabled }) {
  useEffect(() => {
    const isNextButtonDisabled = !(
      formik.values.price.day &&
      formik.values.price.week &&
      formik.values.price.month
    );
    setNextButtonDisabled(isNextButtonDisabled);
  }, [
    formik.values.price.day,
    formik.values.price.week,
    formik.values.price.month
  ]);

  return (
    <>
      <Box className="container col-lg-8">
        <div className="form-group col-lg-12">
          <label className="label-input t-body-regular c-fake-black">
            {t('Status')} <span className="c-red star-required">*</span>
          </label>
          <MultiSelect
            isClearable
            options={[
              { label: 'Available', value: 'available' },
              { label: 'Idle', value: 'idle' }
            ]}
            handleChange={(item) => {
              formik.setFieldValue('status', item?.value);
            }}
            t={t}
            defaultValue={{ label: 'Available', value: 'available' }}
            name="status"
            placeholder={t('Status')}
          />
        </div>

        <Box className="d-lg-flex justify-content-between col-lg-12 mt-4">
          <div className="form-group col-lg-3  mt-2">
            <Input
              type="number"
              label={t('Price_day')}
              name="price.day"
              className="form-control"
              placeholder={t('Day')}
              value={formik.values.price.day}
            />
          </div>

          <div className="form-group col-lg-3 mt-2">
            <Input
              type="number"
              label={t('Price_week')}
              name="price.week"
              className="form-control"
              placeholder={t('Week')}
              value={formik.values.price.week}
            />
          </div>

          <div className="form-group col-lg-3 mt-2">
            <Input
              type="number"
              label={t('Price_four_week')}
              name="price.month"
              className="form-control"
              placeholder={t('Four_week')}
              value={formik.values.price.month}
            />
          </div>
        </Box>
      </Box>
    </>
  );
}
