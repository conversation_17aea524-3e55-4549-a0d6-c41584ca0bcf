import React from 'react';
import RequestByStatus from './Accepted_requests';
import RenderIf from '../../Render_if';
import { isEmpty } from 'lodash';

export default function AllRequests({
  refresh,
  viewMore,
  detectLanguage,
  allRequests,
  request,
  error,
  role,
  t
}) {
  return (
    <>
      {allRequests?.map((data, index) => (
        <RenderIf condition={!isEmpty(data?.requestData)}>
          <RequestByStatus
            key={index}
            refresh={refresh}
            request={request}
            data={data}
            viewMore={viewMore}
            error={error}
            role={role}
            detectLanguage={detectLanguage}
            t={t}
            isAll
          />
        </RenderIf>
      ))}
    </>
  );
}
