import NoResult from '../../../../components/search_result/No_results';
import CustomImage from '../../images/Custom_image';
import { getSessionStorage } from '../../../helpers/Session_storage_helper';
import { cutString } from '../../../helpers/String_helps';
import Tooltip from '@mui/material/Tooltip';
import { isEmpty } from 'lodash';
import BookingEmptyState from '../../../../style/assets/img/empty_state/No_booking_requests.svg';
import { useNavigate } from 'react-router-dom';

export default function SummaryTab({ equipments, t, detectLanguage, role }) {
  const navigate = useNavigate();
  const type = getSessionStorage('type');

  return (
    <>
      {isEmpty(equipments) ? (
        <NoResult
          image={BookingEmptyState}
          message={
            role === 'equipper'
              ? t('You_have_no_requests')
              : t('Make_sure_to_book_an_equipment')
          }
          buttonText={role === 'equipper' ? '' : t('Make_your_first_booking')}
          onClick={() => {
            navigate('/availableInventory');
          }}
        />
      ) : (
        equipments?.map((equipment) => (
          <div className="equipments-content">
            <div className="row">
              <div className="col-lg-2">
                <CustomImage
                  imageUrl={
                    equipment.equipper_equipment_picture &&
                    !equipment.equipper_equipment_picture.includes(
                      'equipment_library/Empty_state_equipment.png'
                    )
                      ? equipment.equipper_equipment_picture
                      : equipment.equipment_image_link
                  }
                  alt={t('Cant_load_image')}
                />
              </div>
              <div className="col-lg-4">
                <h2 className="t-header-h5 extraBold mt-2">
                  {detectLanguage === 'fr'
                    ? equipment.equipment_name_fr
                    : equipment.equipment_name}
                </h2>
                <div className="equipments-content__bottom rental-content">
                  <div className="rental__lodge">
                    <CustomImage
                      imageUrl={equipment.equipper_image_link}
                      alt={t('Cant_load_image')}
                      isUser
                    />
                    <span className="t-base-medium c-primary-color bold">
                      {equipment.equipper_name}
                    </span>
                  </div>
                </div>
              </div>
              <div className="col-lg-6">
                <div className="gery-bg rental-grey-box">
                  <div className="row">
                    <div className="col-4 text-lg-center">
                      <p className="t-label-medium c-primary-color pad-bottom">
                        {t('Rented_on')}
                      </p>
                      <span className="t-label-medium extraBold c-primary-color">
                        {equipment.start_date?.slice(0, 10)}
                      </span>
                    </div>
                    <div className="col-4 text-lg-center">
                      <p className="t-label-medium c-primary-color pad-bottom">
                        {t('Return_date')}
                      </p>
                      <span className="t-label-medium extraBold c-primary-color">
                        {equipment.end_date?.slice(0, 10)}
                      </span>
                    </div>

                    <div className="col-4 text-lg-center">
                      <p className="t-label-medium c-primary-color pad-bottom">
                        {type !== 'pro' ? t('Location') : t('Project')}
                      </p>
                      <Tooltip title={equipment.project_name} placement="top">
                        <span className="t-label-medium extraBold c-primary-color">
                          {type !== 'pro'
                            ? equipment.address
                            : cutString(equipment.project_name, 10)}
                        </span>
                      </Tooltip>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))
      )}
    </>
  );
}
