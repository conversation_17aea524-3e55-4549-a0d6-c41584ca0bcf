package middleware

import (
	"log"
	"net/http"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.opencensus.io/stats"
	"go.opencensus.io/stats/view"
	"go.opencensus.io/tag"
)

var (
	viewLatencyMsSync sync.Once
	latencyMs         *stats.Float64Measure
	keyEndpoint       tag.Key
	keyStatus         tag.Key
)

// Latency is a middleware that calculates the latency in milliseconds for a request.
func Latency() gin.HandlerFunc {
	viewLatencyMsSync.Do(func() {
		latencyMs = stats.Float64("latency", "The latency in milliseconds", "ms")
		keyEndpoint, _ = tag.NewKey("endpoint")
		keyStatus, _ = tag.NewKey("status")

		v := &view.View{
			Name:        "request_latency_distribution",
			Measure:     latencyMs,
			TagKeys:     []tag.Key{keyEndpoint, keyStatus},
			Description: "The distribution of the request latencies",
			Aggregation: view.Distribution(0, 100, 200, 400, 1000, 2000, 4000),
		}
		if err := view.Register(v); err != nil {
			log.Fatalf("Failed to register the view: %v", err)
		}
	})

	return func(c *gin.Context) {
		start := time.Now()

		c.Next()

		latency := time.Since(start)

		defer func() {
			ctx, err := tag.New(c.Request.Context(), tag.Insert(keyEndpoint, c.Request.URL.Path), tag.Insert(keyStatus, strconv.Itoa(c.Writer.Status())))
			if err != nil {
				_ = c.AbortWithError(http.StatusInternalServerError, err)

				return
			}

			stats.Record(ctx, latencyMs.M(float64(latency.Milliseconds())))
		}()
	}
}
