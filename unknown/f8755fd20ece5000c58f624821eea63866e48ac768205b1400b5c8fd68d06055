import 'instantsearch.css/themes/satellite.css';
import React, { useEffect, useState } from 'react';
import { Configure, Menu, RangeInput } from 'react-instantsearch-dom';
import { useParams } from 'react-router-dom';
import { has, isEmpty } from 'lodash';
import InstantSearchAlgolia from '../../components/search_result/Instant_search_algolia';
import ScrollToTop from '../../shared/components/Scroll_to_top';
import { CustomEquipperSearchResult } from './Equipper_search_result';
import UseWindowDimensions from '../../shared/hooks/Screen_size';
import ToloIsLoading from '../../shared/components/cards/Tolo_is_loading';

export default function SearchResult({
  t,
  detectLanguage,
  setShowFPModal,
  showFPModal,
  signIn,
  show,
  setShow
}) {
  UseWindowDimensions({ functions: handleSwitchPlace, dimension: 1200 });
  const { name_fr, name_en, city, start_date } = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [recommendationObjectID, setRecommendationObjectID] = useState('');
  const [switchPlace, setSwitchPlace] = useState(window.innerWidth < 1200);
  const [changedSearchState, setChangedSearchState] = useState(false);
  const [searchState, setSearchState] = useState({});

  function handleSwitchPlace(prop) {
    setSwitchPlace(prop);
    setChangedSearchState(false);
  }

  function handleStateSwitch(searchState) {
    setSearchState(searchState);
    if (
      has(searchState, 'refinementList') &&
      Object.values(searchState.refinementList).filter(
        (item) => item.length > 0 || !isEmpty(item)
      ).length > 0
    ) {
      setChangedSearchState(true);
      sessionStorage.setItem(
        'currentRefinements',
        JSON.stringify(searchState.refinementList)
      );
    } else {
      sessionStorage.setItem('currentRefinements', JSON.stringify([]));
      setChangedSearchState(false);
    }
  }

  useEffect(() => {
    setIsLoading(true);
    setSearchState({
      menu: {
        name: name_en,
        name_fr: name_fr,
        coverage_area: city
      },
      range: {
        available_from: {
          max: start_date
        }
      }
    });

    setIsLoading(false);
  }, [name_en, name_fr, city, start_date]);

  useEffect(() => {
    setChangedSearchState(false);
  }, []);

  useEffect(() => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 500);
  }, [name_en, name_fr, city, start_date]);

  if (isLoading) {
    return <ToloIsLoading />;
  }

  return (
    <ScrollToTop>
      <InstantSearchAlgolia
        searchState={searchState}
        onSearchStateChange={(searchState) => {
          handleStateSwitch(searchState);
        }}
        indexName={import.meta.env.VITE_ALGOLIA_INDEX_NAME}
      >
        <RangeInput className="hidden" attribute="available_from" />
        <Menu className="hidden" attribute="name" />
        <Menu className="hidden" attribute="status" />
        {/* <Menu className="hidden" attribute="name_fr" /> */}
        <Menu className="hidden" attribute="coverage_area" />
        <Menu className="hidden" attribute="is_active" />
        <Configure
          hitsPerPage={1000}
          filters={`available_from<=${start_date} AND is_active:true AND (status:available OR status:booked)`}
        />
        <CustomEquipperSearchResult
          objectIDSetter={setRecommendationObjectID}
          switchPlace={switchPlace}
          changedSearchState={changedSearchState}
          recommendationObjectID={recommendationObjectID}
          setChangedSearchState={setChangedSearchState}
          detectLanguage={detectLanguage}
          location={city}
          nameFr={name_fr}
          nameEn={name_en}
          setShowFPModal={setShowFPModal}
          showFPModal={showFPModal}
          signIn={signIn}
          show={show}
          setIsLoading={setIsLoading}
          setShow={setShow}
          t={t}
        />
      </InstantSearchAlgolia>
    </ScrollToTop>
  );
}
