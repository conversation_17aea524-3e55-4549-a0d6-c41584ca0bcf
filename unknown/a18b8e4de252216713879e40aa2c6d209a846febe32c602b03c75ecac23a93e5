import React, { useEffect, useMemo, useState } from 'react';
import NoResults from '../../../components/search_result/No_results';
import MemberDataItem from './Member_data_item';
import RenderIf from '../Render_if';
import {
  ADMIN,
  COLLABORATOR,
  POWER_COLLABORATOR
} from '../../helpers/Account_type';
import { TableHeadComponent } from '../../../components/lodger_management_portal/project_management/Projects_table';
import { getComparator, stableSort } from '../../helpers/Data_table';
import Box from '@mui/material/Box';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';
import Tooltip from '@mui/material/Tooltip';
import Checkbox from '@mui/material/Checkbox';
import TeamEmptyState from '../../../style/assets/img/empty_state/Team_empty_state.svg';

import {
  cutString,
  firstLetterUpperCase,
  formatPhoneNumber
} from '../../helpers/String_helps';
import { useProjects } from '../../context/Project_context';
import CustomImage from '../images/Custom_image';

function ProjectsData({ member }) {
  const { GetProjects } = useProjects();
  const [projects, setProjects] = useState([]);
  useEffect(() => {
    async function initProjects() {
      const { data, status } = await GetProjects(member.id);
      if (status === 200 && data) {
        setProjects(data);
      }
    }
    initProjects();
  }, [member.id, GetProjects]);

  return (
    <>
      {projects?.map((project, index) => {
        return (
          <>
            {project.members?.find((m) => m.id === member.id) && (
              <button className="round-button light-blue small btn-client mt-2">
                <span key={index} className="t-body-regular c-fake-black">
                  {project.name}
                </span>
              </button>
            )}
          </>
        );
      })}
    </>
  );
}

export default function MembersTable({
  members,
  type,
  onClick,
  status,
  t,
  hasPrivilege
}) {
  const [order, setOrder] = useState('asc');
  const [orderBy, setOrderBy] = useState('name');
  const [selected, setSelected] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const headCells = [
    {
      id: 'email',
      numeric: false,
      disablePadding: true,
      label: 'Email'
    },
    {
      id: 'phone_number',
      numeric: true,
      disablePadding: true,
      label: 'Phone_number'
    },
    {
      id: 'projects',
      numeric: false,
      disablePadding: true,
      label: 'Assigned_projects'
    }
  ];
  const handleRequestSort = (_, property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleClick = (_, row) => {
    if (selected[0] === row.id) {
      setSelected([]);
      checkByOne(null);
    } else {
      setSelected([row.id]);
      checkByOne([row]);
    }
  };

  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const visibleRows = useMemo(
    () =>
      stableSort(members, getComparator(order, orderBy))?.slice(
        page * rowsPerPage,
        page * rowsPerPage + rowsPerPage
      ),
    [order, orderBy, page, rowsPerPage, members]
  );

  const checkByOne = (member) => {
    onClick(member);
  };

  if (!members || members?.length === 0) {
    switch (type) {
      case POWER_COLLABORATOR:
        return (
          <NoResults
            message={t('You_have_no_power_collaborator_for_the_moment')}
            image={TeamEmptyState}
          />
        );
      case COLLABORATOR:
        return (
          <NoResults
            message={t('You_have_no_collaborator_for_the_moment')}
            image={TeamEmptyState}
          />
        );
      case ADMIN:
        return (
          <NoResults
            message={t('Admin_tab_empty_message')}
            image={TeamEmptyState}
          />
        );
      default:
        return (
          <NoResults
            message={t('You_have_no_account_manager_for_the_moment')}
            image={TeamEmptyState}
          />
        );
    }
  }

  return (
    <div className="table-equipments">
      <div className="table-equipments__body three-column">
        <RenderIf condition={members}>
          {status !== 'owner' && (
            <Box sx={{ width: '100%' }}>
              <TableContainer>
                <Table sx={{ minWidth: 750 }} aria-labelledby="tableTitle">
                  <TableHeadComponent
                    numSelected={selected.length}
                    order={order}
                    t={t}
                    headCells={headCells}
                    orderBy={orderBy}
                    onRequestSort={handleRequestSort}
                    rowCount={members?.length}
                  />

                  <TableBody>
                    {visibleRows.map((row, index) => {
                      const labelId = `enhanced-table-checkbox-${index}`;

                      return (
                        <TableRow
                          hover
                          onClick={(event) => {
                            handleClick(event, row);
                          }}
                          role="checkbox"
                          aria-checked={row.id === selected[0]}
                          tabIndex={-1}
                          key={row.id}
                          selected={row.id === selected[0]}
                          sx={{ cursor: 'pointer' }}
                        >
                          <TableCell className="col-2 col-lg-4">
                            <div className="d-flex align-items-center w-100">
                              <RenderIf
                                condition={
                                  row.membership_status === 'owner' ||
                                  hasPrivilege
                                }
                              >
                                <Checkbox
                                  checked={row.id === selected[0]}
                                  inputProps={{
                                    'aria-labelledby': labelId
                                  }}
                                  onChange={(event) => {
                                    handleClick(event, row);
                                  }}
                                />
                              </RenderIf>

                              <div className="d-lg-flex align-items-center w-100">
                                <div className="d-flex align-items-center w-100">
                                  <CustomImage
                                    imageUrl={row && row.photo_url}
                                    alt={t('Cant_load_image')}
                                    isUser
                                  />
                                  <div className="right-side col-4 col-lg-4 ">
                                    <Tooltip
                                      title={row.email}
                                      placement="bottom"
                                    >
                                      <span className="t-body-regular bold c-fake-black">
                                        {cutString(row.email, 30)}
                                      </span>
                                    </Tooltip>
                                  </div>
                                </div>

                                <div
                                  className={`col-lg-3 status-${row.membership_status} d-flex align-items-center justify-content-center`}
                                >
                                  <span className="t-body-regular bold c-primary-color status-text justify-content-center d-inline-block text-center">
                                    {t(
                                      firstLetterUpperCase(
                                        `${row.membership_status}_text`
                                      )
                                    )}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </TableCell>

                          <TableCell
                            align={row.phone_number ? 'left' : 'center'}
                            id={labelId}
                            className="col-lg-2"
                          >
                            {formatPhoneNumber(row.phone_number)}
                          </TableCell>
                          <TableCell
                            align="left"
                            id={labelId}
                            className="col-4"
                          >
                            <ProjectsData member={row} t={t} />
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
              <TablePagination
                rowsPerPageOptions={[5, 10, 25]}
                component="div"
                count={members?.length}
                rowsPerPage={rowsPerPage}
                page={page}
                labelRowsPerPage={t('Rows_per_page')}
                labelDisplayedRows={({ from, to, count }) =>
                  `${from}-${to} ${t('Of')} ${count}`
                }
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            </Box>
          )}
          {status === 'owner' &&
            members?.map((member, key) => {
              return (
                <MemberDataItem key={key} member={member} type={type} t={t} />
              );
            })}
        </RenderIf>
      </div>
    </div>
  );
}
