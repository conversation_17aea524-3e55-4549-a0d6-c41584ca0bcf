import React, { useEffect, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus } from '@fortawesome/fontawesome-free-solid';
import AddPromotionModal from './Add_promotion';
import NoResult from '../../search_result/No_results';
import RenderIf from '../../../shared/components/Render_if';
import { useLodger } from '../../../shared/context/Lodger_context';
import { usePromotions } from '../../../shared/context/Promotion_context';
import { isEmpty } from 'lodash';
import CustomImage from '../../../shared/components/images/Custom_image';
import ConfirmationModal from '../../../shared/components/modals/Confirmation_modal';
import ToloIsLoading from '../../../shared/components/cards/Tolo_is_loading';
import SuccessPopUp from '../../../shared/components/modals/Success_pop_up';
import Popup from '../../../shared/components/modals/Popup';
import { formatPhoneNumber } from '../../../shared/helpers/String_helps';
import CustomButton from '../../../shared/components/buttons/Custom_button';
import { PROMOTION } from '../../../shared/helpers/Url_prefixes';
import PromotionEmptyState from '../../../style/assets/img/empty_state/Promotion_empty_state.svg';

export default function PricingAndMarketing({ t }) {
  const { getAllLodgerByCountry, getLodgerById } = useLodger();
  const { GetPromotions, DeletePromotion } = usePromotions();
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialised, setIsInitialised] = useState(false);
  const [lodgers, setLodgers] = useState([]);
  const [promotions, setPromotions] = useState([]);
  const [show, setShow] = useState(false);
  const [code, setCode] = useState('');
  const [showActions, setShowActions] = useState({
    showSuccess: false,
    showError: false
  });
  const [onAction, setOnAction] = useState(false);
  const [confirmationModal, setConfirmationModal] = useState(false);
  const [response, setResponse] = useState(null);

  const handleClosePopUp = () => {
    setShowActions({ showError: false, showSuccess: false });
    setConfirmationModal(false);
  };

  function handleShow() {
    setShow(!show);
  }

  useEffect(() => {
    async function fetchAndProcessLodgers() {
      const {
        address: { country }
      } = JSON.parse(sessionStorage.getItem('equipper'));
      const { data, status } = await getAllLodgerByCountry(country);
      if (status === 200 && data) {
        const filteredLodgers = data.filter(
          (lodger) => lodger.type === 'pro' || lodger.type === 'private'
        );
        const lodgersToDisplay = isEmpty(promotions)
          ? filteredLodgers
          : filteredLodgers.filter(
              (lodger) =>
                !promotions.some(
                  (promotion) => promotion.lodger_id === lodger.id
                )
            );
        const formattedLodgers = lodgersToDisplay.map((lodger) => ({
          label:
            lodger.type === 'pro'
              ? `${lodger.full_name} ❖ ${lodger.company}`
              : lodger.full_name,
          value: lodger.id
        }));
        setLodgers(formattedLodgers);
      }
    }
    fetchAndProcessLodgers();
  }, [promotions]);

  const handleConfirmationModal = (code) => {
    setConfirmationModal(!confirmationModal);
    setCode(code);
  };

  async function deletePromotion(code) {
    setOnAction(true);
    const { status } = await DeletePromotion(code);
    if (status === 200) {
      setPromotions(promotions.filter((el) => el.promotion_id !== code));
      setShowActions({ showError: false, showSuccess: true });
    } else {
      setResponse(response);
      setShowActions({ showError: true, showSuccess: false });
    }
    setOnAction(false);
  }
  async function getPromotions() {
    setIsLoading(true);
    try {
      const { data, status } = await GetPromotions();
      if (status === 200 && data) {
        const promotionsData = await Promise.all(
          data.map(async (item) => {
            const { data } = await getLodgerById(item.lodger_id);
            return {
              ...item,
              promotion_id: item.id,
              ...data
            };
          })
        );
        setPromotions(promotionsData);
      }
    } catch (error) {
      console.error('Error fetching promotions:', error);
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    if (!isInitialised) {
      getPromotions();
      setIsInitialised(true);
    }
  }, [isInitialised, promotions]);

  if (isLoading) {
    return <ToloIsLoading />;
  }

  return (
    <div className="panel" id="p6">
      <ConfirmationModal
        show={confirmationModal}
        onClose={() => {
          handleConfirmationModal();
          setIsInitialised(false);
        }}
        isLoading={onAction}
        action={() => deletePromotion(code)}
        t={t}
        buttonText={t('Delete_promotion')}
        message={t('Are_you_sure_you_want_to_delete_this_promotion')}
      />
      <SuccessPopUp show={showActions.showSuccess} onClose={handleClosePopUp} />
      <Popup
        show={showActions.showError}
        onClose={handleClosePopUp}
        t={t}
        prefix={PROMOTION}
        response={response}
      />

      <RenderIf condition={show}>
        <AddPromotionModal
          t={t}
          lodgers={lodgers}
          handleShow={handleShow}
          setIsInitialised={setIsInitialised}
        />
      </RenderIf>
      <div className="team-managements white-bg special">
        <h2 className="t-header-medium-34 c-primary-color bold">
          {t('Promotions_for_lodgers')}
        </h2>

        {isEmpty(promotions) ? (
          <NoResult
            message={t('No_promotions_found')}
            onClick={handleShow}
            buttonText={t('Apply_promotion')}
            image={PromotionEmptyState}
          />
        ) : (
          <>
            <div className="equipments-search">
              <div className="row">
                <div className="col-lg-6">
                  <div className="form-group" />
                </div>
                <div className="col-lg-6 text-lg-end">
                  <button
                    className="round-button yellow bold"
                    onClick={handleShow}
                  >
                    <FontAwesomeIcon icon={faPlus} />
                    {t('Apply_promotion')}
                  </button>
                </div>
              </div>
            </div>

            <div className="table-equipments">
              <div className="table-equipments__head">
                <div className="row collab-table">
                  <div className="col-lg-4 border">
                    <span className="t-base-medium extraBold c-primary-color">
                      {t('Renters')}
                    </span>
                  </div>

                  <div className="col-lg-3 border">
                    <span className="t-base-medium extraBold c-primary-color">
                      {t('Phone_number')}
                    </span>
                  </div>
                  <div className="col-lg-2 border">
                    <span className="t-base-medium extraBold c-primary-color">
                      {t('Discounts')} (%)
                    </span>
                  </div>
                  <div className="col-lg-3 border">
                    <span className="t-base-medium extraBold c-primary-color">
                      {t('Actions')}
                    </span>
                  </div>
                </div>
              </div>
              <div className="table-equipments__body three-column">
                {promotions?.map((promotion, key) => {
                  return (
                    <div className="row collab-table" key={key}>
                      <div className="col-lg-4 border ">
                        <div className="align-items-center d-lg-flex">
                          <div className="d-flex">
                            <CustomImage
                              imageUrl={promotion.photo_url}
                              alt={t('Cant_load_image')}
                              isUser
                            />
                            <div className="right-side">
                              <span className="t-base-medium bold c-primary-color pad-top-4 ">
                                {promotion.full_name}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="border col-lg-3">
                        <div className="d-lg-inline-block d-flex">
                          <span className="mobile-left mobile-pad t-base-medium extraBold c-primary-color d-lg-none d-inline-block">
                            {t('Phone_number')}
                          </span>

                          <span className="t-base-medium mobile-pad c-primary-color">
                            {formatPhoneNumber(promotion.phone_number)}
                          </span>
                        </div>
                      </div>
                      <div className="border col-lg-2">
                        <div className="d-lg-inline-block d-flex">
                          <span className="mobile-left mobile-pad t-base-medium extraBold c-primary-color d-lg-none d-inline-block">
                            {t('Discounts')}
                          </span>

                          <span className="t-base-medium mobile-pad c-primary-color">
                            {promotion?.percent_off}%
                          </span>
                        </div>
                      </div>
                      <div className="border col-lg-3">
                        <CustomButton
                          className="delete-button-promotion"
                          textButton={t('Delete')}
                          onClick={() =>
                            handleConfirmationModal(promotion.promotion_id)
                          }
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
