import React, { useMemo, useState } from 'react';
import EquipmentsModal from '../../modals/Equipments_modal';
import RenderIf from '../../Render_if';
import CustomButton from '../../buttons/Custom_button';
import {
  equipmentsDetails,
  priceData,
  pricesDetails
} from '../../../helpers/Data_helper';
import RequestDetail from '../../cards/Request_details';
import { getCookies } from '../../../helpers/Cookies';
import { useRequest } from '../../../context/Requests_context';
import Popup from '../../modals/Popup';
import SuccessModal from '../../modals/Success_pop_up';
import ConfirmationModal from '../../modals/Confirmation_modal';

export default function RequestDetailsModal({
  data,
  onClose,
  showModal,
  role,
  t,
  detectLanguage
}) {
  const [showViewDetails, setShowViewDetails] = useState(false);
  const { AcceptRequest, RejectRequest } = useRequest();
  const [response, setResponse] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [showPopup, setShowPopup] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [buttonText, setButtonText] = useState();
  const [isLoading, setIsLoading] = useState(false);
  const [show, setShow] = useState(false);

  const handleRejectRequest = async (id) => {
    setIsLoading(true);
    const res = await RejectRequest(id);
    if (res.status === 200) {
      setShow(true);
      setResponse(res);
    } else {
      setShowPopup(true);
      setResponse(res);
      setErrorMessage(res.error.error);
    }
    setIsLoading(false);
  };

  const handleAcceptRequest = async (id, data) => {
    setIsLoading(true);
    const res = await AcceptRequest(id, data);
    if (res.status === 200) {
      setShow(true);
      setResponse(res);
    } else {
      setErrorMessage(res.error.error);
      setResponse(res);
      setShowPopup(true);
    }
    setIsLoading(false);
  };

  const handleClose = () => {
    setShowConfirmation(false);
  };

  const declineConfirmationModal = () => {
    setShowConfirmation(true);
    setButtonText('Decline');
  };

  const acceptConfirmationModal = () => {
    setShowConfirmation(true);
    setButtonText('Accept_booking');
  };

  const handleShowViewDetails = () => {
    setShowViewDetails(!showViewDetails);
  };

  const isUS = getCookies('country') === 'United States';

  const onSubmit = () => {
    handleAcceptRequest(data?.id, {
      ...data,
      special_price: {}
    });
  };
  const equipments = useMemo(() => equipmentsDetails(t, data), [data, t]);
  const pricesData = useMemo(() => priceData(data, t), [data, t]);
  const priceDetails = useMemo(
    () => pricesDetails({ ...data, discount: data?.discount_rate }, t),
    [data, t]
  );

  return (
    <>
      <RenderIf condition={showModal && data}>
        <EquipmentsModal
          handleShowViewDetails={handleShowViewDetails}
          showViewDetails={showViewDetails}
          detectLanguage={detectLanguage}
          onClose={() => onClose()}
          data={{ ...data, discount: data?.discount_rate }}
          isBooking
          isEquipper={role === 'equipper'}
          priceDetailsData={priceDetails}
          isBookingDetails
          t={t}
        >
          <div className="infos-equipments">
            <RequestDetail
              t={t}
              data={data}
              role={role}
              isEquipper={role === 'equipper'}
              isUS={isUS}
              showViewDetails={showViewDetails}
              equipments={equipments}
              pricesData={pricesData}
              priceDetails={priceDetails}
              isRequest
            />
          </div>
          <div className="text-center mt-4 fixed-button-modal">
            <RenderIf
              condition={role === 'equipper' && data?.status === 'pending'}
            >
              <CustomButton
                className="round-button black"
                onClick={() => declineConfirmationModal()}
                textButton={t('Decline')}
              />
            </RenderIf>
            <CustomButton
              className="round-button bold yellow "
              isLoading={isLoading}
              onClick={() =>
                role === 'equipper' && data?.status === 'pending'
                  ? acceptConfirmationModal()
                  : onClose()
              }
              textButton={
                role === 'equipper' && data?.status === 'pending'
                  ? t('Accept_booking')
                  : t('Close_button')
              }
            />
          </div>
        </EquipmentsModal>
      </RenderIf>

      <RenderIf condition={showConfirmation}>
        <ConfirmationModal
          show={showConfirmation}
          isLoading={isLoading}
          onClose={handleClose}
          action={
            buttonText === 'Decline'
              ? () => handleRejectRequest(data?.id)
              : () => onSubmit()
          }
          buttonText={t(buttonText)}
          message={`${t('Are_you_sure_you_want_to_declined_this_request')}?`}
          t={t}
        />
      </RenderIf>

      <Popup
        show={showPopup}
        onClose={() => {
          setShowPopup(false);
          setShowConfirmation(false);
          window.location.reload();
        }}
        t={t}
        response={response}
        message={errorMessage}
      />
      <SuccessModal
        show={show}
        onClose={() => {
          setShow(false);
          setShowConfirmation(false);
          window.location.reload();
        }}
      />
    </>
  );
}
