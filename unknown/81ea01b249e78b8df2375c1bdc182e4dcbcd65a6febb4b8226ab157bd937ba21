package firestoredb

import (
	"context"
	"errors"
	"fmt"
	"math"
	"time"

	"cloud.google.com/go/firestore"
	"google.golang.org/api/iterator"

	"github.com/vima-inc/derental/models"
)

const bookEquipmentCollectionName = "book_equipments"

// GetBookEquipmentByID returns a booking equipment by id.
func (f *firestoredb) GetBookEquipmentByID(ctx context.Context, id string) (models.BookEquipment, error) {
	snapshot, err := f.client.Collection(bookEquipmentCollectionName).Doc(id).Get(ctx)
	if err != nil {
		return models.BookEquipment{}, fmt.Errorf("unable to retrieve book equipment with id: %s error: %w", id, err)
	}

	var bookEquipment models.BookEquipment

	err = snapshot.DataTo(&bookEquipment)
	if err != nil {
		return models.BookEquipment{}, fmt.Errorf("unable to parse book equipment with id: %s error: %w", id, err)
	}

	return bookEquipment, nil
}

// GetBookEquipmentByEquipperID returns a book_equipments by equipper id.
func (f *firestoredb) GetBookEquipmentByEquipperID(ctx context.Context, equipperID string) ([]models.BookEquipment, error) {
	var bookEquipments []models.BookEquipment

	rows := f.client.Collection(bookEquipmentCollectionName).Where("equipper_id", "==", equipperID).Documents(ctx)

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve book_equipment with equipper_id: %s error: %w", equipperID, err)
		}

		var bookEquipment models.BookEquipment

		err = doc.DataTo(&bookEquipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse book_equipment with equipper_id: %s error: %w", equipperID, err)
		}

		bookEquipments = append(bookEquipments, bookEquipment)
	}

	return bookEquipments, nil
}

// GetBookedByStatusAndEquipperID returns a book_equipments by equipper id.
func (f *firestoredb) GetBookedByStatusAndEquipperID(ctx context.Context, equipperID string, status models.BookingStatus, limit int, lastID string) ([]models.BookEquipment, error) {
	var bookEquipments []models.BookEquipment

	query := f.client.Collection(bookEquipmentCollectionName).
		Where("equipper_id", "==", equipperID).
		Where("status", "in", []models.BookingStatus{status}).
		OrderBy("id", firestore.Asc)

	if lastID != "" {
		query = query.StartAfter(lastID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	rows := query.Documents(ctx)

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve book_equipment with lodger_id: %s error: %w", equipperID, err)
		}

		var bookEquipment models.BookEquipment

		err = doc.DataTo(&bookEquipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse book_equipment with lodger_id: %s error: %w", equipperID, err)
		}

		bookEquipments = append(bookEquipments, bookEquipment)
	}

	return bookEquipments, nil
}

// GetBookEquipmentByLodgerID returns a book_equipments by lodger id.
func (f *firestoredb) GetBookEquipmentByLodgerID(ctx context.Context, equipperID string) ([]models.BookEquipment, error) {
	var bookEquipments []models.BookEquipment

	rows := f.client.Collection(bookEquipmentCollectionName).
		Where("lodger_id", "==", equipperID).
		Documents(ctx)

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve book_equipment with lodger_id: %s error: %w", equipperID, err)
		}

		var bookEquipment models.BookEquipment

		err = doc.DataTo(&bookEquipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse book_equipment with lodger_id: %s error: %w", equipperID, err)
		}

		bookEquipments = append(bookEquipments, bookEquipment)
	}

	return bookEquipments, nil
}

// GetInProgressBookingByLodgerID returns a book_equipments by lodger id.
func (f *firestoredb) GetBookedEquipmentByStatusAndLodgerID(ctx context.Context, lodgerID string, status models.BookingStatus, limit int, lastID string) ([]models.BookEquipment, error) {
	var bookEquipments []models.BookEquipment

	query := f.client.Collection(bookEquipmentCollectionName).
		Where("lodger_id", "==", lodgerID).
		Where("status", "in", []models.BookingStatus{status}).
		OrderBy("id", firestore.Asc)

	if lastID != "" {
		query = query.StartAfter(lastID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	rows := query.Documents(ctx)

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve book_equipment with lodger_id: %s error: %w", lodgerID, err)
		}

		var bookEquipment models.BookEquipment

		err = doc.DataTo(&bookEquipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse book_equipment with lodger_id: %s error: %w", lodgerID, err)
		}

		bookEquipments = append(bookEquipments, bookEquipment)
	}

	return bookEquipments, nil
}

// AddBookEquipment adds a new equipment.
func (f *firestoredb) AddBookEquipment(ctx context.Context, bookEquipment models.BookEquipment) (string, error) {
	bookEquipment.UpdatedAt = time.Now()

	newDoc := f.client.Collection(bookEquipmentCollectionName).NewDoc()
	bookEquipment.ID = newDoc.ID

	_, err := newDoc.Set(ctx, bookEquipment)
	if err != nil {
		return "", fmt.Errorf("unable to set book equipment with equipper_id: %s and lodger_id: %s error: %w", bookEquipment.EquipperID, bookEquipment.LodgerID, err)
	}

	return bookEquipment.ID, nil
}

// UpdateEquipment updates an existing book equipment.
func (f *firestoredb) UpdateBookEquipment(ctx context.Context, bookEquipment models.BookEquipment) error {
	bookEquipment.UpdatedAt = time.Now()

	_, err := f.client.Collection(bookEquipmentCollectionName).Doc(bookEquipment.ID).Set(ctx, bookEquipment)
	if err != nil {
		return fmt.Errorf("unable to set book equipment with id: %s error: %w", bookEquipment.ID, err)
	}

	return nil
}

// DeleteBookEquipment deletes an existing book equipment by id.
func (f *firestoredb) DeleteBookEquipment(ctx context.Context, id string) error {
	_, err := f.client.Collection(bookEquipmentCollectionName).Doc(id).Delete(ctx)
	if err != nil {
		return fmt.Errorf("unable to delete book equipment with id: %s error: %w", id, err)
	}

	return nil
}

// GetBookEquipmentByEquipmentID returns a book_equipments by equipment id.
func (f *firestoredb) GetBookEquipmentByEquipmentID(ctx context.Context, equipmentID string) ([]models.BookEquipment, error) {
	query := f.client.Collection(bookEquipmentCollectionName).Where("equipment_id", "==", equipmentID).OrderBy("id", firestore.Asc)

	rows := query.Documents(ctx)

	var bookEquipments []models.BookEquipment

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve book_equipment with equipment_id: %s error: %w", equipmentID, err)
		}

		var bookEquipment models.BookEquipment

		err = doc.DataTo(&bookEquipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse book_equipment with equipment_id: %s error: %w", equipmentID, err)
		}

		bookEquipments = append(bookEquipments, bookEquipment)
	}

	return bookEquipments, nil
}

// GetAcceptedBookEquipmentByEquipmentID returns a book_equipments by equipment id.
func (f *firestoredb) GetAcceptedBookEquipmentByEquipmentID(ctx context.Context, equipmentID string) (models.BookEquipment, error) {
	query := f.client.Collection(bookEquipmentCollectionName).Where("equipment_id", "==", equipmentID).Where("status", "==", models.BookingAccepted).OrderBy("id", firestore.Asc)

	rows := query.Documents(ctx)

	var bookEquipment models.BookEquipment

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return bookEquipment, fmt.Errorf("unable to retrieve book_equipment with equipment_id: %s error: %w", equipmentID, err)
		}

		err = doc.DataTo(&bookEquipment)
		if err != nil {
			return bookEquipment, fmt.Errorf("unable to parse book_equipment with equipment_id: %s error: %w", equipmentID, err)
		}
	}

	return bookEquipment, nil
}

// GetBookingRequestsByCCFID returns a book_equipments by ccf id.
func (f *firestoredb) GetBookingRequestsByCCFID(ctx context.Context, ccfID string) ([]models.BookEquipment, error) {
	query := f.client.Collection(bookEquipmentCollectionName).Where("credit_check_form_id", "==", ccfID).OrderBy("id", firestore.Asc)

	rows := query.Documents(ctx)

	var bookEquipments []models.BookEquipment

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve book_equipment with ccf_id: %s error: %w", ccfID, err)
		}

		var bookEquipment models.BookEquipment

		err = doc.DataTo(&bookEquipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse book_equipment with ccf_id: %s error: %w", ccfID, err)
		}

		bookEquipments = append(bookEquipments, bookEquipment)
	}

	return bookEquipments, nil
}

// BatchUpdateBookingRequests updates a batch of booking requests.
func (f *firestoredb) BatchUpdateBookingRequests(ctx context.Context, bookEquipments []models.BookEquipment) error {
	collection := f.client.Collection(bookEquipmentCollectionName)

	totalBooking := len(bookEquipments)
	nbBatch := int(math.Ceil(float64(totalBooking) / float64(maxBatchSize)))

	for i := 0; i < nbBatch; i++ {
		from := i * maxBatchSize
		to := (i + 1) * maxBatchSize

		if to > totalBooking {
			to = totalBooking
		}

		batch := f.client.BulkWriter(ctx)

		for _, booking := range bookEquipments[from:to] {
			if booking.ID == "" {
				return fmt.Errorf("booking request id is empty")
			}

			booking.UpdatedAt = time.Now()
			doc := collection.Doc(booking.ID)

			_, err := batch.Set(doc, booking)
			if err != nil {
				return fmt.Errorf("unable to set booking with id: %s error: %w", booking.ID, err)
			}
		}

		batch.End()
	}

	return nil
}

// GetBookEquipmentByOwnerID returns a book_equipments by owner id.
func (f *firestoredb) GetBookEquipmentByOwnerID(ctx context.Context, ownerID string, status models.BookingStatus) ([]models.BookEquipment, error) {
	query := f.client.Collection(bookEquipmentCollectionName).Where("owner_id", "==", ownerID).Where("status", "==", status).OrderBy("id", firestore.Asc)

	rows := query.Documents(ctx)

	var bookEquipments []models.BookEquipment

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve book_equipment with owner_id: %s error: %w", ownerID, err)
		}

		var bookEquipment models.BookEquipment

		err = doc.DataTo(&bookEquipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse book_equipment with owner_id: %s error: %w", ownerID, err)
		}

		bookEquipments = append(bookEquipments, bookEquipment)
	}

	return bookEquipments, nil
}

// GetBookEquipmentByAdminIDs returns a book_equipments by admin ids.
func (f *firestoredb) GetBookEquipmentByAdminIDs(ctx context.Context, adminID string, status models.BookingStatus) ([]models.BookEquipment, error) {
	query := f.client.Collection(bookEquipmentCollectionName).Where("admin_ids", "array-contains", adminID).Where("status", "==", status).OrderBy("id", firestore.Asc)

	rows := query.Documents(ctx)

	var bookEquipments []models.BookEquipment

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve book_equipment with admin_id: %s error: %w", adminID, err)
		}

		var bookEquipment models.BookEquipment

		err = doc.DataTo(&bookEquipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse book_equipment with admin_id: %s error: %w", adminID, err)
		}

		bookEquipments = append(bookEquipments, bookEquipment)
	}

	return bookEquipments, nil
}

// GetBookEquipmentByStatusAndEndDate returns a book_equipments by status and EndDate
func (f *firestoredb) GetBookEquipmentByStatusAndEndDate(ctx context.Context) ([]models.BookEquipment, error) {
	var bookEquipments []models.BookEquipment
	now := time.Now()
	query := f.client.Collection(bookEquipmentCollectionName).
		Where("status", "==", models.BookingAccepted).
		Where("end_date", "<=", now)

	rows := query.Documents(ctx)

	for {
		doc, err := rows.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to retrieve book_equipments error: %w", err)
		}

		var bookEquipment models.BookEquipment

		err = doc.DataTo(&bookEquipment)
		if err != nil {
			return nil, fmt.Errorf("unable to parse book_equipment with error: %w", err)
		}

		bookEquipments = append(bookEquipments, bookEquipment)
	}

	return bookEquipments, nil
}
