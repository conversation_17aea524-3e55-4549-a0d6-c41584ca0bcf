import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import InstantSearchAlgolia from '../../../components/search_result/Instant_search_algolia';
import SearchInputAutoCompleteEquipment from '../../../features/search_result/Search_input_auto_complete_equipment';
import { getSessionStorage } from '../../helpers/Session_storage_helper';
import CustomButton from '../buttons/Custom_button';
import DatePicker from '../date_picker/Date_picker';
import { navigateToSearchResult } from '../../helpers/Algolia_helper';
import { isDisabled } from '../../helpers/Date_helper';

export default function NavBarSearch({ detectLanguage, t }) {
  const navigate = useNavigate();
  const date = new Date();
  const [endDate, setEndDate] = useState(
    new Date(date.setDate(date.getDate() + 6))
  );
  const [startDate, setStartDate] = useState(new Date());
  const [location, setLocation] = useState({
    value: '',
    isSelected: false
  });
  const [equipmentID, setEquipmentID] = useState('');
  const requestedName = getSessionStorage('requestedName');

  const [equipmentName, setEquipmentName] = useState({
    requestedName: requestedName || '',
    name_en: ''
  });
  const [searchState, setSearchState] = useState({
    range: {
      available_from: {
        max: startDate?.getTime()
      }
    },
    menu: {
      coverage_area: location.value,
      name: equipmentName.requestedName
        ? equipmentName.requestedName
        : equipmentName.name_en,
      name_fr: equipmentName.name_fr
    }
  });

  const currentTime = new Date();

  const handleStartDateChange = (date) => {
    date.setHours(
      currentTime.getHours(),
      currentTime.getMinutes(),
      currentTime.getSeconds(),
      currentTime.getMilliseconds()
    );
    setStartDate(date);
  };

  const handleEndDateChange = (date) => {
    date.setHours(
      currentTime.getHours(),
      currentTime.getMinutes(),
      currentTime.getSeconds(),
      currentTime.getMilliseconds()
    );
    setEndDate(date);
  };

  function handleStateSwitch(searchState) {
    setSearchState(searchState);
  }
  const sessionStorageStartDate = getSessionStorage('start_date');
  const sessionStorageEndDate = getSessionStorage('end_date');
  const id = getSessionStorage('equipmentID');
  const nameEn = getSessionStorage('nameEn');
  const nameFr = getSessionStorage('nameFr');
  const covArea = getSessionStorage('location');
  useEffect(() => {
    setEquipmentID(id);
    if (covArea) {
      setLocation({ value: covArea, isSelected: true });
    }
    if (sessionStorageStartDate && sessionStorageEndDate) {
      setStartDate(new Date(parseInt(sessionStorageStartDate)));
      setEndDate(new Date(parseInt(sessionStorageEndDate)));
    }
    if (requestedName || nameEn || nameFr)
      setEquipmentName({
        requestedName: requestedName,
        name_fr: nameFr,
        name_en: nameEn
      });
  }, [
    detectLanguage,
    sessionStorageStartDate,
    sessionStorageEndDate,
    covArea,
    requestedName,
    nameEn
  ]);
  const isSearchButtonEnabled =
    equipmentID &&
    location.isSelected &&
    !isDisabled(startDate, endDate) &&
    startDate &&
    endDate;

  const popperText = !equipmentID
    ? t('Select_equipment')
    : !location.isSelected
    ? t('Set_your_location')
    : isDisabled(startDate, endDate)
    ? t('Date_error_msg')
    : t('Search');
  return (
    <InstantSearchAlgolia
      searchState={searchState}
      onSearchStateChange={(searchState) => {
        handleStateSwitch(searchState);
      }}
      indexName={import.meta.env.VITE_ALGOLIA_INDEX_NAME}
    >
      <div className="center disapear-on-collapse">
        <div className="row NavBarSearchBar">
          <div className="col-md-3 col-sm-10 col-lg-3 border-gradient-right border-gradient-yellow-navbar">
            <SearchInputAutoCompleteEquipment
              attribute="equipment"
              placeholder={t('Search_placeholder')}
              value={equipmentName.name_en || equipmentName.requestedName || ''}
              onChange={setEquipmentName}
              detectLanguage={detectLanguage}
              indexName={import.meta.env.VITE_ALGOLIA_INDEX_NAME_STANDARDS}
              setEquipmentID={setEquipmentID}
              t={t}
              isAddEquipment
              equipmentNameClassName="container-wrapper-equip-name container wrapper"
              isEquipment
            />
          </div>
          <div className=" col-md-3 col-sm-10 col-lg-3 border-gradient-right moxy border-gradient-yellow-navbar set_location">
            <SearchInputAutoCompleteEquipment
              attribute="location"
              placeholder={t('Set_your_location')}
              value={location.value || ''}
              indexName={import.meta.env.VITE_ALGOLIA_INDEX_COVERAGE_AREAS_NAME}
              t={t}
              onChange={setLocation}
            />
          </div>
          <div className="col-md-2 col-sm-12 border-gradient-right border-gradient-yellow-navbar">
            <DatePicker
              startDateClassName="searchInput-z no-border fwb"
              endDateClassName="searchInput-z no-border fwb"
              handleStartDateChange={handleStartDateChange}
              handleEndDateChange={handleEndDateChange}
              startDate={startDate}
              endDate={endDate}
            />
          </div>
          <div className="col-md-3 col-sm-12 border-gradient-right border-gradient-far-right" />
          <div className="col-md-1 col-sm-10 col-lg-1">
            <CustomButton
              textButton={<FontAwesomeIcon icon="search" />}
              onClick={() => {
                navigateToSearchResult(
                  equipmentName,
                  location,
                  startDate,
                  endDate,
                  equipmentID,
                  navigate
                );
              }}
              className={`button-search-navbar ${
                isSearchButtonEnabled ? '' : 'c-near-grey'
              }`}
              textPopper={popperText}
              disabled={!isSearchButtonEnabled}
            />
          </div>
        </div>
      </div>
    </InstantSearchAlgolia>
  );
}
