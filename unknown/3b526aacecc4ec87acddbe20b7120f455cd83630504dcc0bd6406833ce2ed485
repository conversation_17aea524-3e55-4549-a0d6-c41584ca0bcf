.Sbutton {
  position: relative;
  height: 56px;
  background-color: transparent !important;
  border: transparent !important;
  @media (max-width: 768px) {
    width: 100%;
  }
}

.Sbutton:hover {
  background-color: transparent !important;
  border: transparent !important;
}

.Sbutton input {
  position: relative;
  width: 500px;
  height: 50px;
  border-radius: 6px;
  outline: none;
  cursor: pointer;
  appearance: none;
  box-shadow: none;
  margin-right: 0;
  font-weight: bold;
  @media (max-width: 768px) {
    width: 100%;
  }
  @media (max-width: 572px) {
    font-size: 12px;
  }
}

.ft-12{
  font-size: 12px;
}

.Sbutton input:before,
.Sbutton input:after {
  z-index: 2;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  text-transform: uppercase;
}

.Sbutton input:before {
  content: attr(data-contenta);
  left: 48px;
  @media (min-width: 768px) {
    left: 90px;
  }
}

.Sbutton input:after {
  content: attr(data-contentb);
  right: 66px;
  @media(max-width: 992px) {
    right: 44px;
  }
}

.Sbutton label {
  z-index: 1;
  position: absolute;
  top: 10px;
  bottom: 5px;
  border-radius: 6px;
}

.Sbutton.Sbutton-1 input {
  //transition: 0.2s -0.1s;
}

.Sbutton.Sbutton-1 input:checked {
  background: rgba(236, 168, 105, 0.05);
  border: 1px solid $yellow;
}

.Sbutton.Sbutton-1 input:checked:before {
  color: $primary-color;
  //transition: color 0.5s 0.2s;
}

.Sbutton.Sbutton-1 input:checked:after {
  color: $fake-black;
  //transition: color 0.5s;
}

.Sbutton.Sbutton-1 input:checked + label {
  left: 20px;
  right: 280px;
  background: #ECA869;
  //transition: left 0.5s, right 0.4s 0.2s;
  @media (max-width: 768px) {
    width: 150px;
  }
}

.Sbutton.Sbutton-1 input:not(:checked) {
  background: rgba(236, 168, 105, 0.05);
  border: 1px solid #ECA869;
  //transition: background 0.5s -0.1s;
}

.Sbutton.Sbutton-1 input:not(:checked):before {
  color: $fake-black;
  //transition: color 0.5s;
}

.Sbutton.Sbutton-1 input:not(:checked):after {
  color: $primary-color;
  //transition: color 0.5s 0.2s;
}

.Sbutton.Sbutton-1 input:not(:checked) + label {
  left: 280px;
  right: 20px;
  background: #ECA869;
  //transition: left 0.4s 0.2s, right 0.5s, background 0.35s -0.1s;
  //transition: left 0.4s 0.2s, right 0.5s, background 0.35s -0.1s;
  @media (max-width: 768px) {
    width: 170px;
    left: calc(100% - 192px);
  }
}

.tooloLodger {
  height: 100%;
  padding-right: 70%;
}

.tooloEquipper {
  height: 100%;
  padding-right: 60%;
  @media (max-width: 768px) {
    display: none;
  }
}
