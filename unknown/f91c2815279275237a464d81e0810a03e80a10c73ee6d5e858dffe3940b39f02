import React from 'react';
import { useTranslation } from 'react-i18next';
import CustomImage from '../../shared/components/images/Custom_image';
import toloEmpty from '../../style/assets/img/no_results.svg';

export default function NoResults(props) {
  const { t } = useTranslation();

  return (
    <div className="empty-content empty-state-container mb-5 white-bg mt-5">
      <div className="empty-content__image mt-4">
        <CustomImage
          imageUrl={props.image ? props.image : toloEmpty}
          alt={t('Cant_load_image')}
        />
      </div>
      <div className="empty-content__content text-align-center ">
        <p className="c-black t-subheading-2 bold c-fake-black">
          {props.message ? props.message : t('No_results_found')}
        </p>
        {props.buttonText && (
          <button
            className="round-button yellow bold c-white hover_black width-100 mb-4 mt-4"
            onClick={props.onClick}
          >
            {props.buttonText}
          </button>
        )}
      </div>
    </div>
  );
}
