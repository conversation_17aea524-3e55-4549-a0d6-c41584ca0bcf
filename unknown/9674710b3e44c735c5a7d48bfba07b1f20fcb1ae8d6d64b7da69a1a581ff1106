.privacy-notice,
.terms-conditions {
  body {
    background: #fff;
    color: #000;
    font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
    font-size: 16px;
    line-height: 28px;
    margin: 0;
  }

  h1 {
    font-size: 30px;
    line-height: 60px;
  }

  h1,
  h2 {
    font-weight: 700;
  }

  h2 {
    font-size: 22px;
    line-height: 48px;
  }

  h3 {
    font-size: 18px;
    line-height: 36px;
  }

  h3,
  h4 {
    font-weight: 700;
  }

  h4 {
    font-size: 15px;
    line-height: 30px;
  }

  h5 {
    font-size: 14px;
  }

  h5,
  h6 {
    line-height: 24px;
    font-weight: 700;
  }

  h6 {
    font-size: cacl(16px);
  }

  a {
    text-decoration: none;
    cursor: pointer;
    color: #000;
  }

  a:hover,
  a[rel~='nofollow'] {
    text-decoration: underline;
  }

  a[rel~='nofollow'] {
    color: #0452a5;
  }

  a[rel~='nofollow']:hover {
    text-decoration: none;
  }

  .visible {
    display: block;
  }

  .hidden {
    display: none;
  }

  .page {
    width: 100%;
  }

  .container {
    position: relative;
    width: 90%;
    max-width: 1024px;
    margin: 0 auto;
  }

  .header {
    color: #000;
    padding: 16px 0;
  }

  .header .title {
    font-size: 24px;
    line-height: 24px;
    font-weight: 700;
    margin: 0;
  }

  .translations-list-container {
    color: #000;
    padding-bottom: 8px;
    margin: 0 0 16px;
  }

  .translations-list-container .translations-list {
    margin: 0;
    padding: 0;
    list-style: none;
  }

  .translations-list-container .translations-list .translations-list-item {
    display: inline-block;
    padding: 0;
    margin: 0 8px 8px 0;
    color: #fff;
  }

  .translations-list-container .translations-list .translations-list-item a {
    display: inline-block;
    padding: 4px 8px;
  }

  .translations-list-container
    .translations-list
    .translations-list-item
    a.active {
    color: #fff;
    background: #334055;
  }

  .translations-content-container {
    padding-top: 8px;
    border-top: 1px solid #eee;
  }

  .footer {
    border-top: 1px solid #eee;
    margin: 32px 0 0;
    padding: 16px 0;
  }
}
.title {
  text-align: center;
}
.terms-conditions {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  color: #333;

  .header {
    background-color: #f8f8f8;
    padding: 2rem 0;
    border-bottom: 1px solid #e0e0e0;

    .title {
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
      text-align: center;
    }

    .last-updated {
      text-align: center;
      color: #666;
      font-style: italic;
    }
  }

  .content-container {
    padding: 2rem 0;

    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 0 1rem;
    }

    h2 {
      font-size: 1.8rem;
      margin-top: 2rem;
      margin-bottom: 1rem;
      color: #2c3e50;
    }

    p {
      margin-bottom: 1rem;
    }

    ul {
      margin-bottom: 1rem;
      padding-left: 2rem;

      li {
        margin-bottom: 0.5rem;
      }
    }

    .introduction {
      font-size: 1.1rem;
      font-weight: bold;
      margin-bottom: 1.5rem;
    }

    strong {
      color: #2c3e50;
    }
  }
}
