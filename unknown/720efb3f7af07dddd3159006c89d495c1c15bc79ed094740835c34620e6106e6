package models

import (
	"time"
)

// Currency represents the currency of an order.
type Currency string

const (
	USD Currency = "usd"
	CAD Currency = "cad"
	EUR Currency = "eur"
	GBP Currency = "gbp"
	SAR Currency = "sar"
)

// OrderStatus represents the status of an order.
type OrderStatus string

const (
	OrderStatusPending    OrderStatus = "pending"
	OrderStatusAuthorized OrderStatus = "authorized"
	OrderStatusCaptured   OrderStatus = "captured"
	OrderStatusCancelled  OrderStatus = "cancelled"
	OrderStatusFailed     OrderStatus = "failed"
)

// Order represents an order.
type Order struct {
	ID            string      `json:"id" firestore:"id"`
	Items         []Item      `json:"items" firestore:"items"`
	Currency      Currency    `json:"currency" firestore:"currency"`
	Payment       Payment     `json:"payment" firestore:"payment"`
	Status        OrderStatus `json:"status" firestore:"status"`
	CreatedAt     time.Time   `json:"created_at" firestore:"created_at"`
	UpdatedAt     time.Time   `json:"updated_at" firestore:"updated_at"`
	CustomerID    string      `json:"customer_id" firestore:"customer_id"`
	CustomerEmail string      `json:"customer_email" firestore:"customer_email"`
	UserID        string      `json:"user_id" firestore:"user_id"`
	BookingID     string      `json:"booking_id" firestore:"booking_id"`
	IsBidz        bool        `json:"is_bidz" firestore:"is_bidz"`
	Address       Address     `json:"address" firestore:"address"`
}

// Item represents an item in an order.
type Item struct {
	ID        string `json:"id" firestore:"id"`
	Name      string `json:"name" firestore:"name"`
	Quantity  int64  `json:"quantity" firestore:"quantity"`
	Price     int64  `json:"price" firestore:"price"`
	IsTaxable bool   `json:"is_taxable" firestore:"is_taxable"`
	Coupon    string `json:"coupon" firestore:"coupon"`
}

// Payment represents a payment.
type Payment struct {
	PaymentIntentID string `json:"payment_intent_id" firestore:"payment_intent_id"`
	Amount          int64  `json:"amount" firestore:"amount"`
	AmountCaptured  int64  `json:"amount_captured" firestore:"amount_captured"`
	Discount        int64  `json:"discount" firestore:"discount"`
	Tax             int64  `json:"tax" firestore:"tax"`
}

// ChargeResponse represents a response from the charge endpoint.
type ChargeResponse struct {
	OrderID         string `json:"order_id"`
	PaymentIntentID string `json:"payment_intent_id"`
}

// CheckoutResponse represents a response from the checkout endpoint.
type CheckoutResponse struct {
	PaymentIntentID string  `json:"payment_intent_id"`
	Amount          int64   `json:"amount"`
	PaidAmount      int64   `json:"sub_amount"`
	DiscountAmount  int64   `json:"discount"`
	TaxAmount       int64   `json:"tax"`
	Percentage      float64 `json:"tax_percentage"`
}

// WebhookEvent represents a webhook event.
type WebhookEvent struct {
	Signature string `json:"signature"`
	Payload   []byte `json:"payload"`
}

type InvoiceDetails struct {
	PaymentDate          string        `json:"payment_date"`
	TaxName              string        `json:"registration_name"`
	TaxPercentage        string        `json:"tax_percentage"`
	TaxValue             string        `json:"tax_value"`
	Currency             string        `json:"currency"`
	Products             []Product     `json:"products"`
	ClientName           string        `json:"client_name"`
	InvoiceNumber        int64         `json:"invoice_number"`
	ReceiptNumber        int64         `json:"receipt_number"`
	PaymentMethod        PaymentMethod `json:"payment_method"`
	ZipCode              string        `json:"zip_code"`
	Country              string        `json:"country"`
	ClientEmail          string        `json:"client_email"`
	TotalEquipmentsPrice string        `json:"total_equipments_price"`
	TotalAmount          string        `json:"total_amount"`
	DiscountPercentage   string        `json:"discount_percentage"`
	DiscountValue        string        `json:"discount_value"`
	SubTotalHT           string        `json:"sub_total"`
}

type Product struct {
	Name        string `json:"name"`
	Qty         int64  `json:"qty"`
	UnitPrice   string `json:"unit_price"`
	Currency    string `json:"currency"`
	TotalAmount string `json:"total_amount"`
}
