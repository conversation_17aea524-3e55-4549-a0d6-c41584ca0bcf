import React from 'react';
import { Field, ErrorMessage } from 'formik';

export default function InputForm({
  type,
  value,
  name,
  placeholder,
  onChange,
  className,
  isFormik,
  disabled,
  as
}) {
  return (
    <>
      {isFormik ? (
        <>
          <Field
            disabled={disabled}
            name={name}
            type={type}
            autoComplete="off"
            as={as}
            className={className}
            placeholder={placeholder}
          />
          <ErrorMessage
            name={name}
            component="span"
            className="error-message"
          />
        </>
      ) : (
        <input
          type={type}
          value={value}
          name={name}
          className={className}
          placeholder={placeholder}
          onChange={onChange}
          disabled={disabled}
        />
      )}
    </>
  );
}
