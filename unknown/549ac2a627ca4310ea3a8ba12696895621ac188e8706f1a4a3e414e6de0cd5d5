
.create-account {
  margin-top: 10% !important;
}

.create-account h6 {
  margin-top: 1%;
}

.create-account-button {
  border: none;
  background-color: #fff;
  font-weight: bold;
  margin-left: 0;
}


.forget-password {
  text-decoration: none;
  color: rgba(0, 0, 0, 0.581);
  font-weight: bold;
  margin-bottom: 5%;
  cursor: pointer;
}

.radio-button {
  margin-top: 5%;
  margin-left: 5%;
  margin-right: 2%;
  font-weight: bold;
  background-color: yellow;
}

.switch-button-width {
  margin-left: 20%;
  width: 60% !important;
  font-weight: bold;
  font-size: 15px !important;
  align-items: center;

  &:before {
    content: "I am an equiper";
    width: 40%;
  }

  &-checkbox {
    bottom: 0;
    width: 100%;
    height: 100%;

    &:checked + .switch-button-label:before {
      transform: translateX(98%);
      transition: transform 300ms linear;
    }

    & + .switch-button-label {
      position: relative;
      padding: 15px 0;
      display: block;
      user-select: none;
      pointer-events: none;
      border-radius: 30px;

      &:before {
        width: 80% !important;
        transform: translateX(2%);
        transition: transform 300ms;
      }

      .switch-button-label-span {
        position: relative;
        padding-left: 2%;
        padding-right: 50%;
      }
    }
  }
}

.switch-container {
  width: 100%;
  align-items: center;
}

.login-title {
  margin-bottom: 3%;
  color: black;
  font-weight: bold;
}

.close-button {
  border: none;
  border-radius: 50%;
  background-color: transparent;
}

.fa-close {
  filter: drop-shadow(1px 2px 4px black);
}
