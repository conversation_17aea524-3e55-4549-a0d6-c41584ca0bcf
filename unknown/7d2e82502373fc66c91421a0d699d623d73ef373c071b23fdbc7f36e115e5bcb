import React, { useState } from 'react';
import * as Yup from 'yup';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/fontawesome-free-solid';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import { usePromotions } from '../../../shared/context/Promotion_context';
import CustomButton from '../../../shared/components/buttons/Custom_button';
import SuccessPopUp from '../../../shared/components/modals/Success_pop_up';
import Popup from '../../../shared/components/modals/Popup';
import MultipleSelectCheckmarks from '../../../shared/components/multi_select/Multi_select_mui';

export default function AddPromotionModal({
  t,
  handleShow,
  lodgers,
  setIsInitialised
}) {
  const { CreatePromotion } = usePromotions();
  const [isLoading, setIsLoading] = useState(false);
  const [showActions, setShowActions] = useState({
    showSuccess: false,
    showError: false
  });
  const [response, setResponse] = useState(null);

  const handleClosePopUp = () => {
    setShowActions({ showError: false, showSuccess: false });
    handleShow();
    setIsInitialised(false);
  };

  const handleSubmit = (values) => {
    setIsLoading(true);
    values.lodgers.map(async (lodger) => {
      const response = await CreatePromotion({
        lodger_id: lodger,
        percent_off: values.discount,
        currency: 'CAD',
        code: lodger + values.discount,
        duration: 'forever'
      });
      if (response.status === 200) {
        setShowActions({ showError: false, showSuccess: true });
      } else {
        setShowActions({ showError: true, showSuccess: false });
        setResponse(response);
      }
    });
  };

  const validatationSchema = Yup.object().shape({
    lodgers: Yup.array().nullable().required(t('This_field_is_required')),
    discount: Yup.number().min(1).max(100).required(t('This_field_is_required'))
  });

  return (
    <>
      <div className="modal">
        <div className="modal-content no-title-margeTop promotion-modal">
          <button className="close-button" onClick={handleShow}>
            <FontAwesomeIcon icon={faTimes} color="c-primary-color" />
          </button>
          <Formik
            initialValues={{
              lodgers: null,
              discount: '',
              code: Math.random().toString(36).substring(7),
              duration: new Date()
            }}
            onSubmit={handleSubmit}
            validationSchema={validatationSchema}
          >
            {(formik) => (
              <Form>
                <div className="row">
                  <div className="col-lg-10 mx-auto">
                    <h2 className="t-header-large bold credit-title">
                      {t('Apply_promotions_to_lodgers')}
                    </h2>
                    <p className="form-group pt-2  m-2">
                      <label className="t-body-small c-primary-color bold">
                        {t('Renters')}
                      </label>

                      <MultipleSelectCheckmarks
                        options={lodgers}
                        t={t}
                        name="lodgers"
                        onChange={(values) =>
                          formik.setFieldValue('lodgers', values)
                        }
                        value={formik.values.lodgers}
                        placeholder={t('Renters')}
                      />
                      <ErrorMessage
                        name="lodgers"
                        component="span"
                        className="error-message"
                      />
                    </p>
                    <p className="form-group pt-2 m-2">
                      <label className="t-body-small c-primary-color bold">
                        {t('Discounts')} (%)
                      </label>
                      <Field
                        type="number"
                        name="discount"
                        className="form-control w-100"
                        placeholder={t('Discounts')}
                      />

                      <ErrorMessage
                        name="discount"
                        component="span"
                        className="error-message"
                      />
                    </p>

                    <div className="text-center btn-content">
                      <CustomButton
                        className="round-button yellow bold"
                        type="submit"
                        isloading={isLoading}
                        textButton={t('Apply_promotion')}
                      />
                    </div>
                  </div>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
      <Popup
        t={t}
        show={showActions.showError}
        onClose={handleClosePopUp}
        response={response}
      />
      <SuccessPopUp onClose={handleClosePopUp} show={showActions.showSuccess} />
    </>
  );
}
