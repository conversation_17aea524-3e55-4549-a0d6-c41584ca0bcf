{"_type": "export", "__export_format": 4, "__export_date": "2022-03-16T17:13:03.471Z", "__export_source": "insomnia.desktop.app:v2022.1.1", "resources": [{"_id": "req_0d38f43a615e4c04a8ebd8363b6d841b", "parentId": "fld_e9df0317783449f083b6df59fb0757a9", "modified": 1644421168499, "created": 1638463993196, "url": "{{ _.base_url }}/public/lead", "name": "/lead", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n\t\"name\" : \"test\",\n\t\"email\" : \"<EMAIL>\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_6a878bf69f8d4fc8b04a8c65cc221733"}], "authentication": {}, "metaSortKey": -1638463993196, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_e9df0317783449f083b6df59fb0757a9", "parentId": "fld_846048a5e2ba4f23ae5316828c89233c", "modified": 1638463981833, "created": 1638463981833, "name": "lead", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1638463981833, "_type": "request_group"}, {"_id": "fld_846048a5e2ba4f23ae5316828c89233c", "parentId": "wrk_003489a1600e4f12a3a56d6482f6b3f3", "modified": 1636014683793, "created": 1636014683793, "name": "public", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1636014683793, "_type": "request_group"}, {"_id": "wrk_003489a1600e4f12a3a56d6482f6b3f3", "parentId": null, "modified": 1625846320894, "created": 1625846320894, "name": "tooler", "description": "", "scope": "collection", "_type": "workspace"}, {"_id": "req_6630d21993b24655a0d386473c640879", "parentId": "fld_b8b967d8410e446c9b33171c4dd4e2f9", "modified": 1644835753242, "created": 1625846327359, "url": "{{ _.base_url }}/public/signup", "name": "/signup", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n\t\"type\": \"type\",\n\t\"email\": \"<EMAIL>\",\n\t\"first_name\": \"first_name\",\n\t\"last_name\": \"last_name\",\n\t\"user_name\": \"user_name\",\n\t\"company\": \"company\",\n\t\"phone_number\": \"phone_number\",\n\t\"photo_url\": \"photo_url\",\n\t\"address\": \"address\",\n\t\"address2\": \"address2\",\n\t\"zip_code\": \"zip_code\",\n\t\"city\": \"city\",\n\t\"country\": \"country\",\n\t\"password\": \"password\",\n\t\"is_lodger\": false\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_e3494ac8213b470ab8123570521f5d1a"}], "authentication": {}, "metaSortKey": -1625846327359, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_b8b967d8410e446c9b33171c4dd4e2f9", "parentId": "fld_846048a5e2ba4f23ae5316828c89233c", "modified": 1638377340693, "created": 1638377340693, "name": "auth", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1638377340693, "_type": "request_group"}, {"_id": "req_d1a7af0e32464bcf9422fdcfe26ca798", "parentId": "fld_b8b967d8410e446c9b33171c4dd4e2f9", "modified": 1644836010438, "created": 1634647756745, "url": "{{ _.base_url }}/public/signin", "name": "/signin", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n\t\"email\" : \"<PERSON>@toodler.ca\",\n\t\"password\" : \"Azerty123/\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_e3494ac8213b470ab8123570521f5d1a"}], "authentication": {"type": "bearer", "token": ""}, "metaSortKey": -1625846327334, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_58362382e3c141809c21e297317361e7", "parentId": "fld_b8b967d8410e446c9b33171c4dd4e2f9", "modified": 1639135587855, "created": 1636035141010, "url": "{{ _.base_url }}/public/exist/<EMAIL>", "name": "/exist/{email}", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1625846327309, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_0d6faf0f957d481a8d02c8d9e1b7d3d2", "parentId": "fld_4dc182ccf848401ab1499dc283a8a4be", "modified": 1638377320824, "created": 1638272244450, "url": "{{ _.base_url }}/public/password/forgot/<EMAIL>", "name": "/password/forgot", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1638272244450, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_4dc182ccf848401ab1499dc283a8a4be", "parentId": "fld_846048a5e2ba4f23ae5316828c89233c", "modified": 1638377305137, "created": 1638377305137, "name": "password", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1638377305137, "_type": "request_group"}, {"_id": "req_5b084c8ac2b44ac5bdaecce618b9e324", "parentId": "fld_4dc182ccf848401ab1499dc283a8a4be", "modified": 1639500961145, "created": 1638286644378, "url": "{{ _.base_url }}/public/password/validate", "name": "/password/validate", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n\t\"email\" : \"<EMAIL>\",\n\t\"token\": \"227887\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_931b1aa066be4db0a74db651fa91edee"}], "authentication": {}, "metaSortKey": -1638272244400, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_84cd24d169594fcaa3716d5277a6c536", "parentId": "fld_4dc182ccf848401ab1499dc283a8a4be", "modified": 1641826663290, "created": 1638281223529, "url": "{{ _.base_url }}/public/password/reset", "name": "/password/reset", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n\t\"email\" : \"<EMAIL>\",\n\t\"password\" : \"password\",\n\t\"token\": \"798081\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_931b1aa066be4db0a74db651fa91edee"}], "authentication": {}, "metaSortKey": -1638272244350, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_90f0de88dda34803b5910a46843d70e5", "parentId": "fld_02e7470f4d1c4702970ba87aeb37b634", "modified": 1641826671918, "created": 1638376568114, "url": "{{ _.base_url }}/public/equipment/0oEQBjPuJmvD8VVrkBhD", "name": "/equipment/{equipment_id}", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1636314778940, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_02e7470f4d1c4702970ba87aeb37b634", "parentId": "fld_846048a5e2ba4f23ae5316828c89233c", "modified": 1639248941433, "created": 1638377282380, "name": "equipment", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1638377282380, "_type": "request_group"}, {"_id": "req_ff811b0b6cda4b21a4e2e983197122bf", "parentId": "fld_5957df0e21c04490b8e9548624dc6ed8", "modified": 1644835970429, "created": 1635761370897, "url": "{{ _.base_url }}/member", "name": "/member", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}", "disabled": false}, "metaSortKey": -1635761370897, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_5957df0e21c04490b8e9548624dc6ed8", "parentId": "wrk_003489a1600e4f12a3a56d6482f6b3f3", "modified": 1636038490317, "created": 1635761360921, "name": "member", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1635761360921, "_type": "request_group"}, {"_id": "req_3bf4764daf7d4594a032fd54a5263878", "parentId": "fld_5957df0e21c04490b8e9548624dc6ed8", "modified": 1644835966625, "created": 1635770558129, "url": "{{ _.base_url }}/public/member", "name": "/member", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n\t\"email\": \"email\",\n\t\"first_name\": \"first_name\",\n\t\"last_name\": \"last_name\",\n\t\"phone_number\": \"phone_number\",\n\t\"type\": \"type\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_b0978a956bc94331bf4202166052157e"}], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}", "disabled": true}, "metaSortKey": -1634949324571, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_8e45ebbb16c5406c908d13f3fd5beb99", "parentId": "fld_5957df0e21c04490b8e9548624dc6ed8", "modified": 1640858332208, "created": 1636037824018, "url": "{{ _.base_url }}/member", "name": "/member/{member_id}", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n\t\"email\": \"email\",\n\t\"first_name\": \"first_name\",\n\t\"last_name\": \"last_name\",\n\t\"phone_number\": \"phone_number\",\n\t\"type\": \"type\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_b0978a956bc94331bf4202166052157e"}], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}"}, "metaSortKey": -1634543301408, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_52e34e68710d4c51b0fbdfcce9e23a97", "parentId": "fld_5957df0e21c04490b8e9548624dc6ed8", "modified": 1641823264387, "created": 1636038397942, "url": "{{ _.base_url }}/member", "name": "/member", "description": "", "method": "DELETE", "body": {"mimeType": "application/json", "text": "{\n\t\"member_ids\" : [\"WPDDNzQnxEWMg422TiBm\"]\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_bae1e33054b74784a49cafe78a8ffce6"}], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}", "disabled": true}, "metaSortKey": -1634340289826.5, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_b491a60a0ea24ef4bb831c8125aeabb9", "parentId": "fld_0f38767bb7424baab826bdc795bb5428", "modified": 1644835988076, "created": 1640855251872, "url": "{{ _.base_url }}/equipper/equipments?limit=2&last_id=0egqRN7yTqjwNn27zD9s", "name": "/equipper/equipments", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}", "disabled": false}, "metaSortKey": -1638308299630, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_0f38767bb7424baab826bdc795bb5428", "parentId": "fld_583663e79472484cb9416a670b3ffd56", "modified": 1640855228362, "created": 1640855228362, "name": "equipment", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1640855228363, "_type": "request_group"}, {"_id": "fld_583663e79472484cb9416a670b3ffd56", "parentId": "wrk_003489a1600e4f12a3a56d6482f6b3f3", "modified": 1636038589134, "created": 1636038589134, "name": "equipper", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1635355339022.75, "_type": "request_group"}, {"_id": "req_c922bc456cbc49a2a3f046f5729207b4", "parentId": "fld_0f38767bb7424baab826bdc795bb5428", "modified": 1647015448643, "created": 1642691679937, "url": "{{ _.base_url }}/equipper/equipments/booked?limit=2&last_id=0egqRN7yTqjwNn27zD9s", "name": "/equipper/equipments/booked", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}", "disabled": false}, "metaSortKey": -1638290272040, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_83120fc95c2546a0aca7eb3df39bb5e8", "parentId": "fld_583663e79472484cb9416a670b3ffd56", "modified": 1640858754699, "created": 1636038589141, "url": "{{ _.base_url }}/equipper", "name": "/equipper", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}", "disabled": false}, "metaSortKey": -1635761370897, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_f8befde7e67e4b3495a78c1d34526759", "parentId": "fld_583663e79472484cb9416a670b3ffd56", "modified": 1640858756345, "created": 1636038589154, "url": "{{ _.base_url }}/equipper", "name": "/equipper", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n\t\"id\": \"id\",\n\t\"email\": \"email\",\n\t\"company\": \"company\",\n\t\"status\": \"status\",\n\t\"phone_number\": \"phone_number\",\n\t\"photo_url\": \"photo_url\",\n\t\"address\": \"address\",\n\t\"address2\": \"address2\",\n\t\"zip_code\": \"zip_code\",\n\t\"city\": \"city\",\n\t\"country\": \"country\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_b0978a956bc94331bf4202166052157e"}], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}"}, "metaSortKey": -1634543301408, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_0434b8953b6747c5aae9155f2f9cf07d", "parentId": "fld_583663e79472484cb9416a670b3ffd56", "modified": 1642598257314, "created": 1636038589158, "url": "{{ _.base_url }}/equipper", "name": "/equipper", "description": "", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}"}, "metaSortKey": -1634340289826.5, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_26f2f08178fe40aba90be870987f1c47", "parentId": "fld_583663e79472484cb9416a670b3ffd56", "modified": 1642598283551, "created": 1641465891882, "url": "{{ _.base_url }}/equipper/booking", "name": "/equipper/booking", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}", "prefix": "Bearer"}, "metaSortKey": -1634340289776.5, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_9726713de7534a86aa64e9a131b2f7ae", "parentId": "fld_0e15ec50d9004f9893c468b64a4c49ab", "modified": 1646163594266, "created": 1636038670645, "url": "{{ _.base_url }}/lodger", "name": "/lodger", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}", "disabled": false}, "metaSortKey": -1635761370897, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_0e15ec50d9004f9893c468b64a4c49ab", "parentId": "wrk_003489a1600e4f12a3a56d6482f6b3f3", "modified": 1636038670637, "created": 1636038670637, "name": "lodger", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1635152328073.625, "_type": "request_group"}, {"_id": "req_36ec03fc3f9d421e8a78afbd2240b6e3", "parentId": "fld_0e15ec50d9004f9893c468b64a4c49ab", "modified": 1644836000798, "created": 1636038670655, "url": "{{ _.base_url }}/lodger", "name": "/lodger", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n\t\"id\": \"Zg09yvsfLDXbPap6fhiiPmvcgQ53\",\n\t\"type\": \"pro\",\n\t\"email\": \"<EMAIL>\",\n\t\"first_name\": \"\",\n\t\"last_name\": \"\",\n\t\"company\": \"Oussama Mhamdi\",\n\t\"status\": false,\n\t\"phone_number\": \"+216123456789\",\n\t\"photo_url\": \"\",\n\t\"address\": {\n\t\t\"address\": \"Raouede, Gouvernorat de l’Ariana, Tunisia\",\n\t\t\"address2\": \"\",\n\t\t\"zip_code\": \"2083\",\n\t\t\"city\": \"Ariana\",\n\t\t\"country\": \"Ariana\",\n\t\t\"state\": \"\",\n\t\t\"latitude\": 0,\n\t\t\"longitude\": 0\n\t}\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_b0978a956bc94331bf4202166052157e"}], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}"}, "metaSortKey": -1634543301408, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_0b13c21e0298433198737aa1fec7694b", "parentId": "fld_0e15ec50d9004f9893c468b64a4c49ab", "modified": 1641831206122, "created": 1636038670661, "url": "{{ _.base_url }}/lodger", "name": "/lodger", "description": "", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}"}, "metaSortKey": -1634340289826.5, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_fc31c887ac6b4042bac7383c2a41d5b4", "parentId": "fld_0e15ec50d9004f9893c468b64a4c49ab", "modified": 1642086904844, "created": 1641466032922, "url": "{{ _.base_url }}/lodger/booking", "name": "/lodger/booking", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}", "prefix": "Bearer"}, "metaSortKey": -1634340289776.5, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_6526f6fefba54e3a93be4408fa6fcc03", "parentId": "fld_0e15ec50d9004f9893c468b64a4c49ab", "modified": 1642690411744, "created": 1642598332630, "url": "{{ _.base_url }}/lodger/booking/accepted", "name": "/lodger/booking/accepted", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}", "prefix": "Bearer"}, "metaSortKey": -1633031834711.375, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_85fccfa4b0d24e93aa26507f6250408f", "parentId": "fld_ca68cbddbce143fe86f2fcd12b5ebd46", "modified": 1642690364680, "created": 1636038509028, "url": "{{ _.base_url }}/projects", "name": "/projects", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}"}, "metaSortKey": -1634949324571, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_ca68cbddbce143fe86f2fcd12b5ebd46", "parentId": "wrk_003489a1600e4f12a3a56d6482f6b3f3", "modified": 1636038509016, "created": 1636038509016, "name": "project", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1634949317124.5, "_type": "request_group"}, {"_id": "req_3e0134e3bfab48d7935a7b35be76d5e3", "parentId": "fld_ca68cbddbce143fe86f2fcd12b5ebd46", "modified": 1642608230216, "created": 1642607032570, "url": "{{ _.base_url }}/project", "name": "/project", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n\t\"name\": \"name\",\n\t\"delivery_address\": {\n\t\t\"address\": \"Rao<PERSON><PERSON>, Gouvernorat de l’Ariana, Tunisia\",\n\t\t\"address2\": \"\",\n\t\t\"zip_code\": \"2083\",\n\t\t\"city\": \"Ariana\",\n\t\t\"country\": \"Ariana\",\n\t\t\"state\": \"\",\n\t\t\"latitude\": 0,\n\t\t\"longitude\": 0\n\t},\n\t\"billing_address\": {\n\t\t\"address\": \"Raouede, Gouvernorat de l’Ariana, Tunisia\",\n\t\t\"address2\": \"\",\n\t\t\"zip_code\": \"2083\",\n\t\t\"city\": \"Ariana\",\n\t\t\"country\": \"Ariana\",\n\t\t\"state\": \"\",\n\t\t\"latitude\": 0,\n\t\t\"longitude\": 0\n\t},\n\t\"start_date\": \"0001-01-01T00:00:00Z\",\n\t\"end_date\": \"0001-01-01T00:00:00Z\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_b0978a956bc94331bf4202166052157e"}], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}"}, "metaSortKey": -1634847818780.25, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_f70917009969482080be0404b72d8c37", "parentId": "fld_ca68cbddbce143fe86f2fcd12b5ebd46", "modified": 1642608228594, "created": 1636038509031, "url": "{{ _.base_url }}/project/tCc1e5W8ni8g6kttl4cM", "name": "/project/{project_id}", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n\t\"name\": \"name\",\n\t\"delivery_address\": {\n\t\t\"address\": \"Rao<PERSON><PERSON>, Gouvernorat de l’Ariana, Tunisia\",\n\t\t\"address2\": \"\",\n\t\t\"zip_code\": \"2083\",\n\t\t\"city\": \"Ariana\",\n\t\t\"country\": \"Ariana2\",\n\t\t\"state\": \"\",\n\t\t\"latitude\": 0,\n\t\t\"longitude\": 0\n\t},\n\t\"billing_address\": {\n\t\t\"address\": \"Raouede, Gouvernorat de l’Ariana, Tunisia\",\n\t\t\"address2\": \"\",\n\t\t\"zip_code\": \"2083\",\n\t\t\"city\": \"Ariana\",\n\t\t\"country\": \"Ariana\",\n\t\t\"state\": \"\",\n\t\t\"latitude\": 0,\n\t\t\"longitude\": 0\n\t},\n\t\"start_date\": \"0001-01-01T00:00:00Z\",\n\t\"end_date\": \"0001-01-01T00:00:00Z\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_b0978a956bc94331bf4202166052157e"}], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}"}, "metaSortKey": -1634543301408, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_2564d653837c4a4ba6c53f2036eea0e4", "parentId": "fld_ca68cbddbce143fe86f2fcd12b5ebd46", "modified": 1642608229135, "created": 1636038509036, "url": "{{ _.base_url }}/project/WPDDNzQnxEWMg422TiBm", "name": "/project/{project_id}", "description": "", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}"}, "metaSortKey": -1634340289826.5, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_2731e26b0812444ba155b6ab631e4d40", "parentId": "fld_181b27b874d04876a640559d960a2b46", "modified": 1642607271465, "created": 1639752764632, "url": "{{ _.base_url }}/equipment/book", "name": "/equipment/book", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n    \"equipper_id\": \"dlNamuS9xVESeOk1V3Uy\",\n    \"equipment_id\": \"2Tz327Gt9EPmxTopfPQ5\",\n    \"start_date\": \"2021-12-12T23:00:00Z\",\n    \"end_date\": \"2021-12-13T23:00:00Z\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_b0978a956bc94331bf4202166052157e"}], "authentication": {"type": "bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}", "prefix": "Bearer"}, "metaSortKey": -1639915868911, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_181b27b874d04876a640559d960a2b46", "parentId": "fld_7777b475df604bb2b42c6ca07a28f795", "modified": 1639915868761, "created": 1639915868761, "name": "book", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1639915868761, "_type": "request_group"}, {"_id": "fld_7777b475df604bb2b42c6ca07a28f795", "parentId": "wrk_003489a1600e4f12a3a56d6482f6b3f3", "modified": 1639248868495, "created": 1636038708135, "name": "equipment", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1632027894102.75, "_type": "request_group"}, {"_id": "req_d0119f8fe1074f09a48d6d5f605fe51c", "parentId": "fld_181b27b874d04876a640559d960a2b46", "modified": 1641400465212, "created": 1639757384923, "url": "{{ _.base_url }}/equipment/book/mnjjusqshABNeJeu3bFk/accept", "name": "/equipment/book/{book_equipment_id}/accept", "description": "", "method": "POST", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}"}, "metaSortKey": -1639915868861, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_9529d910fd8b40878ce932a0e0374190", "parentId": "fld_181b27b874d04876a640559d960a2b46", "modified": 1640773415418, "created": 1639915917908, "url": "{{ _.base_url }}/equipment/book/mnjjusqshABNeJeu3bFk/reject", "name": "/equipment/book/{book_equipment_id}/reject", "description": "", "method": "POST", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}"}, "metaSortKey": -1639915868836, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_9c9348585a98422fb35d7412e155ccf7", "parentId": "fld_181b27b874d04876a640559d960a2b46", "modified": 1640858713590, "created": 1639915926544, "url": "{{ _.base_url }}/equipment/book/mnjjusqshABNeJeu3bFk/cancel", "name": "/equipment/book/{book_equipment_id}/cancel", "description": "", "method": "POST", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}"}, "metaSortKey": -1639915868823.5, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_69d7c9bfb8904f98ba2349aec8ec94cb", "parentId": "fld_7777b475df604bb2b42c6ca07a28f795", "modified": 1647450184054, "created": 1636038708153, "url": "{{ _.base_url }}/public/equipment/0oEQBjPuJmvD8VVrkBhD", "name": "/equipment/{equipment_id}", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}", "disabled": true}, "metaSortKey": -1634949324571, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_112b637ad5ab43139716a18b33d71340", "parentId": "fld_7777b475df604bb2b42c6ca07a28f795", "modified": 1647450639872, "created": 1647450199333, "url": "{{ _.base_url }}/equipments/upload", "name": "/equipments/upload", "description": "", "method": "POST", "body": {"mimeType": "multipart/form-data", "params": [{"id": "pair_f99f999878214380aee762564d55a7ec", "name": "file", "value": "", "description": "", "type": "file", "fileName": "/Users/<USER>/Downloads/Location Ekipme.xlsx"}]}, "parameters": [], "headers": [{"name": "Content-Type", "value": "multipart/form-data", "id": "pair_099294a09ba74fdca7aab527dda408bd"}], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}", "disabled": true}, "metaSortKey": -1634898571675.625, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_ff20728b23824584985ac5cc952230ff", "parentId": "fld_7777b475df604bb2b42c6ca07a28f795", "modified": 1640858711139, "created": 1636038766501, "url": "{{ _.base_url }}/equipment", "name": "/equipment", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n\t\"name\": \"name\",\n\t\"address\": \"address\",\n\t\"available_at\": \"available_at\",\n\t\"price\": \"price\",\n\t\"description\": \"description\",\n\t\"technical_info\": \"technical_info\",\n\t\"usage\": \"usage\",\n\t\"warning\": \"warning\",\n\t\"equipper_id\": \"equipper_id\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_b0978a956bc94331bf4202166052157e"}], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}"}, "metaSortKey": -1634746312989.5, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_03c327ce1e53463385a5e01c2fa1f9a4", "parentId": "fld_7777b475df604bb2b42c6ca07a28f795", "modified": 1640858709664, "created": 1636038708168, "url": "{{ _.base_url }}/equipment/equipment_id", "name": "/equipment/{equipment_id}", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n\t\"name\": \"name\",\n\t\"address\": \"address\",\n\t\"available_at\": \"available_at\",\n\t\"price\": \"price\",\n\t\"description\": \"description\",\n\t\"technical_info\": \"technical_info\",\n\t\"usage\": \"usage\",\n\t\"warning\": \"warning\",\n\t\"equipper_id\": \"equipper_id\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_b0978a956bc94331bf4202166052157e"}], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}"}, "metaSortKey": -1634543301408, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_4ddc3d8035104db0b022d7ffa0b550ab", "parentId": "fld_7777b475df604bb2b42c6ca07a28f795", "modified": 1641494901870, "created": 1636038708188, "url": "{{ _.base_url }}/equipment/equipment_id", "name": "/equipment/{equipment_id}", "description": "", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "prefix": "Bearer", "token": "{{ _.token }}"}, "metaSortKey": -1634340289826.5, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "env_5348cf677bec20541fb72e30f377fa1a538eb70d", "parentId": "wrk_003489a1600e4f12a3a56d6482f6b3f3", "modified": 1636039755187, "created": *************, "name": "Base Environment", "data": {"token": "{% response 'body', 'req_d1a7af0e32464bcf9422fdcfe26ca798', 'b64::JC5pZFRva2Vu::46b', 'always', 60 %}"}, "dataPropertyOrder": {"&": ["token"]}, "color": null, "isPrivate": false, "metaSortKey": *************, "_type": "environment"}, {"_id": "jar_5348cf677bec20541fb72e30f377fa1a538eb70d", "parentId": "wrk_003489a1600e4f12a3a56d6482f6b3f3", "modified": *************, "created": *************, "name": "<PERSON><PERSON><PERSON>", "cookies": [{"key": "__Host-GAPS", "value": "1:DUn1vxMnlIwdCIjgVupswF6WOk0CDQ:v2FOAHB2bnGL0h8Y", "expires": "2023-11-01T12:46:06.000Z", "domain": "accounts.google.com", "path": "/", "secure": true, "httpOnly": true, "extensions": ["Priority=HIGH"], "hostOnly": true, "creation": "2021-10-19T12:40:56.647Z", "lastAccessed": "2021-11-01T12:46:06.381Z", "id": "*****************"}], "_type": "cookie_jar"}, {"_id": "spc_8c77529fa1004cd4ae2e2b5bdfd22f26", "parentId": "wrk_003489a1600e4f12a3a56d6482f6b3f3", "modified": *************, "created": *************, "fileName": "tooler", "contents": "", "contentType": "yaml", "_type": "api_spec"}, {"_id": "env_20ce141a5bb14236b205774514b6a646", "parentId": "env_5348cf677bec20541fb72e30f377fa1a538eb70d", "modified": *************, "created": *************, "name": "localhost", "data": {"base_url": "localhost:5051"}, "dataPropertyOrder": {"&": ["base_url"]}, "color": null, "isPrivate": false, "metaSortKey": *************, "_type": "environment"}, {"_id": "env_59e1b614e10f49749be3e51885b64d65", "parentId": "env_5348cf677bec20541fb72e30f377fa1a538eb70d", "modified": *************, "created": 1636037278685, "name": "prod", "data": {"base_url": "https://tooler-zqyj3vghfq-uc.a.run.app"}, "dataPropertyOrder": {"&": ["base_url"]}, "color": null, "isPrivate": false, "metaSortKey": 1636037278685, "_type": "environment"}]}