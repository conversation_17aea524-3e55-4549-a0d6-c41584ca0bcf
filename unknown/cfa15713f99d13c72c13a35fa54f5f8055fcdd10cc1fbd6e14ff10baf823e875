import React, { useState, useEffect } from 'react';
import { useTeamContext } from '../../../context/Team_context';
import { useProjects } from '../../../context/Project_context';
import {
  ADMIN,
  POWER_COLLABORATOR,
  COLLABORATOR,
  POWER_COLLABORATOR_PRIVILEGE,
  COLL<PERSON>ORATOR_PRIVILEGE,
  OWNER_PRIVILEGE
} from '../../../helpers/Account_type';
import MembersTable from '../../tables/Members_table';
import TableSearchBar from '../../buttons/Table_search_bar';
import TableCrudMenu from './Table_crud_menu';
import DeleteModal from '../../modals/Delete_modal';
import EditMemberModal from './Edit_member_modal';
import { UncheckCheckbox } from '../../../helpers/Checkbox';
import {
  concatList,
  extractFromList,
  splitTeamList,
  getConnectedUser
} from '../../../helpers/Team_helper';
import Popup from '../../modals/Popup';
import SuccessPopUp from '../../modals/Success_pop_up';
import { DELETE_MEMBER, UPDATE_MEMBER } from '../../../helpers/Url_prefixes';
import ToloIsLoading from '../../cards/Tolo_is_loading';
import RenderIf from '../../Render_if';
import InviteMemberModal from './Invite_member_modal';
import { isEmpty } from 'lodash';
import NoResults from '../../../../components/search_result/No_results';
import TeamEmptyState from '../../../../style/assets/img/empty_state/Team_empty_state.svg';

import { useNavigate, useOutletContext } from 'react-router-dom';

export default function TeamManagement({ t, role, handleChangeTab }) {
  const [loadedInfo] = useOutletContext();
  const navigate = useNavigate();
  const [showDeleteModalMember, setShowDeleteModalMember] = useState(false);
  const [showAddMemberModal, setShowAddMemberModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editMemberTitle, setEditMemberTitle] = useState();
  const [selectedMembers, setSelectedMembers] = useState([]);
  const [type, setType] = useState(ADMIN);
  const [team, setTeam] = useState({});
  const [status, setStatus] = useState('');
  const { GetProjects } = useProjects();
  const [isLoading, setIsLoading] = useState(true);
  const [messageSuccessPopUp, setMessageSuccessPopUp] = useState('');
  const [messagePopUp, setMessagePopUp] = useState('');
  const [show, setShow] = useState(false);
  const [showSuccessPopUp, setShowSuccessPopUp] = useState(false);
  const [optionalMessage, setOptionalMessage] = useState('');
  const [errorPrefix, setErrorPrefix] = useState(null);
  const [projects, setProjects] = useState([]);
  const [currentUserType, setCurrentUserType] = useState('');
  const [isEmptyTeam, setIsEmptyTeam] = useState(false);

  const {
    AddMember,
    EditMember,
    DeleteMember,
    GetTeamLodger,
    CheckIfMemberExists
  } = useTeamContext();

  async function handleCloseSuccessPopUp() {
    handleCloseAddAdminModal();
    handleHideDeleteModalMember();
    handleCloseEditModal();
    setShowSuccessPopUp(false);
    await refreshTeam();
    UncheckCheckbox();
    setSelectedMembers([]);
  }

  function handleShowSuccessPopUp(message) {
    if (message) {
      setMessageSuccessPopUp(message);
    }
    setShowSuccessPopUp(true);
  }

  async function onClose() {
    handleCloseAddAdminModal();
    handleHideDeleteModalMember();
    handleCloseEditModal();
    setShow(false);
    await refreshTeam();
    UncheckCheckbox();
    setSelectedMembers([]);
  }

  function showPopup(message, result, optionalMessage) {
    setOptionalMessage(optionalMessage);
    setStatus(result);
    setMessagePopUp(message);
    setShow(true);
  }

  function SelectAllMembersByType(type, value) {
    let list = [];
    let treatedList = [];
    switch (type) {
      case ADMIN:
        list = team.admins;
        break;
      case POWER_COLLABORATOR:
        list = team.power_collaborators;
        break;
      case COLLABORATOR:
        list = team.collaborators;
        break;
      default:
        break;
    }
    if (value) {
      treatedList = concatList(list, selectedMembers);
    } else {
      treatedList = extractFromList(list, selectedMembers);
    }
    treatedList.forEach(
      (member, index) =>
        member.membership_status === 'owner' && treatedList.splice(index, 1)
    );
    setSelectedMembers(treatedList);
  }
  const MemberTitle = (type) => {
    switch (type) {
      case POWER_COLLABORATOR:
        setEditMemberTitle(t('Edit_power_collaborator_member'));
        break;

      case COLLABORATOR:
        setEditMemberTitle(t('Edit_collaborator_member'));
        break;

      default:
        setEditMemberTitle(t('Edit_admin_member'));
    }
  };

  function handleShowEditModal(type) {
    if (splitTeamList(selectedMembers, type).length === 1) {
      setType(type);
      setShowEditModal(true);
      MemberTitle(type);
    }
  }

  function handleShowAddAdminModal(type) {
    setType(type);
    setShowAddMemberModal(true);
  }

  function handleCloseEditModal() {
    setShowEditModal(false);
  }

  function handleCloseAddAdminModal() {
    setShowAddMemberModal(false);
  }

  function handleAddMember(member) {
    setSelectedMembers(member);
  }

  async function handleDeleteMember(memberIds) {
    setIsLoading(true);
    const response = await DeleteMember(memberIds);
    if (response.status === 200 || response.status === 201) {
      handleShowSuccessPopUp();
    } else {
      setErrorPrefix(DELETE_MEMBER);
      setStatus(response);
      showPopup(
        t('Delete_member_main_message'),
        response,
        t('Please_try_again_later')
      );
    }
    setIsLoading(false);
  }

  async function handleEditMember(member) {
    const response = await EditMember(member);
    if (response.status === 200 || response.status === 201) {
      handleShowSuccessPopUp();
    } else {
      setErrorPrefix(UPDATE_MEMBER);
      setStatus(response);
      showPopup(
        t('Update_member_main_message'),
        response,
        t('Please_try_again_later')
      );
    }
  }

  function handleShowDeleteModalMember(type) {
    if (splitTeamList(selectedMembers, type).length > 0) {
      setType(type);
      setShowDeleteModalMember(true);
    }
  }

  async function handleHideDeleteModalMember() {
    setShowDeleteModalMember(false);
  }

  async function refreshTeam() {
    setIsLoading(true);
    const res = await GetTeamLodger();
    if (res.status === 200) {
      setTeam(res.data);
    }
    setIsLoading(false);
  }
  useEffect(() => {
    async function initTeamList() {
      setIsLoading(true);
      const res = await GetTeamLodger();
      if (res.status === 200 && res.data !== null) {
        setTeam(res.data);
      } else {
        setIsEmptyTeam(true);
        setTeam({
          admins: [
            {
              ...loadedInfo,
              membership_status: 'owner',
              privilege_level: 10
            }
          ]
        });
      }
    }
    async function intiProjectList() {
      const res = await GetProjects();
      if (res.status === 200 && res.data !== null) {
        setProjects(
          res.data.map((el) => {
            return {
              label: el.name,
              value: el.id
            };
          })
        );
      } else {
        setProjects([]);
      }
      setIsLoading(false);
    }

    initTeamList();
    intiProjectList();
  }, [loadedInfo]);

  useEffect(() => {
    (async function handleDisabledActions() {
      const tempUser = getConnectedUser(team);
      setCurrentUserType(tempUser);
    })();
  }, [team]);

  if (isLoading) {
    return <ToloIsLoading />;
  }

  if (isEmptyTeam && isEmpty(projects) && !isLoading) {
    return (
      <div className="panel" id="p3">
        <div className="team-managements white-bg">
          <NoResults
            message={t('No_projects_found')}
            image={TeamEmptyState}
            buttonText={t('Create_project_LMP')}
            onClick={() => {
              navigate('/renterManagementPortal/projectManagement');
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="panel" id="p3">
      <RenderIf condition={showAddMemberModal}>
        <InviteMemberModal
          CheckIfMemberExists={CheckIfMemberExists}
          show={showAddMemberModal}
          handleClose={handleCloseAddAdminModal}
          onInviteClick={AddMember}
          setErrorPrefix={setErrorPrefix}
          type={type}
          showSuccess={handleShowSuccessPopUp}
          showFailure={showPopup}
          projectsOptions={projects}
        />
      </RenderIf>
      <RenderIf condition={showDeleteModalMember}>
        <DeleteModal
          show={showDeleteModalMember}
          names={selectedMembers}
          handleClose={handleHideDeleteModalMember}
          onDelete={handleDeleteMember}
          type={type}
          isLoading={isLoading}
        />
      </RenderIf>
      <RenderIf condition={showEditModal}>
        <EditMemberModal
          currentUserType={currentUserType}
          show={showEditModal}
          t={t}
          handleClose={handleCloseEditModal}
          selectedMembers={selectedMembers}
          onEditClick={handleEditMember}
          type={type}
          editMemberTitle={editMemberTitle}
          role={role}
        />
      </RenderIf>
      <SuccessPopUp
        show={showSuccessPopUp}
        onClose={handleCloseSuccessPopUp}
        message={messageSuccessPopUp}
      />
      <Popup
        response={status}
        show={show}
        onClose={onClose}
        optionalMessage={optionalMessage}
        message={messagePopUp}
        prefix={errorPrefix}
        t={t}
      />

      <div className="team-managements white-bg mt-4">
        <h2 className="t-header-h6 c-fake-black bold">{t('Owner')}</h2>
        <MembersTable
          type={ADMIN}
          handleChangeTab={handleChangeTab}
          status="owner"
          members={
            team &&
            team.admins &&
            team.admins.filter((member) => member.membership_status === 'owner')
          }
          onClick={handleAddMember}
          selectedMembers={selectedMembers}
          OnCheckAll={SelectAllMembersByType}
          t={t}
        />
      </div>

      <div className="team-managements white-bg mt-4">
        <h2 className="t-header-h6 c-fake-black bold">{t('Administrator')}</h2>
        <RenderIf
          condition={currentUserType.privilege_level === OWNER_PRIVILEGE}
        >
          <TableSearchBar onClick={() => handleShowAddAdminModal(ADMIN)} />
        </RenderIf>
        <MembersTable
          type={ADMIN}
          handleChangeTab={handleChangeTab}
          members={
            team &&
            team.admins &&
            team.admins.filter((member) => member.membership_status !== 'owner')
          }
          hasPrivilege={currentUserType.privilege_level === OWNER_PRIVILEGE}
          onClick={handleAddMember}
          selectedMembers={selectedMembers}
          OnCheckAll={SelectAllMembersByType}
          t={t}
        />
        <RenderIf
          condition={
            team &&
            team.admins &&
            team.admins.length > 1 &&
            currentUserType.privilege_level === OWNER_PRIVILEGE
          }
        >
          <TableCrudMenu
            list={
              team &&
              team.admins &&
              team.admins.filter(
                (member) => member.membership_status !== 'owner'
              )
            }
            disabledEdit={splitTeamList(selectedMembers, ADMIN).length !== 1}
            disabledDelete={splitTeamList(selectedMembers, ADMIN).length === 0}
            onEditClick={() => handleShowEditModal(ADMIN)}
            onDeleteClick={() => handleShowDeleteModalMember(ADMIN)}
            currentUserType={currentUserType}
            t={t}
          />
        </RenderIf>
      </div>

      <div className="team-managements white-bg mt-4">
        <h2 className="t-header-h6 c-fake-black bold">
          {t('Power_collaborator')}
        </h2>
        <RenderIf
          condition={
            currentUserType.privilege_level > POWER_COLLABORATOR_PRIVILEGE
          }
        >
          <TableSearchBar
            onClick={() => handleShowAddAdminModal(POWER_COLLABORATOR)}
          />
        </RenderIf>
        <MembersTable
          handleChangeTab={handleChangeTab}
          members={team.power_collaborators}
          hasPrivilege={
            currentUserType.privilege_level > POWER_COLLABORATOR_PRIVILEGE
          }
          onClick={handleAddMember}
          OnCheckAll={SelectAllMembersByType}
          selectedMembers={selectedMembers}
          type={POWER_COLLABORATOR}
          t={t}
        />
        <RenderIf
          condition={
            team.power_collaborators &&
            currentUserType.privilege_level > POWER_COLLABORATOR_PRIVILEGE
          }
        >
          <TableCrudMenu
            list={team.power_collaborators}
            disabledEdit={
              splitTeamList(selectedMembers, POWER_COLLABORATOR).length !== 1
            }
            disabledDelete={
              splitTeamList(selectedMembers, POWER_COLLABORATOR).length === 0
            }
            onEditClick={() => handleShowEditModal(POWER_COLLABORATOR)}
            onDeleteClick={() =>
              handleShowDeleteModalMember(POWER_COLLABORATOR)
            }
            currentUserType={currentUserType}
            t={t}
          />
        </RenderIf>
      </div>
      <div className="team-managements white-bg mt-4">
        <h2 className="t-header-h6 c-fake-black bold">{t('Collaborator')}</h2>
        <RenderIf
          condition={currentUserType.privilege_level > COLLABORATOR_PRIVILEGE}
        >
          <TableSearchBar
            onClick={() => handleShowAddAdminModal(COLLABORATOR)}
          />
        </RenderIf>
        <MembersTable
          type={COLLABORATOR}
          hasPrivilege={
            currentUserType.privilege_level > COLLABORATOR_PRIVILEGE
          }
          handleChangeTab={handleChangeTab}
          selectedMembers={selectedMembers}
          members={team.collaborators}
          onClick={handleAddMember}
          OnCheckAll={SelectAllMembersByType}
          t={t}
        />
        <RenderIf
          condition={
            team.collaborators &&
            currentUserType.privilege_level > COLLABORATOR_PRIVILEGE
          }
        >
          <TableCrudMenu
            list={team.collaborators}
            disabledEdit={
              splitTeamList(selectedMembers, COLLABORATOR).length !== 1
            }
            disabledDelete={
              splitTeamList(selectedMembers, COLLABORATOR).length === 0
            }
            onEditClick={() => handleShowEditModal(COLLABORATOR)}
            onDeleteClick={() => handleShowDeleteModalMember(COLLABORATOR)}
            currentUserType={currentUserType}
            t={t}
          />
        </RenderIf>
      </div>
    </div>
  );
}
