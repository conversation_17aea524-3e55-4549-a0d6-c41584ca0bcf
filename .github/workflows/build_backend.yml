name: Build and Test backend

on:
  pull_request:
    branches: [backend]

jobs:
  build_test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Install dependencies
        uses: actions/setup-go@v3
        with:
          go-version: "1.23"

      - name: go version
        run: go version

      - name: build
        run: cd backend && go build ./...

      - name: test
        run: cd backend && go test ./... -race -v -count=1

      - name: golangci-lint
        uses: golangci/golangci-lint-action@v3
        with:
          skip-pkg-cache: true
          skip-cache: true
          skip-build-cache: true
          only-new-issues: false
          working-directory: backend
