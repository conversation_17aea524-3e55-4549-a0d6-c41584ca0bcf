name: Build on Push

on:
  push:
    branches: ['**']
  pull_request:
    branches: ['**']

jobs:
  changes:
    runs-on: ubuntu-latest
    outputs:
      backend: ${{ steps.filter.outputs.backend }}
      frontend: ${{ steps.filter.outputs.frontend }}
    steps:
      - uses: actions/checkout@v3
      
      - uses: dorny/paths-filter@v2
        id: filter
        with:
          filters: |
            backend:
              - 'backend/**'
            frontend:
              - 'src/**'
              - 'public/**'
              - 'package.json'
              - 'yarn.lock'
              - 'package-lock.json'
              - 'tsconfig.json'
              - 'vite.config.*'
              - '.eslintrc*'
              - '.prettierrc*'
              - '*.{js,jsx,ts,tsx,json,yml,yaml}'

  build-backend:
    needs: changes
    if: needs.changes.outputs.backend == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: "1.23"

      - name: Go version
        run: go version

      - name: Build backend
        run: cd backend && go build ./...

      - name: Test backend
        run: cd backend && go test ./... -race -v -count=1

      - name: Run golangci-lint
        uses: golangci/golangci-lint-action@v3
        with:
          version: latest
          working-directory: backend

  build-frontend:
    needs: changes
    if: needs.changes.outputs.frontend == 'true'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Build
        run: yarn run build

      - name: Test
        run: yarn run test

      - name: Lint
        run: yarn run lint