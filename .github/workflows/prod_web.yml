name: Deploy Web Front to Production Environment

on:
  push:
    tags:
      - 'v[0-9].[0-9]+.[0-9]+-web'

jobs:
  build_and_preview:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Get Yarn cache directory
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - name: Use Yarn cache
        uses: actions/cache@v3
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ matrix.node-version }}-${{ hashFiles('**/yarn.lock') }}

      - name: Install dependencies
        run: yarn install --prefer-offline --frozen-lockfile

      - name: Create env file
        run: |
          cat << EOF > .env
            FAST_REFRESH= ${{ secrets.FAST_REFRESH }}
            SKIP_PREFLIGHT_CHECK= ${{ secrets.SKIP_PREFLIGHT_CHECK }}
            CHOKIDAR_USEPOLLING= ${{ secrets.CHOKIDAR_USEPOLLING }}
            VITE_REACT_APP_BASE_URL= ${{ secrets.PROD_REACT_APP_BASE_URL }}
            VITE_ALGOLIA_ID= ${{ secrets.ALGOLIA_ID }}
            VITE_ALGOLIA_API_KEY= ${{ secrets.ALGOLIA_API_KEY }}
            VITE_ALGOLIA_INDEX_NAME= ${{ secrets.PROD_ALGOLIA_INDEX_NAME }}
            VITE_ALGOLIA_INDEX_NAME_STANDARDS= ${{ secrets.PROD_ALGOLIA_INDEX_NAME_STANDARDS }}
            VITE_DERENTAL_BIDZ_EMAIL= ${{ secrets.DERENTAL_BIDZ_EMAIL }}
            VITE_DERENTAL_SUPER_ADMIN_PASSWORD= ${{ secrets.DERENTAL_SUPER_ADMIN_PASSWORD }}
            VITE_DERENTAL_BIDZ_STORAGE_EQUIPMENTS_IMG_PATH= ${{ secrets.PROD_DERENTAL_BIDZ_STORAGE_EQUIPMENTS_IMG_PATH }}
            VITE_ALGOLIA_INDEX_COVERAGE_AREAS_NAME= ${{ secrets.PROD_ALGOLIA_INDEX_COVERAGE_AREAS_NAME }}
            VITE_ENV=production
            VITE_PUBLIC_POSTHOG_KEY= ${{ secrets.POSTHOG_API_KEY }}
            VITE_PUBLIC_POSTHOG_HOST= ${{ secrets.POSTHOG_HOST || 'https://us.i.posthog.com' }}
          EOF

      - name: build
        run: yarn run build

      - name: test
        run: yarn run test

      - name: lint
        run: yarn run lint

      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.PROD_FIREBASE_SERVICE_ACCOUNT }}'
          expires: 30d
          projectId: '${{ secrets.PROD_FIREBASE_PROJECT_ID }}'
          channelId: live
